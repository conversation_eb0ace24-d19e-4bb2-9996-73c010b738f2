using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace AMprover.Data.Repositories;

public class DataQueryOptions<TEntity>
{
    public static readonly DataQueryOptions<TEntity> Default = new();

    /// <summary>
    /// Disabling Tracking improved query performance but disables the ability to modify ans save back the retrieved entities. Default: false
    /// </summary>
    public bool DisableTracking { get; set; } = false;

    /// <summary>
    /// Only retrieves the specified columns in the resulting query. Supports nesting (might be needed to add them to IncludedNavigationProperties). Usage: x => new { x.Id, x.Name}
    /// </summary>
    [Obsolete("Not implemented yet)")]
    public Expression<Func<TEntity, dynamic>> SelectProperties { get; set; }

    /// <summary>
    /// Pointers that will included related entities
    /// </summary>
    /// <remarks>Nesting not yet tested.</remarks>
    [Obsolete("Not yet tested")]
    public string[] IncludedNavigationProperties { get; set; }

    /// <summary>
    /// Determine automatically that all DIRECT (only) related entities (navigation properties) will be loaded within the executing query operation. Default: false
    /// </summary>
    public bool IncludeDirectNavigationProperties { get; set; } = false;

    /// <summary>
    /// Apply sorting to the query. Order does matter!
    /// </summary>
    /// <remarks>Nesting not yet tested.</remarks>
    public List<SortOption<TEntity>> SortOptions { get; set; } = [];
}