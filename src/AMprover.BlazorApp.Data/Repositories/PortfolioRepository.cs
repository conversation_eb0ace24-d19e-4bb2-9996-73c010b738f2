using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.Data.Entities.Identity;
using AMprover.Data.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Repositories;

public interface IPortfolioRepository
{
    Task<List<Portfolio>> GetAllAsync();

    Task<List<Portfolio>> GetForUserAsync(string userId);

    Portfolio GetLastSelectedForUser(string userId);

    Task<Portfolio> GetLastSelectedForUserAsync(string userId);

    /// <summary>
    /// Updates the LastSelected of the given project assignment to "now".
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="portfolioId"></param>
    /// <returns></returns>
    void UpdateLastSelectedForUser(string userId, int portfolioId);

    Task UpdateLastSelectedForUserAsync(string userId, int portfolioId);

    /// <summary>
    /// Assigns (true) or Revokes (false) a portfolio to the given user.
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="portfolioId"></param>
    /// <param name="assign"></param>
    Task<int> AssignAsync(string userId, int portfolioId, bool assign);
}

public class PortfolioRepository(IDbContextFactory<MainDbContext> dbContextFactory) : IPortfolioRepository
{
    public async Task<List<Portfolio>> GetAllAsync()
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync();
        return await dbContext.Portfolios.Include(p => p.UserAssignments).AsNoTracking().ToListAsync();
    }

    public async Task<List<Portfolio>> GetForUserAsync(string userId)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync();
        return await dbContext.PortfolioAssignments.Where(pa => pa.UserId == userId).Include(x => x.Portfolio)
            .Select(x => x.Portfolio).AsNoTracking().ToListAsync();
    }

    public Portfolio GetLastSelectedForUser(string userId)
    {
        using var dbContext = dbContextFactory.CreateDbContext();
        return dbContext.PortfolioAssignments?.Where(pa => pa.UserId == userId)
            .OrderByDescending(x => x.LastSelected)
            .Include(x => x.Portfolio).AsNoTracking().FirstOrDefault()?.Portfolio;
    }

    public async Task<Portfolio> GetLastSelectedForUserAsync(string userId)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync();
        return (await dbContext.PortfolioAssignments?.Where(pa => pa.UserId == userId)
            .OrderByDescending(x => x.LastSelected)
            .Include(x => x.Portfolio).AsNoTracking().FirstOrDefaultAsync()!)?.Portfolio;
    }

    public void UpdateLastSelectedForUser(string userId, int portfolioId)
    {
        using var dbContext = dbContextFactory.CreateDbContext();
        var portfolioAssignment =
            dbContext.PortfolioAssignments.SingleOrDefault(pa =>
                pa.UserId == userId && pa.PortfolioId == portfolioId);

        if (portfolioAssignment == null)
            return;

        portfolioAssignment.LastSelected = DateTime.Now;
        dbContext.SaveChanges();
    }

    public async Task UpdateLastSelectedForUserAsync(string userId, int portfolioId)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync();
        var portfolioAssignment =
            await dbContext.PortfolioAssignments.SingleOrDefaultAsync(pa =>
                pa.UserId == userId && pa.PortfolioId == portfolioId);

        if (portfolioAssignment == null)
            return;

        portfolioAssignment.LastSelected = DateTime.Now;
        await dbContext.SaveChangesAsync();
    }

    public int Assign(string userId, int portfolioId, bool assign)
    {
        using var dbContext = dbContextFactory.CreateDbContext();
        if (assign)
        {
            dbContext.PortfolioAssignments.Add(new PortfolioAssignment
            {
                PortfolioId = portfolioId,
                UserId = userId,
                LastSelected = DateTime.Today
            });
        }
        else
        {
            dbContext.PortfolioAssignments.RemoveRange(
                dbContext.PortfolioAssignments.Where(pa => pa.UserId == userId && pa.PortfolioId == portfolioId));
        }

        //persist to database
        return dbContext.SaveChanges();
    }

    public async Task<int> AssignAsync(string userId, int portfolioId, bool assign)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync();
        if (assign)
        {
            await dbContext.PortfolioAssignments.AddAsync(new PortfolioAssignment
            {
                PortfolioId = portfolioId,
                UserId = userId,
                LastSelected = DateTime.Today
            });
        }
        else
        {
            dbContext.PortfolioAssignments.RemoveRange(
                dbContext.PortfolioAssignments.Where(pa => pa.UserId == userId && pa.PortfolioId == portfolioId));
        }

        //persist to database
        return await dbContext.SaveChangesAsync();
    }
}