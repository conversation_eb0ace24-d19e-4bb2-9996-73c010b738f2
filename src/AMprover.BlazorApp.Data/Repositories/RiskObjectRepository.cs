using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace AMprover.Data.Repositories;

public interface IRiskObjectRepository : IAssetManagementBaseRepository<RiskObject>
{
    Task<List<RiskObject>> GetRiskObjectsByScenario(int id);
}

public class RiskObjectRepository(
    IAssetManagementDbContextFactory dbContextFactory,
    ILogger<AssetManagementBaseRepository<RiskObject>> logger)
    : AssetManagementBaseRepository<RiskObject>(dbContextFactory, logger), IRiskObjectRepository
{
    public async Task<List<RiskObject>> GetRiskObjectsByScenario(int id)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync();
        return await dbContext.RiskObject.Where(x => x.RiskObjScenarioId == id).ToListAsync();
    }
}