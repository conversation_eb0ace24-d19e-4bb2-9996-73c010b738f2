using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using AMprover.Data.Entities.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace AMprover.Data.Infrastructure;

/// <summary>
/// Specific class that wraps the AssetManagementDbContext during local migrations commands
/// </summary>
public class AssetManagementDbContextForMigrationsOnly: IDesignTimeDbContextFactory<AssetManagementDbContext>
{
    /// <summary>
    /// Need to be parameterless
    /// </summary>
    public AssetManagementDbContextForMigrationsOnly()
    {
    }

    AssetManagementDbContext IDesignTimeDbContextFactory<AssetManagementDbContext>.CreateDbContext(string[] args)
    {
        // Uncomment to to be able to attach the debugger
        // Note: needs the Just-In-Time debugger as installed individual component of Visual Studio.
        //if (System.Diagnostics.Debugger.IsAttached == false)
        //{
        //    System.Diagnostics.Debugger.Launch();
        //    System.Diagnostics.Debugger.Break();
        //}

        var devboxHostname = Environment.MachineName; // to be able vary per developers machine

        // Repeat the configur builder from the BlazorApp.Program routine, but only focus on Development environment
        IConfigurationRoot configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.Development.json")
            .AddJsonFile($"appsettings.{Environments.Development}.{devboxHostname}.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables()
            .Build();

        var builder = new DbContextOptionsBuilder<AssetManagementDbContext>();

        builder.UseSqlServer(configuration.GetConnectionString(Constants.ConnectionstringConstants.EfCoreMigrationsConnectionStringName)); // Portfolio instance is ignored in our dummy provider

        // Normally the AssetManagementPortfolioResolver ensures the logged in AMprover user will get the data
        // of the selected portfolio (database). For migrations we now only use the single database
        // that you've configured in the above mentioned connectionstring. 
        return new AssetManagementDbContext(builder.Options, new AssetManagementPortfolioResolverForMigrations());
    }
}

public class AssetManagementPortfolioResolverForMigrations : IAssetManagementPortfolioResolver
{
    public Portfolio GetCurrentPortfolio()
    {
        throw new NotImplementedException();
    }

    public Task<Portfolio> GetCurrentPortfolioAsync()
    {
        throw new NotImplementedException();
    }

    public Task<List<Portfolio>> GetPortfoliosForLoggedInUserAsync()
    {
        throw new NotImplementedException();
    }

    public Task<string> GetCurrentPortfolioConnectionStringAsync()
    {
        throw new NotImplementedException();
    }

    public List<Portfolio> GetPortfoliosForLoggedInUser()
    {
        throw new NotImplementedException();
    }

    public string GetCurrentPortfolioConnectionString()
    {
        throw new NotImplementedException();
    }

    public void SwitchPortfolioForLoggedInUser(int portfolioId)
    {
        throw new NotImplementedException();
    }

    public Task SwitchPortfolioForLoggedInUserAsync(int portfolioId)
    {
        throw new NotImplementedException();
    }
}