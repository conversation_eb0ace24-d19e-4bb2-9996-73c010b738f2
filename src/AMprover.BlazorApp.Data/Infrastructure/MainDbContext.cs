using System;
using AMprover.Data.Entities.Identity;
using AMprover.Data.Extensions;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Infrastructure;

public class MainDbContext(DbContextOptions<MainDbContext> options) : IdentityDbContext<UserAccount>(options)
{
    public DbSet<Portfolio> Portfolios { get; init; }
    public DbSet<PortfolioAssignment> PortfolioAssignments { get; init; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        #region Portfolio configuration
        builder.Entity<Portfolio>()
            .Property(p => p.CreatedOn)
            .HasColumnType("smalldatetime")
            .HasDefaultValueSql("GETDATE()");
        builder.Entity<Portfolio>()
            .Property(p => p.LastUpdatedOn)
            .HasColumnType("smalldatetime")
            .HasDefaultValueSql("GETDATE()");
        builder.Entity<PortfolioAssignment>()
            .HasKey(x => new { x.PortfolioId, x.UserId });
        builder.Entity<PortfolioAssignment>()
            .HasOne(u => u.User)
            .WithMany(pu => pu.PortfolioAssignments)
            .HasForeignKey(u => u.UserId);
        builder.Entity<PortfolioAssignment>()
            .HasOne(p => p.Portfolio)
            .WithMany(pu => pu.UserAssignments)
            .HasForeignKey(p => p.PortfolioId);
        builder.Entity<PortfolioAssignment>()
            .Property(pa => pa.LastSelected)
            .HasColumnType<DateTime>("smalldatetime")
            .HasDefaultValueSql("GETDATE()");

        #endregion

        #region Data seeding

        builder.SeedRoles();
        builder.SeedUsers();
        builder.SeedPortfolios();

        #endregion
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);
        
        // Suppress the PendingModelChangesWarning
        optionsBuilder.ConfigureWarnings(warnings => 
            warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.RelationalEventId.PendingModelChangesWarning));
    }
}
