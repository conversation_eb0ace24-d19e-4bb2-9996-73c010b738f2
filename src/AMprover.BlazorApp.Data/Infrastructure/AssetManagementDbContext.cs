using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AMprover.Data.Entities.AM;
using AMprover.Data.Entities.AM.SP;
using AMprover.Data.Entities.AM.SP.Reports;
using AMprover.Data.Enums;
using AMprover.Data.Models;
using Microsoft.EntityFrameworkCore;
using Object = AMprover.Data.Entities.AM.Object;
using Task = AMprover.Data.Entities.AM.Task;

namespace AMprover.Data.Infrastructure;

public class AssetManagementDbContext : DbContext
{
    private readonly IAssetManagementPortfolioResolver? _assetManagementPortfolioResolver;

    public AssetManagementDbContext(DbContextOptions<AssetManagementDbContext> options) : base(options)
    {
    }

    public AssetManagementDbContext(DbContextOptions<AssetManagementDbContext> options,
        IAssetManagementPortfolioResolver assetManagementPortfolioResolver) : base(options)
    {
        _assetManagementPortfolioResolver = assetManagementPortfolioResolver;
    }

    public DbSet<Attachment> Attachment => Set<Attachment>();
    public DbSet<AttachmentCategory> AttachmentCategory => Set<AttachmentCategory>();
    public DbSet<BudgetCost> BudgetCost => Set<BudgetCost>();
    public DbSet<ClusterReport> ClusterReport => Set<ClusterReport>();
    public DbSet<ClusterSiTaskCollection> ClusterSiTaskCollection => Set<ClusterSiTaskCollection>();
    public DbSet<CmnCostCalculation> CmnCostCalculation => Set<CmnCostCalculation>();
    public DbSet<CriticalityRanking> CriticalityRanking => Set<CriticalityRanking>();
    public DbSet<DsRamsReportRams> DsRamsReportRams => Set<DsRamsReportRams>();
    public DbSet<DsRamsReportRamsDetails> DsRamsReportRamsDetails => Set<DsRamsReportRamsDetails>();
    public DbSet<DsRamsReportRamsTable> DsRamsReportRamsTable => Set<DsRamsReportRamsTable>();
    public DbSet<ExportSimco> ExportSimco => Set<ExportSimco>();
    public DbSet<ExportTasks> ExportTasks => Set<ExportTasks>();
    public DbSet<LccDetailGrid> LccDetailGrid => Set<LccDetailGrid>();
    public DbSet<LccFmecaOfRiskObject> LccFmecaOfRiskObject => Set<LccFmecaOfRiskObject>();
    public DbSet<LccGrid> LccGrid => Set<LccGrid>();
    public DbSet<LccMrbCalc> LccMrbCalc => Set<LccMrbCalc>();
    public DbSet<LccOpexCalc> LccOpexCalc => Set<LccOpexCalc>();
    public DbSet<LccOutdated> LccOutdated => Set<LccOutdated>();
    public DbSet<LccOutdatedOnRisk> LccOutdatedOnRisk => Set<LccOutdatedOnRisk>();
    public DbSet<LccRamsCalc> LccRamsCalc => Set<LccRamsCalc>();
    public DbSet<LccSiCalc> LccSiCalc => Set<LccSiCalc>();
    public DbSet<Lookup> Lookup => Set<Lookup>();
    public DbSet<LookupExecutor> LookupExecutor => Set<LookupExecutor>();
    public DbSet<LookupFailCat> LookupFailCat => Set<LookupFailCat>();
    public DbSet<LookupFailMode> LookupFailMode => Set<LookupFailMode>();
    public DbSet<LookupGridColumn> LookupGridColumn => Set<LookupGridColumn>();
    public DbSet<LookupAdditionalData> LookupAdditionalData => Set<LookupAdditionalData>();
    public DbSet<LookupInflationGroup> LookupInflationGroup => Set<LookupInflationGroup>();
    public DbSet<LookupInitiator> LookupInitiator => Set<LookupInitiator>();
    public DbSet<LookupIntervalUnit> LookupIntervalUnit => Set<LookupIntervalUnit>();
    public DbSet<LookupMxPolicy> LookupMxPolicy => Set<LookupMxPolicy>();
    public DbSet<LookupSettings> LookupSettings => Set<LookupSettings>();
    public DbSet<LookupUserDefined> LookupUserDefined => Set<LookupUserDefined>();
    public DbSet<MrbTaskCount> MrbTaskCount => Set<MrbTaskCount>();
    public DbSet<MrbTree> MrbTree => Set<MrbTree>();
    public DbSet<NumberOfSiItems> NumberOfSiItems => Set<NumberOfSiItems>();
    public DbSet<OpexLccDetails> OpexLccDetails => Set<OpexLccDetails>();
    public DbSet<PageNavigation> PageNavigations => Set<PageNavigation>();
    public DbSet<PickSiFromCommonTask> PickSiFromCommonTask => Set<PickSiFromCommonTask>();
    public DbSet<PrioTaskCollection> PrioTaskCollection => Set<PrioTaskCollection>();
    public DbSet<PriorityGridData> PriorityGridData => Set<PriorityGridData>();
    public DbSet<PriorityTaskCost> PriorityTaskCost => Set<PriorityTaskCost>();
    public DbSet<PriorityTaskGridData> PriorityTaskGridData => Set<PriorityTaskGridData>();
    public DbSet<SiRiskCalc> SiRiskCalc => Set<SiRiskCalc>();
    public DbSet<SiStatisticsRiskQualification> SiStatisticsRiskQualification => Set<SiStatisticsRiskQualification>();
    public DbSet<SyncRamsMrb> SyncRamsMrb => Set<SyncRamsMrb>();
    public DbSet<TaskCostOnClusterModified> TaskCostOnClusterModified => Set<TaskCostOnClusterModified>();
    public DbSet<TaskCostOnMrbModified> TaskCostOnMrbModified => Set<TaskCostOnMrbModified>();
    public DbSet<TaskEdit> TaskEdit => Set<TaskEdit>();
    public DbSet<UserSetting> UserSettings => Set<UserSetting>();
    public DbSet<TaskLookupGrid> TaskLookupGrid => Set<TaskLookupGrid>();
    public DbSet<BvAspectSets> BvAspectSets => Set<BvAspectSets>();
    public DbSet<BvRelevanceSets> BvRelevanceSets => Set<BvRelevanceSets>();
    public DbSet<BvSiItems> BvSiItems => Set<BvSiItems>();
    public DbSet<BvWeightingModels> BvWeightingModels => Set<BvWeightingModels>();
    public DbSet<Cluster> Cluster => Set<Cluster>();
    public DbSet<ClusterCost> ClusterCost => Set<ClusterCost>();
    public DbSet<ClusterTaskPlan> ClusterTaskPlan => Set<ClusterTaskPlan>();
    public DbSet<CommonCost> CommonCost => Set<CommonCost>();
    public DbSet<CommonTask> CommonTask => Set<CommonTask>();
    public DbSet<CommonTaskCost> CommonTaskCost => Set<CommonTaskCost>();

    [Obsolete("Not found in AMprover4", true)]
    public DbSet<Company> Company => Set<Company>();

    public DbSet<Department> Department => Set<Department>();
    public DbSet<DerModified> DerModified => Set<DerModified>();
    public DbSet<Descriptions> Descriptions => Set<Descriptions>();
    public DbSet<EditLog> EditLog => Set<EditLog>();
    public DbSet<Filters> Filters => Set<Filters>();
    public DbSet<FiltersSelectionList> FiltersSelectionList => Set<FiltersSelectionList>();

    public DbSet<Fmeca> Fmeca => Set<Fmeca>();

    [Obsolete("Nog niet voorbij zien komen")]
    public DbSet<FmecaSelect> FmecaSelect => Set<FmecaSelect>();

    public DbSet<Lcc> Lcc => Set<Lcc>();
    public DbSet<Lccdetail> Lccdetail => Set<Lccdetail>();
    public DbSet<LcceffectDetail> LcceffectDetail => Set<LcceffectDetail>();

    public DbSet<Mca> Mca => Set<Mca>();
    public DbSet<Mrb> Mrb => Set<Mrb>();
    public DbSet<MrbImage> MrbImage => Set<MrbImage>();
    public DbSet<Object> Object => Set<Object>();
    public DbSet<OpexData> OpexData => Set<OpexData>();
    public DbSet<OpexFactor> OpexFactor => Set<OpexFactor>();
    public DbSet<OpexToLcc> OpexToLcc => Set<OpexToLcc>();
    public DbSet<OpexToLccDetail> OpexToLccDetail => Set<OpexToLccDetail>();
    public DbSet<PickSi> PickSi => Set<PickSi>();
    public DbSet<Priority> Priority => Set<Priority>();
    public DbSet<PriorityBudget> PriorityBudget => Set<PriorityBudget>();
    public DbSet<PriorityCost> PriorityCost => Set<PriorityCost>();
    public DbSet<PriorityTask> PriorityTask => Set<PriorityTask>();
    public DbSet<PriorityVersion> PriorityVersion => Set<PriorityVersion>();
    public DbSet<Rams> Rams => Set<Rams>();
    public DbSet<RamsDiagram> RamsDiagram => Set<RamsDiagram>();
    public DbSet<RiskObject> RiskObject => Set<RiskObject>();
    public DbSet<SapaCollection> SapaCollection => Set<SapaCollection>();
    public DbSet<Sapa> Sapa => Set<Sapa>();
    public DbSet<SapaYear> SapaYear => Set<SapaYear>();
    public DbSet<SapaDetail> SapaDetail => Set<SapaDetail>();
    public DbSet<SapaWorkpackage> SapaWorkpackage => Set<SapaWorkpackage>();
    public DbSet<Scenario> Scenario => Set<Scenario>();
    public DbSet<Si> Si => Set<Si>();
    public DbSet<SiLinkFilters> SiLinkFilters => Set<SiLinkFilters>();
    public DbSet<SiRisks> SiRisks => Set<SiRisks>();
    public DbSet<SiStatistics> SiStatistics => Set<SiStatistics>();
    public DbSet<Spare> Spare => Set<Spare>();
    public DbSet<Task> Task => Set<Task>();
    public DbSet<Vdmxl> Vdmxl => Set<Vdmxl>();
    public DbSet<Workpackage> Workpackage => Set<Workpackage>();
    public DbSet<VwSiFilter> VwSiFilter => Set<VwSiFilter>();
    public DbSet<ReportFilter> ReportFilter => Set<ReportFilter>();
    public DbSet<UserDepartment> UserDepartment => Set<UserDepartment>();

    //Stored Procedures
    public virtual DbSet<RiskWithObjects> RisksWithObjects => Set<RiskWithObjects>();
    public virtual DbSet<TaskWithObjects> TasksWithObjects => Set<TaskWithObjects>();

    public virtual DbSet<ClusterTaskWithObjects> ClusterTasksWithObjects => Set<ClusterTaskWithObjects>();
    public virtual DbSet<ClusterTaskPlanWithObjects> ClusterTaskPlansWithObjects => Set<ClusterTaskPlanWithObjects>();
    public virtual DbSet<ClusterTree> ClusterTree => Set<ClusterTree>();
    public virtual DbSet<ClusterWorkPackageTree> ClusterWorkPackageTree => Set<ClusterWorkPackageTree>();
    public virtual DbSet<ClusterStatus> ClusterStatus => Set<ClusterStatus>();
    public virtual DbSet<RiskOnAbsStoredProcedureResult> RiskOnAbsStoredProcedure => Set<RiskOnAbsStoredProcedureResult>();

    //Stored Procedures: reports
    public virtual DbSet<FunctionalTreeReportItem> FunctionalTreeReportItems => Set<FunctionalTreeReportItem>();
    public virtual DbSet<CommonActionReportItem> CommonActionReportItems => Set<CommonActionReportItem>();
    public virtual DbSet<SignificantItemReportItem> SignificantItemReportItems => Set<SignificantItemReportItem>();
    public virtual DbSet<RiskAnalysisReportItem> RiskAnalysisReportItems => Set<RiskAnalysisReportItem>();
    public virtual DbSet<TaskPlanReportItem> TaskPlanReportItems => Set<TaskPlanReportItem>();
    public virtual DbSet<ClusterReportItem> ClusterReportItems => Set<ClusterReportItem>();
    public virtual DbSet<RiskAndPreviousActionsReportItem> RiskAndPreviousActionsReportItems => Set<RiskAndPreviousActionsReportItem>();

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            var connectionstring = _assetManagementPortfolioResolver?.GetCurrentPortfolioConnectionString()
                                   ?? throw new IndexOutOfRangeException(
                                       $"Missing connectionstring for: {nameof(AssetManagementDbContext)} via {nameof(IAssetManagementPortfolioResolver)}.");

            optionsBuilder.UseSqlServer(
                connectionstring,
                builder =>
                {
                    builder.EnableRetryOnFailure(5, TimeSpan.FromSeconds(10), null);
                    builder.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                });

            optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
        }

        base.OnConfiguring(optionsBuilder);
    }

    public async System.Threading.Tasks.Task SaveAuditLogAsync(string userName, CancellationToken cancellationToken = default)
    {
        ChangeTracker.DetectChanges();

        var entities = ChangeTracker.Entries()
            .Where(x => x.State is not (EntityState.Added or EntityState.Unchanged or EntityState.Detached))
            .ToList();

        if (entities.Count == 0) return;

        var auditEntries = entities.Select(entry => new AuditLog(entry)
        {
            TableName = entry.Entity.GetType().Name,
            Username = userName
        }).ToList();

        foreach (var auditEntry in auditEntries)
        {
            foreach (var property in auditEntry.Entity.Properties)
            {
                var propertyName = property.Metadata.Name;

                switch (auditEntry.Entity.State)
                {
                    case EntityState.Deleted:
                        auditEntry.AuditType = AuditType.Delete;
                        auditEntry.OldValues[propertyName] = property.OriginalValue!;
                        break;
                    case EntityState.Modified when property.IsModified:
                        auditEntry.ChangedColumns.Add(propertyName);
                        auditEntry.AuditType = AuditType.Update;
                        auditEntry.OldValues[propertyName] = property.OriginalValue!;
                        auditEntry.NewValues[propertyName] = property.CurrentValue!;
                        break;
                }
            }

            await EditLog.AddAsync(auditEntry.ToAudit(), cancellationToken);
        }
    }
    
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await base.SaveChangesAsync(cancellationToken);
    }
}
