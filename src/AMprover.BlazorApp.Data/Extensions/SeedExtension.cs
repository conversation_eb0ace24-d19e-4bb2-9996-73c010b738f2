using System;
using System.Linq;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Extensions;

public static class SeedExtension
{
    public static void SeedPortfolios(this ModelBuilder builder)
    {
        // Use fixed dates instead of DateTime.Now
        var fixedDate = new DateTime(2025, 1, 1, 12, 0, 0, DateTimeKind.Utc);
            
        //Has data doesn't work with autogenerated ID's', so we have to define our own id's
        builder.Entity<Portfolio>().HasData(
            new Portfolio { Id = 1, Name = "AMprover 5 Demo", DatabaseName = "Mainnovation_AMprover_Dev_Demo", LastUpdatedOn = fixedDate, CreatedOn = fixedDate },
            new Portfolio { Id = 2, Name = "Waterschap Noorderzijlvest", DatabaseName = "Mainnovation_AMprover_Dev_Waterschap_Noorderzijlvest", LastUpdatedOn = fixedDate, CreatedOn = fixedDate },
            new Portfolio { Id = 3, Name = "AMprover 5 Training", DatabaseName = "Mainnovation_AMprover_Dev_Training", LastUpdatedOn = fixedDate, CreatedOn = fixedDate }
        );

        builder.Entity<PortfolioAssignment>().HasData(
            new PortfolioAssignment { UserId = "1461abf6-0f98-41ea-9365-9cbb52127abe", PortfolioId = 1 },
            new PortfolioAssignment { UserId = "1461abf6-0f98-41ea-9365-9cbb52127abe", PortfolioId = 2 },
            new PortfolioAssignment { UserId = "1461abf6-0f98-41ea-9365-9cbb52127abe", PortfolioId = 3 }
        );
    }

    public static void SeedRoles(this ModelBuilder builder)
    {
        // First check if the Administrators role already exists in the seed data
        bool administratorsRoleExists = builder.Entity<IdentityRole>().Metadata.GetSeedData()
            .Any(e => e["Id"].Equals(RoleConstants.AdministratorsRoleId));

        // Add all other roles
        builder.Entity<IdentityRole>().HasData(
            new IdentityRole
            {
                Id = RoleConstants.AssetManagementRoleId,
                Name = RoleConstants.AssetManagement,
                NormalizedName = RoleConstants.AssetManagement.ToUpper(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            },
            new IdentityRole
            {
                Id = RoleConstants.FinancialControlRoleId,
                Name = RoleConstants.FinancialControl,
                NormalizedName = RoleConstants.FinancialControl.ToUpper(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            },
            new IdentityRole
            {
                Id = RoleConstants.MaintenanceEngineeringRoleId,
                Name = RoleConstants.MaintenanceEngineering,
                NormalizedName = RoleConstants.MaintenanceEngineering.ToUpper(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            },
            new IdentityRole
            {
                Id = RoleConstants.PortfolioAdministratorsRoleId,
                Name = RoleConstants.PortfolioAdministrators,
                NormalizedName = RoleConstants.PortfolioAdministrators.ToUpper(),
                ConcurrencyStamp = Guid.NewGuid().ToString()
            }
        );

        // Only add the Administrators role if it doesn't already exist
        if (!administratorsRoleExists)
        {
            builder.Entity<IdentityRole>().HasData(
                new IdentityRole
                {
                    Id = RoleConstants.AdministratorsRoleId,
                    Name = RoleConstants.Administrators,
                    NormalizedName = RoleConstants.Administrators.ToUpper(),
                    ConcurrencyStamp = Guid.NewGuid().ToString()
                });
        }
    }

    public static void SeedUsers(this ModelBuilder builder)
    {
        const string administratorId = "1461abf6-0f98-41ea-9365-9cbb52127abe";
        string administratorEmail = "<EMAIL>";
        string administratorName = "Support Darestep";
        string adminPassword = "==25_hunger_NEAR_step_94==";
            
        // Don't add the role here, it's already added in SeedRoles
        // Just check if the user exists
        if (!builder.Entity<UserAccount>().Metadata.GetSeedData().Any(e => e["Id"].Equals(administratorId)))
        {
            var hasher = new PasswordHasher<UserAccount>();

            builder.Entity<UserAccount>().HasData(new UserAccount
            {
                Id = administratorId,
                Email = administratorEmail,
                Name = administratorName,
                NormalizedEmail = administratorEmail.ToUpper(),
                UserName = administratorEmail,
                NormalizedUserName = administratorEmail.ToUpper(),
                EmailConfirmed = true,
                PasswordHash = hasher.HashPassword(null, adminPassword),
                SecurityStamp = string.Empty
            });
        }

        // Check if the user role assignment already exists
        if (!builder.Entity<IdentityUserRole<string>>().Metadata.GetSeedData().Any(e => 
                e["UserId"].Equals(administratorId) && e["RoleId"].Equals(RoleConstants.AdministratorsRoleId)))
        {
            builder.Entity<IdentityUserRole<string>>().HasData(new IdentityUserRole<string>
            {
                RoleId = RoleConstants.AdministratorsRoleId,
                UserId = administratorId
            });
        }
    }
}