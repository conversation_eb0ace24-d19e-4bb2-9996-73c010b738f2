using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace AMprover.Data.Migrations.AMproverIdentity;

public partial class portfolioAndUserAssignments : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "Portfolios",
            columns: table => new
            {
                Id = table.Column<int>(nullable: false)
                    .Annotation("SqlServer:Identity", "1, 1"),
                Name = table.Column<string>(nullable: true),
                DatabaseName = table.Column<string>(nullable: true),
                CreatedOn = table.Column<DateTime>(type: "smalldatetime", nullable: false, defaultValueSql: "GETDATE()"),
                LastUpdatedOn = table.Column<DateTime>(type: "smalldatetime", nullable: false, defaultValueSql: "GETDATE()")
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_Portfolios", x => x.Id);
            });

        migrationBuilder.CreateTable(
            name: "PortfolioAssignments",
            columns: table => new
            {
                UserId = table.Column<string>(nullable: false),
                PortfolioId = table.Column<int>(nullable: false),
                LastSelected = table.Column<DateTime>(type: "datetime2(0)", nullable: false, defaultValueSql: "GETDATE()")
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_PortfolioAssignments", x => new { x.PortfolioId, x.UserId });
                table.ForeignKey(
                    name: "FK_PortfolioAssignments_Portfolios_PortfolioId",
                    column: x => x.PortfolioId,
                    principalTable: "Portfolios",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_PortfolioAssignments_AspNetUsers_UserId",
                    column: x => x.UserId,
                    principalTable: "AspNetUsers",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.UpdateData(
            table: "AspNetRoles",
            keyColumn: "Id",
            keyValue: "1461abf6-0f98-41ea-9365-9cbb52127abe",
            column: "ConcurrencyStamp",
            value: "a18d4647-4b51-45d5-8f27-157d2c0f1e74");

        migrationBuilder.UpdateData(
            table: "AspNetUsers",
            keyColumn: "Id",
            keyValue: "1461abf6-0f98-41ea-9365-9cbb52127abe",
            columns: ["ConcurrencyStamp", "PasswordHash"],
            values: ["d808c38c-0976-4b0c-ac5c-5336090d037e", "AQAAAAEAACcQAAAAEEQAThkKjpgeuM/ymyNLCyhbzUTCGu6X+1RACvGwZxRARoO9Pn+uKa9zvpIzyYN0mA=="
            ]);

        migrationBuilder.CreateIndex(
            name: "IX_PortfolioAssignments_UserId",
            table: "PortfolioAssignments",
            column: "UserId");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "PortfolioAssignments");

        migrationBuilder.DropTable(
            name: "Portfolios");

        migrationBuilder.UpdateData(
            table: "AspNetRoles",
            keyColumn: "Id",
            keyValue: "1461abf6-0f98-41ea-9365-9cbb52127abe",
            column: "ConcurrencyStamp",
            value: "51bc1d31-a162-4e6d-a221-51eb140f9532");

        migrationBuilder.UpdateData(
            table: "AspNetUsers",
            keyColumn: "Id",
            keyValue: "1461abf6-0f98-41ea-9365-9cbb52127abe",
            columns: ["ConcurrencyStamp", "PasswordHash"],
            values: ["8bced993-4de0-48b6-9273-d091c748e9fc", "AQAAAAEAACcQAAAAEKOdEZllNjkPQnyapNOmmKsX4yIGsS+ScL+r7c8leAn0/1bEgGcY5r/7ZJP6edmLZg=="
            ]);
    }
}