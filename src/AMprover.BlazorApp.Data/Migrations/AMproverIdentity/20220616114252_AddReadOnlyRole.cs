using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverIdentity;

public partial class AddReadOnlyRole : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.InsertData(
                table: "AspNetRoles",
                columns: ["Id", "Name", "NormalizedName", "ConcurrencyStamp"],
                values: new object[,]
                {
                { "061aa6fb-02dd-4c3a-8c17-38a7cd6de6f4", "ReadOnly", "READONLY", "061aa6fb-02dd-4c3a-8c17-38a7cd6de6f4" },
                });
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "061aa6fb-02dd-4c3a-8c17-38a7cd6de6f4");
        }
}