using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace AMprover.Data.Migrations.AMproverIdentity;

public partial class SeededPortfolio : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.UpdateData(
            table: "AspNetRoles",
            keyColumn: "Id",
            keyValue: "1461abf6-0f98-41ea-9365-9cbb52127abe",
            column: "ConcurrencyStamp",
            value: "520fb81a-561c-4c5e-a29f-9a8854bedc22");

        migrationBuilder.UpdateData(
            table: "AspNetUsers",
            keyColumn: "Id",
            keyValue: "1461abf6-0f98-41ea-9365-9cbb52127abe",
            columns: ["ConcurrencyStamp", "PasswordHash"],
            values: ["d701e5d1-137e-42f2-a7b5-a45c2a558eab", "AQAAAAEAACcQAAAAEBIZS5z2KiFNSpypeq+OutirFe4yqlEJCK3LHqkm5OFNIaE/DOHOQ79kSEx+fCrBkA=="
            ]);

        migrationBuilder.InsertData(
            table: "Portfolios",
            columns: ["Id", "CreatedOn", "DatabaseName", "Name"],
            values: new object[,]
            {
                { 1, new DateTime(2020, 10, 15, 13, 54, 32, 949, DateTimeKind.Local).AddTicks(3250), "Mainnovation_AMprover_Dev_Demo", "AMprover 5 Demo" },
                { 2, new DateTime(2020, 10, 15, 13, 54, 32, 949, DateTimeKind.Local).AddTicks(4180), "Mainnovation_AMprover_Dev_Waterschap_Noorderzijlvest", "Waterschap Noorderzijlvest" },
                { 3, new DateTime(2020, 10, 15, 13, 54, 32, 949, DateTimeKind.Local).AddTicks(4180), "Mainnovation_AMprover_Dev_Training", "AMprover 5 Training" }
            });

        migrationBuilder.InsertData(
            table: "PortfolioAssignments",
            columns: ["PortfolioId", "UserId"],
            values: [1, "1461abf6-0f98-41ea-9365-9cbb52127abe"]);

        migrationBuilder.InsertData(
            table: "PortfolioAssignments",
            columns: ["PortfolioId", "UserId"],
            values: [2, "1461abf6-0f98-41ea-9365-9cbb52127abe"]);

        migrationBuilder.InsertData(
            table: "PortfolioAssignments",
            columns: ["PortfolioId", "UserId"],
            values: [3, "1461abf6-0f98-41ea-9365-9cbb52127abe"]);
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DeleteData(
            table: "PortfolioAssignments",
            keyColumns: ["PortfolioId", "UserId"],
            keyValues: [1, "1461abf6-0f98-41ea-9365-9cbb52127abe"]);

        migrationBuilder.DeleteData(
            table: "PortfolioAssignments",
            keyColumns: ["PortfolioId", "UserId"],
            keyValues: [2, "1461abf6-0f98-41ea-9365-9cbb52127abe"]);

        migrationBuilder.DeleteData(
            table: "PortfolioAssignments",
            keyColumns: ["PortfolioId", "UserId"],
            keyValues: [3, "1461abf6-0f98-41ea-9365-9cbb52127abe"]);

        migrationBuilder.DeleteData(
            table: "Portfolios",
            keyColumn: "Id",
            keyValue: 3);

        migrationBuilder.DeleteData(
            table: "Portfolios",
            keyColumn: "Id",
            keyValue: 2);

        migrationBuilder.DeleteData(
            table: "Portfolios",
            keyColumn: "Id",
            keyValue: 1);

        migrationBuilder.UpdateData(
            table: "AspNetRoles",
            keyColumn: "Id",
            keyValue: "1461abf6-0f98-41ea-9365-9cbb52127abe",
            column: "ConcurrencyStamp",
            value: "87863438-6fd6-4dfc-b3f6-0765bffe0658");

        migrationBuilder.UpdateData(
            table: "AspNetUsers",
            keyColumn: "Id",
            keyValue: "1461abf6-0f98-41ea-9365-9cbb52127abe",
            columns: ["ConcurrencyStamp", "PasswordHash"],
            values: ["7a5ba2aa-fdce-438e-a9ce-4284329d53eb", "AQAAAAEAACcQAAAAENQ27Gge1dBX8qb2KFnUEhExQxOUb5jugTizM8TxYmWvPTpt9R1QhLrsNMoUQIs0wQ=="
            ]);
    }
}