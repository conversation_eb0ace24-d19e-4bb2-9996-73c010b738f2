using System;
using AMprover.Data.Constants;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations;

public partial class AddRolesToTable : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            // Check if roles exist before inserting
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT 1 FROM AspNetRoles WHERE Id = '" + RoleConstants.AdministratorsRoleId + @"')
                BEGIN
                    INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp)
                    VALUES ('" + RoleConstants.AdministratorsRoleId + "', '" + RoleConstants.Administrators + "', '" + 
                    RoleConstants.Administrators.ToUpper() + "', '" + Guid.NewGuid() + @"')
                END");
                
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT 1 FROM AspNetRoles WHERE Id = '" + RoleConstants.AssetManagementRoleId + @"')
                BEGIN
                    INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp)
                    VALUES ('" + RoleConstants.AssetManagementRoleId + "', '" + RoleConstants.AssetManagement + "', '" + 
                    RoleConstants.AssetManagement.ToUpper() + "', '" + Guid.NewGuid() + @"')
                END");
                
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT 1 FROM AspNetRoles WHERE Id = '" + RoleConstants.FinancialControlRoleId + @"')
                BEGIN
                    INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp)
                    VALUES ('" + RoleConstants.FinancialControlRoleId + "', '" + RoleConstants.FinancialControl + "', '" + 
                    RoleConstants.FinancialControl.ToUpper() + "', '" + Guid.NewGuid() + @"')
                END");
                
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT 1 FROM AspNetRoles WHERE Id = '" + RoleConstants.MaintenanceEngineeringRoleId + @"')
                BEGIN
                    INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp)
                    VALUES ('" + RoleConstants.MaintenanceEngineeringRoleId + "', '" + RoleConstants.MaintenanceEngineering + "', '" + 
                    RoleConstants.MaintenanceEngineering.ToUpper() + "', '" + Guid.NewGuid() + @"')
                END");
                
            migrationBuilder.Sql(@"
                IF NOT EXISTS (SELECT 1 FROM AspNetRoles WHERE Id = '" + RoleConstants.PortfolioAdministratorsRoleId + @"')
                BEGIN
                    INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp)
                    VALUES ('" + RoleConstants.PortfolioAdministratorsRoleId + "', '" + RoleConstants.PortfolioAdministrators + "', '" + 
                    RoleConstants.PortfolioAdministrators.ToUpper() + "', '" + Guid.NewGuid() + @"')
                END");
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        }
}