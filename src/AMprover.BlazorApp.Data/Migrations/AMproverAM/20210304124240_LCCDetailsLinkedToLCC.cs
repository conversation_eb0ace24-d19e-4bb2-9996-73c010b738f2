using Microsoft.EntityFrameworkCore.Migrations;

namespace AMprover.Data.Migrations.AMproverAM;

public partial class LCCDetailsLinkedToLCC : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        //Remove all orphaned details. Shouldn't happen but if it shouldn't, it probably does..
        migrationBuilder.Sql(@"DELETE FROM TblLCCDetail 
                                WHERE NOT EXISTS(SELECT 1 FROM TblLCC Where TblLCCDetail.LccDetLccID = TblLCC.LccID)
                                    GO");

        migrationBuilder.CreateIndex(
            "IX_TblLCCDetail_LccDetLccID",
            "TblLCCDetail",
            "LccDetLccID");

        migrationBuilder.AddForeignKey(
            "FK_LccDetail_Lcc",
            "TblLCCDetail",
            "LccDetLccID",
            "TblLCC",
            principalColumn: "LccID",
            onDelete: ReferentialAction.Restrict);
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
    }
}