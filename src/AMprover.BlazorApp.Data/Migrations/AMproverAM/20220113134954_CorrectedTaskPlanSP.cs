using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class CorrectedTaskPlanSP : Migration
{
	protected override void Up(MigrationBuilder migrationBuilder)
	{
		var sql = @"
	 	    	IF OBJECT_ID('GetTaskPlansForCluster', 'P') IS NOT NULL
                DROP PROC GetTaskPlansForCluster
                GO
     
                CREATE PROCEDURE [dbo].GetTaskPlansForCluster
                    @ClusterId int
                AS
                BEGIN
                    SET NOCOUNT ON;
                    SELECT ctp.CltpID as Id, t.TskID as TaskId,t.TskName as Task,  t.TskInterval as Interval, t.TskIntervalUnit as IntervalUnit, r.MRBId as RiskId, r.MrbName as Risk, si.SiName as SignificantItem,
					ctp.CltpQualityScore as QualityScore, ctp.CltpDateExecuted as DateExecuted, ctp.CltpExecuteStatus as ExecuteStatus, ctp.CltpSlack as Slack, iu.IntUnitName as SlackInterval,
					ctp.CltpExecutionDate as ExecutionDate, ctp.CltpSequence as Sequence, ctp.CltpDuration as Duration, ctp.CltpDownTime as DownTime, cl.ClustName as ClusterName,
					ctp.CltpReferenceID as ReferenceId, ctp.CltpDateGenerated as DateGenerated, ctp.CltpPriority as Priority, ctp.CltpUseLastDateExecuted as UseLastDayExecuted,
					ctp.CltpInterruptable as Interruptible, ctp.CltpClusterCostPerUnit as CostPerUnit, ctp.CltpShiftStartDate as ShiftStartDate, ctp.CltpShiftEndDate as ShiftEndDate, ctp.CltpSiUnits as SiUnits
                    FROM TblClusterTaskPlan ctp
					LEFT JOIN TblSi si ON ctp.CltpSiID = si.SiID
				    LEFT JOIN TblMRB r ON  ctp.CltpRiskID = r.MRBId
					LEFT JOIN TblTask t ON  ctp.CltpTaskID = t.TskID
					LEFT JOIN TblRiskObject ro ON r.MrbRiskObject = ro.RiskObjID
					LEFT JOIN TblScenario sc ON ro.RiskObjScenarioID = sc.ScenID
					LEFT JOIN TblCluster cl ON t.TskCluster = cl.ClustID
					LEFT JOIN LookupIntervalUnit iu ON iu.IntUnitID = ctp.CltpSlackIntervalType
                    WHERE ctp.CltpClusterID = @ClusterId
                END";

		migrationBuilder.Sql(sql);
	}

	protected override void Down(MigrationBuilder migrationBuilder)
	{
	}
}