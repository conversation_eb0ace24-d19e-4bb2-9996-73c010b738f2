using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class Addcircuitaffectedcostplumbing : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AddColumn<decimal>(
                name: "CircuitAffectedCostAfter",
                table: "TblMRB",
                type: "decimal(18,3)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "CircuitAffectedCostBefore",
                table: "TblMRB",
                type: "decimal(18,3)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "CircuitAffectedCostPmo",
                table: "TblMRB",
                type: "decimal(18,3)",
                nullable: true);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropColumn(
                name: "CircuitAffectedCostAfter",
                table: "TblMRB");

            migrationBuilder.DropColumn(
                name: "CircuitAffectedCostBefore",
                table: "TblMRB");

            migrationBuilder.DropColumn(
                name: "CircuitAffectedCostPmo",
                table: "TblMRB");
        }
}