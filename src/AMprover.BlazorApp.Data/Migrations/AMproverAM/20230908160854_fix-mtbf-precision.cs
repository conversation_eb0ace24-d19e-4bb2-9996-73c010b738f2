using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class fixmtbfprecision : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AlterColumn<decimal>(
                name: "MrbMtbfPmo",
                table: "TblMRB",
                type: "decimal(18,4)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,3)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "MrbMtbfBefore",
                table: "TblMRB",
                type: "decimal(18,4)",
                nullable: true,
                comment: "MTBF in years before executing preventive actions for the risk",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,3)",
                oldNullable: true,
                oldComment: "MTBF in years before executing preventive actions for the risk");

            migrationBuilder.AlterColumn<decimal>(
                name: "MrbMtbfAfter",
                table: "TblMRB",
                type: "decimal(18,4)",
                nullable: true,
                comment: "MTBF in years after executing preventive actions for the risk",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,3)",
                oldNullable: true,
                oldComment: "MTBF in years after executing preventive actions for the risk");
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AlterColumn<decimal>(
                name: "MrbMtbfPmo",
                table: "TblMRB",
                type: "decimal(18,3)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "MrbMtbfBefore",
                table: "TblMRB",
                type: "decimal(18,3)",
                nullable: true,
                comment: "MTBF in years before executing preventive actions for the risk",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldNullable: true,
                oldComment: "MTBF in years before executing preventive actions for the risk");

            migrationBuilder.AlterColumn<decimal>(
                name: "MrbMtbfAfter",
                table: "TblMRB",
                type: "decimal(18,3)",
                nullable: true,
                comment: "MTBF in years after executing preventive actions for the risk",
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldNullable: true,
                oldComment: "MTBF in years after executing preventive actions for the risk");
        }
}