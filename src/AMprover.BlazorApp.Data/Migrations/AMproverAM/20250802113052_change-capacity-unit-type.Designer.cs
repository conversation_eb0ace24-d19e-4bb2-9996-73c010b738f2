// <auto-generated />
using System;
using AMprover.Data.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    [DbContext(typeof(AssetManagementDbContext))]
    [Migration("20250802113052_change-capacity-unit-type")]
    partial class changecapacityunittype
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("AMprover.Data.Entities.AM.Attachment", b =>
                {
                    b.Property<int>("AtchId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AtchId"));

                    b.Property<int?>("AtchCatgoryId")
                        .HasColumnType("int");

                    b.Property<string>("AtchDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AtchMrbId")
                        .HasColumnType("int");

                    b.Property<int?>("AtchRiskObjectId")
                        .HasColumnType("int");

                    b.Property<int?>("AtchSapaId")
                        .HasColumnType("int");

                    b.Property<string>("AtchTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AtchTskId")
                        .HasColumnType("int");

                    b.Property<string>("AtchUri")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AtchId");

                    b.HasIndex("AtchCatgoryId");

                    b.HasIndex("AtchMrbId");

                    b.HasIndex("AtchRiskObjectId");

                    b.HasIndex("AtchSapaId");

                    b.HasIndex("AtchTskId");

                    b.ToTable("TblAttachment");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.AttachmentCategory", b =>
                {
                    b.Property<int>("AtchCatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AtchCatId"));

                    b.Property<string>("AtchCatDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AtchCatTitle")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AtchCatId");

                    b.ToTable("TblAttachmentCategory");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BudgetCost", b =>
                {
                    b.Property<decimal?>("Accepted")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("Budget")
                        .HasColumnType("decimal(38, 2)")
                        .HasColumnName("budget");

                    b.Property<decimal?>("BudgetSum")
                        .HasColumnType("decimal(38, 2)")
                        .HasColumnName("budgetSum");

                    b.Property<int?>("Category")
                        .HasColumnType("int");

                    b.Property<decimal?>("CostsYear1")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("CostsYear2")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("CostsYear3")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("CostsYear4")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("CostsYear5")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("Postponed")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<int?>("PrioBudVersion")
                        .HasColumnType("int");

                    b.Property<int?>("PrioPartOf")
                        .HasColumnType("int");

                    b.Property<decimal?>("Proposed")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("Rejected")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("Risk")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("RiskDelta")
                        .HasColumnType("decimal(38, 2)");

                    b.ToTable("BudgetCost");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvAspectSets", b =>
                {
                    b.Property<int>("BvAspSetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("BvAspSetID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BvAspSetId"));

                    b.Property<string>("BvAspAspects")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Unique aspects stored in XML. Stores the name, weight factor and the bound relevance set of the aspect.");

                    b.Property<DateTime?>("BvAspDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvAspModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BvAspSetName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the aspect set");

                    b.HasKey("BvAspSetId");

                    b.ToTable("TblBvAspectSets");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvRelevanceSets", b =>
                {
                    b.Property<int>("BvRelSetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("BvRelSetID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BvRelSetId"));

                    b.Property<DateTime?>("BvRelDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvRelModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BvRelRelevances")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Unique relevances stored in XML. Stores the name and the appreciation of the relevance.");

                    b.Property<string>("BvRelSetName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the relevance set");

                    b.HasKey("BvRelSetId");

                    b.ToTable("TblBvRelevanceSets");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvSiItems", b =>
                {
                    b.Property<int>("BvId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("BvID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BvId"));

                    b.Property<int?>("BvBusinessvalue")
                        .HasColumnType("int");

                    b.Property<DateTime?>("BvDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BvPerEffectSet")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("BvRelevanceSelectSet")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("BvSiId")
                        .HasColumnType("int")
                        .HasColumnName("BvSiID");

                    b.Property<int?>("BvWeightingModelId")
                        .HasColumnType("int")
                        .HasColumnName("BvWeightingModelID");

                    b.HasKey("BvId");

                    b.HasIndex("BvWeightingModelId");

                    b.HasIndex(new[] { "BvSiId" }, "IX_BvSiItems")
                        .IsUnique();

                    b.ToTable("TblBvSiItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvWeightingModels", b =>
                {
                    b.Property<int>("BvModelId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("BvModelID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BvModelId"));

                    b.Property<int?>("BvModelAspectSetId")
                        .HasColumnType("int")
                        .HasColumnName("BvModelAspectSetID")
                        .HasComment("ID of the aspect set used for this weighting model.");

                    b.Property<DateTime?>("BvModelDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvModelEffectWeightSet")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Effect weight set stored in XML. Stores the aspect, effect (FMECA) column and the given value of the effect weight set.");

                    b.Property<int?>("BvModelFmecaId")
                        .HasColumnType("int")
                        .HasColumnName("BvModelFmecaID")
                        .HasComment("ID of the FMECA matrix used for this weighting model.");

                    b.Property<string>("BvModelModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BvModelName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the weighting model");

                    b.HasKey("BvModelId");

                    b.HasIndex("BvModelAspectSetId");

                    b.HasIndex("BvModelFmecaId");

                    b.ToTable("TblBvWeightingModels");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Cluster", b =>
                {
                    b.Property<int>("ClustId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ClustID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ClustId"));

                    b.Property<decimal?>("ClustBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("ClustDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ClustDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("ClustDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("ClustDisciplineCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool?>("ClustDivideDownTime")
                        .HasColumnType("bit");

                    b.Property<bool?>("ClustDivideDuration")
                        .HasColumnType("bit");

                    b.Property<decimal?>("ClustDownTime")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustDuration")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustEnergyCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustEstTaskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("ClustExecutor")
                        .HasColumnType("int");

                    b.Property<string>("ClustInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ClustInitiator")
                        .HasColumnType("int");

                    b.Property<bool?>("ClustInterruptable")
                        .HasColumnType("bit");

                    b.Property<decimal?>("ClustInterval")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int?>("ClustIntervalUnit")
                        .HasColumnType("int");

                    b.Property<int>("ClustLevel")
                        .HasColumnType("int");

                    b.Property<string>("ClustLocation")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("ClustMaterialCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ClustModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustOrgId")
                        .HasMaxLength(12)
                        .IsUnicode(false)
                        .HasColumnType("varchar(12)")
                        .HasColumnName("ClustOrgID");

                    b.Property<int?>("ClustPartOf")
                        .HasColumnType("int");

                    b.Property<int?>("ClustPriority")
                        .HasColumnType("int");

                    b.Property<string>("ClustReferenceId")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("ClustReferenceID");

                    b.Property<string>("ClustRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("ClustResponsible")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("ClustRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("ClustRiskObjectID");

                    b.Property<int?>("ClustScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("ClustScenarioID");

                    b.Property<string>("ClustSecondValues")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("ClustSequence")
                        .HasColumnType("int");

                    b.Property<decimal?>("ClustSharedCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("ClustShiftEndDate")
                        .HasColumnType("int");

                    b.Property<int?>("ClustShiftStartDate")
                        .HasColumnType("int");

                    b.Property<string>("ClustShortKey")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustSiteId")
                        .HasMaxLength(12)
                        .IsUnicode(false)
                        .HasColumnType("varchar(12)")
                        .HasColumnName("ClustSiteID");

                    b.Property<int?>("ClustStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("ClustTaskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ClustTemplateType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<decimal?>("ClustToolCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustTotalCmnCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("ClustWorkpackageId")
                        .HasColumnType("int");

                    b.HasKey("ClustId");

                    b.HasIndex("ClustPartOf");

                    b.HasIndex("ClustRiskObjectId");

                    b.HasIndex("ClustScenarioId");

                    b.HasIndex("ClustStatus");

                    b.ToTable("TblCluster");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterCost", b =>
                {
                    b.Property<int>("ClcId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ClcID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ClcId"));

                    b.Property<string>("ClcCalculationType")
                        .IsRequired()
                        .HasMaxLength(8)
                        .IsUnicode(false)
                        .HasColumnType("varchar(8)")
                        .HasComment("The way the common cost are calculated");

                    b.Property<int?>("ClcClusterId")
                        .HasColumnType("int")
                        .HasColumnName("ClcClusterID")
                        .HasComment("ID of the cluster to which the cluster cost is bound (Cluster)");

                    b.Property<int>("ClcCommonCostId")
                        .HasColumnType("int")
                        .HasColumnName("ClcCommonCostID")
                        .HasComment("ID of the common cost to which the cluster cost is bound (CommonCost)");

                    b.Property<decimal>("ClcCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost of the common cost");

                    b.Property<bool?>("ClcIsCommonTaskCost")
                        .HasColumnType("bit")
                        .HasComment("Boolean to see if the cluster cost are referenced to a task");

                    b.Property<decimal>("ClcPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Price of the common cost");

                    b.Property<int?>("ClcPriceIndexYear")
                        .HasColumnType("int")
                        .HasComment("The year in which the cluster cost are indexed");

                    b.Property<decimal?>("ClcQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Quantity of the common cost");

                    b.Property<string>("ClcRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the common cost");

                    b.Property<int?>("ClcTaskId")
                        .HasColumnType("int")
                        .HasColumnName("ClcTaskID")
                        .HasComment("ID of the tasks to which the cluster cost is bound (Task)");

                    b.Property<string>("ClcType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("The Type of cluster cost");

                    b.Property<decimal?>("ClcUnits")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Number of units of the common cost");

                    b.HasKey("ClcId");

                    b.HasIndex("ClcClusterId");

                    b.HasIndex("ClcCommonCostId");

                    b.HasIndex("ClcTaskId");

                    b.ToTable("TblClusterCost", t =>
                        {
                            t.HasComment("Clustercosts are costs specific to a cluster. They can be defined ");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterReport", b =>
                {
                    b.Property<int?>("ClcId")
                        .HasColumnType("int")
                        .HasColumnName("ClcID");

                    b.Property<decimal?>("ClustCostCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("ClustDisciplineCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("ClustDownTime")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("ClustDuration")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("ClustEnergyCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("ClustEstTaskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("ClustExecutor")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ClustId")
                        .HasColumnType("int")
                        .HasColumnName("ClustID");

                    b.Property<string>("ClustInitiator")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("ClustInterval")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<string>("ClustIntervalUnit")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<decimal?>("ClustMaterialCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("ClustName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustName0")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustName1")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustName2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustName3")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("ClustResponsible")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("ClustSecondValues")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("ClustSharedCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("ClustShortKey")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("ClustTaskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("ClustToolCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("ClustTotalCmnCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("CmnCostType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("MrbFailureCause")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("MrbFailureConsequences")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("MrbId")
                        .HasColumnType("int")
                        .HasColumnName("MrbID");

                    b.Property<string>("MrbObject0")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbObject2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbObject3")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbObject4")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ParentClusterName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RiskObject")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RiskObjectDesc")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("Scenario")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("TskCostCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("TskCostDescription")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<decimal?>("TskCostPrice")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("TskCostQuantity")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskCostTskId")
                        .HasColumnType("int")
                        .HasColumnName("TskCostTskID");

                    b.Property<decimal?>("TskCostUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("TskDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<string>("TskExecutor")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("TskGeneralDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("TskId")
                        .HasColumnType("int")
                        .HasColumnName("TskID");

                    b.Property<string>("TskInitiator")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<string>("TskIntervalUnit")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.Property<string>("TskName")
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<string>("TskPermit")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("TskPolicy")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)");

                    b.Property<string>("TskRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("TskType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("TskWorkPackage")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.ToTable("ClusterReport");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterSiTaskCollection", b =>
                {
                    b.Property<int?>("FailRateId")
                        .HasColumnType("int")
                        .HasColumnName("FailRateID");

                    b.Property<decimal?>("IntUnitCalculationKey")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbCustomEffectAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbDirectCostAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbDirectCostBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("MrbFailureMode")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbRiskBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Mrbcustomeffectbefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("NumberOfTasks")
                        .HasColumnType("int");

                    b.Property<int>("PckSiId")
                        .HasColumnType("int");

                    b.Property<int>("PckSiSiId")
                        .HasColumnType("int");

                    b.Property<int?>("SiCategory")
                        .HasColumnType("int");

                    b.Property<string>("SiName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SiQualityScore")
                        .HasColumnType("int");

                    b.Property<string>("SiUnitsTypeValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("SiYear")
                        .HasColumnType("datetime2");

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnType("int");

                    b.Property<int?>("TskCopiedFrom")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskEstCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("TskExecutionDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("TskFinishDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("TskFmecaEffectPct")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("TskId")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("TskIntervalUnit")
                        .HasColumnType("int");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int");

                    b.Property<string>("TskName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskOptimalCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TskPermit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TskPriorityCode")
                        .HasColumnType("int");

                    b.Property<int?>("TskStatus")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TskUnitType")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskUnits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskValidFromYear")
                        .HasColumnType("int");

                    b.Property<int?>("TskValidUntilYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskWorkInspCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskWorkpackage")
                        .HasColumnType("int");

                    b.ToTable("ClusterSiTaskCollection");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterTaskPlan", b =>
                {
                    b.Property<int>("CltpId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CltpID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CltpId"));

                    b.Property<decimal?>("CltpClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("?");

                    b.Property<decimal?>("CltpClusterCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cluster cost for the cluster task plan");

                    b.Property<int?>("CltpClusterId")
                        .HasColumnType("int")
                        .HasColumnName("CltpClusterID")
                        .HasComment("Cluster ID to which the cluster task plan is referenced (Cluster)");

                    b.Property<int?>("CltpCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("CltpCommonTaskID")
                        .HasComment("Common task ID to which the cluster task plan is referenced (CommonTask)");

                    b.Property<DateTime?>("CltpDateExecuted")
                        .HasColumnType("datetime")
                        .HasComment("Date when the cluster task plan is executed");

                    b.Property<DateTime?>("CltpDateGenerated")
                        .HasColumnType("datetime")
                        .HasComment("Date when the cluster task plan is generated");

                    b.Property<decimal?>("CltpDownTime")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Downtime needed in hours");

                    b.Property<decimal?>("CltpDuration")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Time spend in hours");

                    b.Property<decimal?>("CltpEstCosts")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<int?>("CltpExecuteStatus")
                        .HasColumnType("int")
                        .HasComment("Execute status of the cluster task plan");

                    b.Property<DateTime?>("CltpExecutionDate")
                        .HasColumnType("datetime")
                        .HasComment("Date when the cluster task plan must be executed");

                    b.Property<bool?>("CltpInterruptable")
                        .HasColumnType("bit")
                        .HasComment("Allow the task plan to be paused.");

                    b.Property<int?>("CltpObjectId")
                        .HasColumnType("int")
                        .HasColumnName("CltpObjectID")
                        .HasComment("Object ID to which the cluster task plan is referenced (Object)");

                    b.Property<int?>("CltpPriority")
                        .HasColumnType("int")
                        .HasComment("Priority of the cluster task plan");

                    b.Property<int?>("CltpQualityScore")
                        .HasColumnType("int")
                        .HasComment("?");

                    b.Property<string>("CltpReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("CltpReferenceID")
                        .HasComment("?");

                    b.Property<string>("CltpRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the cluster task plan");

                    b.Property<int?>("CltpRiskId")
                        .HasColumnType("int")
                        .HasColumnName("CltpRiskID")
                        .HasComment("Risk ID to which the cluster task plan is referenced (MRB)");

                    b.Property<int?>("CltpSequence")
                        .HasColumnType("int")
                        .HasComment("Sequence of the relationship between cluster task plans.");

                    b.Property<int?>("CltpShiftEndDate")
                        .HasColumnType("int")
                        .HasComment("Amount of days that the end date of the cluster task plan is shifted");

                    b.Property<int?>("CltpShiftStartDate")
                        .HasColumnType("int")
                        .HasComment("Amount of days that the start date of the cluster task plan is shifted");

                    b.Property<int?>("CltpSiId")
                        .HasColumnType("int")
                        .HasColumnName("CltpSiID")
                        .HasComment("Significant item ID to which the cluster task plan is referenced (Cluster)");

                    b.Property<decimal?>("CltpSiUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("?");

                    b.Property<int?>("CltpSlack")
                        .HasColumnType("int")
                        .HasComment("Interval of the slack for the cluster task plan");

                    b.Property<int?>("CltpSlackIntervalType")
                        .HasColumnType("int")
                        .HasComment("Interval unit ID of the slack for the cluster task plan (LookupIntervalUnit)");

                    b.Property<int?>("CltpTaskId")
                        .HasColumnType("int")
                        .HasColumnName("CltpTaskID")
                        .HasComment("Task ID to which the cluster task plan is referenced (Task)");

                    b.Property<decimal?>("CltpToolCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Tool cost for the cluster task plan");

                    b.Property<decimal?>("CltpTotalCosts")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<bool?>("CltpUseLastDateExecuted")
                        .HasColumnType("bit")
                        .HasComment("Use the last date executed as executing date for the cluster task plan");

                    b.HasKey("CltpId");

                    b.HasIndex("CltpClusterId");

                    b.HasIndex("CltpRiskId");

                    b.HasIndex("CltpSiId");

                    b.HasIndex("CltpTaskId");

                    b.ToTable("TblClusterTaskPlan", t =>
                        {
                            t.HasComment("Cluster task plans contain groups of preventive measures, that can be imported into a maintenance information system. \r\n\r\nThe cluster task plans are generated from the clusters. Data in this table is generated using data from tables Cluster, ClusterCost, Task, PriorityTask, Mrb, and RiskObject.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CmnCostCalculation", b =>
                {
                    b.Property<decimal?>("CmnTaskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("CtcCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("CtcCommonTaskID");

                    b.Property<decimal>("CtcCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("CtcPriceIndexYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("InflPercentage")
                        .HasColumnType("decimal(18, 2)");

                    b.ToTable("CmnCostCalculation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonCost", b =>
                {
                    b.Property<int>("CmnCostId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CmnCostID")
                        .HasComment("Unique ID (PK of CommonCost)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CmnCostId"));

                    b.Property<string>("CmnCostCalculationType")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("The way the common cost must be used in calculations (P, PxN, PxNxU)");

                    b.Property<DateTime?>("CmnCostDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("CmnCostDescription")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Descriptive name of the common cost. This value is used for selecting the common costs in the screens that use them.");

                    b.Property<decimal?>("CmnCostExtraCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Extra cost for the common cost (currently not in use)");

                    b.Property<string>("CmnCostModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<decimal?>("CmnCostNumber")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Quantity number, used in calculation of costs. (when calculation type is not set to price-only)");

                    b.Property<string>("CmnCostOrgId")
                        .HasMaxLength(12)
                        .IsUnicode(false)
                        .HasColumnType("varchar(12)")
                        .HasColumnName("CmnCostOrgID")
                        .HasComment("Organisation to which the common cost is bound");

                    b.Property<decimal?>("CmnCostPrice")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Price of one item. Is always used for price calculation.");

                    b.Property<int?>("CmnCostPriceGroup")
                        .HasColumnType("int")
                        .HasComment("Inflation group ID to which the common cost are bound. (FK to LookupUserDefined)");

                    b.Property<int?>("CmnCostPriceIndexYear")
                        .HasColumnType("int")
                        .HasComment("The year in which the common cost were indexed");

                    b.Property<string>("CmnCostReferenceCode")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique reference code or ID (customer)");

                    b.Property<string>("CmnCostRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks field for common costs.");

                    b.Property<bool?>("CmnCostRotating")
                        .HasColumnType("bit")
                        .HasComment("Boolean to set the common cost as a rotating item");

                    b.Property<string>("CmnCostShortKey")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Short key of the common cost. Usually takes the first character of the CmnCostType. (?)");

                    b.Property<bool?>("CmnCostSpare")
                        .HasColumnType("bit")
                        .HasComment("Boolean to set the common cost as a spare part");

                    b.Property<int?>("CmnCostStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the common cost");

                    b.Property<DateTime?>("CmnCostStatusDate")
                        .HasColumnType("datetime")
                        .HasComment("Date the status was last modified.");

                    b.Property<string>("CmnCostSubSubType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Extra added sub sub type as a search field");

                    b.Property<string>("CmnCostSubType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Extra added sub type as a search field");

                    b.Property<string>("CmnCostType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Common cost type domain. Specifies what caused the costs. (tools, disciplines, energy, etc.)");

                    b.Property<string>("CmnCostUnitType")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("The unit type of the common cost. Not currently used by AMprover software. (?)");

                    b.Property<decimal?>("CmnCostUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Number of units, will be used in cost calculation (when calculation type is PxNxU)");

                    b.Property<string>("CmnCostVendorCode")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique reference code or ID of the vendor");

                    b.HasKey("CmnCostId");

                    b.HasIndex("CmnCostPriceGroup");

                    b.ToTable("TblCommonCost", t =>
                        {
                            t.HasComment("Master data table. Common costs define recurring costs, that are used for cost calculations. They define different costs for different types of items. (like materials, tools or use of specific disciplines.) Common costs can be added to common actions and clusters.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTask", b =>
                {
                    b.Property<int>("CmnTaskId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CmnTaskID")
                        .HasComment("Unique ID (PK of CommonTask)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CmnTaskId"));

                    b.Property<bool?>("CmnTaskCostModifiable")
                        .HasColumnType("bit")
                        .HasComment("Boolean that enables modification of the common task costs during risk analysis");

                    b.Property<decimal?>("CmnTaskCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost of common task execution. (Cost per unit?) Is named action costs in the AMprover software.");

                    b.Property<DateTime?>("CmnTaskDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("CmnTaskDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("CmnTaskDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description for the common task");

                    b.Property<decimal?>("CmnTaskDownTime")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Downtime needed to complete the task (in hours)");

                    b.Property<decimal?>("CmnTaskDuration")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Time spent on task. Does not seem to be set from the AMprover software. (?)");

                    b.Property<int?>("CmnTaskExecutor")
                        .HasColumnType("int")
                        .HasComment("Executor ID of the common task (FK to LookupExecutor)");

                    b.Property<bool?>("CmnTaskExecutorModifiable")
                        .HasColumnType("bit")
                        .HasComment("Boolean that enables modification of the common task executor during risk analysis");

                    b.Property<string>("CmnTaskFieldRights")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("? Is not set by the AMprover software");

                    b.Property<string>("CmnTaskFilterRef")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CmnTaskGeneralDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("General description for the common task. Does not seem to be filled by the AMprover software. (?)");

                    b.Property<string>("CmnTaskInitiatedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Username of person that created this cluster");

                    b.Property<int?>("CmnTaskInitiator")
                        .HasColumnType("int")
                        .HasComment("Initiator ID of the common task (FK to LookupInitiator)");

                    b.Property<bool?>("CmnTaskInitiatorModifiable")
                        .HasColumnType("bit")
                        .HasComment("Boolean that enables modification of the common task initiator during risk analysis");

                    b.Property<decimal?>("CmnTaskInterval")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Interval of the common task. The common task needs to be executed each time this interval passes.");

                    b.Property<bool?>("CmnTaskIntervalModifiable")
                        .HasColumnType("bit")
                        .HasComment("Boolean that enables modification of the interval during risk analysis");

                    b.Property<int?>("CmnTaskIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("ID of the the interval unit of the common task (FK to LookupIntervalUnit)");

                    b.Property<int?>("CmnTaskMasterId")
                        .HasColumnType("int")
                        .HasColumnName("CmnTaskMasterID")
                        .HasComment("ID of the master common task (FK to CommonTask) Does not seem to be set by the AMprover software (?)");

                    b.Property<string>("CmnTaskModifiedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<int?>("CmnTaskMxPolicy")
                        .HasColumnType("int")
                        .HasComment("Maintenance policy ID of the common task (FK to LookupMxPolicy)");

                    b.Property<string>("CmnTaskName")
                        .IsRequired()
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)")
                        .HasComment("The name of the common task");

                    b.Property<string>("CmnTaskPermit")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("char(10)")
                        .HasComment("Permit that is needed for the common task. Never set in the AMprover software.");

                    b.Property<int?>("CmnTaskPriorityCode")
                        .HasColumnType("int")
                        .HasComment("Priority code of common task. Is not set by the AMprover software (?)");

                    b.Property<string>("CmnTaskReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("CmnTaskReferenceID")
                        .HasComment("Reference ID of the common task. Not bound to anything, user can enter any string. (?)");

                    b.Property<string>("CmnTaskRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the common task. Does not seem to be filled by the AMprover software. (?)");

                    b.Property<string>("CmnTaskResponsible")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Person or department that is responsible for executing the common task (never set in the AMprover software)");

                    b.Property<int?>("CmnTaskSiCategory")
                        .HasColumnType("int")
                        .HasComment("The si category value to which the common task is bound (FK to LookupUserDefined, UserdefinedFilter is SICategory)");

                    b.Property<int?>("CmnTaskSortOrder")
                        .HasColumnType("int")
                        .HasComment("Custom sequence to order the common tasks");

                    b.Property<string>("CmnTaskType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Type of common task (task, procedure etc.) Type domain is defined in Lookup table, Lookupfilter value is MeasureType. This is called Action type in the AMprover software.");

                    b.Property<int>("CmnTaskUnitType")
                        .HasColumnType("int")
                        .HasComment("The unit type value to which the cost of the common task are bound (FK to LookupUserDefined, FilterType UnitTypes)");

                    b.Property<int?>("CmnTaskValidFromYear")
                        .HasColumnType("int")
                        .HasComment("The starting year of the common, when it becomes valid. Does not seem to be set from the AMprover software. (?)");

                    b.Property<int?>("CmnTaskValidUntilYear")
                        .HasColumnType("int")
                        .HasComment("The year when the common task is no longer valid, and will no longer affect cost calculations. Does not seem to be set from the AMprover software. (?)");

                    b.Property<decimal?>("CmnTaskWorkInspCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Inspection cost of the common task. Is not set by the AMprover software. (?)");

                    b.Property<int?>("CmnTaskWorkPackage")
                        .HasColumnType("int")
                        .HasComment("ID of the work package of the common task (FK to Workpackage)");

                    b.Property<bool?>("CmnTaskWorkPackageModifiable")
                        .HasColumnType("bit")
                        .HasComment("Boolean that enables modification of the work package during risk analysis");

                    b.HasKey("CmnTaskId");

                    b.HasIndex("CmnTaskExecutor");

                    b.HasIndex("CmnTaskInitiator");

                    b.HasIndex("CmnTaskIntervalUnit");

                    b.HasIndex("CmnTaskMxPolicy");

                    b.HasIndex("CmnTaskWorkPackage");

                    b.ToTable("TblCommonTask", t =>
                        {
                            t.HasComment("Master data table. A common task is a task that has to be executed after a defined interval, each time the interval passes. Also named common action.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTaskCost", b =>
                {
                    b.Property<int>("CtcId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CtcID")
                        .HasComment("Unique ID (PK of CommonTaskCost)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CtcId"));

                    b.Property<string>("CtcCalculationType")
                        .IsRequired()
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("The way the common task cost must be calculated (P, PxN, PxNxU)");

                    b.Property<int>("CtcCommonCostId")
                        .HasColumnType("int")
                        .HasColumnName("CtcCommonCostID")
                        .HasComment("ID of the common cost the common task cost is bound to (FK to CommonCost)");

                    b.Property<int?>("CtcCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("CtcCommonTaskID")
                        .HasComment("Binds costs to a specific task. Currently selected task is added automatically when user adds a new common cost to an action. (FK to CommonTask) ");

                    b.Property<decimal>("CtcCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Calculated cost of the common task cost (determined by calculation type, and stored values for Price, Quantity and Units)");

                    b.Property<decimal>("CtcPrice")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Price of the common task cost (price of a single unit)");

                    b.Property<int>("CtcPriceIndexYear")
                        .HasColumnType("int")
                        .HasComment("The year in which the common task cost are indexed");

                    b.Property<decimal?>("CtcQuantity")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Quantity of common cost items needed for this common task cost");

                    b.Property<decimal?>("CtcUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Number of units needed for this common task cost");

                    b.HasKey("CtcId");

                    b.HasIndex("CtcCommonCostId");

                    b.HasIndex("CtcCommonTaskId");

                    b.ToTable("TblCommonTaskCost", t =>
                        {
                            t.HasComment("Master data table. Contains costs related to common tasks. Defined within master data, as part of a common action. (?)");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Company", b =>
                {
                    b.Property<int>("CompanyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CompanyID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CompanyId"));

                    b.Property<string>("CompanyAdres")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Address of the company");

                    b.Property<string>("CompanyContact1")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Contact field of the company");

                    b.Property<string>("CompanyContact2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Contact field of the company");

                    b.Property<string>("CompanyCountry")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Country of the company");

                    b.Property<string>("CompanyDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the company");

                    b.Property<string>("CompanyEmail")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Email address of the company");

                    b.Property<string>("CompanyFax")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Fax number of the company");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("The name of the company");

                    b.Property<string>("CompanyPhone")
                        .HasMaxLength(15)
                        .IsUnicode(false)
                        .HasColumnType("varchar(15)")
                        .HasComment("Phone number of the company");

                    b.Property<string>("CompanyPlace")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("City of the company");

                    b.Property<string>("CompanyPostAdres")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Post address of the company");

                    b.Property<string>("CompanyZipCode")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("Zip code of the company");

                    b.HasKey("CompanyId");

                    b.ToTable("TblCompany", t =>
                        {
                            t.HasComment("Would contain full contact info for companies. Not used by AMprover software. (!)");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CriticalityRanking", b =>
                {
                    b.Property<int>("CritId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CritId"));

                    b.Property<string>("CritCategory")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<DateTime>("CritDateInitiated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CritDateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("CritDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("CritDownTimeAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CritFailureCause")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CritFailureConsequences")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CritFailureMechanism")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("CritFailureMode")
                        .HasColumnType("int");

                    b.Property<int?>("CritFmeca")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca1Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca2")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca2Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca3")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca3Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca4")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca4Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca5")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca5Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca6")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca6Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca7")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca7Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CritFmecaSelect")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CritFmecaVersion")
                        .HasColumnType("int");

                    b.Property<string>("CritInitiatedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("CritKooN")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritMTTR")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CritModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("CritMtbf")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritMtbfValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CritName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("CritProbability")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CritReStrategy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CritRedundant")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("CritRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CritResponsible")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("CritSiId")
                        .HasColumnType("int");

                    b.Property<string>("CritSiName")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("CritStatus")
                        .HasColumnType("int");

                    b.Property<int?>("CritTotal")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritTotalValue")
                        .HasColumnType("decimal(18,3)");

                    b.HasKey("CritId");

                    b.ToTable("TblCriticalityRanking");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Department", b =>
                {
                    b.Property<int>("DepId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("DepID")
                        .HasComment("Unique ID (PK of Department)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DepId"));

                    b.Property<string>("DepDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Full description of the department");

                    b.Property<string>("DepShortKey")
                        .IsRequired()
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)")
                        .HasComment("Short description of the department");

                    b.HasKey("DepId");

                    b.ToTable("TblDepartment", t =>
                        {
                            t.HasComment("Defines different departments within a company. Defined from master data");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DerModified", b =>
                {
                    b.Property<int>("DerModId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("DerModID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DerModId"));

                    b.Property<int?>("DerModCopiedFrom")
                        .HasColumnType("int");

                    b.Property<bool?>("DerModDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("DerModModifications")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("DerModObjectId")
                        .HasColumnType("int")
                        .HasColumnName("DerModObjectID");

                    b.Property<string>("DerModObjectKey")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DerModObjectType")
                        .HasColumnType("int");

                    b.HasKey("DerModId");

                    b.ToTable("TblDerModified");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Descriptions", b =>
                {
                    b.Property<int>("DescId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("DescID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DescId"));

                    b.Property<string>("DescDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("DescExtraId")
                        .HasColumnType("int")
                        .HasColumnName("DescExtraID");

                    b.Property<string>("DescExtraType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int?>("DescFieldType")
                        .HasColumnType("int");

                    b.Property<int?>("DescRiskId")
                        .HasColumnType("int")
                        .HasColumnName("DescRiskID");

                    b.Property<int?>("DescRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("DescRiskObjectID");

                    b.Property<int?>("DescTaskId")
                        .HasColumnType("int")
                        .HasColumnName("DescTaskID");

                    b.HasKey("DescId");

                    b.HasIndex("DescRiskId");

                    b.HasIndex("DescRiskObjectId");

                    b.HasIndex("DescTaskId");

                    b.ToTable("Descriptions");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DsRamsReportRams", b =>
                {
                    b.Property<double?>("RamsAvailabilityInput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsAvailabilityOutput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("RamsClassDc")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasColumnName("RamsClassDC");

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<double?>("RamsDcd")
                        .HasColumnType("float")
                        .HasColumnName("RamsDCd");

                    b.Property<string>("RamsDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<DateTime?>("RamsDgDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDgDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("RamsDgDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsDgHorizon")
                        .HasColumnType("int");

                    b.Property<string>("RamsDgInitiatedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("RamsDgModifiedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("RamsDgName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("RamsDgReferenceID");

                    b.Property<string>("RamsDgRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("RamsDgRiskObject")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgScenario")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgSerialized")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsDgSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgSiID");

                    b.Property<string>("RamsDgSiRefId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("RamsDgSiRefID");

                    b.Property<int?>("RamsDgStatus")
                        .HasColumnType("int");

                    b.Property<int?>("RamsDgTestInterval")
                        .HasColumnType("int");

                    b.Property<bool?>("RamsDgWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsDgWantLCC");

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramRefID");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsFuncReliability")
                        .HasColumnType("float");

                    b.Property<string>("RamsFunctionalDemand")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit");

                    b.Property<string>("RamsInitiatedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int");

                    b.Property<string>("RamsModifiedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn");

                    b.Property<double?>("RamsMttr")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTTR");

                    b.Property<string>("RamsName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsObjectID");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int");

                    b.Property<double?>("RamsPfd")
                        .HasColumnType("float")
                        .HasColumnName("RamsPFD");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int");

                    b.Property<string>("RamsRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsRiskId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskID");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskObjectID");

                    b.Property<int?>("RamsSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsSiID");

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsTechnReliability")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsWantLCC");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int");

                    b.Property<int?>("RamsXposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsXPosition");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int");

                    b.Property<int?>("RamsYposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsYPosition");

                    b.ToTable("DsRamsReportRams");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DsRamsReportRamsDetails", b =>
                {
                    b.Property<double?>("RamsAvailabilityInput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsAvailabilityOutput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("RamsClassDc")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasColumnName("RamsClassDC");

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<double?>("RamsDcd")
                        .HasColumnType("float")
                        .HasColumnName("RamsDCd");

                    b.Property<string>("RamsDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<DateTime?>("RamsDgDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDgDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("RamsDgDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsDgHorizon")
                        .HasColumnType("int");

                    b.Property<string>("RamsDgInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("RamsDgReferenceID");

                    b.Property<string>("RamsDgRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("RamsDgRiskObject")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgScenario")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsDgSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgSiID");

                    b.Property<string>("RamsDgSiRefId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("RamsDgSiRefID");

                    b.Property<int?>("RamsDgStatus")
                        .HasColumnType("int");

                    b.Property<int?>("RamsDgTestInterval")
                        .HasColumnType("int");

                    b.Property<bool?>("RamsDgWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsDgWantLCC");

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramRefID");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsFuncReliability")
                        .HasColumnType("float");

                    b.Property<string>("RamsFunctionalDemand")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit");

                    b.Property<string>("RamsInitiatedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int");

                    b.Property<string>("RamsModifiedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn");

                    b.Property<double?>("RamsMttr")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTTR");

                    b.Property<string>("RamsName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsObjectID");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int");

                    b.Property<double?>("RamsPfd")
                        .HasColumnType("float")
                        .HasColumnName("RamsPFD");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int");

                    b.Property<string>("RamsRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsRiskId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskID");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskObjectID");

                    b.Property<int?>("RamsSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsSiID");

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsTechnReliability")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsWantLCC");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int");

                    b.Property<int?>("RamsXposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsXPosition");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int");

                    b.Property<int?>("RamsYposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsYPosition");

                    b.ToTable("DsRamsReportRamsDetails");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DsRamsReportRamsTable", b =>
                {
                    b.Property<double?>("RamsAvailabilityInput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsAvailabilityOutput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("RamsClassDc")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasColumnName("RamsClassDC");

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<double?>("RamsDcd")
                        .HasColumnType("float")
                        .HasColumnName("RamsDCd");

                    b.Property<string>("RamsDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<DateTime?>("RamsDgDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDgDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("RamsDgDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsDgHorizon")
                        .HasColumnType("int");

                    b.Property<int>("RamsDgId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgID");

                    b.Property<string>("RamsDgInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("RamsDgReferenceID");

                    b.Property<string>("RamsDgRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("RamsDgRiskObject")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgScenario")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsDgSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgSiID");

                    b.Property<string>("RamsDgSiRefId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("RamsDgSiRefID");

                    b.Property<int?>("RamsDgStatus")
                        .HasColumnType("int");

                    b.Property<int?>("RamsDgTestInterval")
                        .HasColumnType("int");

                    b.Property<bool?>("RamsDgWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsDgWantLCC");

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramRefID");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsFuncReliability")
                        .HasColumnType("float");

                    b.Property<string>("RamsFunctionalDemand")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit");

                    b.Property<string>("RamsInitiatedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int");

                    b.Property<string>("RamsModifiedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn");

                    b.Property<double?>("RamsMttr")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTTR");

                    b.Property<string>("RamsName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsObjectID");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int");

                    b.Property<double?>("RamsPfd")
                        .HasColumnType("float")
                        .HasColumnName("RamsPFD");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int");

                    b.Property<string>("RamsRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsRiskId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskID");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskObjectID");

                    b.Property<int?>("RamsSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsSiID");

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsTechnReliability")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsWantLCC");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int");

                    b.Property<int?>("RamsXposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsXPosition");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int");

                    b.Property<int?>("RamsYposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsYPosition");

                    b.Property<string>("SiDescription")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SiMiscTextField1")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<string>("SiName")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.ToTable("DsRamsReportRamsTable");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.EditLog", b =>
                {
                    b.Property<int>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LogID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LogId"));

                    b.Property<DateTime>("LogDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LogModificationType")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("LogModifications")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("LogObjectId")
                        .HasColumnType("int")
                        .HasColumnName("LogObjectID");

                    b.Property<string>("LogObjectType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("LogObjectVarId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("LogObjectVarID");

                    b.Property<string>("LogUserId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("LogUserID");

                    b.HasKey("LogId");

                    b.ToTable("TblEditLog");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ExportSimco", b =>
                {
                    b.Property<string>("CmnTaskReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("CmnTaskReferenceID");

                    b.Property<bool?>("PrioTskAutoSelected")
                        .HasColumnType("bit");

                    b.Property<decimal?>("PrioTskClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskCommonTaskID");

                    b.Property<decimal?>("PrioTskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("PrioTskDateDue")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioTskDateExecuted")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("PrioTskDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskExecutionYear")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskFromReference")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("PrioTskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskID");

                    b.Property<int?>("PrioTskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskObjectID");

                    b.Property<int?>("PrioTskOriginalId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskOriginalID");

                    b.Property<decimal?>("PrioTskPriorityCode")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskPriorityID");

                    b.Property<string>("PrioTskReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("PrioTskReferenceID");

                    b.Property<decimal?>("PrioTskRiskBudget")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskRiskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskRiskID");

                    b.Property<bool?>("PrioTskSealed")
                        .HasColumnType("bit");

                    b.Property<string>("PrioTskSiDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskSiID");

                    b.Property<decimal?>("PrioTskSiUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskTaskID");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SiContractEnd")
                        .HasColumnType("datetime");

                    b.Property<string>("SiMiscTextField1")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("SiPartOf")
                        .HasColumnType("int");

                    b.Property<string>("SiVendor")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.ToTable("ExportSimco");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ExportTasks", b =>
                {
                    b.Property<decimal?>("PrioTskClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskCommonTaskID");

                    b.Property<decimal?>("PrioTskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("PrioTskDateDue")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioTskDateExecuted")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("PrioTskDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskExecutionYear")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskFromReference")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskObjectID");

                    b.Property<decimal?>("PrioTskPriorityCode")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskPriorityID");

                    b.Property<string>("PrioTskReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("PrioTskReferenceID");

                    b.Property<decimal?>("PrioTskRiskBudget")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskRiskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskRiskID");

                    b.Property<string>("PrioTskSiDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskSiID");

                    b.Property<decimal?>("PrioTskSiUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskTaskID");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int");

                    b.ToTable("ExportTasks");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Filters", b =>
                {
                    b.Property<int>("SqlSelId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SqlSelID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SqlSelId"));

                    b.Property<string>("SqlSelGroup")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("SqlSelName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("SqlSelShortKey")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("SqlSelSubGroup")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.HasKey("SqlSelId");

                    b.ToTable("TblFilters");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.FiltersSelectionList", b =>
                {
                    b.Property<int>("SqlSelId")
                        .HasColumnType("int")
                        .HasColumnName("SqlSelID");

                    b.Property<int>("Sequence")
                        .HasColumnType("int");

                    b.Property<string>("AndOr")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("varchar(3)");

                    b.Property<string>("Criterium")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FieldType")
                        .IsRequired()
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)");

                    b.Property<string>("FriendlyName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<short>("Ident")
                        .HasColumnType("smallint");

                    b.Property<bool>("IsAdvanced")
                        .HasColumnType("bit");

                    b.Property<string>("Selection")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("SortType")
                        .HasMaxLength(4)
                        .IsUnicode(false)
                        .HasColumnType("varchar(4)");

                    b.HasKey("SqlSelId", "Sequence");

                    b.ToTable("TblFiltersSelectionList");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Fmeca", b =>
                {
                    b.Property<int>("FmecaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("FmecaID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FmecaId"));

                    b.Property<string>("FmecaDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("FmecaIsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("FmecaMatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("FmecaName")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("FmecaShortName")
                        .IsRequired()
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)");

                    b.Property<int>("FmecaVersion")
                        .HasColumnType("int");

                    b.HasKey("FmecaId");

                    b.ToTable("TblFmeca");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.FmecaSelect", b =>
                {
                    b.Property<int>("MfsId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MfsID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MfsId"));

                    b.Property<int>("MfsColIndex")
                        .HasColumnType("int");

                    b.Property<double?>("MfsCustomAfter")
                        .HasColumnType("float");

                    b.Property<double?>("MfsCustomBefore")
                        .HasColumnType("float");

                    b.Property<string>("MfsDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("MfsLccId")
                        .HasColumnType("int")
                        .HasColumnName("MfsLccID");

                    b.Property<int>("MfsReferenceId")
                        .HasColumnType("int")
                        .HasColumnName("MfsReferenceID");

                    b.Property<int>("MfsRefrenceType")
                        .HasColumnType("int");

                    b.Property<int?>("MfsSelectAfter")
                        .HasColumnType("int");

                    b.Property<int?>("MfsSelectBefore")
                        .HasColumnType("int");

                    b.Property<int?>("MfsTaskId")
                        .HasColumnType("int")
                        .HasColumnName("MfsTaskID");

                    b.Property<double?>("MfsValueAfter")
                        .HasColumnType("float");

                    b.Property<double?>("MfsValueBefore")
                        .HasColumnType("float");

                    b.HasKey("MfsId");

                    b.HasIndex("MfsLccId");

                    b.HasIndex("MfsReferenceId");

                    b.HasIndex("MfsTaskId");

                    b.ToTable("TblFmecaSelect");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lcc", b =>
                {
                    b.Property<int>("LccId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LccID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LccId"));

                    b.Property<decimal?>("LccAECPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccAec")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("LccAEC");

                    b.Property<int>("LccAge")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccAverageOptimalCost")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("LccAverageOptimalCostPmo")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<int?>("LccChildObject")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject1")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject2")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject3")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject4")
                        .HasColumnType("int");

                    b.Property<DateTime?>("LccDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<decimal?>("LccDiscountRate")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("LccEcoFunctional")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccEcoTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<bool?>("LccExclude")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LccInputAvailabilityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccInputAvailabilityTechnicalPmo")
                        .HasPrecision(18, 6)
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccInputReliabilityFunctional")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccMTBFTechnicalPmo")
                        .HasPrecision(18, 6)
                        .HasColumnType("decimal(18,6)");

                    b.Property<int?>("LccMaxYears")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccMcRav")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccMcRavPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("LccModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("LccMtbffunctional")
                        .HasColumnType("decimal(18, 6)")
                        .HasColumnName("LccMTBFFunctional");

                    b.Property<decimal?>("LccMtbftechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasColumnName("LccMTBFTechnical");

                    b.Property<decimal?>("LccNPVPmo")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<int?>("LccNPVyearPmo")
                        .HasColumnType("int");

                    b.Property<string>("LccName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("LccNpv")
                        .HasColumnType("decimal(18, 4)")
                        .HasColumnName("LccNPV");

                    b.Property<int?>("LccNpvyear")
                        .HasColumnType("int")
                        .HasColumnName("LccNPVyear");

                    b.Property<decimal?>("LccOptimalAverageCorrectiveCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("LccOptimalAverageCorrectiveCostPmo")
                        .HasPrecision(18)
                        .HasColumnType("decimal(18,0)");

                    b.Property<decimal?>("LccOptimalAveragePreventiveCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("LccOptimalAveragePreventiveCostPmo")
                        .HasPrecision(18)
                        .HasColumnType("decimal(18,0)");

                    b.Property<byte[]>("LccOptimalImage")
                        .HasColumnType("image");

                    b.Property<decimal?>("LccOutputAvailabilityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccOutputReliabilityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccOutputReliabilityTechnicalPmo")
                        .HasPrecision(18, 6)
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccOverallProductionCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<int?>("LccPartOf")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccPotential")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccPotentialPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccProductionCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccProductivityFunctional")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccProductivityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<int?>("LccRamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsDiagramID");

                    b.Property<int?>("LccRamsId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsID");

                    b.Property<byte[]>("LccRamsImage")
                        .HasColumnType("image");

                    b.Property<byte[]>("LccRealCostImage")
                        .HasColumnType("image");

                    b.Property<string>("LccRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("LccReplacementValue")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<int?>("LccRiskObject")
                        .HasColumnType("int");

                    b.Property<int?>("LccScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("LccScenarioID");

                    b.Property<int?>("LccSiId")
                        .HasColumnType("int")
                        .HasColumnName("LccSiID");

                    b.Property<int?>("LccStartYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccTotalAverageCost")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("LccTotalAverageCostPmo")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("LccUtilizationFunctional")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccUtilizationTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<DateTime?>("LccdateCalculated")
                        .HasColumnType("smalldatetime")
                        .HasColumnName("LCCDateCalculated");

                    b.HasKey("LccId");

                    b.HasIndex("LccChildObject");

                    b.HasIndex("LccChildObject1");

                    b.HasIndex("LccChildObject2");

                    b.HasIndex("LccChildObject3");

                    b.HasIndex("LccChildObject4");

                    b.HasIndex("LccPartOf");

                    b.HasIndex("LccRiskObject");

                    b.HasIndex("LccScenarioId");

                    b.HasIndex("LccSiId");

                    b.ToTable("TblLCC");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccDetailGrid", b =>
                {
                    b.Property<int>("LccDetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LccDetID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LccDetId"));

                    b.Property<decimal?>("LccDetActionCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetAec")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("LccDetAEC");

                    b.Property<decimal?>("LccDetAvailabilityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccDetAverageAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetAverageBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetAverageOptimalCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetAverageRealCorrectiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetAverageRealPreventiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetCorrectiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetDepreciation")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("LccDetDirectCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetEcoTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccDetFailureRate")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccDetFmecaAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaCustomAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaCustomBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaCustomRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaCustomRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("LccDetLccId")
                        .HasColumnType("int")
                        .HasColumnName("LccDetLccID");

                    b.Property<decimal?>("LccDetMaintenanceCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetModificationCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetOpexCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetOptimalCorrectiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetOptimalPreventiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetPreventiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetProcedureCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetProductivityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccDetRealCorrectiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRealOpexCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRealPreventiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRealTotalCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetReliability")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("LccDetRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetSpareCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetTaskCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetTotalCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetTotalNpv")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("LccDetTotalNPV");

                    b.Property<decimal?>("LccDetUtilizationTechnichal")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<int?>("LccDetYear")
                        .HasColumnType("int");

                    b.Property<string>("LccName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("LccProductionCost")
                        .HasColumnType("decimal(18, 2)");

                    b.HasKey("LccDetId");

                    b.ToTable("LccDetailGrid");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccFmecaOfRiskObject", b =>
                {
                    b.Property<string>("FmecaMatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("FmecaVersion")
                        .HasColumnType("int");

                    b.Property<int>("RiskObjFmecaId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjFmecaID");

                    b.Property<int>("RiskObjId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjID");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.ToTable("LccFmecaOfRiskObject");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccGrid", b =>
                {
                    b.Property<int>("LccId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LccID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LccId"));

                    b.Property<string>("ChildObject2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ChildObject3")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ChildObject4")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FmecaMatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("FmecaVersion")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccAec")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("LccAEC");

                    b.Property<decimal?>("LccAverageOptimalCost")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<int?>("LccChildObject")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject1")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject2")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject3")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject4")
                        .HasColumnType("int");

                    b.Property<DateTime?>("LccDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<decimal?>("LccDiscountRate")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("LccEcoFunctional")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccEcoTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<bool?>("LccExclude")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LccInputAvailabilityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccInputReliabilityFunctional")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<int?>("LccMaxYears")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccMcRav")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("LccModifiedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("LccMtbffunctional")
                        .HasColumnType("decimal(18, 6)")
                        .HasColumnName("LccMTBFFunctional");

                    b.Property<decimal?>("LccMtbftechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasColumnName("LccMTBFTechnical");

                    b.Property<string>("LccName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("LccNpv")
                        .HasColumnType("decimal(18, 4)")
                        .HasColumnName("LccNPV");

                    b.Property<int?>("LccNpvyear")
                        .HasColumnType("int")
                        .HasColumnName("LccNPVyear");

                    b.Property<decimal?>("LccOptimalAverageCorrectiveCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("LccOptimalAveragePreventiveCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<byte[]>("LccOptimalImage")
                        .HasColumnType("image");

                    b.Property<decimal?>("LccOutputAvailabilityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccOutputReliabilityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccOverallProductionCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<int?>("LccPartOf")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccPotential")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccProductionCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccProductivityFunctional")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccProductivityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<int?>("LccRamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsDiagramID");

                    b.Property<int?>("LccRamsId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsID");

                    b.Property<byte[]>("LccRamsImage")
                        .HasColumnType("image");

                    b.Property<byte[]>("LccRealCostImage")
                        .HasColumnType("image");

                    b.Property<string>("LccRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("LccReplacementValue")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<int?>("LccRiskObject")
                        .HasColumnType("int");

                    b.Property<int?>("LccScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("LccScenarioID");

                    b.Property<int?>("LccSiId")
                        .HasColumnType("int")
                        .HasColumnName("LccSiID");

                    b.Property<decimal?>("LccTotalAverageCost")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("LccUtilizationFunctional")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccUtilizationTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<DateTime?>("LccdateCalculated")
                        .HasColumnType("smalldatetime")
                        .HasColumnName("LCCDateCalculated");

                    b.Property<decimal?>("Obj2NewValue")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("Obj3NewValue")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("Obj4NewValue")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<int?>("ObjAvailableTime")
                        .HasColumnType("int");

                    b.Property<int?>("ObjId")
                        .HasColumnType("int")
                        .HasColumnName("ObjID");

                    b.Property<string>("ObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("ObjNewValue")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<int?>("ObjProductionTime")
                        .HasColumnType("int");

                    b.Property<int?>("ObjUsableTime")
                        .HasColumnType("int");

                    b.Property<int?>("ObjUtilizationTime")
                        .HasColumnType("int");

                    b.Property<string>("PartOfName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RiskObjNoOfInstallation")
                        .HasColumnType("int");

                    b.Property<decimal?>("RiskObjProdCostHour")
                        .HasColumnType("decimal(18, 2)");

                    b.HasKey("LccId");

                    b.ToTable("LccGrid");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccMrbCalc", b =>
                {
                    b.Property<string>("Failmode")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("failmode");

                    b.Property<int>("Failrateid")
                        .HasColumnType("int")
                        .HasColumnName("failrateid");

                    b.Property<string>("Fmecamatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("fmecamatrix");

                    b.Property<int?>("Fmecaversion")
                        .HasColumnType("int")
                        .HasColumnName("fmecaversion");

                    b.Property<decimal>("Mrbactioncosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrbactioncosts");

                    b.Property<decimal?>("Mrbcapcosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrbcapcosts");

                    b.Property<int?>("Mrbchildobject")
                        .HasColumnType("int")
                        .HasColumnName("mrbchildobject");

                    b.Property<int?>("Mrbchildobject1")
                        .HasColumnType("int")
                        .HasColumnName("mrbchildobject1");

                    b.Property<int?>("Mrbchildobject2")
                        .HasColumnType("int")
                        .HasColumnName("mrbchildobject2");

                    b.Property<int?>("Mrbchildobject3")
                        .HasColumnType("int")
                        .HasColumnName("mrbchildobject3");

                    b.Property<int?>("Mrbchildobject4")
                        .HasColumnType("int")
                        .HasColumnName("mrbchildobject4");

                    b.Property<decimal?>("Mrbcustomafter")
                        .HasColumnType("decimal(18, 3)")
                        .HasColumnName("mrbcustomafter");

                    b.Property<decimal?>("Mrbcustombefore")
                        .HasColumnType("decimal(18, 3)")
                        .HasColumnName("mrbcustombefore");

                    b.Property<decimal?>("Mrbdirectcostafter")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrbdirectcostafter");

                    b.Property<decimal?>("Mrbdirectcostbefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrbdirectcostbefore");

                    b.Property<decimal?>("Mrbdowntimeafter")
                        .HasColumnType("decimal(18, 4)")
                        .HasColumnName("mrbdowntimeafter");

                    b.Property<decimal?>("Mrbdowntimebefore")
                        .HasColumnType("decimal(18, 4)")
                        .HasColumnName("mrbdowntimebefore");

                    b.Property<decimal?>("Mrbeffectafter")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrbeffectafter");

                    b.Property<decimal?>("Mrbeffectbefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrbeffectbefore");

                    b.Property<string>("Mrbfailurecategorie1")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("mrbfailurecategorie1");

                    b.Property<string>("Mrbfailurecategorie2")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("mrbfailurecategorie2");

                    b.Property<int?>("Mrbfailuremode")
                        .HasColumnType("int")
                        .HasColumnName("mrbfailuremode");

                    b.Property<string>("Mrbfmecaselect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("mrbfmecaselect");

                    b.Property<int>("Mrbfmecaversion")
                        .HasColumnType("int")
                        .HasColumnName("mrbfmecaversion");

                    b.Property<int>("Mrbid")
                        .HasColumnType("int")
                        .HasColumnName("mrbid");

                    b.Property<decimal?>("Mrbmtbfafter")
                        .HasColumnType("decimal(18, 3)")
                        .HasColumnName("mrbmtbfafter");

                    b.Property<decimal?>("Mrbmtbfbefore")
                        .HasColumnType("decimal(18, 3)")
                        .HasColumnName("mrbmtbfbefore");

                    b.Property<string>("Mrbname")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("mrbname");

                    b.Property<decimal>("Mrboptimalcosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrboptimalcosts");

                    b.Property<decimal?>("Mrbriskafter")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrbriskafter");

                    b.Property<decimal?>("Mrbriskbefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrbriskbefore");

                    b.Property<int>("Mrbriskobject")
                        .HasColumnType("int")
                        .HasColumnName("mrbriskobject");

                    b.Property<decimal>("Mrbsparecosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrbsparecosts");

                    b.Property<decimal>("Mrbsparemanagecost")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("mrbsparemanagecost");

                    b.Property<decimal?>("NewValue")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("NewValue1")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("NewValue2")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("NewValue3")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("NewValue4")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<int?>("Riskobjnoofinstallation")
                        .HasColumnType("int")
                        .HasColumnName("riskobjnoofinstallation");

                    b.ToTable("LccMrbCalc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccOpexCalc", b =>
                {
                    b.Property<int?>("Expr1")
                        .HasColumnType("int");

                    b.Property<int?>("Expr2")
                        .HasColumnType("int");

                    b.Property<int?>("OpexDataInflationGroupId")
                        .HasColumnType("int")
                        .HasColumnName("OpexDataInflationGroupID");

                    b.Property<int?>("OpexDataInflationGroupId2")
                        .HasColumnType("int")
                        .HasColumnName("OpexDataInflationGroupID2");

                    b.Property<int?>("OpexDataInflationGroupId3")
                        .HasColumnType("int")
                        .HasColumnName("OpexDataInflationGroupID3");

                    b.Property<int?>("OpexDataMethod")
                        .HasColumnType("int");

                    b.Property<string>("OpexDataName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("OpexDataName2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("OpexDataName3")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("OpexDataPercentage")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("OpexDataPercentage2")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("OpexDataPercentage3")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("OpexLccColorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("OpexLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccID");

                    b.Property<int?>("OpexLccLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccLccID");

                    b.Property<string>("OpexLccName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("OpexLccOpexDataId1")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID1");

                    b.Property<int?>("OpexLccOpexDataId2")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID2");

                    b.Property<int?>("OpexLccOpexDataId3")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID3");

                    b.Property<decimal?>("OpexLccPrice")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("OpexLccQuantity")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<bool?>("OpexLccShowInGraph")
                        .HasColumnType("bit");

                    b.Property<decimal?>("OpexLccUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.ToTable("LccOpexCalc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccOutdated", b =>
                {
                    b.Property<DateTime?>("LccDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int>("LccId")
                        .HasColumnType("int")
                        .HasColumnName("LccID");

                    b.Property<int?>("LccRamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsDiagramID");

                    b.Property<int?>("LccRamsId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsID");

                    b.Property<DateTime?>("LccdateCalculated")
                        .HasColumnType("smalldatetime")
                        .HasColumnName("LCCDateCalculated");

                    b.Property<DateTime?>("MrbLastModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("MrbRiskObject")
                        .HasColumnType("int");

                    b.Property<int?>("RamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramID");

                    b.Property<DateTime?>("RamsLastModified")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TaskDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.ToTable("LccOutdated");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccOutdatedOnRisk", b =>
                {
                    b.Property<int>("LccId")
                        .HasColumnType("int")
                        .HasColumnName("LccID");

                    b.Property<DateTime?>("LccdateCalculated")
                        .HasColumnType("smalldatetime")
                        .HasColumnName("LCCDateCalculated");

                    b.Property<DateTime?>("MrbLastModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int>("MrbRiskObject")
                        .HasColumnType("int");

                    b.ToTable("LccOutdatedOnRisk");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccRamsCalc", b =>
                {
                    b.Property<string>("FmecaMatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("FmecaVersion")
                        .HasColumnType("int");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("RamsClassDc")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasColumnName("RamsClassDC");

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<double?>("RamsDcd")
                        .HasColumnType("float")
                        .HasColumnName("RamsDCd");

                    b.Property<string>("RamsDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("RamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramID");

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramRefID");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("RamsFunctionalDemand")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit");

                    b.Property<string>("RamsInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("RamsLccusePfd")
                        .HasColumnType("bit")
                        .HasColumnName("RamsLCCUsePFD");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int");

                    b.Property<string>("RamsModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn");

                    b.Property<double?>("RamsMttr")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTTR");

                    b.Property<string>("RamsName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsObjectID");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int");

                    b.Property<double?>("RamsPfd")
                        .HasColumnType("float")
                        .HasColumnName("RamsPFD");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int");

                    b.Property<string>("RamsRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsRiskId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskID");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskObjectID");

                    b.Property<int?>("RamsSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsSiID");

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsWantLCC");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int");

                    b.Property<int?>("RamsXposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsXPosition");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int");

                    b.Property<int?>("RamsYposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsYPosition");

                    b.Property<int?>("RiskObjNoOfInstallation")
                        .HasColumnType("int");

                    b.Property<decimal?>("ValueObject")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("ValueRiskObject")
                        .HasColumnType("decimal(18, 4)");

                    b.ToTable("LccRamsCalc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccSiCalc", b =>
                {
                    b.Property<string>("FailMode")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int>("FailRateId")
                        .HasColumnType("int")
                        .HasColumnName("FailRateID");

                    b.Property<string>("FmecaMatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("FmecaVersion")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbActionCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbCapCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbDirectCostAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbDirectCostBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbDownTimeAfter")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("MrbDownTimeBefore")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("MrbFailureCategorie1")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("MrbFailureCategorie2")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("MrbFailureMode")
                        .HasColumnType("int");

                    b.Property<string>("MrbFmecaSelect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("MrbFmecaVersion")
                        .HasColumnType("int");

                    b.Property<int>("MrbId")
                        .HasColumnType("int")
                        .HasColumnName("MrbID");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("MrbOptimalCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("MrbRiskObject")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbSpareCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbSpareManageCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("PckSiSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiSiID");

                    b.ToTable("LccSiCalc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lccdetail", b =>
                {
                    b.Property<int>("LccDetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LccDetID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LccDetId"));

                    b.Property<decimal?>("LccDetAECPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetActionCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetActionCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetAec")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("LccDetAEC");

                    b.Property<decimal?>("LccDetAvailabilityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccDetAvailabilityTechnicalPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetAverageAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetAverageBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetAverageOptimalCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetAverageOptimalCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetAveragePmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetAverageRealCorrectiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetAverageRealCorrectiveCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetAverageRealPreventiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetAverageRealPreventiveCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetCapexCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetCapexCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetCorrectiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetCorrectiveCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetDepreciation")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("LccDetDirectCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetDirectCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetDtCostTasks")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetEcoTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<bool?>("LccDetExcluded")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LccDetFailureRate")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccDetFailureRatePmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetFmecaAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaCustomAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaCustomBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaCustomPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetFmecaCustomRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaCustomRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetFmecaCustomRiskPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetFmecaPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("LccDetLccId")
                        .HasColumnType("int")
                        .HasColumnName("LccDetLccID");

                    b.Property<decimal?>("LccDetMaintenanceCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetMaintenanceCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetModificationCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetModificationCostPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetOpexCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetOpexCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetOptimalCorrectiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetOptimalCorrectiveCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetOptimalPreventiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetOptimalPreventiveCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetPreventiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetPreventiveCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetProcedureCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetProcedureCostPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetProductivityTechnical")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("LccDetRealCorrectiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRealCorrectiveCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetRealOpexCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRealOpexCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetRealPreventiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRealPreventiveCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetRealTotalCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRealTotalCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetReliability")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("LccDetReliabilityPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetRiskPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetSpareCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetSpareCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetTaskCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetTaskCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetTotalCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetTotalCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetTotalNpv")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("LccDetTotalNPV");

                    b.Property<decimal?>("LccDetUtilizationTechnichal")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<int?>("LccDetYear")
                        .HasColumnType("int");

                    b.HasKey("LccDetId");

                    b.HasIndex("LccDetLccId");

                    b.ToTable("TblLCCDetail");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LcceffectDetail", b =>
                {
                    b.Property<int>("LccEfctId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LccEfctID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LccEfctId"));

                    b.Property<decimal?>("LccEfctActionCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctActionCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccEfctCorrectiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctCorrectiveCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccEfctCustomAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctCustomBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctCustomPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccEfctCustomRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctCustomRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctCustomRiskPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccEfctDirectCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctDirectCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("LccEfctEffectColumn")
                        .HasColumnType("int");

                    b.Property<string>("LccEfctEffectName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("LccEfctFmecaAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctFmecaBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctFmecaPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("LccEfctLccDetailId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctLccDetailID");

                    b.Property<int?>("LccEfctLccId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctLccID");

                    b.Property<decimal?>("LccEfctOptimalCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctOptimalCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccEfctPreventiveCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctPreventiveCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("LccEfctRamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctRamsDiagramID");

                    b.Property<int?>("LccEfctRamsId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctRamsID");

                    b.Property<decimal?>("LccEfctRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("LccEfctRiskId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctRiskID");

                    b.Property<decimal?>("LccEfctRiskPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccEfctSparePartCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctSparePartCostPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccEfctTaskFmeca")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctTaskFmecaCustom")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccEfctTaskFmecaCustomPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccEfctTaskFmecaPmo")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("LccEfctTaskId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctTaskID");

                    b.Property<int?>("LccEfctType")
                        .HasColumnType("int");

                    b.Property<int?>("LccEfctYear")
                        .HasColumnType("int");

                    b.HasKey("LccEfctId");

                    b.HasIndex("LccEfctLccDetailId");

                    b.HasIndex("LccEfctLccId");

                    b.HasIndex("LccEfctRamsDiagramId");

                    b.HasIndex("LccEfctRamsId");

                    b.HasIndex("LccEfctRiskId");

                    b.HasIndex("LccEfctTaskId");

                    b.ToTable("TblLccEffectDetail");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lookup", b =>
                {
                    b.Property<int>("LookupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LookupID")
                        .HasComment("Unique ID (PK for Lookup)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LookupId"));

                    b.Property<string>("LookupFilter")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Domain name, each unique name defines a different lookup category");

                    b.Property<string>("LookupLongDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Long description");

                    b.Property<string>("LookupShortDescription")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Short description");

                    b.Property<int?>("LookupValue")
                        .HasColumnType("int")
                        .HasComment("Value that distinguishes lookup domain values, must be unique within the same category");

                    b.HasKey("LookupId");

                    b.ToTable("Lookup", t =>
                        {
                            t.HasComment("Master data table that stores domains, that are used throughout the program. Domains are grouped by have the same name for field LookupFilter. A set of values can be created for each domain. Not editable by user.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupAdditionalData", b =>
                {
                    b.Property<int>("AdditionalDataId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AdditionalDataId"));

                    b.Property<string>("AdditionalDataDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AdditionalDataName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("AdditionalDataId");

                    b.ToTable("LookupAdditionalData");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupExecutor", b =>
                {
                    b.Property<int>("ExecutorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ExecutorID")
                        .HasComment("Unique ID (PK for LookupExecutor)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ExecutorId"));

                    b.Property<string>("ExecutorDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the task executor (Which clarifies the name where needed)");

                    b.Property<string>("ExecutorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the task executor");

                    b.HasKey("ExecutorId");

                    b.ToTable("LookupExecutor", t =>
                        {
                            t.HasComment("Master data table that contains all currently defined task executors. A task executor is the person or department that handles execution of a task.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupFailCat", b =>
                {
                    b.Property<int>("FailCatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("FailCatID")
                        .HasComment("Unique ID (PK for LookupFailCat)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FailCatId"));

                    b.Property<string>("FailCatDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Failure category description");

                    b.Property<string>("FailCatGroup")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("The group the failure category belongs to (Group domain is defined in table Lookup)");

                    b.Property<string>("FailCatName")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Failure category name");

                    b.HasKey("FailCatId");

                    b.ToTable("LookupFailCat", t =>
                        {
                            t.HasComment("Master data table that contains all defined failure categories. Each failure category defines a different failure behavior. User is allowed to change failure category names and descriptions.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupFailMode", b =>
                {
                    b.Property<int>("FailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("FailID")
                        .HasComment("Unique ID (PK for LookupFailMode)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FailId"));

                    b.Property<DateTime?>("FailDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("FailDescription")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)")
                        .HasComment("Description of the failure mode");

                    b.Property<int?>("FailDistributionId")
                        .HasColumnType("int")
                        .HasColumnName("FailDistributionID")
                        .HasComment("Distribution type determines what method of calculation will be used to calculate the costs associated with each failure mode. Distribution type values are defined in the Lookup table.");

                    b.Property<int?>("FailIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("Defines the position in the Weibull curve (when combined with Weibull location)");

                    b.Property<string>("FailMode")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Name of the failure mode");

                    b.Property<string>("FailModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<int>("FailRateId")
                        .HasColumnType("int")
                        .HasColumnName("FailRateID")
                        .HasComment("Failure rate. This is the frequency at which a system or component fails. Possible values are defined in Lookup. Defaults to constant.");

                    b.Property<int?>("FailRiskTypeId")
                        .HasColumnType("int")
                        .HasColumnName("FailRiskTypeID")
                        .HasComment("Describes if the failure shows itself, or is invisible. Possible values are defined in Lookup.");

                    b.Property<decimal?>("FailShape")
                        .HasColumnType("decimal(10, 2)")
                        .HasComment("Fail shape defines the slope of the current location on the Weibull curve");

                    b.Property<decimal?>("FailWeibullLocation")
                        .HasColumnType("decimal(10, 2)")
                        .HasComment("Defines the location on the Weibull curve (starting point)");

                    b.HasKey("FailId");

                    b.ToTable("LookupFailMode", t =>
                        {
                            t.HasComment("Master data table that defines failure modes. A failure mode describes a possible cause for the failure, if the failure shows itself, and what the frequency of the failure is.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupGridColumn", b =>
                {
                    b.Property<string>("ControlName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FieldName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ColumnName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ColumnHeader")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("ColumnWidth")
                        .HasColumnType("int");

                    b.Property<int>("DisplayIndex")
                        .HasColumnType("int");

                    b.Property<int>("FieldType")
                        .HasColumnType("int");

                    b.Property<bool>("Visible")
                        .HasColumnType("bit");

                    b.HasKey("ControlName", "FieldName", "ColumnName");

                    b.ToTable("LookupGridColumn");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupInflationGroup", b =>
                {
                    b.Property<int>("InflId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("InflID")
                        .HasComment("Unique ID (PK of LookupInflationGroup)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("InflId"));

                    b.Property<DateTime?>("InflDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("InflModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Last modification of this record was made by this user");

                    b.Property<string>("InflName")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Inflation group name. Describes what the inflation percentage belongs to.");

                    b.Property<decimal?>("InflPercentage")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Inflation percentage for this specific item.");

                    b.HasKey("InflId");

                    b.ToTable("LookupInflationGroup", t =>
                        {
                            t.HasComment("Master data table that contains inflation groups. The inflation groups allow us to connect a specific inflation percentage to a variety of things.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupInitiator", b =>
                {
                    b.Property<int>("InitiatorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("InitiatorID")
                        .HasComment("Unique ID (PK of LookupInitiator)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("InitiatorId"));

                    b.Property<string>("InitiatorDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the initiator");

                    b.Property<string>("InitiatorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the initiator");

                    b.HasKey("InitiatorId");

                    b.ToTable("LookupInitiator", t =>
                        {
                            t.HasComment("Master data table that defines who (or what) will initiate a certain action. Is used for clusters and preventive actions. Editable by user.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupIntervalUnit", b =>
                {
                    b.Property<int>("IntUnitId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("IntUnitID")
                        .HasComment("Unique ID (PK of LookupIntervalUnit)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IntUnitId"));

                    b.Property<decimal?>("IntUnitCalculationKey")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Number of times an interval unit occurs in a year");

                    b.Property<DateTime?>("IntUnitDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("IntUnitDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the interval unit");

                    b.Property<string>("IntUnitModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<string>("IntUnitName")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Name of the interval unit");

                    b.Property<string>("IntUnitShortKey")
                        .IsRequired()
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("Short code for the interval unit (usually consists of the first letter of the name)");

                    b.HasKey("IntUnitId");

                    b.ToTable("LookupIntervalUnit", t =>
                        {
                            t.HasComment("Master data table that holds values that are used in interval calculations. Editable by user.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupMxPolicy", b =>
                {
                    b.Property<int>("PolId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PolID")
                        .HasComment(" (PK of LookupMxPolicy)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PolId"));

                    b.Property<string>("PolDescription")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Provides a more detailed description of the maintenance policy");

                    b.Property<string>("PolEamPolicy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PolName")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)")
                        .HasComment("Name that describes the maintenance policy");

                    b.HasKey("PolId");

                    b.ToTable("LookupMxPolicy", t =>
                        {
                            t.HasComment("Master data table that defines different maintenance policies. A maintenance policy can be described as the reason why maintenance should occur. (?)");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupSettings", b =>
                {
                    b.Property<string>("AmsProperty")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique ID and the name of the setting. (PK of LookupSettings)");

                    b.Property<DateTime?>("AmsDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("AmsDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<decimal?>("AmsDecimalValue")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Decimal value of the setting");

                    b.Property<string>("AmsInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Created by user");

                    b.Property<int?>("AmsIntValue")
                        .HasColumnType("int")
                        .HasComment("Integer value of the setting");

                    b.Property<bool?>("AmsModifiable")
                        .HasColumnType("bit")
                        .HasComment("Boolean value that states if the setting is modifiable");

                    b.Property<string>("AmsModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<string>("AmsTextValue")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("The text value containing the values of the LookupSetting. Can be either null, a pipe ('|') delimited string, or XML.");

                    b.HasKey("AmsProperty");

                    b.ToTable("LookupSettings", t =>
                        {
                            t.HasComment("Master data table that defines settings for a variety of things within the Amprover software. (like grid column settings and program constants) Not editable by user.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupUserDefined", b =>
                {
                    b.Property<int>("UserDefinedId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("UserDefinedID")
                        .HasComment("Unique ID (PK of LookupUserDefined)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserDefinedId"));

                    b.Property<DateTime?>("UserDefinedDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("UserDefinedFilter")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Filter that distinguishes user defined lookup categories (items of the same domain have the same name in this column)");

                    b.Property<string>("UserDefinedLongDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Long description");

                    b.Property<string>("UserDefinedModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<string>("UserDefinedShortDescription")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Short description");

                    b.Property<int?>("UserDefinedValue")
                        .HasColumnType("int")
                        .HasComment("Value for the user defined domain item");

                    b.HasKey("UserDefinedId");

                    b.HasIndex(new[] { "UserDefinedFilter", "UserDefinedValue" }, "IX_LookupUserDefined")
                        .IsUnique()
                        .HasFilter("[UserDefinedFilter] IS NOT NULL AND [UserDefinedValue] IS NOT NULL");

                    b.ToTable("LookupUserDefined", t =>
                        {
                            t.HasComment("Master data table that stores user defined domains.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mca", b =>
                {
                    b.Property<int>("McaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("McaID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("McaId"));

                    b.Property<int?>("McaCommercial")
                        .HasColumnType("int");

                    b.Property<int?>("McaIntensity")
                        .HasColumnType("int");

                    b.Property<int?>("McaQualityLevel")
                        .HasColumnType("int");

                    b.Property<int?>("McaQualityScore")
                        .HasColumnType("int");

                    b.Property<int?>("McaRepresentative")
                        .HasColumnType("int");

                    b.Property<int>("McaSiId")
                        .HasColumnType("int")
                        .HasColumnName("McaSiID");

                    b.Property<int?>("McaSocial")
                        .HasColumnType("int");

                    b.Property<int?>("McaUsage")
                        .HasColumnType("int");

                    b.Property<int?>("McaUtilization")
                        .HasColumnType("int");

                    b.HasKey("McaId");

                    b.HasIndex("McaSiId");

                    b.ToTable("TblMca");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mrb", b =>
                {
                    b.Property<int>("Mrbid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MRBId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Mrbid"));

                    b.Property<decimal?>("MrbActionCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("MrbAdditionalDataId")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbAec")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("MrbCapCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("MrbChildObject")
                        .HasColumnType("int");

                    b.Property<int?>("MrbChildObject1")
                        .HasColumnType("int");

                    b.Property<int?>("MrbChildObject2")
                        .HasColumnType("int");

                    b.Property<int?>("MrbChildObject3")
                        .HasColumnType("int");

                    b.Property<int?>("MrbChildObject4")
                        .HasColumnType("int");

                    b.Property<int?>("MrbChildType")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbCurrentActionCosts")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbCustomEffectAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbCustomEffectBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<DateTime?>("MrbDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("MrbDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("MrbDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("MrbDirectCostAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbDirectCostBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbDirectCostPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbDownTimeAfter")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("MrbDownTimeBefore")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("MrbExclOptCb")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbFailureCategorie1")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("MrbFailureCategorie2")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("MrbFailureCause")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("MrbFailureConsequences")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("MrbFailureMode")
                        .HasColumnType("int");

                    b.Property<int?>("MrbFmecaMtbfAfter")
                        .HasColumnType("int");

                    b.Property<int?>("MrbFmecaMtbfBefore")
                        .HasColumnType("int");

                    b.Property<int?>("MrbFmecaMtbfPmo")
                        .HasColumnType("int");

                    b.Property<string>("MrbFmecaSelect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("MrbFmecaSelectionAfter")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("MrbFmecaSelectionBefore")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("MrbFmecaSelectionPmo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("MrbFmecaVersion")
                        .HasColumnType("int");

                    b.Property<string>("MrbFunction")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("MrbImportId")
                        .HasColumnType("int");

                    b.Property<string>("MrbInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbMarketAfter")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbMarketBefore")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbMarketPmo")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("MrbMasterId")
                        .HasColumnType("int")
                        .HasColumnName("MrbMasterID");

                    b.Property<string>("MrbModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<decimal?>("MrbMtbfPmo")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbNorm")
                        .HasMaxLength(150)
                        .IsUnicode(false)
                        .HasColumnType("varchar(150)");

                    b.Property<string>("MrbOpsProcedure")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("MrbOptimalCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("MrbRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("MrbRemarks1")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("MrbResponsible")
                        .HasMaxLength(150)
                        .IsUnicode(false)
                        .HasColumnType("varchar(150)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("MrbRiskObject")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbRiskPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("MrbSafetyAfter")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbSafetyBefore")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbSafetyPmo")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("MrbSortOrder")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbSpareCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbSpareManageCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbSpareManageCostPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("MrbState")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)");

                    b.Property<int?>("MrbStatus")
                        .HasColumnType("int");

                    b.HasKey("Mrbid");

                    b.HasIndex("MrbAdditionalDataId");

                    b.HasIndex("MrbChildObject");

                    b.HasIndex("MrbChildObject1");

                    b.HasIndex("MrbChildObject2");

                    b.HasIndex("MrbChildObject3");

                    b.HasIndex("MrbChildObject4");

                    b.HasIndex("MrbFailureMode");

                    b.HasIndex("MrbMasterId");

                    b.HasIndex("MrbRiskObject");

                    b.HasIndex("MrbStatus");

                    b.ToTable("TblMRB");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.MrbImage", b =>
                {
                    b.Property<int>("MrbImageId")
                        .HasColumnType("int")
                        .HasColumnName("MrbImageID");

                    b.Property<DateTime>("MrbImageDate")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime");

                    b.Property<string>("MrbImageImagePathAfter")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbImageImagePathBefore")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbImageImagePathPmo")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("MrbImageId");

                    b.ToTable("TblMrbImage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.MrbTaskCount", b =>
                {
                    b.Property<int?>("NumberOfTasks")
                        .HasColumnType("int");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.ToTable("MrbTaskCount");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.MrbTree", b =>
                {
                    b.Property<int?>("ChildObject1Id")
                        .HasColumnType("int")
                        .HasColumnName("ChildObject1ID");

                    b.Property<string>("ChildObject1Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ChildObject2Id")
                        .HasColumnType("int")
                        .HasColumnName("ChildObject2ID");

                    b.Property<string>("ChildObject2Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ChildObject3Id")
                        .HasColumnType("int")
                        .HasColumnName("ChildObject3ID");

                    b.Property<string>("ChildObject3Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ChildObject4Id")
                        .HasColumnType("int")
                        .HasColumnName("ChildObject4ID");

                    b.Property<string>("ChildObject4Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ChildObjectId")
                        .HasColumnType("int")
                        .HasColumnName("ChildObjectID");

                    b.Property<string>("ChildObjectName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("MrbId")
                        .HasColumnType("int")
                        .HasColumnName("MrbID");

                    b.Property<string>("MrbName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RiskObjId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjID");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("ScenarioID");

                    b.Property<string>("ScenarioName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.ToTable("MrbTree");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.NumberOfSiItems", b =>
                {
                    b.Property<int?>("Items")
                        .HasColumnType("int");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int");

                    b.Property<int?>("SiPartOf")
                        .HasColumnType("int");

                    b.Property<string>("UserDefinedShortDescription")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.ToTable("NumberOfSiItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Object", b =>
                {
                    b.Property<int>("ObjId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ObjID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ObjId"));

                    b.Property<int?>("ObjAvailableTime")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ObjDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("ObjDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("ObjFilterRef")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ObjFunction")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<byte[]>("ObjImage")
                        .HasColumnType("image");

                    b.Property<int>("ObjLevel")
                        .HasColumnType("int");

                    b.Property<string>("ObjModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("ObjNewValue")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<int?>("ObjProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("ObjShortKey")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ObjSiCategory")
                        .HasColumnType("int");

                    b.Property<string>("ObjSiType")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ObjUsableTime")
                        .HasColumnType("int");

                    b.Property<int?>("ObjUtilizationTime")
                        .HasColumnType("int");

                    b.HasKey("ObjId");

                    b.ToTable("TblObject");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexData", b =>
                {
                    b.Property<int>("OpexDataId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("OpexDataID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpexDataId"));

                    b.Property<int?>("OpexDataCostType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("OpexDataDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("OpexDataInflationGroupId")
                        .HasColumnType("int")
                        .HasColumnName("OpexDataInflationGroupID");

                    b.Property<int?>("OpexDataMethod")
                        .HasColumnType("int");

                    b.Property<string>("OpexDataModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("OpexDataName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("OpexDataPercentage")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("OpexDataValue")
                        .HasColumnType("decimal(18, 2)");

                    b.HasKey("OpexDataId");

                    b.ToTable("TblOpexData");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexFactor", b =>
                {
                    b.Property<int>("OpexFactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("OpexFactID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpexFactId"));

                    b.Property<DateTime?>("OpexFactDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<decimal?>("OpexFactLookupValue")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("OpexFactModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("OpexFactOpexDataId")
                        .HasColumnType("int")
                        .HasColumnName("OpexFactOpexDataID");

                    b.Property<decimal?>("OpexFactValue")
                        .HasColumnType("decimal(18, 2)");

                    b.HasKey("OpexFactId");

                    b.HasIndex("OpexFactOpexDataId");

                    b.ToTable("TblOpexFactor");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexLccDetails", b =>
                {
                    b.Property<int?>("OpexLccDetOpexLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccDetOpexLccID");

                    b.Property<int?>("OpexLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccID");

                    b.ToTable("OpexLccDetails");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexToLcc", b =>
                {
                    b.Property<int>("OpexLccId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("OpexLccID")
                        .HasComment("Unique ID (PK to OpexToLCC)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpexLccId"));

                    b.Property<string>("OpexLccColorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the line color used for the 'opex to lcc' graph");

                    b.Property<int?>("OpexLccCycle")
                        .HasColumnType("int");

                    b.Property<int?>("OpexLccLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccLccID")
                        .HasComment("Lcc ID the 'opex to lcc' belongs to (FK to Lcc)");

                    b.Property<string>("OpexLccName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the 'opex to lcc'");

                    b.Property<int?>("OpexLccOpexDataId1")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID1")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<int?>("OpexLccOpexDataId2")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID2")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<int?>("OpexLccOpexDataId3")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID3")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<decimal?>("OpexLccPrice")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Price for the 'opex to lcc' (price per unit/quantity)");

                    b.Property<decimal?>("OpexLccQuantity")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Physical quantity applicable for the 'opex to lcc'");

                    b.Property<int?>("OpexLccSequence")
                        .HasColumnType("int")
                        .HasComment("? Sequence used to keep the order of Opex to LCC items within the grid that is used to display them.");

                    b.Property<bool?>("OpexLccShowInGraph")
                        .HasColumnType("bit")
                        .HasComment("Boolean to show the 'opex to lcc' in the graph (shows up in graph when true)");

                    b.Property<int?>("OpexLccSumItem")
                        .HasColumnType("int")
                        .HasComment("?");

                    b.Property<decimal?>("OpexLccUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Number of units applicable for the 'opex to lcc'");

                    b.HasKey("OpexLccId");

                    b.HasIndex("OpexLccOpexDataId1");

                    b.HasIndex("OpexLccOpexDataId2");

                    b.HasIndex("OpexLccOpexDataId3");

                    b.ToTable("TblOpexToLCC", t =>
                        {
                            t.HasComment("Contains operating expenditure (opex) data that is used by life cycle costs (LCC) calculation. These opex items can be found and defined from the LCC module, in the Opex tab (On the bottom half of the screen).");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexToLccDetail", b =>
                {
                    b.Property<int>("OpexLccDetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("OpexLccDetID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpexLccDetId"));

                    b.Property<decimal?>("OpexLccDetAec")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("OpexLccDetAEC");

                    b.Property<decimal?>("OpexLccDetCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("OpexLccDetLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccDetLccID");

                    b.Property<decimal?>("OpexLccDetNpv")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("OpexLccDetNPV");

                    b.Property<int?>("OpexLccDetOpexLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccDetOpexLccID");

                    b.Property<decimal?>("OpexLccDetPv")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("OpexLccDetPV");

                    b.Property<int?>("OpexLccDetYear")
                        .HasColumnType("int");

                    b.HasKey("OpexLccDetId");

                    b.ToTable("TblOpexToLccDetail");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PageNavigation", b =>
                {
                    b.Property<int>("PageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PageId"));

                    b.Property<string>("PagePath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageQuery")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("PageId");

                    b.ToTable("TblPageNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PickSi", b =>
                {
                    b.Property<int>("PckSiId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PckSiID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PckSiId"));

                    b.Property<int?>("PckSiLinkFilterId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiLinkFilterID");

                    b.Property<int>("PckSiMrbId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiMrbID");

                    b.Property<int>("PckSiSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiSiID");

                    b.HasKey("PckSiId");

                    b.HasIndex("PckSiLinkFilterId");

                    b.HasIndex("PckSiMrbId");

                    b.HasIndex("PckSiSiId");

                    b.ToTable("TblPickSI");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PickSiFromCommonTask", b =>
                {
                    b.Property<int?>("CmnTaskSiCategory")
                        .HasColumnType("int");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ObjSiCategory")
                        .HasColumnType("int");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int");

                    b.Property<int>("SiId")
                        .HasColumnType("int")
                        .HasColumnName("SiID");

                    b.Property<string>("SiName")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<string>("SiType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int>("TskId")
                        .HasColumnType("int")
                        .HasColumnName("TskID");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.ToTable("PickSiFromCommonTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PrioTaskCollection", b =>
                {
                    b.Property<decimal?>("CltpClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("CltpClusterCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("CltpClusterId")
                        .HasColumnType("int")
                        .HasColumnName("CltpClusterID");

                    b.Property<DateTime?>("CltpDateExecuted")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CltpDateGenerated")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("CltpDownTime")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("CltpDuration")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("CltpExecuteStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CltpExecutionDate")
                        .HasColumnType("datetime");

                    b.Property<int>("CltpId")
                        .HasColumnType("int")
                        .HasColumnName("CltpID");

                    b.Property<bool?>("CltpInterruptable")
                        .HasColumnType("bit");

                    b.Property<int?>("CltpObjectId")
                        .HasColumnType("int")
                        .HasColumnName("CltpObjectID");

                    b.Property<int?>("CltpPriority")
                        .HasColumnType("int");

                    b.Property<int?>("CltpQualityScore")
                        .HasColumnType("int");

                    b.Property<string>("CltpReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("CltpReferenceID");

                    b.Property<string>("CltpRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("CltpRiskId")
                        .HasColumnType("int")
                        .HasColumnName("CltpRiskID");

                    b.Property<int?>("CltpSequence")
                        .HasColumnType("int");

                    b.Property<int?>("CltpShiftEndDate")
                        .HasColumnType("int");

                    b.Property<int?>("CltpShiftStartDate")
                        .HasColumnType("int");

                    b.Property<int?>("CltpSiId")
                        .HasColumnType("int")
                        .HasColumnName("CltpSiID");

                    b.Property<decimal?>("CltpSiUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("CltpSlack")
                        .HasColumnType("int");

                    b.Property<int?>("CltpSlackIntervalType")
                        .HasColumnType("int");

                    b.Property<int?>("CltpTaskId")
                        .HasColumnType("int")
                        .HasColumnName("CltpTaskID");

                    b.Property<decimal?>("CltpToolCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<bool?>("CltpUseLastDateExecuted")
                        .HasColumnType("bit");

                    b.Property<decimal?>("IntUnitCalculationKey")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("SiCategory")
                        .HasColumnType("int");

                    b.Property<string>("SiName")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("SiPartOf")
                        .HasColumnType("int");

                    b.Property<int?>("SiQualityScore")
                        .HasColumnType("int");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnType("int")
                        .HasColumnName("TskCommonActionID");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<string>("TskName")
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<string>("TskType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.ToTable("PrioTaskCollection");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Priority", b =>
                {
                    b.Property<int>("PrioId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PrioID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PrioId"));

                    b.Property<bool?>("PrioAutoGenerated")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("PrioDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("PrioDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("PrioInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PrioModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PrioName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioPartOf")
                        .HasColumnType("int");

                    b.Property<string>("PrioPath")
                        .HasMaxLength(250)
                        .IsUnicode(false)
                        .HasColumnType("varchar(250)");

                    b.Property<string>("PrioRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("PrioResponsible")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("PrioShortKey")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)");

                    b.Property<int?>("PrioSiCategory")
                        .HasColumnType("int");

                    b.Property<int?>("PrioSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioSiID");

                    b.Property<string>("PrioStatus")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.HasKey("PrioId");

                    b.HasIndex("PrioPartOf");

                    b.ToTable("TblPriority");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityBudget", b =>
                {
                    b.Property<int>("PrioBudId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PrioBudID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PrioBudId"));

                    b.Property<decimal?>("PrioBudAccepted")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudget")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear1")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear2")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear3")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear4")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear5")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetSumOfParts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudCostSelected")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("PrioBudDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioBudDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<bool?>("PrioBudEnableTaskSelect")
                        .HasColumnType("bit");

                    b.Property<string>("PrioBudInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PrioBudModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioBudOriginalId")
                        .HasColumnType("int")
                        .HasColumnName("PrioBudOriginalID");

                    b.Property<decimal?>("PrioBudPostponed")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioBudPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioBudPriorityID");

                    b.Property<decimal?>("PrioBudProposed")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudRejected")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("PrioBudRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("PrioBudRisk")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudRiskDelta")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("PrioBudStatus")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<bool?>("PrioBudTaskSelectDirty")
                        .HasColumnType("bit");

                    b.Property<int?>("PrioBudVersion")
                        .HasColumnType("int");

                    b.HasKey("PrioBudId");

                    b.HasIndex("PrioBudOriginalId");

                    b.HasIndex("PrioBudPriorityId");

                    b.HasIndex("PrioBudVersion");

                    b.ToTable("TblPriorityBudget");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityCost", b =>
                {
                    b.Property<int>("PrioCstVersion")
                        .HasColumnType("int");

                    b.Property<int>("PrioCstId")
                        .HasColumnType("int")
                        .HasColumnName("PrioCstID");

                    b.Property<decimal>("PrioCstCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioCstDirectCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioCstFailRate")
                        .HasColumnType("decimal(18, 6)");

                    b.Property<int>("PrioCstNumberOfTasks")
                        .HasColumnType("int");

                    b.Property<int>("PrioCstPrioId")
                        .HasColumnType("int")
                        .HasColumnName("PrioCstPrioID");

                    b.Property<int?>("PrioCstPrioTskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioCstPrioTskID");

                    b.Property<decimal?>("PrioCstRiskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioCstRiskDelta")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("PrioCstYear")
                        .HasColumnType("int");

                    b.HasKey("PrioCstVersion", "PrioCstId");

                    b.HasIndex("PrioCstPrioId");

                    b.HasIndex("PrioCstPrioTskId");

                    b.ToTable("TblPriorityCost");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityGridData", b =>
                {
                    b.Property<decimal?>("PrioBudAccepted")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudget")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear1")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear2")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear3")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear4")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear5")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudBudgetSumOfParts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudCostSelected")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("PrioBudDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioBudDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<bool?>("PrioBudEnableTaskSelect")
                        .HasColumnType("bit");

                    b.Property<int>("PrioBudId")
                        .HasColumnType("int")
                        .HasColumnName("PrioBudID");

                    b.Property<string>("PrioBudInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PrioBudModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioBudOriginalId")
                        .HasColumnType("int")
                        .HasColumnName("PrioBudOriginalID");

                    b.Property<decimal?>("PrioBudPostponed")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioBudPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioBudPriorityID");

                    b.Property<decimal?>("PrioBudProposed")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioBudRejected")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("PrioBudRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("PrioBudStatus")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<bool?>("PrioBudTaskSelectDirty")
                        .HasColumnType("bit");

                    b.Property<int?>("PrioBudVersion")
                        .HasColumnType("int");

                    b.Property<int>("PrioId")
                        .HasColumnType("int")
                        .HasColumnName("PrioID");

                    b.Property<string>("PrioName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioPartOf")
                        .HasColumnType("int");

                    b.Property<int?>("PrioSiCategory")
                        .HasColumnType("int");

                    b.Property<int?>("PrioSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioSiID");

                    b.Property<string>("PrioStatus")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.ToTable("PriorityGridData");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityTask", b =>
                {
                    b.Property<int>("PrioTskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskID");

                    b.Property<bool?>("PrioTskAutoSelected")
                        .HasColumnType("bit");

                    b.Property<decimal?>("PrioTskBudgetCostYear1")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear2")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear3")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear4")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear5")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskClusterPlanId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskClusterPlanID");

                    b.Property<int?>("PrioTskCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskCommonTaskID");

                    b.Property<decimal?>("PrioTskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("PrioTskDateDue")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioTskDateExecuted")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioTskDateGenerated")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("PrioTskDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("PrioTskDirectCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskDownTime")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskDuration")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskExecuteStatus")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskExecutionYear")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskFromReference")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("PrioTskImported")
                        .HasColumnType("bit");

                    b.Property<decimal?>("PrioTskIntervalPerYear")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("PrioTskNumberOfTimes")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<int?>("PrioTskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskObjectID");

                    b.Property<int?>("PrioTskOriginalId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskOriginalID");

                    b.Property<decimal?>("PrioTskPostponePct")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskPriorityCode")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskPriorityID");

                    b.Property<int?>("PrioTskQualityScore")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("PrioTskReferenceID");

                    b.Property<string>("PrioTskRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("PrioTskRiskBudget")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskRiskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskRiskDelta")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskRiskFactor")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<int?>("PrioTskRiskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskRiskID");

                    b.Property<bool?>("PrioTskSealed")
                        .HasColumnType("bit");

                    b.Property<int?>("PrioTskSelectionSeq")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskSequence")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskSiDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskSiID");

                    b.Property<decimal?>("PrioTskSiUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskSlack")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskSlackIntervalType")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskTaskID");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int");

                    b.HasKey("PrioTskId");

                    b.HasIndex("PrioTskSiId");

                    b.HasIndex("PrioTskTaskId");

                    b.HasIndex(new[] { "PrioTskPriorityId" }, "IX_PriorityTaskPriorityID");

                    b.HasIndex(new[] { "PrioTskVersion" }, "IX_PriorityTaskVersion");

                    b.ToTable("TblPriorityTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityTaskCost", b =>
                {
                    b.Property<int?>("PrioTskExecuteStatus")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskPriorityID");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int");

                    b.Property<decimal?>("RiskBudget")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("RiskCost")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("RiskDelta")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("TaskCost")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("Year1")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("Year2")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("Year3")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("Year4")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<decimal?>("Year5")
                        .HasColumnType("decimal(38, 2)");

                    b.ToTable("PriorityTaskCost");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityTaskGridData", b =>
                {
                    b.Property<int?>("FailRateId")
                        .HasColumnType("int")
                        .HasColumnName("FailRateID");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbCustomEffectAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbCustomEffectBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<string>("MrbName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("MrbRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("NumberOfTasks")
                        .HasColumnType("int");

                    b.Property<string>("PrioPath")
                        .HasMaxLength(250)
                        .IsUnicode(false)
                        .HasColumnType("varchar(250)");

                    b.Property<bool?>("PrioTskAutoSelected")
                        .HasColumnType("bit");

                    b.Property<decimal?>("PrioTskBudgetCostYear1")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear2")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear3")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear4")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear5")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskClusterPlanId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskClusterPlanID");

                    b.Property<int?>("PrioTskCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskCommonTaskID");

                    b.Property<decimal?>("PrioTskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("PrioTskDateDue")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioTskDateExecuted")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioTskDateGenerated")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("PrioTskDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("PrioTskDirectCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskDownTime")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskDuration")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskExecuteStatus")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskExecutionYear")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskFromReference")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("PrioTskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskID");

                    b.Property<bool?>("PrioTskImported")
                        .HasColumnType("bit");

                    b.Property<decimal?>("PrioTskIntervalPerYear")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("PrioTskNumberOfTimes")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<int?>("PrioTskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskObjectID");

                    b.Property<int?>("PrioTskOriginalId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskOriginalID");

                    b.Property<decimal?>("PrioTskPostponePct")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskPriorityCode")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskPriorityID");

                    b.Property<int?>("PrioTskQualityScore")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("PrioTskReferenceID");

                    b.Property<string>("PrioTskRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("PrioTskRiskBudget")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskRiskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskRiskDelta")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("PrioTskRiskFactor")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<int?>("PrioTskRiskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskRiskID");

                    b.Property<bool?>("PrioTskSealed")
                        .HasColumnType("bit");

                    b.Property<int?>("PrioTskSelectionSeq")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskSequence")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskSiDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskSiID");

                    b.Property<decimal?>("PrioTskSiUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("PrioTskSlack")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskSlackIntervalType")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskTaskID");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SiContractEnd")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("SiMtbf")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("SiQualityScore")
                        .HasColumnType("int");

                    b.Property<decimal?>("SiRiskAfterCustom")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("SiRiskAfterValue")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("SiRiskBeforeCustom")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("SiRiskBeforeValue")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<int?>("SiRiskBvId")
                        .HasColumnType("int")
                        .HasColumnName("SiRiskBvID");

                    b.Property<decimal?>("SiRiskMtbfAfter")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("SiRiskMtbfBefore")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<DateTime?>("SiYear")
                        .HasColumnType("datetime");

                    b.Property<string>("TskType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("TskUnitType")
                        .HasColumnType("int");

                    b.ToTable("PriorityTaskGridData");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityVersion", b =>
                {
                    b.Property<int>("PrioVerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PrioVerID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PrioVerId"));

                    b.Property<string>("PrioVerDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("PrioVerName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PrioVerRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("PrioVerScenario")
                        .HasColumnType("int");

                    b.Property<bool?>("PrioVerSealed")
                        .HasColumnType("bit");

                    b.Property<int?>("PrioVerVersion")
                        .HasColumnType("int");

                    b.HasKey("PrioVerId");

                    b.HasIndex("PrioVerScenario");

                    b.ToTable("TblPriorityVersion");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Rams", b =>
                {
                    b.Property<int>("RamsId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("RamsID")
                        .HasComment("Unique ID (PK of Rams)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RamsId"));

                    b.Property<double?>("RamsAvailabilityInput")
                        .HasColumnType("float")
                        .HasComment("Availability of the input of the RAMS block. The value lies between 0 and 1.");

                    b.Property<double?>("RamsAvailabilityOutput")
                        .HasColumnType("float")
                        .HasComment("Availability of the output of the RAMS block. The value lies between 0 and 1.");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float")
                        .HasComment("Weibull factor for this RAMS block. In reliability analysis, the weibull factor is used to clarify age-to-failure data. The weibull factor shows if the maintenance strategy should be better aimed at preventive or corrective maintenance. The value lies between 0 and 1.");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image")
                        .HasComment("Picture of the RAMS block. Was added to allow the user to store a picture of the physical structure the RAMS block represents. (Does not seem to be used in AMprover software) (!)");

                    b.Property<double?>("RamsBufferTime")
                        .HasColumnType("float");

                    b.Property<double?>("RamsCapacityFactor")
                        .HasColumnType("float");

                    b.Property<double?>("RamsCapacityFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsCapacityTechn")
                        .HasColumnType("float");

                    b.Property<int?>("RamsCapacityUnit")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsCharacteristicLife")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The characteristic life (Ƞ) is the moment where 63.2% of the units will fail");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("? Production and corrective costs dependent on the MTBF of this RAMS block. In RAMS containers, this cost will be determined by the sum of the underlying blocks.");

                    b.Property<string>("RamsClassDc")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasColumnName("RamsClassDC")
                        .HasComment("Diagnostic Coverage (DC) class of the RAMS block. This class determines the level of detection of dangerous situations, and thus describes if a danger to this RAMS block would be detected. (dangerous detected, dangers undetected, etc.)");

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit")
                        .HasComment("Boolean that collapses the RAMS blocks within the RAMS block to one block. This is used to keep track of the state of each of the RAMS blocks, so the user will see the same configuration when opening the same diagram.");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit")
                        .HasComment("Boolean that set the analysis of the block to complete. The user decides when a RAMS block is complete, and can set that using a checkbox.");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit")
                        .HasComment("Boolean that makes the RAMS block behave as a container. When creating a RAMS block with the \"add a new container\" toolstrip button, this boolean will be set.");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit")
                        .HasComment("Boolean that, when set, lets RAMS blocks retrieve their costs from linked FMECA object. Can only be set when the block is linked to FMECA, and the cost owner bit is set.");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit")
                        .HasComment("The RamsCostOwner is a bit that is set for containers that will then be the owner of the cost. When this is set, input of costs to blocks belonging to that container will be blocked.");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<double?>("RamsDcd")
                        .HasColumnType("float")
                        .HasColumnName("RamsDCd")
                        .HasComment("Diagnostic coverage of dangerous failures (DCD). This is a percentage for this RAMS block, that describes how many of the possibly dangerous failures that can occur would be detected. The value lies between 0 and 1. (?)");

                    b.Property<string>("RamsDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the RAMS block");

                    b.Property<int>("RamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramID")
                        .HasComment("ID of the RAMS diagram this RAMS block belongs to (FK to RamsDiagram)");

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramRefID")
                        .HasComment("When a rams diagram is linked to another rams diagram, that reference will be stored here. When this reference is present, the rams block will not allow the user to alter any of the values used in calculations.");

                    b.Property<int?>("RamsDistributionType")
                        .HasColumnType("int")
                        .HasComment("The distribution type specific to this RAMS block");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float")
                        .HasComment("Functional eco score of the RAMS block.  Eco score is a measurement of effect to the environment, and is measured with a score from 0 to 100. (closer to 100 means more environmentally friendly)");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float")
                        .HasComment("Technical eco score of the RAMS block. Eco score is a measurement of effect to the environment, and is measured with a score from 0 to 100. (closer to 100 means more environmentally friendly).");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Costs of the corrective measures taken after failure of this RAMS block.");

                    b.Property<int?>("RamsFailureMode")
                        .HasColumnType("int")
                        .HasComment("ID of the failure mode used for the RAMS block (FK to LookupFailMode)");

                    b.Property<string>("RamsFunctionalDemand")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("? (Does not seem to be used by the AMprover software) Functional demand is defined as a demand for specific functionality, such as accessiblity and fire safety.");

                    b.Property<int?>("RamsHft")
                        .HasColumnType("int")
                        .HasColumnName("RamsHFT");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit")
                        .HasComment("Boolean that allows the software to set the properties of all the blocks within the RAMS block to values identical to the first RAMS block. This user can set this during design.");

                    b.Property<string>("RamsInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Created by user with this username");

                    b.Property<double?>("RamsLabdaFunctional")
                        .HasColumnType("float");

                    b.Property<double?>("RamsLabdaTechnical")
                        .HasColumnType("float");

                    b.Property<bool>("RamsLccusePfd")
                        .HasColumnType("bit")
                        .HasColumnName("RamsLCCUsePFD");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int")
                        .HasComment("RAMS link method determines how the RAMS is linked to Mrb items. (Rams link methods are defined in Lookup, though it's the enum value that gets stored.) (?)");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int")
                        .HasComment("Linktype shows what the RAMS block is linked to (RAMS, RiskObject, Object, Risk, or Notset). This value determines how a RAMS block will be processed during LCC calculations.");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int")
                        .HasComment("ID of the RAMS block on the left side of the RAMS block (FK to Rams). Is set to -1 when there is no RAMS block to the left.");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int")
                        .HasComment("ID of the RAMS block on the right side of the RAMS block (FK to Rams). Is set to -1 when there is no RAMS block to the right.");

                    b.Property<string>("RamsModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<double?>("RamsMtbfFuncCalced")
                        .HasColumnType("float");

                    b.Property<double?>("RamsMtbfTecCalced")
                        .HasColumnType("float");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct")
                        .HasComment("Functional mean time between failure (MTBF) for the RAMS block");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn")
                        .HasComment("Technical mean time between failure (MTBF) for this RAMS block.");

                    b.Property<double?>("RamsMttr")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTTR")
                        .HasComment("Mean time to repair (MTTR) for this RAMS block. (The time it takes to repair the block when a failure occurs)");

                    b.Property<string>("RamsName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the RAMS block");

                    b.Property<Guid>("RamsNodeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("RamsObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsObjectID")
                        .HasComment("ID of the object the RAMS block belongs to (FK to Object)");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int")
                        .HasComment("Number of parallel RAMS blocks in this RAMS block. Will only be visible for containers, and is set automatically during design of RAMS diagram.");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int")
                        .HasComment("RAMS ID of the parent RAMS block (FK to Rams)");

                    b.Property<double?>("RamsPfd")
                        .HasColumnType("float")
                        .HasColumnName("RamsPFD")
                        .HasComment("Probability of failure on demand (PFD) for this RAMS block, which is the probability this RAMS block will fail when it is actually used. This value can be used to determine the Safety Integrity Level (SIL)");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Total costs of preventive measures taken for this RAMS block");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float")
                        .HasComment("Functional productivity (time) of this RAMS block");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float")
                        .HasComment("Technical productivity (time) of this RAMS block");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int")
                        .HasComment("? The read sequence is used to rebuild a nested diagram in the correct order. The field is set automatically by methods DiagramToRows and DoDiagramToRows (in SyncRamsData).");

                    b.Property<double?>("RamsReliabilityFunctional")
                        .HasColumnType("float");

                    b.Property<double?>("RamsReliabilityTechnical")
                        .HasColumnType("float");

                    b.Property<string>("RamsRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks for this RAMS block");

                    b.Property<int?>("RamsRiskId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskID")
                        .HasComment("ID of the risk the RAMS block belongs to (FK to MRB)");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskObjectID")
                        .HasComment("ID of the risk object the RAMS block belongs to (FK to RiskObject)");

                    b.Property<decimal?>("RamsSff")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("RamsSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsSiID")
                        .HasComment("ID of the significant item the RAMS block belongs to (FK to Si)");

                    b.Property<string>("RamsSil")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)");

                    b.Property<string>("RamsSilAc")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)")
                        .HasColumnName("RamsSilAC");

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the RAMS block. The possible statuses are defined in Lookup.");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Total costs of technical corrective measures taken for this RAMS block (?)");

                    b.Property<int?>("RamsTestInterval")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Total costs for this RAMS block");

                    b.Property<string>("RamsType")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)");

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float")
                        .HasComment("Functional utilization (time) of this RAMS block. Asset utilization represents the total revenue earned for every dollar of assets a company owns. (?)");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float")
                        .HasComment("Technical utilization (time) of this RAMS block. Asset utilization represents the total revenue earned for every dollar of assets a company owns.  (?)");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsWantLCC")
                        .HasComment("Boolean that makes the RAMS block available for LCC calculations. When a RAMS block is defined as a container, this value will be set. The user can also enable this by setting the checkbox.");

                    b.Property<decimal?>("RamsWeibullShape")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The Weibull shape (β) defines the slope of the current location on the Weibull curve");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int")
                        .HasComment("RAMS XooN is a number that shows how many RAMS blocks are contained within this RAMS block. Is only set to anything other than 1 for containers. (?)");

                    b.Property<int?>("RamsXposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsXPosition")
                        .HasComment("X-position of the RAMS block in the RAMS diagram. Used to more easily rebuild the RAMS blocks in the correct relative order.");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int")
                        .HasComment("? Year the object the RAMS block represents was first used.");

                    b.Property<int?>("RamsYposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsYPosition")
                        .HasComment("Y-position of the RAMS block in the RAMS diagram. Used to more easily rebuild the RAMS blocks in the correct relative order.");

                    b.HasKey("RamsId");

                    b.HasIndex("RamsDiagramId");

                    b.HasIndex("RamsSiId");

                    b.ToTable("TblRams", t =>
                        {
                            t.HasComment("This table defines RAMS objects. RAMS is a set of methods which are used to visualize the performance of a system, looking specifically at the system Reliability, Availability, Maintainability and Safety. (RAMS) The RAMS objects are user defined from the RAMS controls.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RamsDiagram", b =>
                {
                    b.Property<int>("RamsDgId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("RamsDgID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RamsDgId"));

                    b.Property<double>("RamsDgAvailableTime")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsDgBudgetCapex")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Capital expenditure budget for the RAMS diagram");

                    b.Property<decimal?>("RamsDgBudgetOpex")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Operational expenditure budget for the RAMS diagram");

                    b.Property<bool>("RamsDgCalculateAvailability")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsDgCalculationCompatibilityMode")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDgDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("RamsDgDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("RamsDgDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the RAMS diagram");

                    b.Property<int?>("RamsDgHorizon")
                        .HasColumnType("int")
                        .HasComment("Period applied to the RAMS diagram. (?)");

                    b.Property<string>("RamsDgInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Created by user");

                    b.Property<int?>("RamsDgLccLifetime")
                        .HasColumnType("int")
                        .HasComment("Lifetime in years for LCC calculations");

                    b.Property<string>("RamsDgModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<string>("RamsDgName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the RAMS diagram");

                    b.Property<string>("RamsDgPageBreaks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal>("RamsDgPeriodFrom")
                        .HasColumnType("decimal(18, 10)");

                    b.Property<decimal>("RamsDgPeriodTo")
                        .HasColumnType("decimal(18, 10)");

                    b.Property<string>("RamsDgPrerequisites")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<double?>("RamsDgRealizationAvailability")
                        .HasColumnType("float")
                        .HasComment("Actual availability achieved for the RAMS diagram");

                    b.Property<double?>("RamsDgRealizationCapacity")
                        .HasColumnType("float")
                        .HasComment("Actual capacity achieved for the RAMS diagram");

                    b.Property<string>("RamsDgRealizationCapacityUnit")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Unit of measurement for actual capacity");

                    b.Property<decimal?>("RamsDgRealizationCapex")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Actual capital expenditure for the RAMS diagram");

                    b.Property<string>("RamsDgRealizationEcologicalFootprint")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)")
                        .HasComment("Actual ecological footprint for the RAMS diagram");

                    b.Property<decimal?>("RamsDgRealizationOpex")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Actual operational expenditure for the RAMS diagram");

                    b.Property<string>("RamsDgRealizationQuality")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)")
                        .HasComment("Actual quality achieved for the RAMS diagram");

                    b.Property<double?>("RamsDgRealizationReliability")
                        .HasColumnType("float")
                        .HasComment("Actual reliability achieved for the RAMS diagram");

                    b.Property<string>("RamsDgReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("RamsDgReferenceID")
                        .HasComment("Reference ID of the RAMS diagram (not bound and used) (!)");

                    b.Property<string>("RamsDgRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the RAMS diagram");

                    b.Property<double?>("RamsDgRequirementAvailability")
                        .HasColumnType("float")
                        .HasComment("Availability requirement for the RAMS diagram");

                    b.Property<double?>("RamsDgRequirementCapacity")
                        .HasColumnType("float")
                        .HasComment("Capacity requirement for the RAMS diagram");

                    b.Property<int?>("RamsDgRequirementCapacityUnit")
                        .IsUnicode(false)
                        .HasColumnType("int")
                        .HasComment("Unit of measurement for capacity requirement");

                    b.Property<string>("RamsDgRequirementEcologicalFootprint")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)")
                        .HasComment("Ecological footprint requirement for the RAMS diagram");

                    b.Property<string>("RamsDgRequirementQuality")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)")
                        .HasComment("Quality requirement for the RAMS diagram");

                    b.Property<double?>("RamsDgRequirementReliability")
                        .HasColumnType("float")
                        .HasComment("Reliability requirement for the RAMS diagram");

                    b.Property<int?>("RamsDgRiskObject")
                        .HasColumnType("int")
                        .HasComment("ID of the risk object linked to the RAMS diagram (FK to RiskObject)");

                    b.Property<int>("RamsDgScenId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgScenID")
                        .HasComment("ID of the scenario the RAMS diagram belongs to (FK to Scenario)");

                    b.Property<string>("RamsDgSerialized")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RamsDgSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgSiID")
                        .HasComment("ID of the significant item bound to the RAMS diagram (FK to Si)");

                    b.Property<string>("RamsDgSiRefId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("RamsDgSiRefID")
                        .HasComment("Reference ID of the significant item bound to the RAMS diagram. (Does not seem to be in use in the AMprover software) (!)");

                    b.Property<int?>("RamsDgStatus")
                        .HasColumnType("int")
                        .HasComment("Rams status value of the RAMS diagram (FK to Lookup) (Does not seem to be used in AMprover software) (!)");

                    b.Property<int?>("RamsDgTestInterval")
                        .HasColumnType("int")
                        .HasComment("Test interval for the RAMS diagram.");

                    b.Property<bool?>("RamsDgWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsDgWantLCC")
                        .HasComment("Boolean that makes the RAMS diagram available for LCC calculations");

                    b.HasKey("RamsDgId");

                    b.HasIndex("RamsDgScenId");

                    b.ToTable("TblRamsDiagram", t =>
                        {
                            t.HasComment("Stores the graphical representation of the RAMS analysis. Some specific properties can be set through the controls of the properties tab within the RAMS analysis.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ReportFilter", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedUserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedUserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReportName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SelectedIds")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("TblReportFilter");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RiskObject", b =>
                {
                    b.Property<int>("RiskObjId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("RiskObjID")
                        .HasComment("Unique ID (PK to RiskObject)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RiskObjId"));

                    b.Property<string>("RiskObjAbmstate")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("RiskObjABMstate")
                        .HasComment("State of the risk object (master, revision etc.)");

                    b.Property<string>("RiskObjAmrbattended")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("RiskObjAMRBattended")
                        .HasComment("People who have attended the risk analysis session. (Does not seem to be used by AMprover software)");

                    b.Property<string>("RiskObjAnalyseType")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("Analysis type of the risk object. The analysis type is defined from the masterdata, though the string value is stored here. (Lookup)");

                    b.Property<int?>("RiskObjCopiedFrom")
                        .HasColumnType("int")
                        .HasComment("When a risk object was copied from another risk object, this field contains the ID of the source risk object (master risk object) (FK to RiskObject)");

                    b.Property<DateTime?>("RiskObjDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<int?>("RiskObjDepartmentId")
                        .HasColumnType("int");

                    b.Property<int>("RiskObjFmecaId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjFmecaID")
                        .HasComment("ID of the FMECA matrix used for this risk object. (FK to Fmeca)");

                    b.Property<byte[]>("RiskObjFuncDecomp")
                        .HasColumnType("image")
                        .HasComment("Picture of the functional decomposition of the risk object");

                    b.Property<int?>("RiskObjLccyear")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjLCCYear")
                        .HasComment("Optimal lifetime of the risk object (not used)");

                    b.Property<string>("RiskObjModifiedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<string>("RiskObjMrbstartPoint")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("RiskObjMRBstartPoint")
                        .HasComment("Start point of the risk object. (Does not seem to be used in the AMprover software)");

                    b.Property<string>("RiskObjName")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Name of the risk object");

                    b.Property<int?>("RiskObjNoOfInstallation")
                        .HasColumnType("int")
                        .HasComment("Number of objects contained within the risk object. Used in LCC calculations.");

                    b.Property<int>("RiskObjObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjObjectID")
                        .HasComment("ID of the object, which is a level 1 risk object (FK to Object)");

                    b.Property<int?>("RiskObjParentObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjParentObjectID")
                        .HasComment("ID of the parent object, which is a level 0 risk object (FK to Object)");

                    b.Property<decimal?>("RiskObjProdCostHour")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Production costs (per hour) for this risk object");

                    b.Property<string>("RiskObjResponsible")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Person or department that are responsible for this risk object");

                    b.Property<decimal?>("RiskObjRforSpares")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Given reliability for the spare parts");

                    b.Property<int>("RiskObjScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjScenarioID")
                        .HasComment("ID of the scenario that contains the risk object (FK to Scenario)");

                    b.Property<int?>("RiskObjStatus")
                        .HasColumnType("int");

                    b.Property<int?>("RiskObjVolgNo")
                        .HasColumnType("int")
                        .HasComment("Serial number of the risk object. (Does not seem to be used by the AMprover software)");

                    b.HasKey("RiskObjId");

                    b.HasIndex("RiskObjDepartmentId");

                    b.HasIndex("RiskObjFmecaId");

                    b.HasIndex("RiskObjObjectId");

                    b.HasIndex("RiskObjParentObjectId");

                    b.HasIndex("RiskObjScenarioId");

                    b.HasIndex("RiskObjStatus");

                    b.ToTable("TblRiskObject", t =>
                        {
                            t.HasComment("Stores riskobjects, which are defined within the risk analysis. A risk object is an asset, of which we want to know the risks that are present during normal usage. A risk object contains risks, or systems.");
                        });
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.ClusterStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ClusterLevel")
                        .HasColumnType("int");

                    b.Property<int?>("LowestStatus")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("ClusterStatus");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.ClusterTaskPlanWithObjects", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClusterName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("CostPerUnit")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime?>("DateExecuted")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateGenerated")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("DownTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("Duration")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("ExecuteStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ExecutionDate")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("Interruptible")
                        .HasColumnType("bit");

                    b.Property<decimal?>("Interval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("IntervalUnit")
                        .HasColumnType("int");

                    b.Property<int?>("Priority")
                        .HasColumnType("int");

                    b.Property<int?>("QualityScore")
                        .HasColumnType("int");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Risk")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskId")
                        .HasColumnType("int");

                    b.Property<int?>("Sequence")
                        .HasColumnType("int");

                    b.Property<int?>("ShiftEndDate")
                        .HasColumnType("int");

                    b.Property<int?>("ShiftStartDate")
                        .HasColumnType("int");

                    b.Property<decimal?>("SiUnits")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("SignificantItem")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Slack")
                        .HasColumnType("int");

                    b.Property<string>("SlackInterval")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Task")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TaskId")
                        .HasColumnType("int");

                    b.Property<bool?>("UseLastDayExecuted")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.ToTable("ClusterTaskPlansWithObjects");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.ClusterTaskWithObjects", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Assembly")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Cluster")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ClusterCostMember")
                        .HasColumnType("bit");

                    b.Property<decimal?>("ClusterCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Collection")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Component")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Costs")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("DownTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("Duration")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("EstimatedCostPerUnit")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("EstimatedCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Executor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GeneralDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Initiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Installation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Interval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("IntervalUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Policy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Risk")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskId")
                        .HasColumnType("int");

                    b.Property<string>("RiskObject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Scenario")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("System")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UnitType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Units")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("WorkPackage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ClusterTasksWithObjects");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.ClusterTree", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ClusterId")
                        .HasColumnType("int");

                    b.Property<int?>("ObjectId")
                        .HasColumnType("int");

                    b.Property<string>("ObjectShortKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskObjId")
                        .HasColumnType("int");

                    b.Property<string>("RiskObjectName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ScenarioChildType")
                        .HasColumnType("int");

                    b.Property<int?>("ScenarioId")
                        .HasColumnType("int");

                    b.Property<string>("ScenarioName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ScenarioShortKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TaskWorkPackageId")
                        .HasColumnType("int");

                    b.Property<decimal?>("WorkPackageInterval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("WorkPackageIntervalUnitId")
                        .HasColumnType("int");

                    b.Property<string>("WorkPackageShortDescription")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ClusterTree");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.ClusterWorkPackageTree", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ClusterId")
                        .HasColumnType("int");

                    b.Property<int?>("WorkPackageId")
                        .HasColumnType("int");

                    b.Property<decimal>("WorkPackageInterval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("WorkPackageIntervalUnitId")
                        .HasColumnType("int");

                    b.Property<string>("WorkPackageName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkPackageShortDescription")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ClusterWorkPackageTree");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.ClusterReportItem", b =>
                {
                    b.Property<int?>("ClcId")
                        .HasColumnType("int");

                    b.Property<decimal?>("ClustCostCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("ClustCostDescr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ClustCostPrice")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("ClustCostQuantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("ClustCostRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ClustCostTaskId")
                        .HasColumnType("int");

                    b.Property<decimal?>("ClustCostUnits")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("ClustDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ClustDisciplineCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("ClustDownTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("ClustDuration")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("ClustEnergyCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("ClustEstTaskCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("ClustExecutor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ClustId")
                        .HasColumnType("int");

                    b.Property<string>("ClustInitiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ClustInterval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("ClustIntervalUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ClustLevel")
                        .HasColumnType("int");

                    b.Property<decimal?>("ClustMaterialCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("ClustName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClustName0")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClustName1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClustName2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClustName3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ClustPartOf")
                        .HasColumnType("int");

                    b.Property<string>("ClustRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClustResponsible")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ClustScenarioID")
                        .HasColumnType("int");

                    b.Property<string>("ClustSecondValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ClustSharedCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("ClustShortKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ClustStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("ClustTaskCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("ClustToolCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("ClustTotalCmnCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CmnCostType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("MrbChildObject")
                        .HasColumnType("int");

                    b.Property<int?>("MrbChildObject1")
                        .HasColumnType("int");

                    b.Property<int?>("MrbChildObject2")
                        .HasColumnType("int");

                    b.Property<string>("MrbFailureCause")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbFailureConsequences")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("MrbId")
                        .HasColumnType("int");

                    b.Property<string>("MrbObject0")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbObject2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbObject3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbObject4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ParentClusterName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObjectDesc")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Scenario")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskCostCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskCostDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskCostPrice")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("TskCostQuantity")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("TskCostTskId")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCostUnits")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<bool?>("TskDerived")
                        .HasColumnType("bit");

                    b.Property<string>("TskDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskExecutor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskGeneralDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TskId")
                        .HasColumnType("int");

                    b.Property<string>("TskInitiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskIntervalUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int");

                    b.Property<string>("TskName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskPermit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskPolicy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("TskRemoved")
                        .HasColumnType("bit");

                    b.Property<int?>("TskSortOrder")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskWorkPackage")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("ClusterReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.CommonActionReportItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Executor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FilterSiCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FilterUnitTypes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Initiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Interval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Policy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Task")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Unit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UnitType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkPackage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CommonActionReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.FunctionalTreeReportItem", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Obj1Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Obj1Function")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Obj1Level")
                        .HasColumnType("int");

                    b.Property<string>("Obj1Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Obj1NewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("Obj1ProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("Obj2Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Obj2Function")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Obj2Level")
                        .HasColumnType("int");

                    b.Property<string>("Obj2Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Obj2NewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("Obj2ProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("Obj3Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Obj3Function")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Obj3Level")
                        .HasColumnType("int");

                    b.Property<string>("Obj3Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Obj3NewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("Obj3ProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("Obj4Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Obj4Function")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Obj4Level")
                        .HasColumnType("int");

                    b.Property<string>("Obj4Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Obj4NewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("Obj4ProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("ObjDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ObjFunction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ObjLevel")
                        .HasColumnType("int");

                    b.Property<string>("ObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ObjNewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("ObjProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("RiskObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObjObjFunction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskObjObjLevel")
                        .HasColumnType("int");

                    b.Property<string>("RiskObjObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("RiskObjObjNewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("RiskObjObjProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("RiskObjResponsible")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObjStartPoint")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskObjectId")
                        .HasColumnType("int");

                    b.Property<string>("ScenarioDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ScenarioId")
                        .HasColumnType("int");

                    b.Property<string>("ScenarioName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("FunctionalTreeReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.RiskAnalysisReportItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("ActionCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("CustomAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("CustomBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("DownTimeAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("DownTimeBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("EffectAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("EffectBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("FailMechanism")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FailureCategory1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FailureCategory2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FailureCause")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FailureConsequences")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FmecaSelect")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("FmecaVersion")
                        .HasColumnType("int");

                    b.Property<byte[]>("MatrixAfter")
                        .HasColumnType("varbinary(max)");

                    b.Property<byte[]>("MatrixBefore")
                        .HasColumnType("varbinary(max)");

                    b.Property<byte[]>("MatrixPmo")
                        .HasColumnType("varbinary(max)");

                    b.Property<decimal?>("MtbfAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MtbfBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("OptimalCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("PmoActionCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("PmoSpareCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("PmoSpareManagementCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Remarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Remarks1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("RiskAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("RiskBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("RiskObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("RiskPmo")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("RiskSpareCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("SapaIndex")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("SapaName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Scenario")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SpareCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("SpareCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("SpareDepreciationPct")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("SpareId")
                        .HasColumnType("int");

                    b.Property<decimal?>("SpareManagementCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("SpareName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SpareNoOfItems")
                        .HasColumnType("int");

                    b.Property<int?>("SpareObjectCount")
                        .HasColumnType("int");

                    b.Property<decimal?>("SpareOrderLeadTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("SparePmo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("SparePurchasePrice")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("SparePurchaseYear")
                        .HasColumnType("int");

                    b.Property<string>("SpareReferenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SpareRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SpareStockNumber")
                        .HasColumnType("int");

                    b.Property<string>("SpareSupplierId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SpareVendorId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("SpareYearlyCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskCluster")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskExecutor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskGeneralDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TskId")
                        .HasColumnType("int");

                    b.Property<string>("TskInitiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskIntervalUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskMxPolicy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskPmo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskWorkPackage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("RiskAnalysisReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.RiskAndPreviousActionsReportItem", b =>
                {
                    b.Property<int>("TskID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TskID"));

                    b.Property<string>("FailMode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("MRBId")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbActionCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomEffectAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomEffectBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("MrbDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("MrbDirectCostAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbDirectCostBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbDownTimeAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbDownTimeBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("MrbFailureCategorie1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbFailureCategorie2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbFailureCause")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbFailureConsequences")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("MrbFailureMode")
                        .HasColumnType("int");

                    b.Property<int?>("MrbMasterID")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("MrbName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbNorm")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("MrbOptimalCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("MrbRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbResponsible")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbRiskBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbSpareCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbSpareManageCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("MrbState")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("MrbStatus")
                        .HasColumnType("int");

                    b.Property<string>("Object")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskObjNoOfInstallation")
                        .HasColumnType("int");

                    b.Property<string>("RiskObject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Scenario")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TskClustID")
                        .HasColumnType("int");

                    b.Property<string>("TskClustName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TskClusterCostMember")
                        .HasColumnType("bit");

                    b.Property<decimal?>("TskClusterCostPerUnit")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("TskClusterCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("TskCommonActionID")
                        .HasColumnType("int");

                    b.Property<int?>("TskCopiedFrom")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("TskCostPerUnit")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime?>("TskExecutionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TskExecutor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("TskFinishDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("TskFmecaEffectPct")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskGeneralDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskInitiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskIntervalUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskMxPolicy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskNorm")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskOptimalCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("TskPartOf")
                        .HasColumnType("int");

                    b.Property<string>("TskPermit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TskSortOrder")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskUnitType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskUnits")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("TskValidFromYear")
                        .HasColumnType("int");

                    b.Property<int?>("TskValidUntilYear")
                        .HasColumnType("int");

                    b.Property<string>("TskWorkPackage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserDefinedFilter")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("TskID");

                    b.ToTable("RiskAndPreviousActionsReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.SignificantItemReportItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ContractEnd")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Height")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("Length")
                        .HasColumnType("decimal(18,3)");

                    b.Property<bool?>("MiscBitField1")
                        .HasColumnType("bit");

                    b.Property<string>("MiscTextField1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField5")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField6")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField7")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Mtbf")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PartOfSiName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("QualityScore")
                        .HasColumnType("int");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Remarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierTextField1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TotalUnits")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UnitType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UnitTypeValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Units")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Vendor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("WarrantyPeriod")
                        .HasColumnType("int");

                    b.Property<decimal?>("Width")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime?>("Year")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("SignificantItemReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.TaskPlanReportItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Assembly")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("CltpClusterCostPerUnit")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime?>("CltpDateExecuted")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CltpDateGenerated")
                        .HasColumnType("datetime2");

                    b.Property<string>("CltpDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("CltpDownTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("CltpDuration")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("CltpEstCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime?>("CltpExecutionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CltpExecutor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CltpInitiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CltpInstruction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("CltpInterval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CltpIntervalUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CltpMxPolicy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CltpName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CltpQualityScore")
                        .HasColumnType("int");

                    b.Property<string>("CltpRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CltpRiskID")
                        .HasColumnType("int");

                    b.Property<decimal?>("CltpSiUnits")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("CltpTotalCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CltpValidFrom")
                        .HasColumnType("int");

                    b.Property<int?>("CltpValidUntil")
                        .HasColumnType("int");

                    b.Property<int?>("ClustPartOf")
                        .HasColumnType("int");

                    b.Property<string>("Collection")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Component")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FailureMode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Installation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbEffect")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbFailureCause")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbFailureConsequences")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PartOfSi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PartOfSiPartOfSi")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Risk")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Scenario")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiAttribute1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiAttribute2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiAttribute3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiAttribute4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiAttribute5")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiAttribute6")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiAttribute7")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("SiSHECritical")
                        .HasColumnType("bit");

                    b.Property<string>("System")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TskID")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("TaskPlanReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.RiskOnAbsStoredProcedureResult", b =>
                {
                    b.Property<string>("AssetCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AssetId")
                        .HasColumnType("int");

                    b.Property<string>("AssetName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssetParentCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CategoryId")
                        .HasColumnType("int");

                    b.Property<int?>("FmecaId")
                        .HasColumnType("int");

                    b.Property<int?>("FmecaMtbfAfter")
                        .HasColumnType("int");

                    b.Property<int?>("FmecaMtbfBefore")
                        .HasColumnType("int");

                    b.Property<int?>("FmecaMtbfPmo")
                        .HasColumnType("int");

                    b.Property<string>("FmecaSelectionAfter")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FmecaSelectionBefore")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FmecaSelectionPmo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskId")
                        .HasColumnType("int");

                    b.Property<string>("RiskName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskObjectId")
                        .HasColumnType("int");

                    b.ToTable("RiskOnAbsStoredProcedure");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.RiskWithObjects", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Assembly")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Component")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Installation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RiskObjectId")
                        .HasColumnType("int");

                    b.Property<string>("System")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("RisksWithObjects");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.TaskWithObjects", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Costs")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Executor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Initiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Interval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("IntervalUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Policy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RiskId")
                        .HasColumnType("int");

                    b.Property<string>("WorkPackage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("TasksWithObjects");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Sapa", b =>
                {
                    b.Property<int>("SapaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SapaId"));

                    b.Property<bool>("SapaAccepted")
                        .HasColumnType("bit");

                    b.Property<bool>("SapaApproveForAllYears")
                        .HasColumnType("bit");

                    b.Property<decimal>("SapaBudget")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SapaBudgetApproved")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SapaBudgetRequest")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SapaCbiItem")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("SapaCbiScore")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SapaCollectionId")
                        .HasColumnType("int");

                    b.Property<string>("SapaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SapaExecutorId")
                        .HasColumnType("int");

                    b.Property<int?>("SapaFirstYear")
                        .HasColumnType("int");

                    b.Property<int?>("SapaInitiatorId")
                        .HasColumnType("int");

                    b.Property<int?>("SapaLastYear")
                        .HasColumnType("int");

                    b.Property<string>("SapaName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapaRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapaResponsible")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("SapaRiskObjId")
                        .HasColumnType("int");

                    b.Property<int?>("SapaStatusId")
                        .HasColumnType("int");

                    b.Property<int?>("SapaWorkpackageId")
                        .HasColumnType("int");

                    b.HasKey("SapaId");

                    b.HasIndex("SapaCollectionId");

                    b.HasIndex("SapaExecutorId");

                    b.HasIndex("SapaInitiatorId");

                    b.HasIndex("SapaRiskObjId");

                    b.HasIndex("SapaWorkpackageId");

                    b.ToTable("TblSapa");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SapaCollection", b =>
                {
                    b.Property<int>("SapaCollId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SapaCollId"));

                    b.Property<string>("SapaCollDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapaCollName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapaCollRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SapaCollRiskObjId")
                        .HasColumnType("int");

                    b.Property<bool>("SapaCollSelected")
                        .HasColumnType("bit");

                    b.HasKey("SapaCollId");

                    b.HasIndex("SapaCollRiskObjId");

                    b.ToTable("TblSapaCollection");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SapaDetail", b =>
                {
                    b.Property<int>("SapaDetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SapaDetId"));

                    b.Property<bool>("SapaDetApproved")
                        .HasColumnType("bit");

                    b.Property<decimal>("SapaDetCostYear1")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SapaDetCostYear2")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SapaDetCostYear3")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SapaDetCostYear4")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SapaDetCostYear5")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SapaDetMotivation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SapaDetMrbId")
                        .HasColumnType("int");

                    b.Property<decimal?>("SapaDetScore")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SapaDetTotalCapexNeeded")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SapaDetTskId")
                        .HasColumnType("int");

                    b.Property<int>("SapaDetYearId")
                        .HasColumnType("int");

                    b.HasKey("SapaDetId");

                    b.HasIndex("SapaDetMrbId");

                    b.HasIndex("SapaDetTskId");

                    b.HasIndex("SapaDetYearId");

                    b.ToTable("TblSapaDetail");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SapaWorkpackage", b =>
                {
                    b.Property<int>("SapaWpId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SapaWpId"));

                    b.Property<string>("SapaWpDescription")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SapaWpKey")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("SapaWpName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("SapaWpId");

                    b.ToTable("TblSapaWorkpackage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SapaYear", b =>
                {
                    b.Property<int>("SapaYearId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SapaYearId"));

                    b.Property<decimal>("SapaYearBudget")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SapaYearBudgetApproved")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SapaYearBudgetRequest")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SapaYearSapaId")
                        .HasColumnType("int");

                    b.Property<int>("SapaYearYear")
                        .HasColumnType("int");

                    b.HasKey("SapaYearId");

                    b.HasIndex("SapaYearSapaId");

                    b.ToTable("TblSapaYear");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Scenario", b =>
                {
                    b.Property<int>("ScenId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ScenID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ScenId"));

                    b.Property<int?>("ScenChildType")
                        .HasColumnType("int");

                    b.Property<int?>("ScenCopiedFrom")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ScenDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ScenDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("ScenDescr")
                        .HasColumnType("text");

                    b.Property<string>("ScenInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ScenModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ScenName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ScenShrtKey")
                        .IsRequired()
                        .HasMaxLength(4)
                        .IsUnicode(false)
                        .HasColumnType("char(4)");

                    b.Property<string>("ScenStartPoint")
                        .HasColumnType("text");

                    b.Property<int?>("ScenStatus")
                        .HasColumnType("int");

                    b.HasKey("ScenId");

                    b.HasIndex("ScenStatus");

                    b.ToTable("TblScenario");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Si", b =>
                {
                    b.Property<int>("SiId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SiID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SiId"));

                    b.Property<string>("SiAssetManager")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("SiAssetOwner")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("SiAssetType")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("SiBvId")
                        .HasColumnType("int")
                        .HasColumnName("SiBvID");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SiContractEnd")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SiDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("SiDescription")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)");

                    b.Property<decimal?>("SiHeight")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("SiLength")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("SiLocation")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("SiMiscBitField1")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("SiMiscDateField1")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("SiMiscDecimalField1")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("SiMiscDecimalField2")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("SiMiscTextField1")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SiMiscTextField2")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SiMiscTextField3")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SiMiscTextField4")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SiMiscTextField5")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SiMiscTextField6")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SiMiscTextField7")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SiModifiedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("SiMtbf")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("SiName")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<string>("SiParentName")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("SiPartOf")
                        .HasColumnType("int");

                    b.Property<decimal?>("SiPrice")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("SiQualityScore")
                        .HasColumnType("int");

                    b.Property<string>("SiReferenceId")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("SiReferenceID");

                    b.Property<string>("SiRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("SiSerialNumber")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<string>("SiServiceManager")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("SiServiceProvider")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("SiSite")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("SiStatus")
                        .HasColumnType("int");

                    b.Property<bool?>("SiSupplierBitField1")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("SiSupplierDateField1")
                        .HasColumnType("datetime");

                    b.Property<int?>("SiSupplierIntField1")
                        .HasColumnType("int");

                    b.Property<string>("SiSupplierTextField1")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SiSupplierTextField2")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SiSupplierTextField3")
                        .HasMaxLength(255)
                        .IsUnicode(false)
                        .HasColumnType("varchar(255)");

                    b.Property<decimal?>("SiTotalUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("SiType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("SiUnitType")
                        .HasMaxLength(15)
                        .IsUnicode(false)
                        .HasColumnType("varchar(15)");

                    b.Property<decimal?>("SiUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("SiUnitsTypeValues")
                        .HasMaxLength(150)
                        .IsUnicode(false)
                        .HasColumnType("varchar(150)");

                    b.Property<string>("SiVendor")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int?>("SiWarrantyPeriod")
                        .HasColumnType("int");

                    b.Property<decimal?>("SiWidth")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("SiYear")
                        .HasColumnType("datetime");

                    b.HasKey("SiId");

                    b.HasIndex(new[] { "SiName" }, "IX_Si");

                    b.HasIndex(new[] { "SiCategory" }, "IX_Si_Category");

                    b.HasIndex(new[] { "SiPartOf" }, "IX_tblSiPartOf");

                    b.ToTable("TblSi");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiLinkFilters", b =>
                {
                    b.Property<int>("SifId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SifID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SifId"));

                    b.Property<int?>("SifCategory")
                        .HasColumnType("int");

                    b.Property<int?>("SifChildObject1Id")
                        .HasColumnType("int")
                        .HasColumnName("SifChildObject1ID");

                    b.Property<int?>("SifChildObject2Id")
                        .HasColumnType("int")
                        .HasColumnName("SifChildObject2ID");

                    b.Property<int?>("SifChildObject3Id")
                        .HasColumnType("int")
                        .HasColumnName("SifChildObject3ID");

                    b.Property<int?>("SifChildObject4Id")
                        .HasColumnType("int")
                        .HasColumnName("SifChildObject4ID");

                    b.Property<int?>("SifChildObjectId")
                        .HasColumnType("int")
                        .HasColumnName("SifChildObjectID");

                    b.Property<string>("SifDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<bool>("SifExcludeFromParent")
                        .HasColumnType("bit");

                    b.Property<int?>("SifFilterId")
                        .HasColumnType("int")
                        .HasColumnName("SifFilterID");

                    b.Property<string>("SifName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("SifObjectId")
                        .HasColumnType("int")
                        .HasColumnName("SifObjectID");

                    b.Property<int?>("SifRiskId")
                        .HasColumnType("int")
                        .HasColumnName("SifRiskID");

                    b.Property<int?>("SifRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("SifRiskObjectID");

                    b.Property<int?>("SifScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("SifScenarioId");

                    b.Property<int?>("SifTaskId")
                        .HasColumnType("int")
                        .HasColumnName("SifTaskID");

                    b.HasKey("SifId");

                    b.HasIndex("SifChildObject1Id");

                    b.HasIndex("SifChildObject2Id");

                    b.HasIndex("SifChildObject3Id");

                    b.HasIndex("SifChildObject4Id");

                    b.HasIndex("SifChildObjectId");

                    b.HasIndex("SifFilterId")
                        .IsUnique()
                        .HasFilter("[SifFilterID] IS NOT NULL");

                    b.HasIndex("SifObjectId");

                    b.HasIndex("SifRiskId");

                    b.HasIndex("SifRiskObjectId");

                    b.HasIndex("SifTaskId");

                    b.ToTable("TblSiLinkFilters");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiRiskCalc", b =>
                {
                    b.Property<DateTime?>("BvDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("BvId")
                        .HasColumnType("int")
                        .HasColumnName("BvID");

                    b.Property<string>("BvPerEffectSet")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("BvWeightingModelId")
                        .HasColumnType("int")
                        .HasColumnName("BvWeightingModelID");

                    b.Property<DateTime?>("MrbDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("MrbFmecaSelect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("MrbFmecaVersion")
                        .HasColumnType("int");

                    b.Property<int>("PckSiActive")
                        .HasColumnType("int");

                    b.Property<int?>("PckSiDateModified")
                        .HasColumnType("int");

                    b.Property<int?>("PckSiExecutionYear")
                        .HasColumnType("int");

                    b.Property<int>("PckSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiID");

                    b.Property<int?>("PckSiItems")
                        .HasColumnType("int");

                    b.Property<int?>("PckSiLinkFilterId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiLinkFilterID");

                    b.Property<int?>("PckSiModifiedBy")
                        .HasColumnType("int");

                    b.Property<int>("PckSiMrbId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiMrbID");

                    b.Property<int>("PckSiSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiSiID");

                    b.Property<decimal?>("SiMtbf")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("SifTaskId")
                        .HasColumnType("int")
                        .HasColumnName("SifTaskID");

                    b.ToTable("SiRiskCalc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiRisks", b =>
                {
                    b.Property<decimal?>("SiRiskAfterCustom")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("SiRiskAfterValue")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("SiRiskBeforeCustom")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("SiRiskBeforeValue")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("SiRiskBvId")
                        .HasColumnType("int")
                        .HasColumnName("SiRiskBvID");

                    b.Property<int?>("SiRiskDateModified")
                        .HasColumnType("int");

                    b.Property<string>("SiRiskFmecaSelData")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("SiRiskId")
                        .HasColumnType("int")
                        .HasColumnName("SiRiskID");

                    b.Property<int?>("SiRiskModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("int");

                    b.Property<decimal?>("SiRiskMtbfAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("SiRiskMtbfBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("SiRiskRiskAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("SiRiskRiskBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("SiRiskRiskFactorBefore")
                        .HasColumnType("int");

                    b.Property<int>("SiRiskRiskId")
                        .HasColumnType("int")
                        .HasColumnName("SiRiskRiskID");

                    b.Property<int>("SiRiskRiskfactorAfter")
                        .HasColumnType("int");

                    b.Property<int>("SiRiskSiId")
                        .HasColumnType("int")
                        .HasColumnName("SiRiskSiID");

                    b.ToTable("TblSiRisks");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiStatistics", b =>
                {
                    b.Property<int>("SiStatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SiStatID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SiStatId"));

                    b.Property<string>("SiStatColorListAfter")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("SiStatColorListBefore")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<DateTime?>("SiStatDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("SiStatFmecaId")
                        .HasColumnType("int")
                        .HasColumnName("SiStatFmecaID");

                    b.Property<string>("SiStatModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("SiStatRiskAfter")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("SiStatRiskBefore")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<int>("SiStatScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("SiStatScenarioID");

                    b.Property<int>("SiStatSiId")
                        .HasColumnType("int")
                        .HasColumnName("SiStatSiID");

                    b.Property<string>("SiStatSumListAfter")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("SiStatSumListBefore")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.HasKey("SiStatId");

                    b.HasIndex(new[] { "SiStatSiId" }, "IX_SiStatisticsSiID");

                    b.HasIndex(new[] { "SiStatScenarioId" }, "IX_SiStatistics_ScenarioId");

                    b.ToTable("TblSiStatistics");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiStatisticsRiskQualification", b =>
                {
                    b.Property<decimal?>("RiskIndexAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("RiskIndexBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal>("RiskNumberAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal>("RiskNumberBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int");

                    b.Property<string>("SiDescription")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)");

                    b.Property<int>("SiId")
                        .HasColumnType("int")
                        .HasColumnName("SiID");

                    b.Property<string>("SiName")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int>("SiStatId")
                        .HasColumnType("int")
                        .HasColumnName("SiStatID");

                    b.Property<int>("SiStatScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("SiStatScenarioID");

                    b.ToTable("SiStatisticsRiskQualification");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Spare", b =>
                {
                    b.Property<int>("SpareId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SpareID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SpareId"));

                    b.Property<string>("SpareCategory")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("SpareChildType")
                        .HasColumnType("int");

                    b.Property<int?>("SpareCopiedFrom")
                        .HasColumnType("int");

                    b.Property<decimal?>("SpareCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("SpareDepreciationPct")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<bool>("SpareDerived")
                        .HasColumnType("bit");

                    b.Property<int>("SpareMrbId")
                        .HasColumnType("int")
                        .HasColumnName("SpareMrbID");

                    b.Property<string>("SpareName")
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<int?>("SpareNoOfItems")
                        .HasColumnType("int");

                    b.Property<int?>("SpareObjectCount")
                        .HasColumnType("int");

                    b.Property<decimal?>("SpareOrderLeadTime")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<bool>("SparePmo")
                        .HasColumnType("bit");

                    b.Property<decimal?>("SparePurchasePrice")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("SparePurchaseYear")
                        .HasColumnType("int");

                    b.Property<string>("SpareReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("SpareReferenceID");

                    b.Property<decimal?>("SpareReliability")
                        .HasColumnType("decimal(18, 5)");

                    b.Property<string>("SpareRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("SpareStockNumber")
                        .HasColumnType("int");

                    b.Property<string>("SpareSupplierId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("SpareSupplierID");

                    b.Property<string>("SpareVendorId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("SpareVendorID");

                    b.Property<decimal?>("SpareYearlyCost")
                        .HasColumnType("decimal(18, 2)");

                    b.HasKey("SpareId");

                    b.HasIndex("SpareMrbId");

                    b.ToTable("TblSpare");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SyncRamsMrb", b =>
                {
                    b.Property<int>("MrbId")
                        .HasColumnType("int")
                        .HasColumnName("MrbID");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit");

                    b.Property<int>("RamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramID");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn");

                    b.ToTable("SyncRamsMrb");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Task", b =>
                {
                    b.Property<int>("TskId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("TskID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TskId"));

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int");

                    b.Property<bool?>("TskClusterCostMember")
                        .HasColumnType("bit");

                    b.Property<decimal?>("TskClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("TskClusterCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnType("int")
                        .HasColumnName("TskCommonActionID");

                    b.Property<int?>("TskCopiedFrom")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCostY1")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskCostY2")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskCostY3")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskCostY4")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskCostY5")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("TskDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TskDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<bool?>("TskDerived")
                        .HasColumnType("bit");

                    b.Property<string>("TskDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("TskDtCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("TskEstCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("TskEstCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("TskExecutionDate")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("TskExecutor")
                        .HasColumnType("int");

                    b.Property<bool?>("TskExtraBool2")
                        .HasColumnType("bit");

                    b.Property<bool?>("TskExtraBool3")
                        .HasColumnType("bit");

                    b.Property<bool?>("TskExtraBool4")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("TskFinishDate")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("TskFmecaEffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskFmecaEffectPct")
                        .HasColumnType("numeric(18, 2)");

                    b.Property<int>("TskFmecaVersion")
                        .HasColumnType("int");

                    b.Property<string>("TskGeneralDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("TskInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("TskInitiator")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<int?>("TskIntervalUnit")
                        .HasColumnType("int");

                    b.Property<string>("TskLcceffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("TskLCCEffect");

                    b.Property<bool?>("TskMaster")
                        .HasColumnType("bit");

                    b.Property<string>("TskModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.Property<int?>("TskMxPolicy")
                        .HasColumnType("int");

                    b.Property<string>("TskName")
                        .IsRequired()
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<string>("TskNorm")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskOptimalCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskPartOf")
                        .HasColumnType("int");

                    b.Property<string>("TskPermit")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<bool>("TskPmo")
                        .HasColumnType("bit");

                    b.Property<int?>("TskPriorityCode")
                        .HasColumnType("int");

                    b.Property<string>("TskReferenceId")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("TskReferenceID");

                    b.Property<string>("TskRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<bool?>("TskRemoved")
                        .HasColumnType("bit");

                    b.Property<string>("TskResponsible")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("TskSapaWorkpackage")
                        .HasColumnType("int");

                    b.Property<bool?>("TskSkipInLcc")
                        .HasColumnType("bit");

                    b.Property<int?>("TskSortOrder")
                        .HasColumnType("int");

                    b.Property<int?>("TskStatus")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int>("TskUnitType")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskValidFromYear")
                        .HasColumnType("int");

                    b.Property<int?>("TskValidUntilYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskWorkInspCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskWorkpackage")
                        .HasColumnType("int");

                    b.HasKey("TskId");

                    b.HasIndex("TskCommonActionId");

                    b.HasIndex("TskExecutor");

                    b.HasIndex("TskInitiator");

                    b.HasIndex("TskIntervalUnit");

                    b.HasIndex("TskMxPolicy");

                    b.HasIndex("TskPartOf");

                    b.HasIndex("TskSapaWorkpackage");

                    b.HasIndex("TskWorkpackage");

                    b.HasIndex(new[] { "TskCluster" }, "IX_TaskClusterID");

                    b.HasIndex(new[] { "TskMrbId" }, "IX_TaskMrbID");

                    b.ToTable("TblTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.TaskCostOnClusterModified", b =>
                {
                    b.Property<decimal?>("ClusterTaskCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(38, 2)");

                    b.ToTable("TaskCostOnClusterModified");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.TaskCostOnMrbModified", b =>
                {
                    b.Property<decimal?>("MrbCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("MrbDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(38, 2)");

                    b.Property<DateTime?>("TskDate")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.ToTable("TaskCostOnMrbModified");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.TaskEdit", b =>
                {
                    b.Property<string>("ObjLevel0Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjLevel2Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjLevel3Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjLevel4Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RiskObjId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjID");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ScenChildType")
                        .HasColumnType("int");

                    b.Property<int?>("ScenId")
                        .HasColumnType("int")
                        .HasColumnName("ScenID");

                    b.Property<string>("ScenName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int");

                    b.Property<bool?>("TskClusterCostMember")
                        .HasColumnType("bit");

                    b.Property<decimal?>("TskClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("TskClusterCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnType("int")
                        .HasColumnName("TskCommonActionID");

                    b.Property<int?>("TskCopiedFrom")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("TskDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TskDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<bool?>("TskDerived")
                        .HasColumnType("bit");

                    b.Property<string>("TskDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("TskEstCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("TskEstCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("TskExecutionDate")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("TskExecutor")
                        .HasColumnType("int");

                    b.Property<bool?>("TskExtraBool2")
                        .HasColumnType("bit");

                    b.Property<bool?>("TskExtraBool3")
                        .HasColumnType("bit");

                    b.Property<bool?>("TskExtraBool4")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("TskFinishDate")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("TskFmecaEffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskFmecaEffectPct")
                        .HasColumnType("numeric(18, 2)");

                    b.Property<int>("TskFmecaVersion")
                        .HasColumnType("int");

                    b.Property<string>("TskGeneralDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("TskId")
                        .HasColumnType("int")
                        .HasColumnName("TskID");

                    b.Property<string>("TskInitiatedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("TskInitiator")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<int>("TskIntervalUnit")
                        .HasColumnType("int");

                    b.Property<string>("TskLcceffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("TskLCCEffect");

                    b.Property<bool?>("TskMaster")
                        .HasColumnType("bit");

                    b.Property<string>("TskModifiedBy")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.Property<int?>("TskMxPolicy")
                        .HasColumnType("int");

                    b.Property<string>("TskName")
                        .IsRequired()
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<string>("TskNorm")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskOptimalCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskPartOf")
                        .HasColumnType("int");

                    b.Property<string>("TskPermit")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("TskPriorityCode")
                        .HasColumnType("int");

                    b.Property<int?>("TskReferenceId")
                        .HasColumnType("int")
                        .HasColumnName("TskReferenceID");

                    b.Property<string>("TskRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<bool?>("TskRemoved")
                        .HasColumnType("bit");

                    b.Property<string>("TskResponsible")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("TskSkipInLcc")
                        .HasColumnType("bit");

                    b.Property<int?>("TskSortOrder")
                        .HasColumnType("int");

                    b.Property<int?>("TskStatus")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int>("TskUnitType")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskValidFromYear")
                        .HasColumnType("int");

                    b.Property<int?>("TskValidUntilYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskWorkInspCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskWorkpackage")
                        .HasColumnType("int");

                    b.ToTable("TaskEdit");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.TaskLookupGrid", b =>
                {
                    b.Property<string>("ClustName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ExecutorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("InitiatorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("IntUnitName")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjectName2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjectName3")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjectName4")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PolName")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)");

                    b.Property<int>("RiskObjId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjID");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("RiskObjScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjScenarioID");

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int");

                    b.Property<bool?>("TskClusterCostMember")
                        .HasColumnType("bit");

                    b.Property<decimal?>("TskClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("TskClusterCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnType("int")
                        .HasColumnName("TskCommonActionID");

                    b.Property<int?>("TskCopiedFrom")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("TskDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TskDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<bool?>("TskDerived")
                        .HasColumnType("bit");

                    b.Property<string>("TskDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<decimal?>("TskEstCostPerUnit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("TskExecutionDate")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("TskExecutor")
                        .HasColumnType("int");

                    b.Property<bool?>("TskExtraBool2")
                        .HasColumnType("bit");

                    b.Property<bool?>("TskExtraBool3")
                        .HasColumnType("bit");

                    b.Property<bool?>("TskExtraBool4")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("TskFinishDate")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("TskFmecaEffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskFmecaEffectPct")
                        .HasColumnType("numeric(18, 2)");

                    b.Property<int>("TskFmecaVersion")
                        .HasColumnType("int");

                    b.Property<string>("TskGeneralDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("TskId")
                        .HasColumnType("int")
                        .HasColumnName("TskID");

                    b.Property<string>("TskInitiatedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("TskInitiator")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18, 4)");

                    b.Property<int>("TskIntervalUnit")
                        .HasColumnType("int");

                    b.Property<string>("TskLcceffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("TskLCCEffect");

                    b.Property<bool?>("TskMaster")
                        .HasColumnType("bit");

                    b.Property<string>("TskModifiedBy")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.Property<int?>("TskMxPolicy")
                        .HasColumnType("int");

                    b.Property<string>("TskName")
                        .IsRequired()
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<decimal?>("TskOptimalCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("TskPermit")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("TskPriorityCode")
                        .HasColumnType("int");

                    b.Property<string>("TskRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<bool?>("TskRemoved")
                        .HasColumnType("bit");

                    b.Property<string>("TskResponsible")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("TskSkipInLcc")
                        .HasColumnType("bit");

                    b.Property<int?>("TskSortOrder")
                        .HasColumnType("int");

                    b.Property<int?>("TskStatus")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int>("TskUnitType")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskUnits")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskValidFromYear")
                        .HasColumnType("int");

                    b.Property<int?>("TskValidUntilYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskWorkInspCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("TskWorkpackage")
                        .HasColumnType("int");

                    b.Property<string>("WpName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.ToTable("TaskLookupGrid");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.UserDepartment", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("DepartmentId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "DepartmentId");

                    b.HasIndex("DepartmentId");

                    b.ToTable("TblUserDepartment");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.UserSetting", b =>
                {
                    b.Property<int>("UsrSetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UsrSetId"));

                    b.Property<string>("UsrSetSetting")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UsrSetUsername")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UsrSetValue")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("UsrSetId");

                    b.ToTable("TblUserSettings");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Vdmxl", b =>
                {
                    b.Property<int>("VdmId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("VdmID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("VdmId"));

                    b.Property<decimal>("VdmAecAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmAecBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmAecPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmCapexAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmCapexBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmCapexPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmCapexPoBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmCapexPoPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmCapexPoiAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("VdmDtCostTasks")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca1After")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca1Before")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca1Pmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca2After")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca2Before")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca2Pmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca3After")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca3Before")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca3Pmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca4After")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca4Before")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca4Pmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca5After")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca5Before")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca5Pmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca6After")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca6Before")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca6Pmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca7After")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca7Before")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca7Pmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca8After")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca8Before")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmFmeca8Pmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("VdmLccId")
                        .HasColumnType("int")
                        .HasColumnName("VdmLccId");

                    b.Property<string>("VdmName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("VdmNpvAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmNpvBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmNpvPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmOpexAfter")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmOpexBefore")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("VdmOpexPmo")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("VdmOptYearAfter")
                        .HasColumnType("int");

                    b.Property<int>("VdmOptYearBefore")
                        .HasColumnType("int");

                    b.Property<int>("VdmOptYearPmo")
                        .HasColumnType("int");

                    b.HasKey("VdmId");

                    b.HasIndex("VdmLccId")
                        .IsUnique();

                    b.ToTable("TblVdmxl");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.VwSiFilter", b =>
                {
                    b.Property<int?>("PartOfCategory")
                        .HasColumnType("int");

                    b.Property<string>("PartOfName")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<string>("Siassetmanager")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("siassetmanager");

                    b.Property<string>("Siassetowner")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("siassetowner");

                    b.Property<string>("Siassettype")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("siassettype");

                    b.Property<int?>("Sibvid")
                        .HasColumnType("int")
                        .HasColumnName("sibvid");

                    b.Property<int>("Sicategory")
                        .HasColumnType("int")
                        .HasColumnName("sicategory");

                    b.Property<DateTime?>("Sicontractend")
                        .HasColumnType("datetime")
                        .HasColumnName("sicontractend");

                    b.Property<DateTime?>("Sidatemodified")
                        .HasColumnType("smalldatetime")
                        .HasColumnName("sidatemodified");

                    b.Property<string>("Sidescription")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("sidescription");

                    b.Property<decimal?>("Siheight")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("siheight");

                    b.Property<int>("Siid")
                        .HasColumnType("int")
                        .HasColumnName("siid");

                    b.Property<decimal?>("Silength")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("silength");

                    b.Property<string>("Silocation")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("silocation");

                    b.Property<bool?>("Simiscbitfield1")
                        .HasColumnType("bit")
                        .HasColumnName("simiscbitfield1");

                    b.Property<DateTime?>("Simiscdatefield1")
                        .HasColumnType("datetime")
                        .HasColumnName("simiscdatefield1");

                    b.Property<decimal?>("Simiscdecimalfield1")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("simiscdecimalfield1");

                    b.Property<decimal?>("Simiscdecimalfield2")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("simiscdecimalfield2");

                    b.Property<string>("Simisctextfield1")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield1");

                    b.Property<string>("Simisctextfield2")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield2");

                    b.Property<string>("Simisctextfield3")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield3");

                    b.Property<string>("Simisctextfield4")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield4");

                    b.Property<string>("Simisctextfield5")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield5");

                    b.Property<string>("Simisctextfield6")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield6");

                    b.Property<string>("Simisctextfield7")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield7");

                    b.Property<string>("Simodifiedby")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("simodifiedby");

                    b.Property<decimal?>("Simtbf")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("simtbf");

                    b.Property<string>("Siname")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("siname");

                    b.Property<int?>("Sipartof")
                        .HasColumnType("int")
                        .HasColumnName("sipartof");

                    b.Property<decimal?>("Siprice")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("siprice");

                    b.Property<int?>("Siqualityscore")
                        .HasColumnType("int")
                        .HasColumnName("siqualityscore");

                    b.Property<string>("Sireferenceid")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("sireferenceid");

                    b.Property<string>("Siremarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("siremarks");

                    b.Property<string>("Siserialnumber")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("siserialnumber");

                    b.Property<string>("Siservicemanager")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("siservicemanager");

                    b.Property<string>("Siserviceprovider")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("siserviceprovider");

                    b.Property<string>("Sisite")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("sisite");

                    b.Property<int?>("Sistatus")
                        .HasColumnType("int")
                        .HasColumnName("sistatus");

                    b.Property<bool?>("Sisupplierbitfield1")
                        .HasColumnType("bit")
                        .HasColumnName("sisupplierbitfield1");

                    b.Property<DateTime?>("Sisupplierdatefield1")
                        .HasColumnType("datetime")
                        .HasColumnName("sisupplierdatefield1");

                    b.Property<int?>("Sisupplierintfield1")
                        .HasColumnType("int")
                        .HasColumnName("sisupplierintfield1");

                    b.Property<string>("Sisuppliertextfield1")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("sisuppliertextfield1");

                    b.Property<string>("Sisuppliertextfield2")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("sisuppliertextfield2");

                    b.Property<string>("Sisuppliertextfield3")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("sisuppliertextfield3");

                    b.Property<decimal?>("Sitotalunits")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("sitotalunits");

                    b.Property<string>("Sitype")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("sitype");

                    b.Property<decimal?>("Siunits")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("siunits");

                    b.Property<string>("Siunitstypevalues")
                        .HasMaxLength(150)
                        .IsUnicode(false)
                        .HasColumnType("varchar(150)")
                        .HasColumnName("siunitstypevalues");

                    b.Property<string>("Siunittype")
                        .HasMaxLength(15)
                        .IsUnicode(false)
                        .HasColumnType("varchar(15)")
                        .HasColumnName("siunittype");

                    b.Property<string>("Sivendor")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("sivendor");

                    b.Property<int?>("Siwarrantyperiod")
                        .HasColumnType("int")
                        .HasColumnName("siwarrantyperiod");

                    b.Property<decimal?>("Siwidth")
                        .HasColumnType("decimal(18, 2)")
                        .HasColumnName("siwidth");

                    b.Property<DateTime?>("Siyear")
                        .HasColumnType("datetime")
                        .HasColumnName("siyear");

                    b.ToTable("VwSiFilter");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Workpackage", b =>
                {
                    b.Property<int>("WpId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("WpID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("WpId"));

                    b.Property<string>("WpDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("WpExecutor")
                        .HasColumnType("int");

                    b.Property<decimal>("WpInterval")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("WpIntervalUnit")
                        .HasColumnType("int");

                    b.Property<string>("WpName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("WpShortDescription")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.HasKey("WpId");

                    b.HasIndex("WpExecutor");

                    b.HasIndex("WpIntervalUnit");

                    b.ToTable("TblWorkPackage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Attachment", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.AttachmentCategory", "AttachmentCategory")
                        .WithMany("Attachments")
                        .HasForeignKey("AtchCatgoryId");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "Risk")
                        .WithMany("Attachments")
                        .HasForeignKey("AtchMrbId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany()
                        .HasForeignKey("AtchRiskObjectId");

                    b.HasOne("AMprover.Data.Entities.AM.Sapa", "Sapa")
                        .WithMany("Attachments")
                        .HasForeignKey("AtchSapaId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("AMprover.Data.Entities.AM.Task", "Task")
                        .WithMany("Attachments")
                        .HasForeignKey("AtchTskId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AttachmentCategory");

                    b.Navigation("Risk");

                    b.Navigation("RiskObject");

                    b.Navigation("Sapa");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvSiItems", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Si", "BvSi")
                        .WithOne("BvSiItems")
                        .HasForeignKey("AMprover.Data.Entities.AM.BvSiItems", "BvSiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.BvWeightingModels", "BvWeightingModel")
                        .WithMany("BvSiItems")
                        .HasForeignKey("BvWeightingModelId");

                    b.Navigation("BvSi");

                    b.Navigation("BvWeightingModel");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvWeightingModels", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.BvAspectSets", "BvModelAspectSet")
                        .WithMany("BvWeightingModels")
                        .HasForeignKey("BvModelAspectSetId");

                    b.HasOne("AMprover.Data.Entities.AM.Fmeca", "BvModelFmeca")
                        .WithMany("BvWeightingModels")
                        .HasForeignKey("BvModelFmecaId");

                    b.Navigation("BvModelAspectSet");

                    b.Navigation("BvModelFmeca");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Cluster", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "ClustPartOfCluster")
                        .WithMany("ClusterChildren")
                        .HasForeignKey("ClustPartOf");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany()
                        .HasForeignKey("ClustRiskObjectId");

                    b.HasOne("AMprover.Data.Entities.AM.Scenario", "Scenario")
                        .WithMany("Clusters")
                        .HasForeignKey("ClustScenarioId");

                    b.HasOne("AMprover.Data.Entities.AM.Lookup", "ClustStatusNavigation")
                        .WithMany("Clusters")
                        .HasForeignKey("ClustStatus");

                    b.Navigation("ClustPartOfCluster");

                    b.Navigation("ClustStatusNavigation");

                    b.Navigation("RiskObject");

                    b.Navigation("Scenario");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterCost", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "ClcCluster")
                        .WithMany("ClusterCost")
                        .HasForeignKey("ClcClusterId");

                    b.HasOne("AMprover.Data.Entities.AM.CommonCost", "ClcCommonCost")
                        .WithMany("ClusterCost")
                        .HasForeignKey("ClcCommonCostId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.CommonTask", "CommonTask")
                        .WithMany("ClusterCost")
                        .HasForeignKey("ClcCommonCostId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Task", "ClcTask")
                        .WithMany("ClusterCost")
                        .HasForeignKey("ClcTaskId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ClcCluster");

                    b.Navigation("ClcCommonCost");

                    b.Navigation("ClcTask");

                    b.Navigation("CommonTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterTaskPlan", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "CltpCluster")
                        .WithMany("ClusterTaskPlan")
                        .HasForeignKey("CltpClusterId");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "CltpRisk")
                        .WithMany()
                        .HasForeignKey("CltpRiskId");

                    b.HasOne("AMprover.Data.Entities.AM.Si", "CltpSi")
                        .WithMany()
                        .HasForeignKey("CltpSiId");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "CltpTask")
                        .WithMany("ClusterTaskPlan")
                        .HasForeignKey("CltpTaskId");

                    b.Navigation("CltpCluster");

                    b.Navigation("CltpRisk");

                    b.Navigation("CltpSi");

                    b.Navigation("CltpTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonCost", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupInflationGroup", "CmnCostPriceGroupNavigation")
                        .WithMany("CommonCost")
                        .HasForeignKey("CmnCostPriceGroup");

                    b.Navigation("CmnCostPriceGroupNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTask", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupExecutor", "CmnTaskExecutorNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskExecutor");

                    b.HasOne("AMprover.Data.Entities.AM.LookupInitiator", "CmnTaskInitiatorNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskInitiator");

                    b.HasOne("AMprover.Data.Entities.AM.LookupIntervalUnit", "CmnTaskIntervalUnitNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskIntervalUnit");

                    b.HasOne("AMprover.Data.Entities.AM.LookupMxPolicy", "CmnTaskMxPolicyNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskMxPolicy");

                    b.HasOne("AMprover.Data.Entities.AM.Workpackage", "CmnTaskWorkPackageNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskWorkPackage");

                    b.Navigation("CmnTaskExecutorNavigation");

                    b.Navigation("CmnTaskInitiatorNavigation");

                    b.Navigation("CmnTaskIntervalUnitNavigation");

                    b.Navigation("CmnTaskMxPolicyNavigation");

                    b.Navigation("CmnTaskWorkPackageNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTaskCost", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.CommonCost", "CtcCommonCost")
                        .WithMany("CommonTaskCost")
                        .HasForeignKey("CtcCommonCostId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.CommonTask", "CtcCommonTask")
                        .WithMany("CommonTaskCost")
                        .HasForeignKey("CtcCommonTaskId");

                    b.Navigation("CtcCommonCost");

                    b.Navigation("CtcCommonTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Descriptions", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "Risk")
                        .WithMany()
                        .HasForeignKey("DescRiskId");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany()
                        .HasForeignKey("DescRiskObjectId");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "Task")
                        .WithMany()
                        .HasForeignKey("DescTaskId");

                    b.Navigation("Risk");

                    b.Navigation("RiskObject");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.FiltersSelectionList", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Filters", "SqlSel")
                        .WithMany("FiltersSelectionList")
                        .HasForeignKey("SqlSelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SqlSel");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.FmecaSelect", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "MfsLcc")
                        .WithMany()
                        .HasForeignKey("MfsLccId");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "MfsReference")
                        .WithMany()
                        .HasForeignKey("MfsReferenceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Task", "MfsTask")
                        .WithMany()
                        .HasForeignKey("MfsTaskId");

                    b.Navigation("MfsLcc");

                    b.Navigation("MfsReference");

                    b.Navigation("MfsTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lcc", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject")
                        .WithMany("ChildObjects")
                        .HasForeignKey("LccChildObject");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject1")
                        .WithMany("ChildObjects1")
                        .HasForeignKey("LccChildObject1");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject2")
                        .WithMany("ChildObjects2")
                        .HasForeignKey("LccChildObject2");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject3")
                        .WithMany("ChildObjects3")
                        .HasForeignKey("LccChildObject3");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject4")
                        .WithMany("ChildObjects4")
                        .HasForeignKey("LccChildObject4");

                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "LccPartOfLcc")
                        .WithMany("LccChildren")
                        .HasForeignKey("LccPartOf");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany("LccItems")
                        .HasForeignKey("LccRiskObject");

                    b.HasOne("AMprover.Data.Entities.AM.Scenario", "LccScenario")
                        .WithMany()
                        .HasForeignKey("LccScenarioId");

                    b.HasOne("AMprover.Data.Entities.AM.Si", "LccSi")
                        .WithMany()
                        .HasForeignKey("LccSiId");

                    b.Navigation("ChildObject");

                    b.Navigation("ChildObject1");

                    b.Navigation("ChildObject2");

                    b.Navigation("ChildObject3");

                    b.Navigation("ChildObject4");

                    b.Navigation("LccPartOfLcc");

                    b.Navigation("LccScenario");

                    b.Navigation("LccSi");

                    b.Navigation("RiskObject");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lccdetail", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "Lcc")
                        .WithMany("Details")
                        .HasForeignKey("LccDetLccId");

                    b.Navigation("Lcc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LcceffectDetail", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Lccdetail", "LccEfctLccDetail")
                        .WithMany("EffectDetails")
                        .HasForeignKey("LccEfctLccDetailId");

                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "LccEfctLcc")
                        .WithMany()
                        .HasForeignKey("LccEfctLccId");

                    b.HasOne("AMprover.Data.Entities.AM.RamsDiagram", "RamsDiagram")
                        .WithMany()
                        .HasForeignKey("LccEfctRamsDiagramId");

                    b.HasOne("AMprover.Data.Entities.AM.Rams", "Rams")
                        .WithMany()
                        .HasForeignKey("LccEfctRamsId");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "Risk")
                        .WithMany()
                        .HasForeignKey("LccEfctRiskId");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "Task")
                        .WithMany()
                        .HasForeignKey("LccEfctTaskId");

                    b.Navigation("LccEfctLcc");

                    b.Navigation("LccEfctLccDetail");

                    b.Navigation("Rams");

                    b.Navigation("RamsDiagram");

                    b.Navigation("Risk");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mca", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Si", "Si")
                        .WithMany()
                        .HasForeignKey("McaSiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Si");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mrb", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupAdditionalData", "MrbAdditionalData")
                        .WithMany("Mrbs")
                        .HasForeignKey("MrbAdditionalDataId");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject")
                        .WithMany("MrbChildObjects")
                        .HasForeignKey("MrbChildObject");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "Installation")
                        .WithMany("MrbChildObjects1")
                        .HasForeignKey("MrbChildObject1");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "System")
                        .WithMany("MrbChildObjects2")
                        .HasForeignKey("MrbChildObject2");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "Component")
                        .WithMany("MrbChildObjects3")
                        .HasForeignKey("MrbChildObject3");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "Assembly")
                        .WithMany("MrbChildObjects4")
                        .HasForeignKey("MrbChildObject4");

                    b.HasOne("AMprover.Data.Entities.AM.LookupFailMode", "FailureMode")
                        .WithMany("Risks")
                        .HasForeignKey("MrbFailureMode");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "MasterRisk")
                        .WithMany("ChildRisks")
                        .HasForeignKey("MrbMasterId");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany("Risks")
                        .HasForeignKey("MrbRiskObject")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Lookup", "MrbStatusNavigation")
                        .WithMany("Risks")
                        .HasForeignKey("MrbStatus");

                    b.Navigation("Assembly");

                    b.Navigation("ChildObject");

                    b.Navigation("Component");

                    b.Navigation("FailureMode");

                    b.Navigation("Installation");

                    b.Navigation("MasterRisk");

                    b.Navigation("MrbAdditionalData");

                    b.Navigation("MrbStatusNavigation");

                    b.Navigation("RiskObject");

                    b.Navigation("System");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.MrbImage", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "MrbImageNavigation")
                        .WithOne("MrbImage")
                        .HasForeignKey("AMprover.Data.Entities.AM.MrbImage", "MrbImageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MrbImageNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexFactor", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexFactOpexData")
                        .WithMany("OpexFactor")
                        .HasForeignKey("OpexFactOpexDataId");

                    b.Navigation("OpexFactOpexData");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexToLcc", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexLccOpexDataId1Navigation")
                        .WithMany("OpexToLccOpexLccOpexDataId1Navigation")
                        .HasForeignKey("OpexLccOpexDataId1");

                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexLccOpexDataId2Navigation")
                        .WithMany("OpexToLccOpexLccOpexDataId2Navigation")
                        .HasForeignKey("OpexLccOpexDataId2");

                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexLccOpexDataId3Navigation")
                        .WithMany("OpexToLccOpexLccOpexDataId3Navigation")
                        .HasForeignKey("OpexLccOpexDataId3");

                    b.Navigation("OpexLccOpexDataId1Navigation");

                    b.Navigation("OpexLccOpexDataId2Navigation");

                    b.Navigation("OpexLccOpexDataId3Navigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PickSi", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.SiLinkFilters", "PckSiLinkFilter")
                        .WithMany("PickSi")
                        .HasForeignKey("PckSiLinkFilterId");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "PckSiMrb")
                        .WithMany("PickSis")
                        .HasForeignKey("PckSiMrbId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Si", "PckSiSi")
                        .WithMany("PickSi")
                        .HasForeignKey("PckSiSiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PckSiLinkFilter");

                    b.Navigation("PckSiMrb");

                    b.Navigation("PckSiSi");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Priority", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Priority", "ParentPriority")
                        .WithMany("ChildPriorities")
                        .HasForeignKey("PrioPartOf");

                    b.Navigation("ParentPriority");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityBudget", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.PriorityBudget", "OriginalPriorityBudget")
                        .WithMany("DerivedPriorityBudgets")
                        .HasForeignKey("PrioBudOriginalId");

                    b.HasOne("AMprover.Data.Entities.AM.Priority", "Priority")
                        .WithMany("PriorityBudgets")
                        .HasForeignKey("PrioBudPriorityId");

                    b.HasOne("AMprover.Data.Entities.AM.PriorityVersion", "PriorityVersion")
                        .WithMany()
                        .HasForeignKey("PrioBudVersion");

                    b.Navigation("OriginalPriorityBudget");

                    b.Navigation("Priority");

                    b.Navigation("PriorityVersion");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityCost", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Priority", "Priority")
                        .WithMany("PriorityCosts")
                        .HasForeignKey("PrioCstPrioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.PriorityTask", "PriorityTask")
                        .WithMany()
                        .HasForeignKey("PrioCstPrioTskId");

                    b.HasOne("AMprover.Data.Entities.AM.PriorityVersion", "PriorityVersion")
                        .WithMany()
                        .HasForeignKey("PrioCstVersion")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Priority");

                    b.Navigation("PriorityTask");

                    b.Navigation("PriorityVersion");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityTask", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Priority", "Priority")
                        .WithMany("PriorityTasks")
                        .HasForeignKey("PrioTskPriorityId");

                    b.HasOne("AMprover.Data.Entities.AM.Si", "Si")
                        .WithMany()
                        .HasForeignKey("PrioTskSiId");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "Task")
                        .WithMany()
                        .HasForeignKey("PrioTskTaskId");

                    b.HasOne("AMprover.Data.Entities.AM.PriorityVersion", "PriorityVersion")
                        .WithMany()
                        .HasForeignKey("PrioTskVersion");

                    b.Navigation("Priority");

                    b.Navigation("PriorityVersion");

                    b.Navigation("Si");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityVersion", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Scenario", "Scenario")
                        .WithMany()
                        .HasForeignKey("PrioVerScenario");

                    b.Navigation("Scenario");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Rams", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.RamsDiagram", "RamsDiagram")
                        .WithMany("Rams")
                        .HasForeignKey("RamsDiagramId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Si", "RamsSi")
                        .WithMany()
                        .HasForeignKey("RamsSiId");

                    b.Navigation("RamsDiagram");

                    b.Navigation("RamsSi");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RamsDiagram", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Scenario", "Scenario")
                        .WithMany()
                        .HasForeignKey("RamsDgScenId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Scenario");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RiskObject", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Department", "RiskObjDepartment")
                        .WithMany()
                        .HasForeignKey("RiskObjDepartmentId");

                    b.HasOne("AMprover.Data.Entities.AM.Fmeca", "RiskObjFmeca")
                        .WithMany("RiskObject")
                        .HasForeignKey("RiskObjFmecaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Object", "RiskObjObject")
                        .WithMany("RiskObject")
                        .HasForeignKey("RiskObjObjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Object", "RiskObjParentObject")
                        .WithMany("ChildRiskObjects")
                        .HasForeignKey("RiskObjParentObjectId");

                    b.HasOne("AMprover.Data.Entities.AM.Scenario", "RiskObjScenario")
                        .WithMany("RiskObject")
                        .HasForeignKey("RiskObjScenarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Lookup", "RiskObjStatusNavigation")
                        .WithMany("RiskObjects")
                        .HasForeignKey("RiskObjStatus");

                    b.Navigation("RiskObjDepartment");

                    b.Navigation("RiskObjFmeca");

                    b.Navigation("RiskObjObject");

                    b.Navigation("RiskObjParentObject");

                    b.Navigation("RiskObjScenario");

                    b.Navigation("RiskObjStatusNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Sapa", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.SapaCollection", "SapaCollection")
                        .WithMany("Sapas")
                        .HasForeignKey("SapaCollectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.LookupExecutor", "Executor")
                        .WithMany("Sapa")
                        .HasForeignKey("SapaExecutorId");

                    b.HasOne("AMprover.Data.Entities.AM.LookupInitiator", "Initiator")
                        .WithMany("Sapa")
                        .HasForeignKey("SapaInitiatorId");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany("RiskObjSapa")
                        .HasForeignKey("SapaRiskObjId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.SapaWorkpackage", "SapaWorkpackage")
                        .WithMany("Sapa")
                        .HasForeignKey("SapaWorkpackageId");

                    b.Navigation("Executor");

                    b.Navigation("Initiator");

                    b.Navigation("RiskObject");

                    b.Navigation("SapaCollection");

                    b.Navigation("SapaWorkpackage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SapaCollection", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany("SapaCollections")
                        .HasForeignKey("SapaCollRiskObjId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RiskObject");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SapaDetail", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "Risk")
                        .WithMany()
                        .HasForeignKey("SapaDetMrbId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Task", "Task")
                        .WithMany("SapaDetails")
                        .HasForeignKey("SapaDetTskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.SapaYear", "SapaYear")
                        .WithMany("Details")
                        .HasForeignKey("SapaDetYearId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Risk");

                    b.Navigation("SapaYear");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SapaYear", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Sapa", "Sapa")
                        .WithMany("Years")
                        .HasForeignKey("SapaYearSapaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Sapa");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Scenario", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Lookup", "ScenStatusNavigation")
                        .WithMany("Scenarios")
                        .HasForeignKey("ScenStatus");

                    b.Navigation("ScenStatusNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Si", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupUserDefined", "SiCategoryNavigation")
                        .WithMany("Si")
                        .HasForeignKey("SiCategory")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SiCategoryNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiLinkFilters", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject1")
                        .WithMany("SiLinkFiltersSifChildObject1")
                        .HasForeignKey("SifChildObject1Id");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject2")
                        .WithMany("SiLinkFiltersSifChildObject2")
                        .HasForeignKey("SifChildObject2Id");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject3")
                        .WithMany("SiLinkFiltersSifChildObject3")
                        .HasForeignKey("SifChildObject3Id");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject4")
                        .WithMany("SiLinkFiltersSifChildObject4")
                        .HasForeignKey("SifChildObject4Id");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject")
                        .WithMany("SiLinkFiltersSifChildObject")
                        .HasForeignKey("SifChildObjectId");

                    b.HasOne("AMprover.Data.Entities.AM.Filters", "SifFilterNavigation")
                        .WithOne("SiLinkFilters")
                        .HasForeignKey("AMprover.Data.Entities.AM.SiLinkFilters", "SifFilterId");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "SifRisk")
                        .WithMany("SiLinkFilters")
                        .HasForeignKey("SifRiskId");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "SifRiskObject")
                        .WithMany("SiLinkFilters")
                        .HasForeignKey("SifRiskObjectId");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "SifTask")
                        .WithMany("SiLinkFilters")
                        .HasForeignKey("SifTaskId");

                    b.Navigation("SifChildObject");

                    b.Navigation("SifChildObject1");

                    b.Navigation("SifChildObject2");

                    b.Navigation("SifChildObject3");

                    b.Navigation("SifChildObject4");

                    b.Navigation("SifFilterNavigation");

                    b.Navigation("SifRisk");

                    b.Navigation("SifRiskObject");

                    b.Navigation("SifTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Spare", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "SpareMrb")
                        .WithMany("Spares")
                        .HasForeignKey("SpareMrbId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SpareMrb");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Task", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "TskClusterNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskCluster");

                    b.HasOne("AMprover.Data.Entities.AM.CommonTask", "TskCommonAction")
                        .WithMany("Tasks")
                        .HasForeignKey("TskCommonActionId");

                    b.HasOne("AMprover.Data.Entities.AM.LookupExecutor", "TskExecutorNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskExecutor");

                    b.HasOne("AMprover.Data.Entities.AM.LookupInitiator", "TskInitiatorNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskInitiator");

                    b.HasOne("AMprover.Data.Entities.AM.LookupIntervalUnit", "TskIntervalUnitNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskIntervalUnit");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "TskMrb")
                        .WithMany("Tasks")
                        .HasForeignKey("TskMrbId");

                    b.HasOne("AMprover.Data.Entities.AM.LookupMxPolicy", "TskMxPolicyNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskMxPolicy");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "TskParent")
                        .WithMany("TskChildren")
                        .HasForeignKey("TskPartOf");

                    b.HasOne("AMprover.Data.Entities.AM.SapaWorkpackage", "TskSapaWorkpackageNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskSapaWorkpackage");

                    b.HasOne("AMprover.Data.Entities.AM.Workpackage", "TskWorkpackageNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskWorkpackage");

                    b.Navigation("TskClusterNavigation");

                    b.Navigation("TskCommonAction");

                    b.Navigation("TskExecutorNavigation");

                    b.Navigation("TskInitiatorNavigation");

                    b.Navigation("TskIntervalUnitNavigation");

                    b.Navigation("TskMrb");

                    b.Navigation("TskMxPolicyNavigation");

                    b.Navigation("TskParent");

                    b.Navigation("TskSapaWorkpackageNavigation");

                    b.Navigation("TskWorkpackageNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.UserDepartment", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Vdmxl", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "Lcc")
                        .WithOne("VdmxlItem")
                        .HasForeignKey("AMprover.Data.Entities.AM.Vdmxl", "VdmLccId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Lcc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Workpackage", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupExecutor", "WpExecutorNavigation")
                        .WithMany("Workpackage")
                        .HasForeignKey("WpExecutor");

                    b.HasOne("AMprover.Data.Entities.AM.LookupIntervalUnit", "WpIntervalUnitNavigation")
                        .WithMany("Workpackage")
                        .HasForeignKey("WpIntervalUnit")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WpExecutorNavigation");

                    b.Navigation("WpIntervalUnitNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.AttachmentCategory", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvAspectSets", b =>
                {
                    b.Navigation("BvWeightingModels");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvWeightingModels", b =>
                {
                    b.Navigation("BvSiItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Cluster", b =>
                {
                    b.Navigation("ClusterChildren");

                    b.Navigation("ClusterCost");

                    b.Navigation("ClusterTaskPlan");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonCost", b =>
                {
                    b.Navigation("ClusterCost");

                    b.Navigation("CommonTaskCost");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTask", b =>
                {
                    b.Navigation("ClusterCost");

                    b.Navigation("CommonTaskCost");

                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Filters", b =>
                {
                    b.Navigation("FiltersSelectionList");

                    b.Navigation("SiLinkFilters");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Fmeca", b =>
                {
                    b.Navigation("BvWeightingModels");

                    b.Navigation("RiskObject");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lcc", b =>
                {
                    b.Navigation("Details");

                    b.Navigation("LccChildren");

                    b.Navigation("VdmxlItem");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lccdetail", b =>
                {
                    b.Navigation("EffectDetails");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lookup", b =>
                {
                    b.Navigation("Clusters");

                    b.Navigation("RiskObjects");

                    b.Navigation("Risks");

                    b.Navigation("Scenarios");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupAdditionalData", b =>
                {
                    b.Navigation("Mrbs");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupExecutor", b =>
                {
                    b.Navigation("CommonTask");

                    b.Navigation("Sapa");

                    b.Navigation("Task");

                    b.Navigation("Workpackage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupFailMode", b =>
                {
                    b.Navigation("Risks");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupInflationGroup", b =>
                {
                    b.Navigation("CommonCost");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupInitiator", b =>
                {
                    b.Navigation("CommonTask");

                    b.Navigation("Sapa");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupIntervalUnit", b =>
                {
                    b.Navigation("CommonTask");

                    b.Navigation("Task");

                    b.Navigation("Workpackage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupMxPolicy", b =>
                {
                    b.Navigation("CommonTask");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupUserDefined", b =>
                {
                    b.Navigation("Si");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mrb", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("ChildRisks");

                    b.Navigation("MrbImage");

                    b.Navigation("PickSis");

                    b.Navigation("SiLinkFilters");

                    b.Navigation("Spares");

                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Object", b =>
                {
                    b.Navigation("ChildObjects");

                    b.Navigation("ChildObjects1");

                    b.Navigation("ChildObjects2");

                    b.Navigation("ChildObjects3");

                    b.Navigation("ChildObjects4");

                    b.Navigation("ChildRiskObjects");

                    b.Navigation("MrbChildObjects");

                    b.Navigation("MrbChildObjects1");

                    b.Navigation("MrbChildObjects2");

                    b.Navigation("MrbChildObjects3");

                    b.Navigation("MrbChildObjects4");

                    b.Navigation("RiskObject");

                    b.Navigation("SiLinkFiltersSifChildObject");

                    b.Navigation("SiLinkFiltersSifChildObject1");

                    b.Navigation("SiLinkFiltersSifChildObject2");

                    b.Navigation("SiLinkFiltersSifChildObject3");

                    b.Navigation("SiLinkFiltersSifChildObject4");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexData", b =>
                {
                    b.Navigation("OpexFactor");

                    b.Navigation("OpexToLccOpexLccOpexDataId1Navigation");

                    b.Navigation("OpexToLccOpexLccOpexDataId2Navigation");

                    b.Navigation("OpexToLccOpexLccOpexDataId3Navigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Priority", b =>
                {
                    b.Navigation("ChildPriorities");

                    b.Navigation("PriorityBudgets");

                    b.Navigation("PriorityCosts");

                    b.Navigation("PriorityTasks");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityBudget", b =>
                {
                    b.Navigation("DerivedPriorityBudgets");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RamsDiagram", b =>
                {
                    b.Navigation("Rams");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RiskObject", b =>
                {
                    b.Navigation("LccItems");

                    b.Navigation("RiskObjSapa");

                    b.Navigation("Risks");

                    b.Navigation("SapaCollections");

                    b.Navigation("SiLinkFilters");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Sapa", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Years");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SapaCollection", b =>
                {
                    b.Navigation("Sapas");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SapaWorkpackage", b =>
                {
                    b.Navigation("Sapa");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SapaYear", b =>
                {
                    b.Navigation("Details");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Scenario", b =>
                {
                    b.Navigation("Clusters");

                    b.Navigation("RiskObject");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Si", b =>
                {
                    b.Navigation("BvSiItems");

                    b.Navigation("PickSi");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiLinkFilters", b =>
                {
                    b.Navigation("PickSi");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Task", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("ClusterCost");

                    b.Navigation("ClusterTaskPlan");

                    b.Navigation("SapaDetails");

                    b.Navigation("SiLinkFilters");

                    b.Navigation("TskChildren");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Workpackage", b =>
                {
                    b.Navigation("CommonTask");

                    b.Navigation("Task");
                });
#pragma warning restore 612, 618
        }
    }
}
