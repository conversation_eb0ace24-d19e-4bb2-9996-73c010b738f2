using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class Empty_Migration_for_stored_procedure_changes : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
           // This migration has manually been cleared. Stored proceure changes show up in the migration history, but they should not be executed on the database
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
           
        }
}