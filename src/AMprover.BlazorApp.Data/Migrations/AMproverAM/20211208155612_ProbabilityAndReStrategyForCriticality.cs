using Microsoft.EntityFrameworkCore.Migrations;

namespace AMprover.Data.Migrations.AMproverAM;

public partial class ProbabilityAndReStrategyForCriticality : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AddColumn<decimal>(
            "CritProbability",
            "TblCriticalityRanking",
            "decimal(18,2)",
            nullable: true);

        migrationBuilder.AddColumn<string>(
            "CritReStrategy",
            "TblCriticalityRanking",
            "nvarchar(max)",
            nullable: true);
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            "CritProbability",
            "TblCriticalityRanking");

        migrationBuilder.DropColumn(
            "CritReStrategy",
            "TblCriticalityRanking");
    }
}