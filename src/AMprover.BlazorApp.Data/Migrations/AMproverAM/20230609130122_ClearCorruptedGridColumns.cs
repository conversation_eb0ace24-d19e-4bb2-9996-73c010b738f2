using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class ClearCorruptedGridColumns : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            // CommonTaskModel.cs did not contains a reference to IntervalUnitModel.cs, fixing this caused the saved GridColumns to be corrupt.
            migrationBuilder.Sql(
                @"DELETE FROM [LookupGridColumn] 
                  WHERE controlName = 'Common_Actions'
                  GO");
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {

        }
}