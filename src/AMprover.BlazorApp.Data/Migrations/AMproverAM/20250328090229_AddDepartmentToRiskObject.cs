using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations;

public partial class AddDepartmentToRiskObject : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AddColumn<int>(
                name: "RiskObjDepartmentId",
                table: "TblRiskObject",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TblRiskObject_RiskObjDepartmentId",
                table: "TblRiskObject",
                column: "RiskObjDepartmentId");

            migrationBuilder.AddForeignKey(
                name: "FK_TblRiskObject_TblDepartment_RiskObjDepartmentId",
                table: "TblRiskObject",
                column: "RiskObjDepartmentId",
                principalTable: "TblDepartment",
                principalColumn: "DepID");
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropForeignKey(
                name: "FK_TblRiskObject_TblDepartment_RiskObjDepartmentId",
                table: "TblRiskObject");

            migrationBuilder.DropIndex(
                name: "IX_TblRiskObject_RiskObjDepartmentId",
                table: "TblRiskObject");

            migrationBuilder.DropColumn(
                name: "RiskObjDepartmentId",
                table: "TblRiskObject");
        }
}