using Microsoft.EntityFrameworkCore.Migrations;

namespace AMprover.Data.Migrations;

public partial class setpicksitblname : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // TODO: Validate this renaming was correct and then removed the commented out code

        //migrationBuilder.RenameTable(
        //    name: "PickSI",
        //    newName: "TblPickSI");

        //migrationBuilder.RenameIndex(
        //    name: "IX_PickSI_PckSiSiID",
        //    table: "TblPickSI",
        //    newName: "IX_TblPickSI_PckSiSiID");

        //migrationBuilder.RenameIndex(
        //    name: "IX_PickSI_PckSiMrbID",
        //    table: "TblPickSI",
        //    newName: "IX_TblPickSI_PckSiMrbID");

        //migrationBuilder.RenameIndex(
        //    name: "IX_PickSI_PckSiLinkFilterID",
        //    table: "TblPickSI",
        //    newName: "IX_TblPickSI_PckSiLinkFilterID");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        //migrationBuilder.RenameTable(
        //    name: "TblPickSI",
        //    newName: "PickSI");

        //migrationBuilder.RenameIndex(
        //    name: "IX_TblPickSI_PckSiSiID",
        //    table: "PickSI",
        //    newName: "IX_PickSI_PckSiSiID");

        //migrationBuilder.RenameIndex(
        //    name: "IX_TblPickSI_PckSiMrbID",
        //    table: "PickSI",
        //    newName: "IX_PickSI_PckSiMrbID");

        //migrationBuilder.RenameIndex(
        //    name: "IX_TblPickSI_PckSiLinkFilterID",
        //    table: "PickSI",
        //    newName: "IX_PickSI_PckSiLinkFilterID");
    }
}