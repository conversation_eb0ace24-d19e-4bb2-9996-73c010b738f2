using Microsoft.EntityFrameworkCore.Migrations;

namespace AMprover.Data.Migrations;

public partial class Initial : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        //Empty because if existing databases
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            "Amprover3_EditLog");

        migrationBuilder.DropTable(
            "Amprover3_Mrb_ID");

        migrationBuilder.DropTable(
            "Amprover3_PickClusterSI");

        migrationBuilder.DropTable(
            "Amprover3_PickSI");

        migrationBuilder.DropTable(
            "Amprover3_SiLinkFilters");

        migrationBuilder.DropTable(
            "Company");

        migrationBuilder.DropTable(
            "DbScriptsExecuted");

        migrationBuilder.DropTable(
            "DerModified");

        migrationBuilder.DropTable(
            "Descriptions");

        migrationBuilder.DropTable(
            "EditLog");

        migrationBuilder.DropTable(
            "FiltersSelectionList");

        migrationBuilder.DropTable(
            "FmecaSelect");

        migrationBuilder.DropTable(
            "LCC");

        migrationBuilder.DropTable(
            "LCCDetail");

        migrationBuilder.DropTable(
            "LCCEffectDetail");

        migrationBuilder.DropTable(
            "Login");

        migrationBuilder.DropTable(
            "Lookup");

        migrationBuilder.DropTable(
            "LookupFailCat");

        migrationBuilder.DropTable(
            "LookupFailMode");

        migrationBuilder.DropTable(
            "LookupGridColumn");

        migrationBuilder.DropTable(
            "LookupSettings");

        migrationBuilder.DropTable(
            "LookupUserDefined");

        migrationBuilder.DropTable(
            "Mca");

        migrationBuilder.DropTable(
            "MrbImage");

        migrationBuilder.DropTable(
            "OpexFactor");

        migrationBuilder.DropTable(
            "OpexToLCC");

        migrationBuilder.DropTable(
            "OpexToLccDetail");

        migrationBuilder.DropTable(
            "PickSI");

        migrationBuilder.DropTable(
            "Priority");

        migrationBuilder.DropTable(
            "PriorityBudget");

        migrationBuilder.DropTable(
            "PriorityCost");

        migrationBuilder.DropTable(
            "PriorityTask");

        migrationBuilder.DropTable(
            "PriorityVersion");

        migrationBuilder.DropTable(
            "Rams");

        migrationBuilder.DropTable(
            "SiStatistics");

        migrationBuilder.DropTable(
            "TblBvRelevanceSets");

        migrationBuilder.DropTable(
            "TblBvSiItems");

        migrationBuilder.DropTable(
            "TblClusterCost");

        migrationBuilder.DropTable(
            "TblClusterTaskPlan");

        migrationBuilder.DropTable(
            "TblCommonTaskCost");

        migrationBuilder.DropTable(
            "TblSpare");

        migrationBuilder.DropTable(
            "User");

        migrationBuilder.DropTable(
            "OpexData");

        migrationBuilder.DropTable(
            "SiLinkFilters");

        migrationBuilder.DropTable(
            "RamsDiagram");

        migrationBuilder.DropTable(
            "BvWeightingModels");

        migrationBuilder.DropTable(
            "TblSi");

        migrationBuilder.DropTable(
            "TblCommonCost");

        migrationBuilder.DropTable(
            "Filters");

        migrationBuilder.DropTable(
            "TblTask");

        migrationBuilder.DropTable(
            "TblBvAspectSets");

        migrationBuilder.DropTable(
            "LookupInflationGroup");

        migrationBuilder.DropTable(
            "TblCluster");

        migrationBuilder.DropTable(
            "TblCommonTask");

        migrationBuilder.DropTable(
            "TblMRB");

        migrationBuilder.DropTable(
            "LookupInitiator");

        migrationBuilder.DropTable(
            "LookupMxPolicy");

        migrationBuilder.DropTable(
            "TblWorkPackage");

        migrationBuilder.DropTable(
            "TblRiskObject");

        migrationBuilder.DropTable(
            "LookupExecutor");

        migrationBuilder.DropTable(
            "LookupIntervalUnit");

        migrationBuilder.DropTable(
            "TblFmeca");

        migrationBuilder.DropTable(
            "TblObject");

        migrationBuilder.DropTable(
            "TblScenario");

        migrationBuilder.DropTable(
            "TblDepartment");
    }
}