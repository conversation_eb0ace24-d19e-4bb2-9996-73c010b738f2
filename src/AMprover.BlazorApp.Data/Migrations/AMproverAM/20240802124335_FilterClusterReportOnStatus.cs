using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb;

public partial class FilterClusterReportOnStatus : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            var sql = @"
	 	    	IF OBJECT_ID('GetClusterReport', 'P') IS NOT NULL
                DROP PROC GetClusterReport
                GO
     
                CREATE PROCEDURE [dbo].[GetClusterReport]
                @FilterChildObject0 int = NULL,
                @FilterChildObject1 int = NULL,
                @FilterChildObject2 int = NULL,
                @FilterScenario int = NULL,
				@FilterStatus int = NULL,
                @FilterClustName NVARCHAR(100)
                AS
                BEGIN
                    SET NOCOUNT ON;
					SELECT ClustID as ClustId, 
                    ClustName, 
                    ClustDescription, 
                    ClustScenarioID,
                    ClustLevel, 
                    ClustPartOf, 
                    ClustInterval, 
                    ClustIntervalUnit, 
                    ClustInitiator, 
                    ClustExecutor, 
                    ClustEstTaskCosts, 
                    ClustTaskCosts, 
                    ClustSharedCosts, 
                    (Select SUM(ClcCost) FROM TblClusterCost WHERE ClcType = 'Discipline' AND ClcClusterID = ClustID) as ClustDisciplineCosts, 
                    (Select SUM(ClcCost) FROM TblClusterCost WHERE ClcType = 'Material' AND ClcClusterID = ClustID) as ClustMaterialCosts, 
                    (Select SUM(ClcCost) FROM TblClusterCost WHERE ClcType = 'Energy' AND ClcClusterID = ClustID) as ClustEnergyCosts, 
                    (Select SUM(ClcCost) FROM TblClusterCost WHERE ClcType = 'Tool' AND ClcClusterID = ClustID) as ClustToolCosts, 
                    ClustTotalCmnCost, 
                    ClustDownTime, 
                    ClustDuration, 
                    ClustStatus, 
                    ClustShortKey, 
                    ClustRemark, 
                    ClustResponsible, 
                    ClustSecondValues, 
                    ClustCostDescr, 
                    ClustCostTaskID as ClustCostTaskId, 
                    ClustCostQuantity, 
                    ClustCostUnits, 
                    ClustCostPrice, 
                    ClustCostCost, 
                    ClustCostRemarks, 
                    TskID as TskId, 
                    TskName, 
                    TskGeneralDescription, 
                    TskRemark, 
                    TskDescription, 
                    TskInterval, 
                    TskIntervalUnit, 
                    TskWorkPackage, 
                    TskPolicy, 
                    TskExecutor, 
                    TskInitiator, 
                    TskMrbID as TskMrbId, 
                    TskCosts, 
                    TskDuration, 
                    TskDownTime, 
                    TskType, 
                    TskCostTskID as TskCostTskId, 
                    TskCostUnits, 
                    TskCostQuantity, 
                    TskCostPrice, 
                    TskCostCost, 
                    TskCostDescription, 
                    Scenario, 
                    RiskObject, 
                    MrbChildObject,
				    MrbChildObject1,
				    MrbChildObject2,
                    MrbObject0, 
                    MrbObject2, 
                    MrbObject3, 
                    MrbObject4, 
                    ClcID as ClcId, 
                    CmnCostType, 
                    RiskObjName, 
                    RiskObjectDesc, 
                    TskSortOrder, 
                    ParentClusterName, 
                    TskRemoved, 
                    TskDerived, 
                    TskPermit, 
                    ClustName0, 
                    ClustName1, 
                    ClustName2, 
                    ClustName3,
                    MrbFailureCause,
                    MrbFailureConsequences,
                    MrbID as MrbId
                    FROM ClusterReport As Cluster
					WHERE (@FilterScenario IS NULL OR ClustScenarioID = @FilterScenario)
                    AND (@FilterChildObject0 IS NULL OR MrbChildObject = @FilterChildObject0)
                    AND (@FilterChildObject1 IS NULL OR MrbChildObject1 = @FilterChildObject1)
                    AND (@FilterChildObject2 IS NULL OR MrbChildObject2 = @FilterChildObject2)
					AND (@FilterStatus IS NULL OR Cluster.ClustStatus = @FilterStatus)
                    AND (@FilterClustName IS NULL OR Cluster.ClustName LIKE '%' +  @FilterClustName + '%' )
                END";

            migrationBuilder.Sql(sql);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {

        }
}