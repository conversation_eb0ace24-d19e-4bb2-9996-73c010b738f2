// <auto-generated />
using System;
using AMprover.Data.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace AMprover.Data.Migrations
{
    [DbContext(typeof(AssetManagementDbContext))]
    [Migration("20210309101352_LccEffectDetailsConfiguration")]
    partial class LccEffectDetailsConfiguration
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "3.1.9")
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("AMprover.Data.Entities.AM.Amprover3EditLog", b =>
                {
                    b.Property<DateTime>("LogDate")
                        .HasColumnType("datetime");

                    b.Property<int>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("LogID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("LogModificationType")
                        .HasColumnType("varchar(10)")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.Property<string>("LogModifications")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<int?>("LogObjectId")
                        .HasColumnName("LogObjectID")
                        .HasColumnType("int");

                    b.Property<string>("LogObjectType")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("LogObjectVarId")
                        .HasColumnName("LogObjectVarID")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("LogUserId")
                        .HasColumnName("LogUserID")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.ToTable("Amprover3_EditLog");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Amprover3MrbId", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("ID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("MrbId")
                        .HasColumnType("int");

                    b.ToTable("Amprover3_Mrb_ID");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Amprover3PickClusterSi", b =>
                {
                    b.Property<int?>("PckClClusterId")
                        .HasColumnName("PckClClusterID")
                        .HasColumnType("int");

                    b.Property<int>("PckClId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PckClID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("PckClSiCategory")
                        .HasColumnType("int");

                    b.Property<int?>("PckClSiId")
                        .HasColumnName("PckClSiID")
                        .HasColumnType("int");

                    b.Property<string>("PckClSiType")
                        .HasColumnType("varchar(50)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("PckClTaskId")
                        .HasColumnName("PckClTaskID")
                        .HasColumnType("int");

                    b.ToTable("Amprover3_PickClusterSI");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Amprover3PickSi", b =>
                {
                    b.Property<bool>("PckSiActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("PckSiDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PckSiDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("PckSiDescription")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<DateTime?>("PckSiExecutionYear")
                        .HasColumnType("datetime");

                    b.Property<bool?>("PckSiExpand")
                        .HasColumnType("bit");

                    b.Property<int>("PckSiId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PckSiID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("PckSiInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("PckSiItems")
                        .HasColumnType("int");

                    b.Property<int?>("PckSiLinkFilterId")
                        .HasColumnName("PckSiLinkFilterID")
                        .HasColumnType("int");

                    b.Property<string>("PckSiModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int>("PckSiMrbId")
                        .HasColumnName("PckSiMrbID")
                        .HasColumnType("int");

                    b.Property<string>("PckSiReferenceId")
                        .HasColumnName("PckSiReferenceID")
                        .HasColumnType("varchar(50)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("PckSiSiCategory")
                        .HasColumnType("int");

                    b.Property<int>("PckSiSiId")
                        .HasColumnName("PckSiSiID")
                        .HasColumnType("int");

                    b.Property<string>("PckSiSiType")
                        .HasColumnType("varchar(50)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("PckSiTaskId")
                        .HasColumnType("int");

                    b.Property<bool?>("PckSiXoutofN")
                        .HasColumnType("bit");

                    b.ToTable("Amprover3_PickSI");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Amprover3SiLinkFilters", b =>
                {
                    b.Property<int?>("SifCategory")
                        .HasColumnType("int");

                    b.Property<int?>("SifCompactCatId")
                        .HasColumnName("SifCompactCatID")
                        .HasColumnType("int");

                    b.Property<bool?>("SifCompactDetail")
                        .HasColumnType("bit");

                    b.Property<string>("SifDescription")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<string>("SifFilter")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<string>("SifGroup")
                        .HasColumnType("varchar(50)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int>("SifId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("SifID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool>("SifInheritParent")
                        .HasColumnType("bit");

                    b.Property<string>("SifName")
                        .HasColumnType("varchar(50)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("SifObjectId")
                        .HasColumnName("SifObjectID")
                        .HasColumnType("int");

                    b.Property<int?>("SifParentId")
                        .HasColumnName("SifParentID")
                        .HasColumnType("int");

                    b.Property<int?>("SifRiskId")
                        .HasColumnName("SifRiskID")
                        .HasColumnType("int");

                    b.Property<string>("SifSubGroup")
                        .HasColumnType("varchar(50)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("SifTaskId")
                        .HasColumnName("SifTaskID")
                        .HasColumnType("int");

                    b.Property<int>("SifType")
                        .HasColumnType("int");

                    b.ToTable("Amprover3_SiLinkFilters");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvAspectSets", b =>
                {
                    b.Property<int>("BvAspSetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("BvAspSetID")
                        .HasColumnType("int")
                        .HasComment("Unique ID")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("BvAspAspects")
                        .HasColumnType("varchar(max)")
                        .HasComment("Unique aspects stored in XML. Stores the name, weight factor and the bound relevance set of the aspect.")
                        .IsUnicode(false);

                    b.Property<DateTime?>("BvAspDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvAspModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("BvAspSetName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the aspect set")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.HasKey("BvAspSetId")
                        .HasName("BvAspectID");

                    b.ToTable("TblBvAspectSets");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvRelevanceSets", b =>
                {
                    b.Property<int>("BvRelSetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("BvRelSetID")
                        .HasColumnType("int")
                        .HasComment("Unique ID")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("BvRelDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvRelModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("BvRelRelevances")
                        .HasColumnType("varchar(max)")
                        .HasComment("Unique relevances stored in XML. Stores the name and the appreciation of the relevance.")
                        .IsUnicode(false);

                    b.Property<string>("BvRelSetName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the relevance set")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.HasKey("BvRelSetId");

                    b.ToTable("TblBvRelevanceSets");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvSiItems", b =>
                {
                    b.Property<int>("BvId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("BvID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("BvBusinessvalue")
                        .HasColumnType("int");

                    b.Property<DateTime?>("BvDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("BvPerEffectSet")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<string>("BvRelevanceSelectSet")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<int>("BvSiId")
                        .HasColumnName("BvSiID")
                        .HasColumnType("int");

                    b.Property<int?>("BvWeightingModelId")
                        .HasColumnName("BvWeightingModelID")
                        .HasColumnType("int");

                    b.HasKey("BvId");

                    b.HasIndex("BvSiId")
                        .IsUnique()
                        .HasName("IX_BvSiItems");

                    b.HasIndex("BvWeightingModelId");

                    b.ToTable("TblBvSiItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvWeightingModels", b =>
                {
                    b.Property<int>("BvModelId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("BvModelID")
                        .HasColumnType("int")
                        .HasComment("Unique ID")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("BvModelAspectSetId")
                        .HasColumnName("BvModelAspectSetID")
                        .HasColumnType("int")
                        .HasComment("ID of the aspect set used for this weighting model.");

                    b.Property<DateTime?>("BvModelDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvModelEffectWeightSet")
                        .HasColumnType("varchar(max)")
                        .HasComment("Effect weight set stored in XML. Stores the aspect, effect (FMECA) column and the given value of the effect weight set.")
                        .IsUnicode(false);

                    b.Property<int?>("BvModelFmecaId")
                        .HasColumnName("BvModelFmecaID")
                        .HasColumnType("int")
                        .HasComment("ID of the FMECA matrix used for this weighting model.");

                    b.Property<string>("BvModelModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("BvModelName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the weighting model")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.HasKey("BvModelId")
                        .HasName("PK_BvWeightingModels");

                    b.HasIndex("BvModelAspectSetId");

                    b.HasIndex("BvModelFmecaId");

                    b.ToTable("BvWeightingModels");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Cluster", b =>
                {
                    b.Property<int>("ClustId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("ClustID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of Cluster)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("ClustDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("ClustDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("ClustDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the cluster")
                        .IsUnicode(false);

                    b.Property<decimal?>("ClustDisciplineCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs for disciplines that are essential for execution of the cluster (? what are disciplines in this context?)");

                    b.Property<bool?>("ClustDivideDownTime")
                        .HasColumnType("bit")
                        .HasComment("A bit used for the way the downtime is devided. false :The input in the cluster is devided over the tasks. True duration is the sum of the downtime in the tasks");

                    b.Property<bool?>("ClustDivideDuration")
                        .HasColumnType("bit")
                        .HasComment("A bit used for the way the duration is devided. false :The input in the cluster is devided over the tasks. True duration is the sum of the duration in the tasks");

                    b.Property<decimal?>("ClustDownTime")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Downtime needed to complete cluster of tasks (in hours)");

                    b.Property<decimal?>("ClustDuration")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Total time spent on cluster (in hours)");

                    b.Property<decimal?>("ClustEnergyCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Estimated energy costs made during execution of the cluster");

                    b.Property<decimal?>("ClustEstTaskCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Estimated task costs (?)");

                    b.Property<int?>("ClustExecutor")
                        .HasColumnType("int")
                        .HasComment("ID of the executor (FK to LookupExecutor)");

                    b.Property<string>("ClustInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Username of person that created this cluster")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("ClustInitiator")
                        .HasColumnType("int")
                        .HasComment("ID of the initiator (FK to LookupInitiator)");

                    b.Property<bool?>("ClustInterruptable")
                        .HasColumnType("bit")
                        .HasComment("Allow the cluster to be paused.");

                    b.Property<decimal?>("ClustInterval")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Cluster interval, interval at which the cluster will be executed");

                    b.Property<int?>("ClustIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("Interval unit ID (FK to LookupIntervalUnit)");

                    b.Property<int>("ClustLevel")
                        .HasColumnType("int")
                        .HasComment("Describes at what level of the tree the cluster is situated.");

                    b.Property<string>("ClustLocation")
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique (technical) identifier of the related location")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<decimal?>("ClustMaterialCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs of materials that are essential for execution of the cluster");

                    b.Property<string>("ClustModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("ClustName")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of cluster")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("ClustOrgId")
                        .HasColumnName("ClustOrgID")
                        .HasColumnType("varchar(12)")
                        .HasComment("Organisation to which the cluster is bound")
                        .HasMaxLength(12)
                        .IsUnicode(false);

                    b.Property<int?>("ClustPartOf")
                        .HasColumnType("int")
                        .HasComment("Cluster ID of the parent cluster (FK to self, Cluster)");

                    b.Property<int?>("ClustPriority")
                        .HasColumnType("int")
                        .HasComment("Priority of the cluster");

                    b.Property<string>("ClustReferenceId")
                        .HasColumnName("ClustReferenceID")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<string>("ClustRemark")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks allow for some extra information regarding this cluster")
                        .IsUnicode(false);

                    b.Property<string>("ClustResponsible")
                        .HasColumnType("varchar(max)")
                        .HasComment("People/departments who are responsible for execution of the cluster")
                        .IsUnicode(false);

                    b.Property<int?>("ClustRiskObjectId")
                        .HasColumnName("ClustRiskObjectID")
                        .HasColumnType("int")
                        .HasComment("Risk Object ID the cluster is referring to (FK to RiskObject)");

                    b.Property<int?>("ClustScenarioId")
                        .HasColumnName("ClustScenarioID")
                        .HasColumnType("int")
                        .HasComment("Scenario ID the cluster is a part of (FK to Scenario)");

                    b.Property<string>("ClustSecondValues")
                        .HasColumnType("varchar(max)")
                        .HasComment("?")
                        .IsUnicode(false);

                    b.Property<int?>("ClustSequence")
                        .HasColumnType("int")
                        .HasComment("Sequence of the relationship between clusters.");

                    b.Property<decimal?>("ClustSharedCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("? Costs that are shared by items that belong to this cluster");

                    b.Property<int?>("ClustShiftEndDate")
                        .HasColumnType("int")
                        .HasComment("Amount of days that the end date of the cluster is shifted");

                    b.Property<int?>("ClustShiftStartDate")
                        .HasColumnType("int")
                        .HasComment("Amount of days that the start date of the cluster is shifted");

                    b.Property<string>("ClustShortKey")
                        .HasColumnType("varchar(10)")
                        .HasComment("Short key of the cluster (needs to be unique within Cluster)")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.Property<string>("ClustSiteId")
                        .HasColumnName("ClustSiteID")
                        .HasColumnType("varchar(12)")
                        .HasComment("SiteID of the asset")
                        .HasMaxLength(12)
                        .IsUnicode(false);

                    b.Property<int?>("ClustStatus")
                        .HasColumnType("int")
                        .HasComment("Cluster status, user can enter any string for status.  (? status domain --> There is no status domain, should there be one ?) ");

                    b.Property<decimal?>("ClustTaskCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Actual task costs for this cluster");

                    b.Property<string>("ClustTemplateType")
                        .HasColumnType("varchar(30)")
                        .HasComment("The template type of the cluster")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<decimal?>("ClustToolCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs for tools needed for completion of the cluster");

                    b.Property<decimal?>("ClustTotalCmnCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("? Total costs common to this cluster (?)");

                    b.Property<int?>("ClustWorkpackageId")
                        .HasColumnName("ClustWorkpackageID")
                        .HasColumnType("int")
                        .HasComment("Workpackage ID the cluster is a part of (FK to Workpackage)");

                    b.HasKey("ClustId");

                    b.HasIndex("ClustPartOf")
                        .HasName("IX_ClusterPartOf");

                    b.ToTable("TblCluster");

                    b.HasComment("A cluster is a group of related preventive tasks, that can be grouped for entry into a maintenance information system. Most of the values stored in this table can be set from the ClusterControl. Some values get recalculated within SyncClusterData.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterCost", b =>
                {
                    b.Property<int>("ClcId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("ClcID")
                        .HasColumnType("int")
                        .HasComment("Unique ID")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("ClcCalculationType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(8)")
                        .HasDefaultValueSql("('PxN')")
                        .HasComment("The way the common cost are calculated")
                        .HasMaxLength(8)
                        .IsUnicode(false);

                    b.Property<int?>("ClcClusterId")
                        .HasColumnName("ClcClusterID")
                        .HasColumnType("int")
                        .HasComment("ID of the cluster to which the cluster cost is bound (Cluster)");

                    b.Property<int>("ClcCommonCostId")
                        .HasColumnName("ClcCommonCostID")
                        .HasColumnType("int")
                        .HasComment("ID of the common cost to which the cluster cost is bound (CommonCost)");

                    b.Property<decimal>("ClcCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost of the common cost");

                    b.Property<bool?>("ClcIsCommonTaskCost")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean to see if the cluster cost are referenced to a task");

                    b.Property<decimal>("ClcPrice")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Price of the common cost");

                    b.Property<int?>("ClcPriceIndexYear")
                        .HasColumnType("int")
                        .HasComment("The year in which the cluster cost are indexed");

                    b.Property<decimal?>("ClcQuantity")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Quantity of the common cost");

                    b.Property<string>("ClcRemarks")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the common cost")
                        .IsUnicode(false);

                    b.Property<int?>("ClcTaskId")
                        .HasColumnName("ClcTaskID")
                        .HasColumnType("int")
                        .HasComment("ID of the tasks to which the cluster cost is bound (Task)");

                    b.Property<string>("ClcType")
                        .HasColumnType("varchar(20)")
                        .HasComment("The Type of cluster cost")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<decimal?>("ClcUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Number of units of the common cost");

                    b.HasKey("ClcId")
                        .HasName("PK_PickClusterCost");

                    b.HasIndex("ClcClusterId");

                    b.HasIndex("ClcCommonCostId");

                    b.HasIndex("ClcTaskId");

                    b.ToTable("TblClusterCost");

                    b.HasComment("Clustercosts are costs specific to a cluster. They can be defined ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterTaskPlan", b =>
                {
                    b.Property<int>("CltpId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("CltpID")
                        .HasColumnType("int")
                        .HasComment("Unique ID")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<decimal?>("CltpClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("?");

                    b.Property<decimal?>("CltpClusterCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cluster cost for the cluster task plan");

                    b.Property<int?>("CltpClusterId")
                        .HasColumnName("CltpClusterID")
                        .HasColumnType("int")
                        .HasComment("Cluster ID to which the cluster task plan is referenced (Cluster)");

                    b.Property<int?>("CltpCommonTaskId")
                        .HasColumnName("CltpCommonTaskID")
                        .HasColumnType("int")
                        .HasComment("Common task ID to which the cluster task plan is referenced (CommonTask)");

                    b.Property<DateTime?>("CltpDateExecuted")
                        .HasColumnType("datetime")
                        .HasComment("Date when the cluster task plan is executed");

                    b.Property<DateTime?>("CltpDateGenerated")
                        .HasColumnType("datetime")
                        .HasComment("Date when the cluster task plan is generated");

                    b.Property<decimal?>("CltpDownTime")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Downtime needed in hours");

                    b.Property<decimal?>("CltpDuration")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Time spend in hours");

                    b.Property<int?>("CltpExecuteStatus")
                        .HasColumnType("int")
                        .HasComment("Execute status of the cluster task plan");

                    b.Property<DateTime?>("CltpExecutionDate")
                        .HasColumnType("datetime")
                        .HasComment("Date when the cluster task plan must be executed");

                    b.Property<bool?>("CltpInterruptable")
                        .HasColumnType("bit")
                        .HasComment("Allow the task plan to be paused.");

                    b.Property<int?>("CltpObjectId")
                        .HasColumnName("CltpObjectID")
                        .HasColumnType("int")
                        .HasComment("Object ID to which the cluster task plan is referenced (Object)");

                    b.Property<int?>("CltpPriority")
                        .HasColumnType("int")
                        .HasComment("Priority of the cluster task plan");

                    b.Property<int?>("CltpQualityScore")
                        .HasColumnType("int")
                        .HasComment("?");

                    b.Property<string>("CltpReferenceId")
                        .HasColumnName("CltpReferenceID")
                        .HasColumnType("varchar(50)")
                        .HasComment("?")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("CltpRemarks")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the cluster task plan")
                        .IsUnicode(false);

                    b.Property<int?>("CltpRiskId")
                        .HasColumnName("CltpRiskID")
                        .HasColumnType("int")
                        .HasComment("Risk ID to which the cluster task plan is referenced (MRB)");

                    b.Property<int?>("CltpSequence")
                        .HasColumnType("int")
                        .HasComment("Sequence of the relationship between cluster task plans.");

                    b.Property<int?>("CltpShiftEndDate")
                        .HasColumnType("int")
                        .HasComment("Amount of days that the end date of the cluster task plan is shifted");

                    b.Property<int?>("CltpShiftStartDate")
                        .HasColumnType("int")
                        .HasComment("Amount of days that the start date of the cluster task plan is shifted");

                    b.Property<int?>("CltpSiId")
                        .HasColumnName("CltpSiID")
                        .HasColumnType("int")
                        .HasComment("Significant item ID to which the cluster task plan is referenced (Cluster)");

                    b.Property<decimal?>("CltpSiUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("?");

                    b.Property<int?>("CltpSlack")
                        .HasColumnType("int")
                        .HasComment("Interval of the slack for the cluster task plan");

                    b.Property<int?>("CltpSlackIntervalType")
                        .HasColumnType("int")
                        .HasComment("Interval unit ID of the slack for the cluster task plan (LookupIntervalUnit)");

                    b.Property<int?>("CltpTaskId")
                        .HasColumnName("CltpTaskID")
                        .HasColumnType("int")
                        .HasComment("Task ID to which the cluster task plan is referenced (Task)");

                    b.Property<decimal?>("CltpToolCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Tool cost for the cluster task plan");

                    b.Property<bool?>("CltpUseLastDateExecuted")
                        .HasColumnType("bit")
                        .HasComment("Use the last date executed as executing date for the cluster task plan");

                    b.HasKey("CltpId");

                    b.HasIndex("CltpClusterId");

                    b.HasIndex("CltpRiskId");

                    b.HasIndex("CltpSiId");

                    b.HasIndex("CltpTaskId");

                    b.ToTable("TblClusterTaskPlan");

                    b.HasComment(@"Cluster task plans contain groups of preventive measures, that can be imported into a maintenance information system. 

The cluster task plans are generated from the clusters. Data in this table is generated using data from tables Cluster, ClusterCost, Task, PriorityTask, Mrb, and RiskObject.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonCost", b =>
                {
                    b.Property<int>("CmnCostId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("CmnCostID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of CommonCost)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CmnCostCalculationType")
                        .HasColumnType("varchar(10)")
                        .HasComment("The way the common cost must be used in calculations (P, PxN, PxNxU)")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.Property<DateTime?>("CmnCostDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("CmnCostDescription")
                        .IsRequired()
                        .HasColumnType("varchar(40)")
                        .HasComment("Descriptive name of the common cost. This value is used for selecting the common costs in the screens that use them.")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<decimal?>("CmnCostExtraCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Extra cost for the common cost (currently not in use)");

                    b.Property<string>("CmnCostModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<decimal?>("CmnCostNumber")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Quantity number, used in calculation of costs. (when calculation type is not set to price-only)");

                    b.Property<string>("CmnCostOrgId")
                        .HasColumnName("CmnCostOrgID")
                        .HasColumnType("varchar(12)")
                        .HasComment("Organisation to which the common cost is bound")
                        .HasMaxLength(12)
                        .IsUnicode(false);

                    b.Property<decimal?>("CmnCostPrice")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Price of one item. Is always used for price calculation.");

                    b.Property<int?>("CmnCostPriceGroup")
                        .HasColumnType("int")
                        .HasComment("Inflation group ID to which the common cost are bound. (FK to LookupUserDefined)");

                    b.Property<int?>("CmnCostPriceIndexYear")
                        .HasColumnType("int")
                        .HasComment("The year in which the common cost were indexed");

                    b.Property<string>("CmnCostReferenceCode")
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique reference code or ID (customer)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("CmnCostRemarks")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks field for common costs. ")
                        .IsUnicode(false);

                    b.Property<bool?>("CmnCostRotating")
                        .HasColumnType("bit")
                        .HasComment("Boolean to set the common cost as a rotating item");

                    b.Property<string>("CmnCostShortKey")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasComment("Short key of the common cost. Usually takes the first character of the CmnCostType. (?)")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<bool?>("CmnCostSpare")
                        .HasColumnType("bit")
                        .HasComment("Boolean to set the common cost as a spare part");

                    b.Property<int?>("CmnCostStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the common cost");

                    b.Property<DateTime?>("CmnCostStatusDate")
                        .HasColumnType("datetime")
                        .HasComment("Date the status was last modified.");

                    b.Property<string>("CmnCostSubSubType")
                        .HasColumnType("varchar(20)")
                        .HasComment("Extra added sub sub type as a search field")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("CmnCostSubType")
                        .HasColumnType("varchar(20)")
                        .HasComment("Extra added sub type as a search field")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("CmnCostType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValueSql("('PxN')")
                        .HasComment("Common cost type domain. Specifies what caused the costs. (tools, disciplines, energy, etc.)")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("CmnCostUnitType")
                        .HasColumnType("varchar(10)")
                        .HasComment("The unit type of the common cost. Not currently used by AMprover software. (?)")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.Property<decimal?>("CmnCostUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Number of units, will be used in cost calculation (when calculation type is PxNxU)");

                    b.Property<string>("CmnCostVendorCode")
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique reference code or ID of the vendor")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.HasKey("CmnCostId")
                        .HasName("PK_ClusterCosts");

                    b.HasIndex("CmnCostPriceGroup");

                    b.ToTable("TblCommonCost");

                    b.HasComment("Master data table. Common costs define recurring costs, that are used for cost calculations. They define different costs for different types of items. (like materials, tools or use of specific disciplines.) Common costs can be added to common actions and clusters.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTask", b =>
                {
                    b.Property<int>("CmnTaskId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("CmnTaskID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of CommonTask)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool?>("CmnTaskCostModifiable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean that enables modification of the common task costs during risk analysis");

                    b.Property<decimal?>("CmnTaskCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost of common task execution. (Cost per unit?) Is named action costs in the AMprover software.");

                    b.Property<DateTime?>("CmnTaskDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("CmnTaskDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("CmnTaskDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("Description for the common task")
                        .IsUnicode(false);

                    b.Property<decimal?>("CmnTaskDownTime")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Downtime needed to complete the task (in hours)");

                    b.Property<decimal?>("CmnTaskDuration")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Time spent on task. Does not seem to be set from the AMprover software. (?)");

                    b.Property<int?>("CmnTaskExecutor")
                        .HasColumnType("int")
                        .HasComment("Executor ID of the common task (FK to LookupExecutor)");

                    b.Property<bool?>("CmnTaskExecutorModifiable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean that enables modification of the common task executor during risk analysis");

                    b.Property<string>("CmnTaskFieldRights")
                        .HasColumnType("varchar(max)")
                        .HasComment("? Is not set by the AMprover software")
                        .IsUnicode(false);

                    b.Property<string>("CmnTaskGeneralDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("General description for the common task. Does not seem to be filled by the AMprover software. (?)")
                        .IsUnicode(false);

                    b.Property<string>("CmnTaskInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Username of person that created this cluster")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("CmnTaskInitiator")
                        .HasColumnType("int")
                        .HasComment("Initiator ID of the common task (FK to LookupInitiator)");

                    b.Property<bool?>("CmnTaskInitiatorModifiable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean that enables modification of the common task initiator during risk analysis");

                    b.Property<decimal?>("CmnTaskInterval")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 4)")
                        .HasDefaultValueSql("((1))")
                        .HasComment("Interval of the common task. The common task needs to be executed each time this interval passes. ");

                    b.Property<bool?>("CmnTaskIntervalModifiable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean that enables modification of the interval during risk analysis");

                    b.Property<int?>("CmnTaskIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("ID of the the interval unit of the common task (FK to LookupIntervalUnit)");

                    b.Property<int?>("CmnTaskMasterId")
                        .HasColumnName("CmnTaskMasterID")
                        .HasColumnType("int")
                        .HasComment("ID of the master common task (FK to CommonTask) Does not seem to be set by the AMprover software (?)");

                    b.Property<string>("CmnTaskModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("CmnTaskMxPolicy")
                        .HasColumnType("int")
                        .HasComment("Maintenance policy ID of the common task (FK to LookupMxPolicy)");

                    b.Property<string>("CmnTaskName")
                        .IsRequired()
                        .HasColumnType("varchar(40)")
                        .HasComment("The name of the common task")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("CmnTaskPermit")
                        .HasColumnType("char(10)")
                        .IsFixedLength(true)
                        .HasComment("Permit that is needed for the common task. Never set in the AMprover software.")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.Property<int?>("CmnTaskPriorityCode")
                        .HasColumnType("int")
                        .HasComment("Priority code of common task. Is not set by the AMprover software (?)");

                    b.Property<string>("CmnTaskReferenceId")
                        .HasColumnName("CmnTaskReferenceID")
                        .HasColumnType("varchar(30)")
                        .HasComment("Reference ID of the common task. Not bound to anything, user can enter any string. (?)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("CmnTaskRemark")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the common task. Does not seem to be filled by the AMprover software. (?)")
                        .IsUnicode(false);

                    b.Property<string>("CmnTaskResponsible")
                        .HasColumnType("varchar(50)")
                        .HasComment("Person or department that is responsible for executing the common task (never set in the AMprover software)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("CmnTaskSiCategory")
                        .HasColumnType("int")
                        .HasComment("The si category value to which the common task is bound (FK to LookupUserDefined, UserdefinedFilter is SICategory)");

                    b.Property<int?>("CmnTaskSortOrder")
                        .HasColumnType("int")
                        .HasComment("Custom sequence to order the common tasks");

                    b.Property<string>("CmnTaskType")
                        .HasColumnType("varchar(20)")
                        .HasComment("Type of common task (task, procedure etc.) Type domain is defined in Lookup table, Lookupfilter value is MeasureType. This is called Action type in the AMprover software.")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<int>("CmnTaskUnitType")
                        .HasColumnType("int")
                        .HasComment("The unit type value to which the cost of the common task are bound (FK to LookupUserDefined, FilterType UnitTypes)");

                    b.Property<int?>("CmnTaskValidFromYear")
                        .HasColumnType("int")
                        .HasComment("The starting year of the common, when it becomes valid. Does not seem to be set from the AMprover software. (?)");

                    b.Property<int?>("CmnTaskValidUntilYear")
                        .HasColumnType("int")
                        .HasComment("The year when the common task is no longer valid, and will no longer affect cost calculations. Does not seem to be set from the AMprover software. (?)");

                    b.Property<decimal?>("CmnTaskWorkInspCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Inspection cost of the common task. Is not set by the AMprover software. (?)");

                    b.Property<int?>("CmnTaskWorkPackage")
                        .HasColumnType("int")
                        .HasComment("ID of the work package of the common task (FK to Workpackage)");

                    b.Property<bool?>("CmnTaskWorkPackageModifiable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean that enables modification of the work package during risk analysis");

                    b.HasKey("CmnTaskId");

                    b.HasIndex("CmnTaskExecutor");

                    b.HasIndex("CmnTaskInitiator");

                    b.HasIndex("CmnTaskIntervalUnit");

                    b.HasIndex("CmnTaskMxPolicy");

                    b.HasIndex("CmnTaskWorkPackage");

                    b.ToTable("TblCommonTask");

                    b.HasComment("Master data table. A common task is a task that has to be executed after a defined interval, each time the interval passes. Also named common action.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTaskCost", b =>
                {
                    b.Property<int>("CtcId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("CtcID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of CommonTaskCost)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CtcCalculationType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(10)")
                        .HasDefaultValueSql("('PxN')")
                        .HasComment("The way the common task cost must be calculated (P, PxN, PxNxU)")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.Property<int>("CtcCommonCostId")
                        .HasColumnName("CtcCommonCostID")
                        .HasColumnType("int")
                        .HasComment("ID of the common cost the common task cost is bound to (FK to CommonCost)");

                    b.Property<int?>("CtcCommonTaskId")
                        .HasColumnName("CtcCommonTaskID")
                        .HasColumnType("int")
                        .HasComment("Binds costs to a specific task. Currently selected task is added automatically when user adds a new common cost to an action. (FK to CommonTask) ");

                    b.Property<decimal>("CtcCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Calculated cost of the common task cost (determined by calculation type, and stored values for Price, Quantity and Units)");

                    b.Property<decimal>("CtcPrice")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Price of the common task cost (price of a single unit)");

                    b.Property<int>("CtcPriceIndexYear")
                        .HasColumnType("int")
                        .HasComment("The year in which the common task cost are indexed");

                    b.Property<decimal?>("CtcQuantity")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Quantity of common cost items needed for this common task cost");

                    b.Property<decimal?>("CtcUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Number of units needed for this common task cost");

                    b.HasKey("CtcId");

                    b.HasIndex("CtcCommonCostId");

                    b.HasIndex("CtcCommonTaskId");

                    b.ToTable("TblCommonTaskCost");

                    b.HasComment("Master data table. Contains costs related to common tasks. Defined within master data, as part of a common action. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Company", b =>
                {
                    b.Property<int>("CompanyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("CompanyID")
                        .HasColumnType("int")
                        .HasComment("Unique ID")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CompanyAdres")
                        .HasColumnType("varchar(50)")
                        .HasComment("Address of the company")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("CompanyContact1")
                        .HasColumnType("varchar(50)")
                        .HasComment("Contact field of the company")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("CompanyContact2")
                        .HasColumnType("varchar(50)")
                        .HasComment("Contact field of the company")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("CompanyCountry")
                        .HasColumnType("varchar(30)")
                        .HasComment("Country of the company")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("CompanyDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the company")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("CompanyEmail")
                        .HasColumnType("varchar(50)")
                        .HasComment("Email address of the company")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("CompanyFax")
                        .HasColumnType("varchar(50)")
                        .HasComment("Fax number of the company")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("The name of the company")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("CompanyPhone")
                        .HasColumnType("varchar(15)")
                        .HasComment("Phone number of the company")
                        .HasMaxLength(15)
                        .IsUnicode(false);

                    b.Property<string>("CompanyPlace")
                        .HasColumnType("varchar(30)")
                        .HasComment("City of the company")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("CompanyPostAdres")
                        .HasColumnType("varchar(50)")
                        .HasComment("Post address of the company")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("CompanyZipCode")
                        .HasColumnType("varchar(10)")
                        .HasComment("Zip code of the company")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.HasKey("CompanyId");

                    b.ToTable("Company");

                    b.HasComment("Would contain full contact info for companies. Not used by AMprover software. (!)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DbScriptsExecuted", b =>
                {
                    b.Property<string>("ScriptName")
                        .HasColumnType("nvarchar(255)")
                        .HasMaxLength(255);

                    b.Property<DateTime>("DateExecuted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.HasKey("ScriptName");

                    b.ToTable("DbScriptsExecuted");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Department", b =>
                {
                    b.Property<int>("DepId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("DepID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of Department)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("DepAccessRights")
                        .HasColumnType("varchar(max)")
                        .HasComment("Access rights held by members of the department")
                        .IsUnicode(false);

                    b.Property<string>("DepDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Full description of the department")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("DepShortkey")
                        .IsRequired()
                        .HasColumnType("varchar(5)")
                        .HasComment("Short description of the department")
                        .HasMaxLength(5)
                        .IsUnicode(false);

                    b.HasKey("DepId");

                    b.ToTable("TblDepartment");

                    b.HasComment("Defines different departments within a company. Defined from master data");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DerModified", b =>
                {
                    b.Property<int>("DerModId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("DerModID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("DerModCopiedFrom")
                        .HasColumnType("int")
                        .HasComment("For auditing reasons it is easier to know what the original object is");

                    b.Property<bool?>("DerModDeleted")
                        .HasColumnType("bit")
                        .HasComment("if this bit is set then the object is will be shown a if it has been deleted but the parent properties will be still be shown");

                    b.Property<string>("DerModModifications")
                        .HasColumnType("varchar(max)")
                        .HasComment("xml string with the modifications")
                        .IsUnicode(false);

                    b.Property<int?>("DerModObjectId")
                        .HasColumnName("DerModObjectID")
                        .HasColumnType("int")
                        .HasComment("The ID of the object whereto modifications have been applied");

                    b.Property<string>("DerModObjectKey")
                        .HasColumnType("varchar(50)")
                        .HasComment("not used can be applied for objects that have no unique int key")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int>("DerModObjectType")
                        .HasColumnType("int")
                        .HasComment("0=scenario 1=riskobject 2=risk 3 = task 4=siFilter 5=pickSi ");

                    b.HasKey("DerModId");

                    b.ToTable("DerModified");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Descriptions", b =>
                {
                    b.Property<int>("DescId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("DescID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("DescDescription")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<int?>("DescExtraId")
                        .HasColumnName("DescExtraID")
                        .HasColumnType("int");

                    b.Property<string>("DescExtraType")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("DescFieldType")
                        .HasColumnType("int");

                    b.Property<int?>("DescRiskId")
                        .HasColumnName("DescRiskID")
                        .HasColumnType("int");

                    b.Property<int?>("DescRiskObjectId")
                        .HasColumnName("DescRiskObjectID")
                        .HasColumnType("int");

                    b.Property<int?>("DescTaskId")
                        .HasColumnName("DescTaskID")
                        .HasColumnType("int");

                    b.HasKey("DescId");

                    b.ToTable("Descriptions");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.EditLog", b =>
                {
                    b.Property<int>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("LogID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of EditLog)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("LogDate")
                        .HasColumnType("datetime")
                        .HasComment("Date the modification occurred");

                    b.Property<string>("LogModificationType")
                        .HasColumnType("varchar(10)")
                        .HasComment("Modification type (edit, delete etc.)")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.Property<string>("LogModifications")
                        .HasColumnType("varchar(max)")
                        .HasComment("Describes the actual modification (column names, old value and new value)")
                        .IsUnicode(false);

                    b.Property<int?>("LogObjectId")
                        .HasColumnName("LogObjectID")
                        .HasColumnType("int")
                        .HasComment("Object ID which was modified (?)");

                    b.Property<string>("LogObjectType")
                        .HasColumnType("varchar(30)")
                        .HasComment("Describes the table that was modified (table name)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("LogObjectVarId")
                        .HasColumnName("LogObjectVarID")
                        .HasColumnType("varchar(30)")
                        .HasComment("?")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("LogUserId")
                        .HasColumnName("LogUserID")
                        .HasColumnType("varchar(30)")
                        .HasComment("Name of the user who performed the modification (not an ID!)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.HasKey("LogId");

                    b.ToTable("EditLog");

                    b.HasComment("Contains the edit logs that are generated when a user makes changes to certain tables. The editlogs contain the columns changed, the old value and the new value. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Filters", b =>
                {
                    b.Property<int>("SqlSelId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("SqlSelID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of Filters)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("SqlSelGroup")
                        .HasColumnType("varchar(20)")
                        .HasComment("Main filter group. Describes the AMprover module the filter applies to. User cannot set this.")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("SqlSelName")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("Descriptive name of the filter, user can enter any name. ")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("SqlSelShortKey")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasComment("Shortkey of the filter, needs to be unique within the entire Filters. ")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("SqlSelSubGroup")
                        .HasColumnType("varchar(20)")
                        .HasComment("Filter sub group. Is set by the code, using hardcoded values which are different for each subgroup of filters. Needs to be unique within the same main group. ")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.HasKey("SqlSelId");

                    b.ToTable("Filters");

                    b.HasComment("Defines filters used throughout the program. (reports, priority box, clusters, life cycle costs). The filters are used to limit the amount of data the user has to go through to find the item they want. The filter can be set up on each of the columns of the related table. Filters are identified by the combination of SqlSelGroup and SqlSubSelGroup. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.FiltersSelectionList", b =>
                {
                    b.Property<int>("SqlSelId")
                        .HasColumnName("SqlSelID")
                        .HasColumnType("int");

                    b.Property<int>("Sequence")
                        .HasColumnType("int");

                    b.Property<string>("AndOr")
                        .HasColumnType("varchar(3)")
                        .HasMaxLength(3)
                        .IsUnicode(false);

                    b.Property<string>("Criterium")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("FieldType")
                        .IsRequired()
                        .HasColumnType("varchar(6)")
                        .HasMaxLength(6)
                        .IsUnicode(false);

                    b.Property<string>("FriendlyName")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<short>("Ident")
                        .HasColumnType("smallint");

                    b.Property<bool>("IsAdvanced")
                        .HasColumnType("bit");

                    b.Property<string>("Selection")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("SortType")
                        .HasColumnType("varchar(4)")
                        .HasMaxLength(4)
                        .IsUnicode(false);

                    b.HasKey("SqlSelId", "Sequence");

                    b.ToTable("FiltersSelectionList");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Fmeca", b =>
                {
                    b.Property<int>("FmecaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("FmecaID")
                        .HasColumnType("int")
                        .HasComment(" (PK of Fmeca)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("FmecaDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the FMECA matrix")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("FmecaMatrix")
                        .HasColumnType("varchar(max)")
                        .HasComment("Layout of the FMECA matrix, stored in XML. Stores the colors, and effects for each cell of the matrix.")
                        .IsUnicode(false);

                    b.Property<string>("FmecaName")
                        .HasColumnType("varchar(30)")
                        .HasComment("Name for the FMECA matrix")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("FmecaShortName")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(6)")
                        .HasDefaultValueSql("('-')")
                        .HasComment("Short description of the FMECA matrix")
                        .HasMaxLength(6)
                        .IsUnicode(false);

                    b.Property<int>("FmecaVersion")
                        .HasColumnType("int")
                        .HasComment("FMECA matrix version (default is 1 for AMprover 3.0)");

                    b.HasKey("FmecaId")
                        .HasName("PK_tblFMECA_1");

                    b.ToTable("TblFmeca");

                    b.HasComment("This table defines the rows, columns and cells of risk matrices. The cell contents, colors and values are stored in XML. The created risk matrices can be selected during risk analysis.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.FmecaSelect", b =>
                {
                    b.Property<int>("MfsId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("MfsID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of FmecaSelect)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("MfsColIndex")
                        .HasColumnType("int")
                        .HasComment("Fmeca matrix column number (99 is mtbf)");

                    b.Property<double?>("MfsCustomAfter")
                        .HasColumnType("float")
                        .HasComment("Custom value after performing the FMECA");

                    b.Property<double?>("MfsCustomBefore")
                        .HasColumnType("float")
                        .HasComment("Custom value before performing the FMECA");

                    b.Property<string>("MfsDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("Description for the FMECA column.")
                        .IsUnicode(false);

                    b.Property<int?>("MfsLccId")
                        .HasColumnName("MfsLccID")
                        .HasColumnType("int")
                        .HasComment("Reference to Lcc ID (FK to LCC)");

                    b.Property<int>("MfsReferenceId")
                        .HasColumnName("MfsReferenceID")
                        .HasColumnType("int")
                        .HasComment("Reference to risk ID (FK to Mrb)");

                    b.Property<int>("MfsRefrenceType")
                        .HasColumnType("int")
                        .HasComment("Used for mrb lcc and columncost per task. Is set to 999 by SyncMrb. ");

                    b.Property<int?>("MfsSelectAfter")
                        .HasColumnType("int")
                        .HasComment("Selected value after performing the FMECA");

                    b.Property<int?>("MfsSelectBefore")
                        .HasColumnType("int")
                        .HasComment("Select value before performing the FMECA");

                    b.Property<int?>("MfsTaskId")
                        .HasColumnName("MfsTaskID")
                        .HasColumnType("int")
                        .HasComment("Reference to task ID (FK to Task)");

                    b.Property<double?>("MfsValueAfter")
                        .HasColumnType("float")
                        .HasComment("Value after performing the FMECA");

                    b.Property<double?>("MfsValueBefore")
                        .HasColumnType("float")
                        .HasComment("Value before performing the FMECA");

                    b.HasKey("MfsId");

                    b.ToTable("FmecaSelect");

                    b.HasComment("Contains the monetary values (of each risk type) before and after the preventive actions defined during risk analysis.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lcc", b =>
                {
                    b.Property<int>("LccId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("LccID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of LCC)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<decimal?>("LccAec")
                        .HasColumnName("LccAEC")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Annual equivalent cost of the LCC");

                    b.Property<decimal?>("LccAverageOptimalCost")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Average optimal cost of the LCC");

                    b.Property<int?>("LccChildObject")
                        .HasColumnType("int")
                        .HasComment("Object ID of the LCC (FK to Object)");

                    b.Property<int?>("LccChildObject1")
                        .HasColumnType("int")
                        .HasComment("Object ID of the LCC (FK to Object)");

                    b.Property<int?>("LccChildObject2")
                        .HasColumnType("int")
                        .HasComment("Object ID of the LCC (FK to Object)");

                    b.Property<int?>("LccChildObject3")
                        .HasColumnType("int")
                        .HasComment("Object ID of the LCC (FK to Object)");

                    b.Property<int?>("LccChildObject4")
                        .HasColumnType("int")
                        .HasComment("Object ID of the LCC (FK to Object)");

                    b.Property<DateTime?>("LccDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<decimal?>("LccDiscountRate")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Discount rate for the LCC is a factror reflecting the time value of money that is used to convert cash flows occurring at different times, to a common time. ");

                    b.Property<decimal?>("LccEcoFunctional")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Functional eco score of the LCC");

                    b.Property<decimal?>("LccEcoTechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Technical eco score of the LCC");

                    b.Property<bool?>("LccExclude")
                        .HasColumnType("bit")
                        .HasComment("Boolean that excludes the LCC from calculation when true");

                    b.Property<decimal?>("LccInputAvailabilityTechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Availability of technical input for the LCC");

                    b.Property<decimal?>("LccInputReliabilityFunctional")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Reliability of functional input for the LCC");

                    b.Property<int?>("LccMaxYears")
                        .HasColumnType("int")
                        .HasComment("Number of years the LCC should be calculated for");

                    b.Property<decimal?>("LccMcRav")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Maintenance cost divided by the replacement value");

                    b.Property<string>("LccModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<decimal?>("LccMtbffunctional")
                        .HasColumnName("LccMTBFFunctional")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Output of the total functional MTBF (mean time between failure) of the LCC");

                    b.Property<decimal?>("LccMtbftechnical")
                        .HasColumnName("LccMTBFTechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Output of the total technical MTBF (mean time between failure) of the LCC");

                    b.Property<string>("LccName")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasComment("Name of the LCC")
                        .HasMaxLength(50);

                    b.Property<decimal?>("LccNpv")
                        .HasColumnName("LccNPV")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Net present value of the LCC. NPV is the sum of discounted future cash flows, inlcuding both costs and benefits/revenues.");

                    b.Property<int?>("LccNpvyear")
                        .HasColumnName("LccNPVyear")
                        .HasColumnType("int")
                        .HasComment("Optimal year of the LCC (year where the AEC is on his lowest point)");

                    b.Property<decimal?>("LccOptimalAverageCorrectiveCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Average optimal cost for corrective maintenance of the LCC");

                    b.Property<decimal?>("LccOptimalAveragePreventiveCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Average optimal cost for preventive maintenance of the LCC");

                    b.Property<byte[]>("LccOptimalImage")
                        .HasColumnType("image")
                        .HasComment("Image containing the optimal cost graph of the LCC");

                    b.Property<decimal?>("LccOutputAvailabilityTechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Availability of technical output for the LCC");

                    b.Property<decimal?>("LccOutputReliabilityTechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Reliability of functional input for the LCC");

                    b.Property<decimal?>("LccOverallProductionCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Total production cost of the LCC");

                    b.Property<int?>("LccPartOf")
                        .HasColumnType("int")
                        .HasComment("LCC ID of the parent LCC (FK to LCC)");

                    b.Property<decimal?>("LccPotential")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Saving potential of the LCC");

                    b.Property<decimal?>("LccProductionCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Production cost of the LCC");

                    b.Property<decimal?>("LccProductivityFunctional")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Functional productivity of the LCC");

                    b.Property<decimal?>("LccProductivityTechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Technical productivity of the LCC");

                    b.Property<int?>("LccRamsDiagramId")
                        .HasColumnName("LccRamsDiagramID")
                        .HasColumnType("int")
                        .HasComment("RAMS diagram ID of the LCC (FK to RamsDiagram)");

                    b.Property<int?>("LccRamsId")
                        .HasColumnName("LccRamsID")
                        .HasColumnType("int")
                        .HasComment("RAMS ID of the LCC (FK to Rams)");

                    b.Property<byte[]>("LccRamsImage")
                        .HasColumnType("image")
                        .HasComment("Image containing the RAMS graph of the LCC");

                    b.Property<byte[]>("LccRealCostImage")
                        .HasColumnType("image")
                        .HasComment("Image containing the real cost graph of the LCC");

                    b.Property<string>("LccRemark")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks field allowing for more detailed information about the LCC item")
                        .IsUnicode(false);

                    b.Property<decimal?>("LccReplacementValue")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Replacement value of the LCC object");

                    b.Property<int?>("LccRiskObject")
                        .HasColumnType("int")
                        .HasComment("Risk object ID of the LCC (FK to RiskObject)");

                    b.Property<int?>("LccScenarioId")
                        .HasColumnName("LccScenarioID")
                        .HasColumnType("int")
                        .HasComment("Scenario ID of the LCC (FK to Scenario)");

                    b.Property<int?>("LccSiId")
                        .HasColumnName("LccSiID")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccTotalAverageCost")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Total average cost of the LCC");

                    b.Property<decimal?>("LccUtilizationFunctional")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Functional utilization (time) of the LCC");

                    b.Property<decimal?>("LccUtilizationTechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Technical utilization (time) of the LCC");

                    b.Property<DateTime?>("LccdateCalculated")
                        .HasColumnName("LCCDateCalculated")
                        .HasColumnType("smalldatetime");

                    b.HasKey("LccId");

                    b.HasIndex("LccChildObject");

                    b.HasIndex("LccChildObject1");

                    b.HasIndex("LccChildObject2");

                    b.HasIndex("LccChildObject3");

                    b.HasIndex("LccChildObject4");

                    b.HasIndex("LccPartOf")
                        .HasName("IX_LCCPartOf");

                    b.HasIndex("LccRiskObject");

                    b.HasIndex("LccScenarioId");

                    b.ToTable("TblLCC");

                    b.HasComment("Contains the life cycle costs for each defined risk object. Calculations can be based on risk and/or RAMS analysis. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lccdetail", b =>
                {
                    b.Property<int>("LccDetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("LccDetID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of LCCDetail)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<decimal?>("LccDetActionCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost for actions of the LCC detail (? is this different than LCCDetTaskCost?) ");

                    b.Property<decimal?>("LccDetAec")
                        .HasColumnName("LccDetAEC")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Annual equivalent costs. Is used to compare investment options where the natural replacement cycle cannot easily be related to the period of analysis. ");

                    b.Property<decimal?>("LccDetAvailabilityTechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Technical availability (time) of the LCC detail. The amount of time the riskobject is available for use. ");

                    b.Property<decimal?>("LccDetAverageAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Average cost after executing preventive actions ");

                    b.Property<decimal?>("LccDetAverageBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Average costs before executing preventive actions ");

                    b.Property<decimal?>("LccDetAverageOptimalCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Average optimal costs for the current year only");

                    b.Property<decimal?>("LccDetAverageRealCorrectiveCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Average costs of corrective measures for the current year only");

                    b.Property<decimal?>("LccDetAverageRealPreventiveCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Average costs of preventive measures for the current year only");

                    b.Property<decimal?>("LccDetCapexCost")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("LccDetCorrectiveCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs for corrective measures taken");

                    b.Property<decimal?>("LccDetDepreciation")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Depreciation cost of the LCC detail");

                    b.Property<decimal?>("LccDetDirectCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Direct cost of the LCC detail");

                    b.Property<decimal?>("LccDetEcoTechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Technical eco score of the LCC detail.");

                    b.Property<bool?>("LccDetExcluded")
                        .HasColumnType("bit")
                        .HasComment("Boolean that excludes the LCC detail from calculation when true.");

                    b.Property<decimal?>("LccDetFailureRate")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Failure rate of the LCC detail. Failure rate is the frequency with which an engineered system or component fails. Usually expressed in failures per hour. ");

                    b.Property<decimal?>("LccDetFmecaAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("FMECA cost after executing preventive actions ");

                    b.Property<decimal?>("LccDetFmecaBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("FMECA cost before executing preventive actions ");

                    b.Property<decimal?>("LccDetFmecaCustomAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Custom cost after executing preventive actions");

                    b.Property<decimal?>("LccDetFmecaCustomBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Custom FMECA costs before executing preventive actions");

                    b.Property<decimal?>("LccDetFmecaCustomRiskAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Custom risk cost after executing preventive actions");

                    b.Property<decimal?>("LccDetFmecaCustomRiskBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Custom risk before executing preventive actions");

                    b.Property<int?>("LccDetLccId")
                        .HasColumnName("LccDetLccID")
                        .HasColumnType("int")
                        .HasComment("LCC ID to which the LCC detail belongs (FK to LCC)");

                    b.Property<decimal?>("LccDetMaintenanceCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Maintenance cost of the LCC detail");

                    b.Property<decimal?>("LccDetModificationCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost for modifications of the LCC detail");

                    b.Property<decimal?>("LccDetOpexCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Opex cost of the LCC detail (operating expenditures)");

                    b.Property<decimal?>("LccDetOptimalCorrectiveCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Optimal costs of corrective measures for the current year only");

                    b.Property<decimal?>("LccDetOptimalPreventiveCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Optimal costs of preventive measures for the current year only");

                    b.Property<decimal?>("LccDetPreventiveCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs of preventive measures taken");

                    b.Property<decimal?>("LccDetProcedureCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost for procedures of the LCC detail");

                    b.Property<decimal?>("LccDetProductivityTechnical")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Technical productivity time of the LCC detail. Productivity is an average measure of the efficiency of production. It is usually expressed as a ratio of production output to what is required to produce it. ");

                    b.Property<decimal?>("LccDetRealCorrectiveCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs of the corrective measures for the current year only");

                    b.Property<decimal?>("LccDetRealOpexCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Opex cost of the current year only (operating expenditures)");

                    b.Property<decimal?>("LccDetRealPreventiveCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs of the preventive measures for the current year only");

                    b.Property<decimal?>("LccDetRealTotalCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Real total costs for the current year only");

                    b.Property<decimal?>("LccDetReliability")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Reliability of the LCC detail");

                    b.Property<decimal?>("LccDetRiskAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Risk cost after executing preventive actions");

                    b.Property<decimal?>("LccDetRiskBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Risk cost before executing preventive actions");

                    b.Property<decimal?>("LccDetSpareCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost for spare parts needed for the LCC detail");

                    b.Property<decimal?>("LccDetTaskCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost for tasks of the LCC detail (? is this different than LCCDetActionCost?) ");

                    b.Property<decimal?>("LccDetTotalCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The costs of this year and all previous years");

                    b.Property<decimal?>("LccDetTotalNpv")
                        .HasColumnName("LccDetTotalNPV")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Total net present value of the LCC detail");

                    b.Property<decimal?>("LccDetUtilizationTechnichal")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Technical utilization time of the LCC detail. The time the riskobject is actually being used in production. In basic terms, utilization is a measure of the actual revenue earned by the assets against the potential revenue they could have earned.");

                    b.Property<int?>("LccDetYear")
                        .HasColumnType("int")
                        .HasComment("LCC detail year");

                    b.HasKey("LccDetId")
                        .HasName("PK_CalcLCCtotal");

                    b.HasIndex("LccDetLccId");

                    b.ToTable("TblLCCDetail");

                    b.HasComment(@"Contains the life cycle cost details for each risk object, for each year the LCC needs to be calculated. Can be calculated based on:
- risk per fmeca effect column
- risk per year
- task per year
(one or all of the above can be chosen from the LCC command center control, which generates the LCC details based on the users' preferences)

All of the LCCDetails combined will provide the data for the actual Life Cycle Costing calculation for each riskobject.  
");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LcceffectDetail", b =>
                {
                    b.Property<int>("LccEfctId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("LccEfctID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of LCCEffectDetail)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<decimal?>("LccEfctActionCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The costs of a task not specified (Task, action, modification or inspection)");

                    b.Property<decimal?>("LccEfctCorrectiveCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Total costs of corrective actions for this LCC effect detail");

                    b.Property<decimal?>("LccEfctCustomAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Custom cost of LCC effect detail, after executing preventive action");

                    b.Property<decimal?>("LccEfctCustomBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Custom cost of LCC effect detail, before executing preventive action");

                    b.Property<decimal?>("LccEfctCustomRiskAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Custom risk cost of the LCC effect detail, after executing preventive actions");

                    b.Property<decimal?>("LccEfctCustomRiskBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Custom risk cost of the LCC effect detail, before executing preventive actions");

                    b.Property<decimal?>("LccEfctDirectCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Direct cost of the LCC effect detail");

                    b.Property<int?>("LccEfctEffectColumn")
                        .HasColumnType("int")
                        .HasComment("Concerning FMECA column of the LCC effect detail (?)");

                    b.Property<string>("LccEfctEffectName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name for the FMECA column of the LCC effect detail")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<decimal?>("LccEfctFmecaAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("FMECA cost for this LCC effect detail, after executing preventive actions ");

                    b.Property<decimal?>("LccEfctFmecaBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("FMECA cost for this LCC effect detail, before executing preventive actions ");

                    b.Property<int?>("LccEfctLccDetailId")
                        .HasColumnName("LccEfctLccDetailID")
                        .HasColumnType("int")
                        .HasComment("LCC detail ID the LCC effect detail belongs to (FK to LCCDetail)");

                    b.Property<int?>("LccEfctLccId")
                        .HasColumnName("LccEfctLccID")
                        .HasColumnType("int")
                        .HasComment("LCC ID the LCC effect detail belongs to (FK to LCC)");

                    b.Property<decimal?>("LccEfctOptimalCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Optimal cost of the LCC effect detail");

                    b.Property<decimal?>("LccEfctPreventiveCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Total costs of preventive actions for this LCC effect detail");

                    b.Property<int?>("LccEfctRamsDiagramId")
                        .HasColumnName("LccEfctRamsDiagramID")
                        .HasColumnType("int")
                        .HasComment("RAMS diagram ID the LCC effect detail belongs to (FK to RamsDiagram)");

                    b.Property<int?>("LccEfctRamsId")
                        .HasColumnName("LccEfctRamsID")
                        .HasColumnType("int")
                        .HasComment("RAMS ID the LCC effect detail belongs to (FK to Rams)");

                    b.Property<decimal?>("LccEfctRiskAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Risk cost of LCC effect detail, after executing preventive action ");

                    b.Property<decimal?>("LccEfctRiskBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Risk cost of LCC effect detail, before executing preventive action ");

                    b.Property<int?>("LccEfctRiskId")
                        .HasColumnName("LccEfctRiskID")
                        .HasColumnType("int")
                        .HasComment("Risk ID the LCC effect detail belongs to (FK to MRB)");

                    b.Property<decimal?>("LccEfctSparePartCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The maintenance costs (real expenses), these are the costs for storage and purchase of spare parts for this LCC effect detail");

                    b.Property<decimal?>("LccEfctTaskFmeca")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("?");

                    b.Property<decimal?>("LccEfctTaskFmecaCustom")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("?");

                    b.Property<int?>("LccEfctTaskId")
                        .HasColumnName("LccEfctTaskID")
                        .HasColumnType("int")
                        .HasComment("Task ID the LCC effect detail belongs to (FK to Task)");

                    b.Property<int?>("LccEfctType")
                        .HasColumnType("int")
                        .HasComment("Type of LCC effect detail, describes what caused the LCC effect detail (Type domain contains EffectDetail, RiskDetail, TaskDetail or RamsDetail)");

                    b.Property<int?>("LccEfctYear")
                        .HasColumnType("int")
                        .HasComment("Year the LCC effect detail contains costs for");

                    b.HasKey("LccEfctId");

                    b.HasIndex("LccEfctLccDetailId");

                    b.HasIndex("LccEfctLccId");

                    b.ToTable("TblLccEffectDetail");

                    b.HasComment(@"Contains the life cycle cost effect details. These details are generated from data gathered from LCC, LCCDetail, Task, Mrb and RiskObject. (?)

LCC effect details are be generated from the LCC command centre control, based on the users' preferences.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Login", b =>
                {
                    b.Property<string>("LogInUserMachineName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Computer name used by logged in user")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int>("LoginId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("LoginID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("LoginLoginDate")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date and time the login occurred");

                    b.Property<DateTime?>("LoginLogoutDate")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("LoginUserDomain")
                        .HasColumnType("varchar(50)")
                        .HasComment("Domain of the logged in user")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("LoginUserId")
                        .HasColumnName("LoginUserID")
                        .HasColumnType("int")
                        .HasComment("User name of the logged in user");

                    b.ToTable("Login");

                    b.HasComment("Contains data that shows which user logged in from where. This table is not used in the AMprover software (!)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lookup", b =>
                {
                    b.Property<int>("LookupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("LookupID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK for Lookup)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("LookupFilter")
                        .HasColumnType("varchar(20)")
                        .HasComment("Domain name, each unique name defines a different lookup category")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("LookupLongDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Long description")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("LookupShortDescription")
                        .HasColumnType("varchar(20)")
                        .HasComment("Short description")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<int?>("LookupValue")
                        .HasColumnType("int")
                        .HasComment("Value that distinguishes lookup domain values, must be unique within the same category");

                    b.HasKey("LookupId");

                    b.HasIndex("LookupFilter", "LookupValue")
                        .IsUnique()
                        .HasName("IX_Lookup")
                        .HasFilter("[LookupFilter] IS NOT NULL AND [LookupValue] IS NOT NULL");

                    b.ToTable("Lookup");

                    b.HasComment("Master data table that stores domains, that are used throughout the program. Domains are grouped by have the same name for field LookupFilter. A set of values can be created for each domain. Not editable by user. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupExecutor", b =>
                {
                    b.Property<int>("ExecutorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("ExecutorID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK for LookupExecutor)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("ExecutorDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the task executor (Which clarifies the name where needed)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("ExecutorName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the task executor")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.HasKey("ExecutorId")
                        .HasName("PK_LookupTaskExecutor");

                    b.ToTable("LookupExecutor");

                    b.HasComment("Master data table that contains all currently defined task executors. A task executor is the person or department that handles execution of a task. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupFailCat", b =>
                {
                    b.Property<int>("FailCatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("FailCatID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK for LookupFailCat)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("FailCatDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Failure category description")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("FailCatGroup")
                        .HasColumnType("varchar(20)")
                        .HasComment("The group the failure category belongs to (Group domain is defined in table Lookup)")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("FailCatName")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasComment("Failure category name")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.HasKey("FailCatId")
                        .HasName("PK_LookupFailCat");

                    b.HasIndex("FailCatName")
                        .IsUnique()
                        .HasName("IX_LookupFailCat");

                    b.ToTable("LookupFailCat");

                    b.HasComment("Master data table that contains all defined failure categories. Each failure category defines a different failure behavior. User is allowed to change failure category names and descriptions.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupFailMode", b =>
                {
                    b.Property<int>("FailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("FailID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK for LookupFailMode)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("FailDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("FailDescription")
                        .HasColumnType("varchar(100)")
                        .HasComment("Description of the failure mode")
                        .HasMaxLength(100)
                        .IsUnicode(false);

                    b.Property<int?>("FailDistributionId")
                        .HasColumnName("FailDistributionID")
                        .HasColumnType("int")
                        .HasComment("Distribution type determines what method of calculation will be used to calculate the costs associated with each failure mode. Distribution type values are defined in the Lookup table.");

                    b.Property<int?>("FailIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("Defines the position in the Weibull curve (when combined with Weibull location)");

                    b.Property<string>("FailMode")
                        .IsRequired()
                        .HasColumnType("varchar(40)")
                        .HasComment("Name of the failure mode")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("FailModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int>("FailRateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("FailRateID")
                        .HasColumnType("int")
                        .HasDefaultValueSql("((3))")
                        .HasComment("Failure rate. This is the frequency at which a system or component fails. Possible values are defined in Lookup. Defaults to constant.");

                    b.Property<int?>("FailRiskTypeId")
                        .HasColumnName("FailRiskTypeID")
                        .HasColumnType("int")
                        .HasComment("Describes if the failure shows itself, or is invisible. Possible values are defined in Lookup.");

                    b.Property<decimal?>("FailShape")
                        .HasColumnType("decimal(10, 2)")
                        .HasComment("Fail shape defines the slope of the current location on the Weibull curve");

                    b.Property<decimal?>("FailWeibullLocation")
                        .HasColumnType("decimal(10, 2)")
                        .HasComment("Defines the location on the Weibull curve (starting point)");

                    b.HasKey("FailId");

                    b.ToTable("LookupFailMode");

                    b.HasComment("Master data table that defines failure modes. A failure mode describes a possible cause for the failure, if the failure shows itself, and what the frequency of the failure is. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupGridColumn", b =>
                {
                    b.Property<string>("ControlName")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("ColumnName")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("ColumnHeader")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<int>("ColumnWidth")
                        .HasColumnType("int");

                    b.Property<int>("DisplayIndex")
                        .HasColumnType("int");

                    b.Property<int>("FieldType")
                        .HasColumnType("int");

                    b.Property<bool>("Visible")
                        .HasColumnType("bit");

                    b.HasKey("ControlName", "FieldName", "ColumnName", "ColumnHeader")
                        .HasName("PK_LookupGridColumn_ID");

                    b.ToTable("LookupGridColumn");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupInflationGroup", b =>
                {
                    b.Property<int>("InflId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("InflID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of LookupInflationGroup)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("InflDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("InflModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Last modification of this record was made by this user")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("InflName")
                        .HasColumnType("varchar(30)")
                        .HasComment("Inflation group name. Describes what the inflation percentage belongs to.")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<decimal?>("InflPercentage")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Inflation percentage for this specific item.");

                    b.HasKey("InflId")
                        .HasName("PK_InflationGroups");

                    b.ToTable("LookupInflationGroup");

                    b.HasComment("Master data table that contains inflation groups. The inflation groups allow us to connect a specific inflation percentage to a variety of things. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupInitiator", b =>
                {
                    b.Property<int>("InitiatorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("InitiatorID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of LookupInitiator)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("InitiatorDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the initiator")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("InitiatorName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the initiator")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.HasKey("InitiatorId")
                        .HasName("PK_LookupTaskInitiator");

                    b.ToTable("LookupInitiator");

                    b.HasComment("Master data table that defines who (or what) will initiate a certain action. Is used for clusters and preventive actions. Editable by user.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupIntervalUnit", b =>
                {
                    b.Property<int>("IntUnitId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("IntUnitID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of LookupIntervalUnit)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<decimal?>("IntUnitCalculationKey")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Number of times an interval unit occurs in a year");

                    b.Property<DateTime?>("IntUnitDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("IntUnitDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the interval unit")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("IntUnitModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("IntUnitName")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasComment("Name of the interval unit")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("IntUnitShortKey")
                        .IsRequired()
                        .HasColumnType("varchar(10)")
                        .HasComment("Short code for the interval unit (usually consists of the first letter of the name)")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.HasKey("IntUnitId")
                        .HasName("PK_LookupTaskIntervalUnit");

                    b.ToTable("LookupIntervalUnit");

                    b.HasComment("Master data table that holds values that are used in interval calculations. Editable by user.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupMxPolicy", b =>
                {
                    b.Property<int>("PolId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PolID")
                        .HasColumnType("int")
                        .HasComment(" (PK of LookupMxPolicy)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("PolDescription")
                        .HasColumnType("varchar(30)")
                        .HasComment("Provides a more detailed description of the maintenance policy")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("PolName")
                        .HasColumnType("varchar(5)")
                        .HasComment("Name that describes the maintenance policy")
                        .HasMaxLength(5)
                        .IsUnicode(false);

                    b.HasKey("PolId");

                    b.ToTable("LookupMxPolicy");

                    b.HasComment("Master data table that defines different maintenance policies. A maintenance policy can be described as the reason why maintenance should occur. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupSettings", b =>
                {
                    b.Property<string>("AmsProperty")
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique ID and the name of the setting. (PK of LookupSettings)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<DateTime?>("AmsDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("AmsDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<decimal?>("AmsDecimalValue")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Decimal value of the setting");

                    b.Property<string>("AmsInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("AmsIntValue")
                        .HasColumnType("int")
                        .HasComment("Integer value of the setting");

                    b.Property<bool?>("AmsModifiable")
                        .HasColumnType("bit")
                        .HasComment("Boolean value that states if the setting is modifiable");

                    b.Property<string>("AmsModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("AmsTextValue")
                        .HasColumnType("varchar(max)")
                        .HasComment("The text value containing the values of the LookupSetting. Can be either null, a pipe ('|') delimited string, or XML. ")
                        .IsUnicode(false);

                    b.HasKey("AmsProperty");

                    b.ToTable("LookupSettings");

                    b.HasComment("Master data table that defines settings for a variety of things within the Amprover software. (like grid column settings and program constants) Not editable by user. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupUserDefined", b =>
                {
                    b.Property<int>("UserDefinedId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("UserDefinedID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of LookupUserDefined)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("UserDefinedDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("UserDefinedFilter")
                        .HasColumnType("varchar(20)")
                        .HasComment("Filter that distinguishes user defined lookup categories (items of the same domain have the same name in this column)")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("UserDefinedLongDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Long description")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("UserDefinedModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("UserDefinedShortDescription")
                        .HasColumnType("varchar(20)")
                        .HasComment("Short description")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<int?>("UserDefinedValue")
                        .HasColumnType("int")
                        .HasComment("Value for the user defined domain item");

                    b.HasKey("UserDefinedId");

                    b.HasIndex("UserDefinedFilter", "UserDefinedValue")
                        .IsUnique()
                        .HasName("IX_LookupUserDefined")
                        .HasFilter("[UserDefinedFilter] IS NOT NULL AND [UserDefinedValue] IS NOT NULL");

                    b.ToTable("LookupUserDefined");

                    b.HasComment("Master data table that stores user defined domains. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mca", b =>
                {
                    b.Property<int>("McaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("McaID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of Mca)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("McaCommercial")
                        .HasColumnType("int")
                        .HasComment("Commercial score for the multi criteria analyse");

                    b.Property<int?>("McaIntensity")
                        .HasColumnType("int")
                        .HasComment("Intensity score for the multi criteria analyse");

                    b.Property<int?>("McaQualityLevel")
                        .HasColumnType("int")
                        .HasComment("Level of the multi criteria analyse depending of the business value (0,1,2) ");

                    b.Property<int?>("McaQualityScore")
                        .HasColumnType("int")
                        .HasComment("Total score (business value) of the multi criteria analyse");

                    b.Property<int?>("McaRepresentative")
                        .HasColumnType("int")
                        .HasComment("Representative score for the multi criteria analyse");

                    b.Property<int>("McaSiId")
                        .HasColumnName("McaSiID")
                        .HasColumnType("int")
                        .HasComment("ID of the significant item the multi criteria analyse belongs to (FK to Si)");

                    b.Property<int?>("McaSocial")
                        .HasColumnType("int")
                        .HasComment("Social score for the multi criteria analyse");

                    b.Property<int?>("McaUsage")
                        .HasColumnType("int")
                        .HasComment("Usage score for the multi criteria analyse");

                    b.Property<int?>("McaUtilization")
                        .HasColumnType("int")
                        .HasComment("Utilization score for the multi criteria analyse");

                    b.HasKey("McaId");

                    b.HasIndex("McaSiId")
                        .IsUnique()
                        .HasName("IX_Mca");

                    b.ToTable("Mca");

                    b.HasComment("Contains data for multiple criteria analysis. (MCA) MCA is a scientific procedure that allows for a rational choice based on more than one distinguishing criterium.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mrb", b =>
                {
                    b.Property<int>("Mrbid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("MRBId")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<decimal>("MrbActionCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Total cost per year for all tasks that are bound to the risk");

                    b.Property<decimal?>("MrbCapCosts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 2)")
                        .HasDefaultValueSql("((0))")
                        .HasComment("(?) not used in amprover software");

                    b.Property<int?>("MrbChildObject")
                        .HasColumnType("int")
                        .HasComment("ID of the object the risk belongs to (FK to Object)");

                    b.Property<int?>("MrbChildObject1")
                        .HasColumnType("int")
                        .HasComment("ID of the object (level 1) the risk belongs to (FK to Object)");

                    b.Property<int?>("MrbChildObject2")
                        .HasColumnType("int")
                        .HasComment("ID of the object (level 2) the risk belongs to (FK to Object)");

                    b.Property<int?>("MrbChildObject3")
                        .HasColumnType("int")
                        .HasComment("ID of the object (level 3) the risk belongs to (FK to Object)");

                    b.Property<int?>("MrbChildObject4")
                        .HasColumnType("int")
                        .HasComment("ID of the object (level 4) the risk belongs to (FK to Object)");

                    b.Property<int?>("MrbChildType")
                        .HasColumnType("int")
                        .HasComment("The way the risk is derived of another risk (eg child, master)");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18, 3)")
                        .HasComment("FMECA custom value after executing preventive actions for the risk");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18, 3)")
                        .HasComment("FMECA custom value before preventive actions for the risk are executed");

                    b.Property<decimal?>("MrbCustomEffectAfter")
                        .HasColumnType("decimal(18, 3)")
                        .HasComment("FMECA custom effect after executing preventive actions for the risk");

                    b.Property<decimal?>("MrbCustomEffectBefore")
                        .HasColumnType("decimal(18, 3)")
                        .HasComment("FMECA custom effect before executing preventive actions for the risk");

                    b.Property<DateTime?>("MrbDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("MrbDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("MrbDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the risk, contains the possible effects of the risk (?)")
                        .IsUnicode(false);

                    b.Property<decimal?>("MrbDirectCostAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Direct cost of the risk after executing preventive actions");

                    b.Property<decimal?>("MrbDirectCostBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Direct costs of the risk before executing preventive actions");

                    b.Property<decimal?>("MrbDownTimeAfter")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 4)")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Down time after executing preventive actions (in hours) caused by the risk (not used) (!)");

                    b.Property<decimal?>("MrbDownTimeBefore")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 4)")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Down time after preventive actions (in hours) caused by the risk (not used) (!)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("FMECA effect after executing preventive actions for the risk");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("FMECA effect before preventive actions for the risk are executed (needed for Priority)");

                    b.Property<string>("MrbExclOptCb")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(50)")
                        .HasDefaultValueSql("(N'false')")
                        .HasComment("?")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("MrbFailureCategorie1")
                        .HasColumnType("varchar(20)")
                        .HasComment("Failure category 1, used for the risk (FK to LookupFailCat)")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("MrbFailureCategorie2")
                        .HasColumnType("varchar(20)")
                        .HasComment("Failure category 2, used for the risk (FK to LookupFailCat)")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<string>("MrbFailureCause")
                        .HasColumnType("varchar(max)")
                        .HasComment("Cause of the risk  (does not seem to be used in the AMprover software !)")
                        .IsUnicode(false);

                    b.Property<string>("MrbFailureConsequences")
                        .HasColumnType("varchar(max)")
                        .HasComment("Consequences, should the described risk occur ")
                        .IsUnicode(false);

                    b.Property<int?>("MrbFailureMode")
                        .HasColumnType("int")
                        .HasComment("ID of the failure mode used for the risk (FK to LookupFailMode)");

                    b.Property<string>("MrbFmecaSelect")
                        .HasColumnType("varchar(max)")
                        .HasComment("Selected values of the FMECA matrix before and after preventive actions. Values are stored in XML format. ")
                        .IsUnicode(false);

                    b.Property<int>("MrbFmecaVersion")
                        .HasColumnType("int")
                        .HasComment("Version of the FMECA matrix used for this risk");

                    b.Property<string>("MrbInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by this user with this username")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("MrbMasterId")
                        .HasColumnName("MrbMasterID")
                        .HasColumnType("int")
                        .HasComment("ID of the risk this risk was copied from (master risk) (FK to MRB)");

                    b.Property<string>("MrbModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18, 3)")
                        .HasComment("MTBF in years after executing preventive actions for the risk");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18, 3)")
                        .HasComment("MTBF in years before executing preventive actions for the risk");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("Name describing the risk")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("MrbNorm")
                        .HasColumnType("varchar(150)")
                        .HasComment("Norm for the risk (seems to not be in use) (!)")
                        .HasMaxLength(150)
                        .IsUnicode(false);

                    b.Property<string>("MrbOpsProcedure")
                        .HasColumnType("varchar(max)")
                        .HasComment("? Not used in amprover software")
                        .IsUnicode(false);

                    b.Property<decimal>("MrbOptimalCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Optimal costs for preventive actions for the risk (based on risk after preventive action)");

                    b.Property<decimal?>("MrbPointsAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbPointsBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbPointsEffectAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbPointsEffectBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MrbRemarks")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks for the risk, which provides a place to leave any extra information ")
                        .IsUnicode(false);

                    b.Property<string>("MrbRemarks1")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the risk (not used) (!)")
                        .IsUnicode(false);

                    b.Property<string>("MrbResponsible")
                        .HasColumnType("varchar(150)")
                        .HasComment("Person or department responsible for the risk (? responsible for solving it, or monitoring?)")
                        .HasMaxLength(150)
                        .IsUnicode(false);

                    b.Property<decimal?>("MrbRiskAfter")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 2)")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Risk cost after executing preventive actions");

                    b.Property<decimal?>("MrbRiskBefore")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 2)")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Risk cost before executing preventive actions");

                    b.Property<int>("MrbRiskObject")
                        .HasColumnType("int")
                        .HasComment("ID of the risk object the risk belongs to (FK to RiskObject)");

                    b.Property<decimal>("MrbSpareCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs of spare parts that are needed for preventive/corrective measures");

                    b.Property<decimal>("MrbSpareManageCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs made to manage the spare parts that are needed for corrective/preventive measures (not used)");

                    b.Property<string>("MrbState")
                        .HasColumnType("varchar(10)")
                        .HasComment("State of the risk (? Is this still used?)")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.Property<int?>("MrbStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the risk (master, slave, or lock) (status is defined in Lookup)");

                    b.HasKey("Mrbid");

                    b.HasIndex("MrbFailureMode");

                    b.HasIndex("MrbRiskObject")
                        .HasName("RiskObject");

                    b.HasIndex("MrbChildObject", "MrbChildObject1", "MrbChildObject2", "MrbChildObject3", "MrbChildObject4")
                        .HasName("IX_MRB_ChildObject");

                    b.ToTable("TblMRB");

                    b.HasComment("Contains the data that defines risks, and their relations to other risks. It also contains everything needed to build the risk matrix and the monetary before/after values. (?)Risks can be defined within the Risk Analysis module. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.MrbImage", b =>
                {
                    b.Property<int>("MrbImageId")
                        .HasColumnName("MrbImageID")
                        .HasColumnType("int");

                    b.Property<DateTime>("MrbImageDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<byte[]>("MrbImageImageAfter")
                        .IsRequired()
                        .HasColumnType("image");

                    b.Property<byte[]>("MrbImageImageBefore")
                        .IsRequired()
                        .HasColumnType("image");

                    b.HasKey("MrbImageId");

                    b.ToTable("MrbImage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Object", b =>
                {
                    b.Property<int>("ObjId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("ObjID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of Object)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("ObjAvailableTime")
                        .HasColumnType("int")
                        .HasComment("Available time of the object. (Time the object is available for use) (Does not seem to be used by Amprover software)");

                    b.Property<DateTime?>("ObjDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("ObjDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("Object description")
                        .IsUnicode(false);

                    b.Property<string>("ObjFunction")
                        .HasColumnType("varchar(max)")
                        .HasComment("Function of the object. Provides a description that explains the functionality of this specific part of the model.")
                        .IsUnicode(false);

                    b.Property<byte[]>("ObjImage")
                        .HasColumnType("image")
                        .HasComment(" (does not seem to be used by the AMprover software) (!)");

                    b.Property<int>("ObjLevel")
                        .HasColumnType("int")
                        .HasComment("Level (hierarchy) of the object. The level determines what the object actually is. (a whole system, an assembly, etc.)");

                    b.Property<string>("ObjModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("ObjName")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the object")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<decimal?>("ObjNewValue")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Replacement value of the object.");

                    b.Property<int?>("ObjProductionTime")
                        .HasColumnType("int")
                        .HasComment("Production time. (Time the object can be used in production?) (Does not seem to be used by Amprover software)");

                    b.Property<string>("ObjShortKey")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("Short key of the object")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("ObjSiCategory")
                        .HasColumnType("int")
                        .HasComment("Relation to the kind of technical objects. SiCategories are defined in masterdata. (FK to LookupUserDefined) ");

                    b.Property<string>("ObjSiType")
                        .HasColumnType("varchar(50)")
                        .HasComment("The type of the related technical objects (?)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("ObjUsableTime")
                        .HasColumnType("int")
                        .HasComment("Usable time of the object. (Time the object can be used) (What's the difference with Utilization time ?) (Does not seem to be used by Amprover software)");

                    b.Property<int?>("ObjUtilizationTime")
                        .HasColumnType("int")
                        .HasComment("Utilization time of the object (time the object can be used for its specific function) (?) (Does not seem to be used by Amprover software)");

                    b.HasKey("ObjId");

                    b.ToTable("TblObject");

                    b.HasComment(@"Stores objects that are used for modelling risks. The object level determines what the object is. The levels and mapping to object type/name are defined in master data, by the items contained within the functional objects treenode. 

Objects define a part of the system we're modelling, and can describe an object, installation, system, assembly or component. The user can name 5 levels of objects. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexData", b =>
                {
                    b.Property<int>("OpexDataId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("OpexDataID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of OpexData)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("OpexDataCostType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("OpexDataDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<int?>("OpexDataInflationGroupId")
                        .HasColumnName("OpexDataInflationGroupID")
                        .HasColumnType("int")
                        .HasComment("ID of the inflation group that is used by the opex data (FK to LookupInflationGroup)");

                    b.Property<int?>("OpexDataMethod")
                        .HasColumnType("int")
                        .HasComment("Opex factor method value which is used for the 'opex data' (FK to Lookup)");

                    b.Property<string>("OpexDataModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("OpexDataName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the 'opex data'")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<decimal?>("OpexDataPercentage")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Precentage used for the 'opex data'");

                    b.Property<decimal?>("OpexDataValue")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Value used for the 'opex data'");

                    b.HasKey("OpexDataId");

                    b.ToTable("OpexData");

                    b.HasComment("Contains data that defines the operating expenditures (opex) caused by specific failure causes. The operating expenditures can be defined in the masterdata, and are used by the LCC calculations. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexFactor", b =>
                {
                    b.Property<int>("OpexFactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("OpexFactID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK to OpexFactor)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("OpexFactDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<decimal?>("OpexFactLookupValue")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("? Defined in Masterdata.");

                    b.Property<string>("OpexFactModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("OpexFactOpexDataId")
                        .HasColumnName("OpexFactOpexDataID")
                        .HasColumnType("int")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<decimal?>("OpexFactValue")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("? Defined in Masterdata.");

                    b.HasKey("OpexFactId");

                    b.HasIndex("OpexFactOpexDataId");

                    b.ToTable("OpexFactor");

                    b.HasComment("Contains factors that are used in operating expenditure calculations. 0 or more of these factors can be defined for each Opex data item, from the masterdata. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexToLcc", b =>
                {
                    b.Property<int>("OpexLccId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("OpexLccID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK to OpexToLCC)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("OpexLccColorName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the line color used for the 'opex to lcc' graph")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("OpexLccCycle")
                        .HasColumnType("int");

                    b.Property<int?>("OpexLccLccId")
                        .HasColumnName("OpexLccLccID")
                        .HasColumnType("int")
                        .HasComment("Lcc ID the 'opex to lcc' belongs to (FK to Lcc)");

                    b.Property<string>("OpexLccName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the 'opex to lcc'")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("OpexLccOpexDataId1")
                        .HasColumnName("OpexLccOpexDataID1")
                        .HasColumnType("int")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<int?>("OpexLccOpexDataId2")
                        .HasColumnName("OpexLccOpexDataID2")
                        .HasColumnType("int")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<int?>("OpexLccOpexDataId3")
                        .HasColumnName("OpexLccOpexDataID3")
                        .HasColumnType("int")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<decimal?>("OpexLccPrice")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Price for the 'opex to lcc' (price per unit/quantity)");

                    b.Property<decimal?>("OpexLccQuantity")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Physical quantity applicable for the 'opex to lcc'");

                    b.Property<int?>("OpexLccSequence")
                        .HasColumnType("int")
                        .HasComment("? Sequence used to keep the order of Opex to LCC items within the grid that is used to display them.");

                    b.Property<bool?>("OpexLccShowInGraph")
                        .HasColumnType("bit")
                        .HasComment("Boolean to show the 'opex to lcc' in the graph (shows up in graph when true)");

                    b.Property<int?>("OpexLccSumItem")
                        .HasColumnType("int")
                        .HasComment("?");

                    b.Property<decimal?>("OpexLccUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Number of units applicable for the 'opex to lcc'");

                    b.HasKey("OpexLccId");

                    b.HasIndex("OpexLccOpexDataId1");

                    b.HasIndex("OpexLccOpexDataId2");

                    b.HasIndex("OpexLccOpexDataId3");

                    b.ToTable("OpexToLCC");

                    b.HasComment("Contains operating expenditure (opex) data that is used by life cycle costs (LCC) calculation. These opex items can be found and defined from the LCC module, in the Opex tab (On the bottom half of the screen).");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexToLccDetail", b =>
                {
                    b.Property<int>("OpexLccDetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("OpexLccDetID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of OpexToLCCDetail)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<decimal?>("OpexLccDetAec")
                        .HasColumnName("OpexLccDetAEC")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Annual equivalent cost for the 'opex to lcc detail'");

                    b.Property<decimal?>("OpexLccDetCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs for this year of this 'opex to lcc detail'");

                    b.Property<int?>("OpexLccDetLccId")
                        .HasColumnName("OpexLccDetLccID")
                        .HasColumnType("int")
                        .HasComment("Lcc ID to which the 'opex to lcc detail' belongs (FK to Lcc)");

                    b.Property<decimal?>("OpexLccDetNpv")
                        .HasColumnName("OpexLccDetNPV")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Net present value for the 'opex to lcc detail'");

                    b.Property<int?>("OpexLccDetOpexLccId")
                        .HasColumnName("OpexLccDetOpexLccID")
                        .HasColumnType("int")
                        .HasComment("Opex lcc ID to which the 'opex to lcc detail' belongs (FK to OpexToLcc)");

                    b.Property<decimal?>("OpexLccDetPv")
                        .HasColumnName("OpexLccDetPV")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Present value for the 'opex to lcc detail'");

                    b.Property<int?>("OpexLccDetYear")
                        .HasColumnType("int")
                        .HasComment("Year for which the 'opex to lcc detail' contains opex data");

                    b.HasKey("OpexLccDetId");

                    b.ToTable("OpexToLccDetail");

                    b.HasComment("Contains operating expenditure (opex) data that is used to help calculate life cycle costing (LCC) detail costs. Will contain records for each LCC item, for each year of the LCC calculation. This provides a way to see and edit the costs per year of the life cycle of an item defined in the risk analysis. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PickSi", b =>
                {
                    b.Property<int>("PckSiId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PckSiID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("PckSiLinkFilterId")
                        .HasColumnName("PckSiLinkFilterID")
                        .HasColumnType("int");

                    b.Property<int>("PckSiMrbId")
                        .HasColumnName("PckSiMrbID")
                        .HasColumnType("int");

                    b.Property<int>("PckSiSiId")
                        .HasColumnName("PckSiSiID")
                        .HasColumnType("int");

                    b.HasKey("PckSiId")
                        .HasName("PK_PickMSI");

                    b.HasIndex("PckSiLinkFilterId");

                    b.HasIndex("PckSiMrbId");

                    b.HasIndex("PckSiSiId");

                    b.ToTable("PickSI");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Priority", b =>
                {
                    b.Property<int>("PrioId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PrioID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of Priority)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool?>("PrioAutoGenerated")
                        .HasColumnType("bit")
                        .HasComment("Boolean that display if the priority was auto generated (true if it was generated)");

                    b.Property<DateTime?>("PrioDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("PrioDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("PrioDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the priority, can be used to add any remarks that could clarify the existance of the priority group. ")
                        .IsUnicode(false);

                    b.Property<string>("PrioInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user with this username")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("PrioModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("PrioName")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("Name describing the priority group")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("PrioPartOf")
                        .HasColumnType("int")
                        .HasComment("Priority ID of the parent priority (FK to Priority)");

                    b.Property<string>("PrioPath")
                        .HasColumnType("varchar(250)")
                        .HasComment("Path to display the way the priority is linked to other priorities. Each linked item is an FK to Priority, they'll be separated by a '-' char.")
                        .HasMaxLength(250)
                        .IsUnicode(false);

                    b.Property<string>("PrioRemark")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks that should clarify the reason for the existance of the priority. ")
                        .IsUnicode(false);

                    b.Property<string>("PrioResponsible")
                        .HasColumnType("varchar(max)")
                        .HasComment("People or departments that are responsible for execution of the priority")
                        .IsUnicode(false);

                    b.Property<string>("PrioShortKey")
                        .HasColumnType("varchar(10)")
                        .HasComment("Short key of the priority")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.Property<int?>("PrioSiCategory")
                        .HasColumnType("int")
                        .HasComment("Si category ID the chosen significant item belongs to (FK to LookupUserDefined)");

                    b.Property<int?>("PrioSiId")
                        .HasColumnName("PrioSiID")
                        .HasColumnType("int")
                        .HasComment("Significant item ID the priority is bound to (FK to Si)");

                    b.Property<string>("PrioStatus")
                        .HasColumnType("varchar(20)")
                        .HasComment("Priority status, user can enter any string here. (?)")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.HasKey("PrioId");

                    b.HasIndex("PrioPartOf")
                        .HasName("IX_PriorityPartOf");

                    b.ToTable("Priority");

                    b.HasComment("Contains priority groups, which are generated from data in PriorityTask and PriorityBudget. A priority group combines groups of tasks that belong together (like tasks that need to be performed in one geographical location). (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityBudget", b =>
                {
                    b.Property<int>("PrioBudId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PrioBudID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK to PriorityBudget)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<decimal?>("PrioBudAccepted")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Calculated value for the accepted costs of the priority budget");

                    b.Property<decimal?>("PrioBudBudget")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Budget for priority group (amount) (?)");

                    b.Property<decimal?>("PrioBudBudgetCostYear1")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Priority budget costs for year 1");

                    b.Property<decimal?>("PrioBudBudgetCostYear2")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Priority budget costs for year 2");

                    b.Property<decimal?>("PrioBudBudgetCostYear3")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Priority budget costs for year 3");

                    b.Property<decimal?>("PrioBudBudgetCostYear4")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Priority budget costs for year 4");

                    b.Property<decimal?>("PrioBudBudgetCostYear5")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Priority budget costs for year 5");

                    b.Property<decimal?>("PrioBudBudgetSumOfParts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Calculated value for budget, is built from a sum of all priority item budgets from this priority group. (?) ");

                    b.Property<decimal?>("PrioBudCostSelected")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Calculated value. Selected cost for the priority budget");

                    b.Property<DateTime?>("PrioBudDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("PrioBudDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<bool?>("PrioBudEnableTaskSelect")
                        .HasColumnType("bit")
                        .HasComment("? Boolean that determines if the selected costs are added or not. (?) (true adds them)");

                    b.Property<string>("PrioBudInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user with this username")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("PrioBudModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("PrioBudOriginalId")
                        .HasColumnName("PrioBudOriginalID")
                        .HasColumnType("int")
                        .HasComment("Refers to itself, to keep track of where the priority budget was originally from (?) (FK to PriorityBudget)");

                    b.Property<decimal?>("PrioBudPostponed")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Calculated value for the postponed costs of the priority budget");

                    b.Property<int?>("PrioBudPriorityId")
                        .HasColumnName("PrioBudPriorityID")
                        .HasColumnType("int")
                        .HasComment("Priority item the priorityBudget refers to (FK to Priority)");

                    b.Property<decimal?>("PrioBudProposed")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Calculated value for the proposed costs of the priority budget");

                    b.Property<decimal?>("PrioBudRejected")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Calculated value for the rejected costs of the priority budget");

                    b.Property<string>("PrioBudRemark")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks that can clarify priority budget")
                        .IsUnicode(false);

                    b.Property<decimal?>("PrioBudRisk")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Risk costs for the current year");

                    b.Property<decimal?>("PrioBudRiskDelta")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("? Risk delta for the whole priority period, which is used for... (?) ");

                    b.Property<string>("PrioBudStatus")
                        .HasColumnType("varchar(20)")
                        .HasComment("Status of the priority budget (under review etc.)")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<bool?>("PrioBudTaskSelectDirty")
                        .HasColumnType("bit")
                        .HasComment("? Boolean that allows a different way of selecting task selection. (the dirty way) ");

                    b.Property<int?>("PrioBudVersion")
                        .HasColumnType("int")
                        .HasComment("Version (year and number) of the priority version which the priority budget is bound to (defined in PriorityVersion, but no FK)");

                    b.HasKey("PrioBudId");

                    b.HasIndex("PrioBudPriorityId", "PrioBudVersion")
                        .IsUnique()
                        .HasName("IX_PriorityBudget")
                        .HasFilter("[PrioBudPriorityID] IS NOT NULL AND [PrioBudVersion] IS NOT NULL");

                    b.ToTable("PriorityBudget");

                    b.HasComment("Defines budgets for each item in the prioritygroup. Editable in the priority groups control. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityCost", b =>
                {
                    b.Property<int>("PrioCstVersion")
                        .HasColumnType("int")
                        .HasComment("Version (year and number) of the priority version which the priority cost is bound to (PriorityVersion)");

                    b.Property<int>("PrioCstId")
                        .HasColumnName("PrioCstID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of PriorityCost)");

                    b.Property<decimal>("PrioCstCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The actual costs for this priority for this year ");

                    b.Property<decimal?>("PrioCstDirectCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The direct cost is the price that can be completely attributed to the production of specific goods and services. These costs refer to materials, labor and expenses related to the production of a product. ");

                    b.Property<decimal?>("PrioCstFailRate")
                        .HasColumnType("decimal(18, 6)")
                        .HasComment("Failure rate is the frequency with which an engineered system or component fails, expressed in failures per hour. (?)");

                    b.Property<int>("PrioCstNumberOfTasks")
                        .HasColumnType("int")
                        .HasComment("Number of tasks that are bound to this priority cost item");

                    b.Property<int>("PrioCstPrioId")
                        .HasColumnName("PrioCstPrioID")
                        .HasColumnType("int")
                        .HasComment("ID of the priority the priority cost is bound to (FK to Priority)");

                    b.Property<int?>("PrioCstPrioTskId")
                        .HasColumnName("PrioCstPrioTskID")
                        .HasColumnType("int")
                        .HasComment("ID of the priority task the priority cost is bound to (FK to PriorityTask)");

                    b.Property<decimal?>("PrioCstRiskCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The projected risk costs for this priority for this year (?)");

                    b.Property<decimal?>("PrioCstRiskDelta")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("?");

                    b.Property<int>("PrioCstYear")
                        .HasColumnType("int")
                        .HasComment("The year the priority costs have been calculated for. (?)");

                    b.HasKey("PrioCstVersion", "PrioCstId");

                    b.ToTable("PriorityCost");

                    b.HasComment("Defines the costs for each prioritygroup, for each year. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityTask", b =>
                {
                    b.Property<int>("PrioTskId")
                        .HasColumnName("PrioTskID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of PriorityTask)");

                    b.Property<bool?>("PrioTskAutoSelected")
                        .HasColumnType("bit")
                        .HasComment("? Boolean that determines if a task will be auto-selected for postponement. Set to true for auto-select. ");

                    b.Property<decimal?>("PrioTskBudgetCostYear1")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Priority task costs for year 1");

                    b.Property<decimal?>("PrioTskBudgetCostYear2")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Priority task costs for year 2");

                    b.Property<decimal?>("PrioTskBudgetCostYear3")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Priority task costs for year 3");

                    b.Property<decimal?>("PrioTskBudgetCostYear4")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Priority task costs for year 4");

                    b.Property<decimal?>("PrioTskBudgetCostYear5")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Priority task costs for year 5");

                    b.Property<decimal?>("PrioTskClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cluster cost per unit of the priority task. Retrieved from CltpCLusterCostPerUnit or CmnTaskCosts.");

                    b.Property<int?>("PrioTskClusterPlanId")
                        .HasColumnName("PrioTskClusterPlanID")
                        .HasColumnType("int")
                        .HasComment("ID of the cluster plan the priority task is bound to (FK to ClusterTaskPlan)");

                    b.Property<int?>("PrioTskCommonTaskId")
                        .HasColumnName("PrioTskCommonTaskID")
                        .HasColumnType("int")
                        .HasComment("ID of the common action the priority task is bound to (FK to CommonTask)");

                    b.Property<decimal?>("PrioTskCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Original cost for the priority task as calculated by the clusterTaskPlan");

                    b.Property<DateTime?>("PrioTskDateDue")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date when the priority task is due (deadline for task completion)");

                    b.Property<DateTime?>("PrioTskDateExecuted")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date when the priority task was executed");

                    b.Property<DateTime?>("PrioTskDateGenerated")
                        .HasColumnType("smalldatetime")
                        .HasComment("The priority task was generated on this date and time.");

                    b.Property<string>("PrioTskDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the priority task")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<decimal?>("PrioTskDirectCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("? Direct costs associated with this priority task. (costs for materials, man hours, etc) Does not seem to be used in the AMprover software (!) ");

                    b.Property<decimal?>("PrioTskDownTime")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Downtime needed to complete task (in hours)");

                    b.Property<decimal?>("PrioTskDuration")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Time spent on task (in hours)");

                    b.Property<int?>("PrioTskExecuteStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the priority task (postponed, proposed etc.)");

                    b.Property<int?>("PrioTskExecutionYear")
                        .HasColumnType("int")
                        .HasComment("We need to keep track of modifications in the execution date. The year will be used when the execution is modified. (?)");

                    b.Property<string>("PrioTskFromReference")
                        .HasColumnType("varchar(50)")
                        .HasComment(@"This is a composite field, containing several items, delimited by the '-' char. The field contains the following items:
Date due, RiskID, PrioTaskID, SiID, TaskType, and TaskID.

The field will be used for importing corrective tasks based on an inspection. ")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<bool?>("PrioTskImported")
                        .HasColumnType("bit")
                        .HasComment("Boolean that sets the imported status for the priority task (Does not seem to be used in the AMprover software) (!)");

                    b.Property<decimal?>("PrioTskIntervalPerYear")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Number of times the task is performed per year");

                    b.Property<decimal?>("PrioTskNumberOfTimes")
                        .HasColumnType("decimal(18, 3)")
                        .HasComment("? The amount of times a priority task will be executed. Based on the interval per year and the interval count.");

                    b.Property<int?>("PrioTskObjectId")
                        .HasColumnName("PrioTskObjectID")
                        .HasColumnType("int")
                        .HasComment("ID of the object the priority task is bound to (FK to Object)");

                    b.Property<int?>("PrioTskOriginalId")
                        .HasColumnName("PrioTskOriginalID")
                        .HasColumnType("int")
                        .HasComment("Task ID that is used to keep track of which task the priotask was originally created for. Seems to only be filled with the PrioTskID, is this still needed? (?)");

                    b.Property<decimal?>("PrioTskPostponePct")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("? Used in year cost calculation, as inflation correction factor.  ");

                    b.Property<decimal?>("PrioTskPriorityCode")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("? A code that defines the inspection priority. Defaults to 1. Value can be 1-100. (?)");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnName("PrioTskPriorityID")
                        .HasColumnType("int")
                        .HasComment("ID of the priority the priority task is bound to (FK to Priority)");

                    b.Property<int?>("PrioTskQualityScore")
                        .HasColumnType("int")
                        .HasComment("Business value of the bound significant item. Contains the quality score of the linked significant item. (if that's present)");

                    b.Property<string>("PrioTskReferenceId")
                        .HasColumnName("PrioTskReferenceID")
                        .HasColumnType("varchar(50)")
                        .HasComment(@"This is a composite field, containing several items, delimited by the '-' char. The field contains the following items:
Date due, RiskID, PrioTaskID, SiID, TaskType, and TaskID.

The field will be used for importing corrective tasks based on an inspection. ")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("PrioTskRemarks")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the priority task")
                        .IsUnicode(false);

                    b.Property<decimal?>("PrioTskRiskBudget")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The risk budget is defined as the costs per task. It can be calculated in different ways: By dividing the PrioTskRiskCost (or MrbRiskAfter) by the number of tasks, by looking at failrate type, failrate, age and effect after (and dividing that by number of tasks).");

                    b.Property<decimal?>("PrioTskRiskCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("? Filled with value of Mrb Risk after");

                    b.Property<decimal?>("PrioTskRiskDelta")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Calculated value that is used for determining priority of tasks. Calculated by subtracting the PrioTskRiskbudget from the MrbRiskbefore, and dividing that by number of tasks. The delta is the difference between the \"normal\" risk, and the risk after prioritizing tasks. (?)");

                    b.Property<decimal?>("PrioTskRiskFactor")
                        .HasColumnType("decimal(18, 3)")
                        .HasComment("? A variable used in risk calculations. Determined by looking at failure type, fail rate, age and mtbf.");

                    b.Property<int?>("PrioTskRiskId")
                        .HasColumnName("PrioTskRiskID")
                        .HasColumnType("int")
                        .HasComment("ID of the risk the priority task is bound to (FK to MRB)");

                    b.Property<bool?>("PrioTskSealed")
                        .HasColumnType("bit")
                        .HasComment("Boolean that seals the priority task. When this is true, the priority task is sealed. (which means it is no longer editable) ");

                    b.Property<int?>("PrioTskSelectionSeq")
                        .HasColumnType("int")
                        .HasComment("? Determines the selection sequence. A lower number will be processed sooner. ");

                    b.Property<int?>("PrioTskSequence")
                        .HasColumnType("int")
                        .HasComment("A calculation that uses fields: PrioTskRiskFactor, PrioTskQualityScore, PrioTskPriorityCode to determine what prioritytask should be executed first. (?)");

                    b.Property<string>("PrioTskSiDescription")
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the bound significant item, is filled with the Si name")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("PrioTskSiId")
                        .HasColumnName("PrioTskSiID")
                        .HasColumnType("int")
                        .HasComment("ID of the significant item the priority task is bound to (FK to Si)");

                    b.Property<decimal?>("PrioTskSiUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Number of significant item units for this priority task. Filled with value of CltpSiUnits.");

                    b.Property<int?>("PrioTskSlack")
                        .HasColumnType("int")
                        .HasComment("Slack for the priority task, which is a period of time a task can be postponed. Slack is measured in years. ");

                    b.Property<int?>("PrioTskSlackIntervalType")
                        .HasColumnType("int")
                        .HasComment("Interval unit ID of the slack for the priority task (FK to LookupIntervalUnit) The value is taken from CltpSlackIntervalType.");

                    b.Property<int?>("PrioTskTaskId")
                        .HasColumnName("PrioTskTaskID")
                        .HasColumnType("int")
                        .HasComment("ID of the task the priority task is bound to (FK to Task)");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int")
                        .HasComment("Version (year and number) of the priority task (versions need to be present in PriorityVersion)");

                    b.HasKey("PrioTskId");

                    b.HasIndex("PrioTskPriorityId")
                        .HasName("IX_PriorityTaskPriorityID");

                    b.HasIndex("PrioTskVersion")
                        .HasName("IX_PriorityTaskVersion");

                    b.ToTable("PriorityTask");

                    b.HasComment("Contains the tasks that have been prioritized. This data is generated from the priority box command center. Data is gathered from Mrb, RIskObject, Task, and Priority.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityVersion", b =>
                {
                    b.Property<int>("PrioVerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("PrioVerID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of PriorityVersion)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("PrioVerDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the priority version")
                        .IsUnicode(false);

                    b.Property<string>("PrioVerName")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the priority version")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("PrioVerRemark")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the priority version")
                        .IsUnicode(false);

                    b.Property<int?>("PrioVerScenario")
                        .HasColumnType("int")
                        .HasComment("Scenario the priority version was created for.");

                    b.Property<bool?>("PrioVerSealed")
                        .HasColumnType("bit")
                        .HasComment("Boolean to seal the priority version. Setting this to true seals it. ");

                    b.Property<int?>("PrioVerVersion")
                        .HasColumnType("int")
                        .HasComment("Version (year and number) of the priority version");

                    b.HasKey("PrioVerId");

                    b.ToTable("PriorityVersion");

                    b.HasComment(@"Each priority calculation is created around a PriorityVersion. Defining a priority version allows the user to generate different priority groups, which enables the user to see the differences in the resulting risks and costs between several priority configurations. 

The priority version is created from the priority box command center. A Priority version can be created for each scenario.
(?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Rams", b =>
                {
                    b.Property<int>("RamsDiagramId")
                        .HasColumnName("RamsDiagramID")
                        .HasColumnType("int")
                        .HasComment("ID of the RAMS diagram this RAMS block belongs to (FK to RamsDiagram)");

                    b.Property<int>("RamsId")
                        .HasColumnName("RamsID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of Rams)");

                    b.Property<double?>("RamsAvailabilityInput")
                        .HasColumnType("float")
                        .HasComment("Availability of the input of the RAMS block. The value lies between 0 and 1.");

                    b.Property<double?>("RamsAvailabilityOutput")
                        .HasColumnType("float")
                        .HasComment("Availability of the output of the RAMS block. The value lies between 0 and 1.");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float")
                        .HasComment("Weibull factor for this RAMS block. In reliability analysis, the weibull factor is used to clarify age-to-failure data. The weibull factor shows if the maintenance strategy should be better aimed at preventive or corrective maintenance. The value lies between 0 and 1.");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image")
                        .HasComment("Picture of the RAMS block. Was added to allow the user to store a picture of the physical structure the RAMS block represents. (Does not seem to be used in AMprover software) (!)");

                    b.Property<double?>("RamsBufferTime")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsCharacteristicLife")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The characteristic life (Ƞ) is the moment where 63.2% of the units will fail");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("? Production and corrective costs dependent on the MTBF of this RAMS block. In RAMS containers, this cost will be determined by the sum of the underlying blocks.");

                    b.Property<string>("RamsClassDc")
                        .HasColumnName("RamsClassDC")
                        .HasColumnType("varchar(6)")
                        .HasComment("Diagnostic Coverage (DC) class of the RAMS block. This class determines the level of detection of dangerous situations, and thus describes if a danger to this RAMS block would be detected. (dangerous detected, dangers undetected, etc.) ")
                        .HasMaxLength(6)
                        .IsUnicode(false);

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit")
                        .HasComment("Boolean that collapses the RAMS blocks within the RAMS block to one block. This is used to keep track of the state of each of the RAMS blocks, so the user will see the same configuration when opening the same diagram.");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit")
                        .HasComment("Boolean that set the analysis of the block to complete. The user decides when a RAMS block is complete, and can set that using a checkbox.");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit")
                        .HasComment("Boolean that makes the RAMS block behave as a container. When creating a RAMS block with the \"add a new container\" toolstrip button, this boolean will be set.");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit")
                        .HasComment("Boolean that, when set, lets RAMS blocks retrieve their costs from linked FMECA object. Can only be set when the block is linked to FMECA, and the cost owner bit is set. ");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit")
                        .HasComment("The RamsCostOwner is a bit that is set for containers that will then be the owner of the cost. When this is set, input of costs to blocks belonging to that container will be blocked.");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<double?>("RamsDcd")
                        .HasColumnName("RamsDCd")
                        .HasColumnType("float")
                        .HasComment("Diagnostic coverage of dangerous failures (DCD). This is a percentage for this RAMS block, that describes how many of the possibly dangerous failures that can occur would be detected. The value lies between 0 and 1. (?)");

                    b.Property<string>("RamsDescr")
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the RAMS block")
                        .IsUnicode(false);

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnName("RamsDiagramRefID")
                        .HasColumnType("int")
                        .HasComment("When a rams diagram is linked to another rams diagram, that reference will be stored here. When this reference is present, the rams block will not allow the user to alter any of the values used in calculations. ");

                    b.Property<int?>("RamsDistributionType")
                        .HasColumnType("int")
                        .HasComment("The distribution type specific to this RAMS block");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float")
                        .HasComment("Functional eco score of the RAMS block.  Eco score is a measurement of effect to the environment, and is measured with a score from 0 to 100. (closer to 100 means more environmentally friendly)");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float")
                        .HasComment("Technical eco score of the RAMS block. Eco score is a measurement of effect to the environment, and is measured with a score from 0 to 100. (closer to 100 means more environmentally friendly).");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Costs of the corrective measures taken after failure of this RAMS block.");

                    b.Property<int?>("RamsFailureMode")
                        .HasColumnType("int")
                        .HasComment("ID of the failure mode used for the RAMS block (FK to LookupFailMode)");

                    b.Property<string>("RamsFunctionalDemand")
                        .HasColumnType("varchar(max)")
                        .HasComment("? (Does not seem to be used by the AMprover software) Functional demand is defined as a demand for specific functionality, such as accessiblity and fire safety.")
                        .IsUnicode(false);

                    b.Property<int?>("RamsHft")
                        .HasColumnName("RamsHFT")
                        .HasColumnType("int");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit")
                        .HasComment("Boolean that allows the software to set the properties of all the blocks within the RAMS block to values identical to the first RAMS block. This user can set this during design. ");

                    b.Property<string>("RamsInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user with this username")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<double?>("RamsLabdaFunctional")
                        .HasColumnType("float");

                    b.Property<double?>("RamsLabdaTechnical")
                        .HasColumnType("float");

                    b.Property<bool>("RamsLccusePfd")
                        .HasColumnName("RamsLCCUsePFD")
                        .HasColumnType("bit");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int")
                        .HasComment("RAMS link method determines how the RAMS is linked to Mrb items. (Rams link methods are defined in Lookup, though it's the enum value that gets stored.) (?)");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int")
                        .HasComment("Linktype shows what the RAMS block is linked to (RAMS, RiskObject, Object, Risk, or Notset). This value determines how a RAMS block will be processed during LCC calculations.  ");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int")
                        .HasComment("ID of the RAMS block on the left side of the RAMS block (FK to Rams). Is set to -1 when there is no RAMS block to the left. ");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int")
                        .HasComment("ID of the RAMS block on the right side of the RAMS block (FK to Rams). Is set to -1 when there is no RAMS block to the right. ");

                    b.Property<string>("RamsModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<double?>("RamsMtbfFuncCalced")
                        .HasColumnType("float");

                    b.Property<double?>("RamsMtbfTecCalced")
                        .HasColumnType("float");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnName("RamsMTBFFunct")
                        .HasColumnType("float")
                        .HasComment("Functional mean time between failure (MTBF) for the RAMS block");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnName("RamsMTBFTechn")
                        .HasColumnType("float")
                        .HasComment("Technical mean time between failure (MTBF) for this RAMS block. ");

                    b.Property<double?>("RamsMttr")
                        .HasColumnName("RamsMTTR")
                        .HasColumnType("float")
                        .HasComment("Mean time to repair (MTTR) for this RAMS block. (The time it takes to repair the block when a failure occurs)");

                    b.Property<string>("RamsName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the RAMS block")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("RamsObjectId")
                        .HasColumnName("RamsObjectID")
                        .HasColumnType("int")
                        .HasComment("ID of the object the RAMS block belongs to (FK to Object)");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int")
                        .HasComment("Number of parallel RAMS blocks in this RAMS block. Will only be visible for containers, and is set automatically during design of RAMS diagram.");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int")
                        .HasComment("RAMS ID of the parent RAMS block (FK to Rams)");

                    b.Property<double?>("RamsPfd")
                        .HasColumnName("RamsPFD")
                        .HasColumnType("float")
                        .HasComment("Probability of failure on demand (PFD) for this RAMS block, which is the probability this RAMS block will fail when it is actually used. This value can be used to determine the Safety Integrity Level (SIL) ");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Total costs of preventive measures taken for this RAMS block");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float")
                        .HasComment("Functional productivity (time) of this RAMS block");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float")
                        .HasComment("Technical productivity (time) of this RAMS block");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int")
                        .HasComment("? The read sequence is used to rebuild a nested diagram in the correct order. The field is set automatically by methods DiagramToRows and DoDiagramToRows (in SyncRamsData).");

                    b.Property<double?>("RamsReliabilityFunctional")
                        .HasColumnType("float");

                    b.Property<double?>("RamsReliabilityTechnical")
                        .HasColumnType("float");

                    b.Property<string>("RamsRemark")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks for this RAMS block")
                        .IsUnicode(false);

                    b.Property<int?>("RamsRiskId")
                        .HasColumnName("RamsRiskID")
                        .HasColumnType("int")
                        .HasComment("ID of the risk the RAMS block belongs to (FK to MRB)");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnName("RamsRiskObjectID")
                        .HasColumnType("int")
                        .HasComment("ID of the risk object the RAMS block belongs to (FK to RiskObject)");

                    b.Property<decimal?>("RamsSff")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int?>("RamsSiId")
                        .HasColumnName("RamsSiID")
                        .HasColumnType("int")
                        .HasComment("ID of the significant item the RAMS block belongs to (FK to Si)");

                    b.Property<string>("RamsSil")
                        .HasColumnType("varchar(5)")
                        .HasMaxLength(5)
                        .IsUnicode(false);

                    b.Property<string>("RamsSilAc")
                        .HasColumnName("RamsSilAC")
                        .HasColumnType("varchar(5)")
                        .HasMaxLength(5)
                        .IsUnicode(false);

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the RAMS block. The possible statuses are defined in Lookup. ");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Total costs of technical corrective measures taken for this RAMS block (?)");

                    b.Property<int?>("RamsTestInterval")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18, 0)")
                        .HasComment("Total costs for this RAMS block");

                    b.Property<string>("RamsType")
                        .HasColumnType("varchar(6)")
                        .HasMaxLength(6)
                        .IsUnicode(false);

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float")
                        .HasComment("Functional utilization (time) of this RAMS block. Asset utilization represents the total revenue earned for every dollar of assets a company owns. (?)");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float")
                        .HasComment("Technical utilization (time) of this RAMS block. Asset utilization represents the total revenue earned for every dollar of assets a company owns.  (?)");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnName("RamsWantLCC")
                        .HasColumnType("bit")
                        .HasComment("Boolean that makes the RAMS block available for LCC calculations. When a RAMS block is defined as a container, this value will be set. The user can also enable this by setting the checkbox.");

                    b.Property<decimal?>("RamsWeibullShape")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("The Weibull shape (β) defines the slope of the current location on the Weibull curve");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int")
                        .HasComment("RAMS XooN is a number that shows how many RAMS blocks are contained within this RAMS block. Is only set to anything other than 1 for containers. (?)");

                    b.Property<int?>("RamsXposition")
                        .HasColumnName("RamsXPosition")
                        .HasColumnType("int")
                        .HasComment("X-position of the RAMS block in the RAMS diagram. Used to more easily rebuild the RAMS blocks in the correct relative order.");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int")
                        .HasComment("? Year the object the RAMS block represents was first used.");

                    b.Property<int?>("RamsYposition")
                        .HasColumnName("RamsYPosition")
                        .HasColumnType("int")
                        .HasComment("Y-position of the RAMS block in the RAMS diagram. Used to more easily rebuild the RAMS blocks in the correct relative order.");

                    b.HasKey("RamsDiagramId", "RamsId")
                        .HasName("PK_RAMS");

                    b.HasIndex("RamsSiId");

                    b.ToTable("Rams");

                    b.HasComment("This table defines RAMS objects. RAMS is a set of methods which are used to visualize the performance of a system, looking specifically at the system Reliability, Availability, Maintainability and Safety. (RAMS) The RAMS objects are user defined from the RAMS controls.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RamsDiagram", b =>
                {
                    b.Property<int>("RamsDgId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("RamsDgID")
                        .HasColumnType("int")
                        .HasComment("Unique ID")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<double>("RamsDgAvailableTime")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("float")
                        .HasDefaultValueSql("((8760))");

                    b.Property<bool>("RamsDgCalculateAvailability")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsDgCalculationCompatibilityMode")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDgDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("RamsDgDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("RamsDgDescr")
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the RAMS diagram")
                        .IsUnicode(false);

                    b.Property<int?>("RamsDgHorizon")
                        .HasColumnType("int")
                        .HasComment("Period applied to the RAMS diagram. (?)");

                    b.Property<string>("RamsDgInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("RamsDgModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("RamsDgName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the RAMS diagram")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("RamsDgPageBreaks")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<decimal>("RamsDgPeriodFrom")
                        .HasColumnType("decimal(18, 10)");

                    b.Property<decimal>("RamsDgPeriodTo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 10)")
                        .HasDefaultValueSql("((1))");

                    b.Property<string>("RamsDgPrerequisites")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<string>("RamsDgReferenceId")
                        .HasColumnName("RamsDgReferenceID")
                        .HasColumnType("varchar(30)")
                        .HasComment("Reference ID of the RAMS diagram (not bound and used) (!)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("RamsDgRemark")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the RAMS diagram")
                        .IsUnicode(false);

                    b.Property<int?>("RamsDgRiskObject")
                        .HasColumnType("int")
                        .HasComment("ID of the risk object linked to the RAMS diagram (FK to RiskObject)");

                    b.Property<int>("RamsDgScenId")
                        .HasColumnName("RamsDgScenID")
                        .HasColumnType("int")
                        .HasComment("ID of the scenario the RAMS diagram belongs to (FK to Scenario)");

                    b.Property<string>("RamsDgSerialized")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RamsDgSiId")
                        .HasColumnName("RamsDgSiID")
                        .HasColumnType("int")
                        .HasComment("ID of the significant item bound to the RAMS diagram (FK to Si)");

                    b.Property<string>("RamsDgSiRefId")
                        .HasColumnName("RamsDgSiRefID")
                        .HasColumnType("varchar(50)")
                        .HasComment("Reference ID of the significant item bound to the RAMS diagram. (Does not seem to be in use in the AMprover software) (!)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("RamsDgStatus")
                        .HasColumnType("int")
                        .HasComment("Rams status value of the RAMS diagram (FK to Lookup) (Does not seem to be used in AMprover software) (!)");

                    b.Property<int?>("RamsDgTestInterval")
                        .HasColumnType("int")
                        .HasComment("Test interval for the RAMS diagram. ");

                    b.Property<bool?>("RamsDgWantLcc")
                        .HasColumnName("RamsDgWantLCC")
                        .HasColumnType("bit")
                        .HasComment("Boolean that makes the RAMS diagram available for LCC calculations");

                    b.HasKey("RamsDgId")
                        .HasName("PK_RamsDetail_1");

                    b.ToTable("RamsDiagram");

                    b.HasComment("Stores the graphical representation of the RAMS analysis. Some specific properties can be set through the controls of the properties tab within the RAMS analysis.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RiskObject", b =>
                {
                    b.Property<int>("RiskObjId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("RiskObjID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK to RiskObject)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("RiskObjAbmstate")
                        .HasColumnName("RiskObjABMstate")
                        .HasColumnType("varchar(50)")
                        .HasComment("State of the risk object (master, revision etc.) (? what is ABM in this context?)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("RiskObjAmrbattended")
                        .HasColumnName("RiskObjAMRBattended")
                        .HasColumnType("varchar(max)")
                        .HasComment("People who have attended the risk analysis session. (Does not seem to be used by AMprover software) (!)")
                        .IsUnicode(false);

                    b.Property<string>("RiskObjAnalyseType")
                        .HasColumnType("varchar(10)")
                        .HasComment("Analysis type of the risk object. The analysis type is defined from the masterdata, though the string value is stored here. (Lookup)")
                        .HasMaxLength(10)
                        .IsUnicode(false);

                    b.Property<int?>("RiskObjCopiedFrom")
                        .HasColumnType("int")
                        .HasComment("When a risk object was copied from another risk object, this field contains the ID of the source risk object (master risk object) (FK to RiskObject)");

                    b.Property<DateTime?>("RiskObjDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<int>("RiskObjFmecaId")
                        .HasColumnName("RiskObjFmecaID")
                        .HasColumnType("int")
                        .HasComment("ID of the FMECA matrix used for this risk object. (FK to Fmeca)");

                    b.Property<byte[]>("RiskObjFuncDecomp")
                        .HasColumnType("image")
                        .HasComment("Picture of the functional decomposition of the risk object");

                    b.Property<int?>("RiskObjLccyear")
                        .HasColumnName("RiskObjLCCYear")
                        .HasColumnType("int")
                        .HasComment("Optimal lifetime of the risk object (not used)");

                    b.Property<string>("RiskObjModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("RiskObjMrbstartPoint")
                        .HasColumnName("RiskObjMRBstartPoint")
                        .HasColumnType("varchar(max)")
                        .HasComment("Start point of the risk object. (Does not seem to be used in the AMprover software) (!) (?)")
                        .IsUnicode(false);

                    b.Property<string>("RiskObjName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the risk object")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("RiskObjNoOfInstallation")
                        .HasColumnType("int")
                        .HasComment("Number of objects contained within the risk object. (? Or the number of installations of its kind?) Used in LCC calculations.");

                    b.Property<int>("RiskObjObjectId")
                        .HasColumnName("RiskObjObjectID")
                        .HasColumnType("int")
                        .HasComment("ID of the object, which is a level 1 risk object (FK to Object) (?)");

                    b.Property<int?>("RiskObjParentObjectId")
                        .HasColumnName("RiskObjParentObjectID")
                        .HasColumnType("int")
                        .HasComment("ID of the parent object, which is a level 0 risk object (FK to Object)");

                    b.Property<decimal?>("RiskObjProdCostHour")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Production costs (per hour) for this risk object");

                    b.Property<string>("RiskObjResponsible")
                        .HasColumnType("varchar(max)")
                        .HasComment("Person or department that are responsible for this risk object")
                        .IsUnicode(false);

                    b.Property<decimal?>("RiskObjRforSpares")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 4)")
                        .HasDefaultValueSql("((0.95))")
                        .HasComment("Given reliability for the spare parts");

                    b.Property<int>("RiskObjScenarioId")
                        .HasColumnName("RiskObjScenarioID")
                        .HasColumnType("int")
                        .HasComment("ID of the scenario that contains the risk object (FK to Scenario)");

                    b.Property<int?>("RiskObjStatus")
                        .HasColumnType("int");

                    b.Property<int?>("RiskObjVolgNo")
                        .HasColumnType("int")
                        .HasComment("Serial number of the risk object. (Does not seem to be used by the AMprover software) (!)");

                    b.HasKey("RiskObjId");

                    b.HasIndex("RiskObjFmecaId");

                    b.HasIndex("RiskObjObjectId");

                    b.HasIndex("RiskObjScenarioId");

                    b.ToTable("TblRiskObject");

                    b.HasComment("Stores riskobjects, which are defined within the risk analysis. A risk object is an asset, of which we want to know the risks that are present during normal usage. A risk object contains risks, or systems. (?)   ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Scenario", b =>
                {
                    b.Property<int>("ScenId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("ScenID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of Scenario)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("ScenChildType")
                        .HasColumnType("int")
                        .HasComment(@"Determines the typeof relation the scenario has with its parent.
It can be a plain copy (version 3.15 and before) 
or
Derived scenario for creating a scenario with overriden risks / tasks but keeping the parent risks/tasks
");

                    b.Property<int?>("ScenCopiedFrom")
                        .HasColumnType("int")
                        .HasComment(@"Used when scenario was copied from another scenario. Contains the ID of the scenario which was the source of the copy (master scenario) (FK to Scenario)
This ID is used in the tree for building hierarchical scenario's");

                    b.Property<DateTime?>("ScenDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("ScenDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<int>("ScenDepartment")
                        .HasColumnType("int")
                        .HasComment("Department ID the scenario belongs to (FK to Department)");

                    b.Property<string>("ScenDescr")
                        .HasColumnType("text")
                        .HasComment("Description of the scenario");

                    b.Property<string>("ScenInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("ScenModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("ScenName")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the scenario")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("ScenShrtKey")
                        .IsRequired()
                        .HasColumnType("char(4)")
                        .IsFixedLength(true)
                        .HasComment("Short key of the scenario")
                        .HasMaxLength(4)
                        .IsUnicode(false);

                    b.Property<string>("ScenStartPoint")
                        .HasColumnType("text")
                        .HasComment("Start point of the scenario");

                    b.Property<int?>("ScenStatus")
                        .HasColumnType("int");

                    b.HasKey("ScenId")
                        .HasName("PK_Scen");

                    b.HasIndex("ScenDepartment");

                    b.ToTable("TblScenario");

                    b.HasComment("Allows the user to define risks differently, which enables them to see the differences in outcome when taking a different approach to maintenance.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Si", b =>
                {
                    b.Property<int>("SiId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("SiID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of Si)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("SiAssetManager")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the responsible asset manager of the item")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("SiAssetOwner")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the owner of the item")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("SiAssetType")
                        .HasColumnType("varchar(40)")
                        .HasComment("Logical classification of the item (eg elevator)")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<int?>("SiBvId")
                        .HasColumnName("SiBvID")
                        .HasColumnType("int")
                        .HasComment("The ID of the businessvalue where the item belongs to");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int")
                        .HasComment("SI category value the significant item belongs to (stores value of SICategory item from LookupUserDefined, which can be defined in the master data) (! should be named SICategoryValue?)");

                    b.Property<DateTime?>("SiContractEnd")
                        .HasColumnType("datetime")
                        .HasComment("End of contract date for the significant item ");

                    b.Property<DateTime?>("SiDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("SiDescription")
                        .HasColumnType("varchar(100)")
                        .HasComment("Description of the significant item")
                        .HasMaxLength(100)
                        .IsUnicode(false);

                    b.Property<decimal?>("SiHeight")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Height of the significant item");

                    b.Property<decimal?>("SiLength")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Length of the significant item");

                    b.Property<string>("SiLocation")
                        .HasColumnType("varchar(50)")
                        .HasComment("Geographical location of the item")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<bool?>("SiMiscBitField1")
                        .HasColumnType("bit")
                        .HasComment("Boolean that sets the significant item to SHE critical. (what is SHE critical?) ");

                    b.Property<DateTime?>("SiMiscDateField1")
                        .HasColumnType("datetime")
                        .HasComment("Miscellaneous date field for the significant item (not used)");

                    b.Property<decimal?>("SiMiscDecimalField1")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Miscellaneous decimal field for the significant item (not used) (!)");

                    b.Property<decimal?>("SiMiscDecimalField2")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Miscellaneous decimal field for the significant item (not used) (!)");

                    b.Property<string>("SiMiscTextField1")
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("SiMiscTextField2")
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("SiMiscTextField3")
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("SiMiscTextField4")
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("SiMiscTextField5")
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("SiMiscTextField6")
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("SiMiscTextField7")
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("SiModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<decimal?>("SiMtbf")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Mean time between failures for this significant item. (?) (not used)");

                    b.Property<string>("SiName")
                        .IsRequired()
                        .HasColumnType("varchar(40)")
                        .HasComment("Name of the significant item")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<int?>("SiPartOf")
                        .HasColumnType("int")
                        .HasComment("Si ID of the parent significant item (FK to Si)");

                    b.Property<decimal?>("SiPrice")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Price of the significant item. Used for LCC calculations.");

                    b.Property<int?>("SiQualityScore")
                        .HasColumnType("int")
                        .HasComment("Business value of the significant item (Quality scores are defined in Mca, but user can enter any number here) (?)");

                    b.Property<string>("SiReferenceId")
                        .HasColumnName("SiReferenceID")
                        .HasColumnType("varchar(40)")
                        .HasComment("Reference ID of the significant item. This shows what this significant item is attached to. Each connected significant item is separated by a '-'. (?)")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("SiRemarks")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the significant item")
                        .IsUnicode(false);

                    b.Property<string>("SiSerialNumber")
                        .HasColumnType("varchar(40)")
                        .HasComment("Unique serial number of the item")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("SiServiceManager")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the service manager of the item")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("SiServiceProvider")
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the service provider of the item")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("SiSite")
                        .HasColumnType("varchar(40)")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<int?>("SiStatus")
                        .HasColumnType("int");

                    b.Property<bool?>("SiSupplierBitField1")
                        .HasColumnType("bit")
                        .HasComment("Supplier boolean field (not used) (!)");

                    b.Property<DateTime?>("SiSupplierDateField1")
                        .HasColumnType("datetime")
                        .HasComment("Supplier date field (not used) (!)");

                    b.Property<int?>("SiSupplierIntField1")
                        .HasColumnType("int")
                        .HasComment("Supplier integer field (not used) (!)");

                    b.Property<string>("SiSupplierTextField1")
                        .HasColumnType("varchar(30)")
                        .HasComment("Supplier of the significant item.")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("SiSupplierTextField2")
                        .HasColumnType("varchar(40)")
                        .HasComment("Supplier text field (not used) (!)")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<string>("SiSupplierTextField3")
                        .HasColumnType("varchar(40)")
                        .HasComment("Supplier text field (not used) (!)")
                        .HasMaxLength(40)
                        .IsUnicode(false);

                    b.Property<decimal?>("SiTotalUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Total number of units for this significant item");

                    b.Property<string>("SiType")
                        .HasColumnType("varchar(30)")
                        .HasComment("Type of significant item. User can enter any string here.")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<string>("SiUnitType")
                        .HasColumnType("varchar(15)")
                        .HasComment("SI unit type which applies to the significant item (possible values are stored in Lookup)")
                        .HasMaxLength(15)
                        .IsUnicode(false);

                    b.Property<decimal?>("SiUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Number of units for this significant item");

                    b.Property<string>("SiUnitsTypeValues")
                        .HasColumnType("varchar(150)")
                        .HasComment("Value of each unit type, stored in a sequence, items are separated by '|'")
                        .HasMaxLength(150)
                        .IsUnicode(false);

                    b.Property<string>("SiVendor")
                        .HasColumnType("varchar(30)")
                        .HasComment("Vendor of the significant item")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("SiWarrantyPeriod")
                        .HasColumnType("int")
                        .HasComment("Number of years of warranty for the significant item. The warranty will run out this amount of years after the construction year. (SiYear) ");

                    b.Property<decimal?>("SiWidth")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Width of the significant item");

                    b.Property<DateTime?>("SiYear")
                        .HasColumnType("datetime")
                        .HasComment("Construction year of the significant item");

                    b.HasKey("SiId")
                        .HasName("PK_MSI");

                    b.HasIndex("SiCategory")
                        .HasName("IX_Si_Category");

                    b.HasIndex("SiName")
                        .HasName("IX_Si");

                    b.HasIndex("SiPartOf")
                        .HasName("IX_tblSiPartOf");

                    b.ToTable("TblSi");

                    b.HasComment("Contains significant items (SI's). A significant item is an item which represents a physical object we want to measure the risks for. The significant item can be assigned to parts of the functional decomposition. The significant items and their  relationships are defined in the master data. Assignment of significant items to risk objects occurs in the Risk analysis.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiLinkFilters", b =>
                {
                    b.Property<int>("SifId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("SifID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of SiLinkFilters)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("SifCategory")
                        .HasColumnType("int")
                        .HasComment("Sif category value the significant item belongs to (stores value of SICategory item from LookupUserDefined, which can be defined in the master data) (! should be named SifCategoryValue?)");

                    b.Property<int?>("SifChildObject1Id")
                        .HasColumnName("SifChildObject1ID")
                        .HasColumnType("int");

                    b.Property<int?>("SifChildObject2Id")
                        .HasColumnName("SifChildObject2ID")
                        .HasColumnType("int");

                    b.Property<int?>("SifChildObject3Id")
                        .HasColumnName("SifChildObject3ID")
                        .HasColumnType("int");

                    b.Property<int?>("SifChildObject4Id")
                        .HasColumnName("SifChildObject4ID")
                        .HasColumnType("int");

                    b.Property<int?>("SifChildObjectId")
                        .HasColumnName("SifChildObjectID")
                        .HasColumnType("int");

                    b.Property<string>("SifDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("Contains a text representation of the defined filter. This representation is human readable, and was added to allow for some oversight while defining filters.")
                        .IsUnicode(false);

                    b.Property<bool>("SifExcludeFromParent")
                        .HasColumnType("bit");

                    b.Property<string>("SifFilter")
                        .HasColumnType("varchar(max)")
                        .HasComment("The actual filter, stored in XML. Filters contain 0-n sqlSelectItems. An SqlSelectItem is added for each column the user adds a filter for. ")
                        .IsUnicode(false);

                    b.Property<int?>("SifFilterId")
                        .HasColumnName("SifFilterID")
                        .HasColumnType("int");

                    b.Property<string>("SifName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Descriptive name for the filter")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("SifObjectId")
                        .HasColumnName("SifObjectID")
                        .HasColumnType("int")
                        .HasComment("ID of the object the selected Si items are a part of. (FK to Object)");

                    b.Property<int?>("SifRiskId")
                        .HasColumnName("SifRiskID")
                        .HasColumnType("int")
                        .HasComment("ID of the risk object the selected Si items are a part of. (FK to RiskObject)");

                    b.Property<int?>("SifRiskObjectId")
                        .HasColumnName("SifRiskObjectID")
                        .HasColumnType("int");

                    b.Property<int?>("SifTaskId")
                        .HasColumnName("SifTaskID")
                        .HasColumnType("int");

                    b.HasKey("SifId");

                    b.HasIndex("SifChildObject1Id");

                    b.HasIndex("SifChildObject2Id");

                    b.HasIndex("SifChildObject3Id");

                    b.HasIndex("SifChildObject4Id");

                    b.HasIndex("SifChildObjectId");

                    b.HasIndex("SifFilterId");

                    b.HasIndex("SifObjectId");

                    b.HasIndex("SifRiskId");

                    b.HasIndex("SifRiskObjectId");

                    b.HasIndex("SifTaskId");

                    b.ToTable("SiLinkFilters");

                    b.HasComment(@"Contains a variation of the filters stored in Filter, which are specific to (linking) significant items. These filters are used to limit the amount of data the user has to search through when linking significant items to functional objects. 

Filters are defined from the Risk analysis, switch to Si assignment tab, and click on the advanced button to start defining a new filter.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiStatistics", b =>
                {
                    b.Property<int>("SiStatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("SiStatID")
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("SiStatColorListAfter")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<string>("SiStatColorListBefore")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<DateTime?>("SiStatDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("SiStatFmecaId")
                        .HasColumnName("SiStatFmecaID")
                        .HasColumnType("int");

                    b.Property<string>("SiStatModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<decimal?>("SiStatRiskAfter")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 0)")
                        .HasDefaultValueSql("((0))");

                    b.Property<decimal?>("SiStatRiskBefore")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 0)")
                        .HasDefaultValueSql("((0))");

                    b.Property<int>("SiStatScenarioId")
                        .HasColumnName("SiStatScenarioID")
                        .HasColumnType("int");

                    b.Property<int>("SiStatSiId")
                        .HasColumnName("SiStatSiID")
                        .HasColumnType("int");

                    b.Property<string>("SiStatSumListAfter")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.Property<string>("SiStatSumListBefore")
                        .HasColumnType("varchar(max)")
                        .IsUnicode(false);

                    b.HasKey("SiStatId")
                        .HasName("PK_SiStatistics");

                    b.HasIndex("SiStatScenarioId")
                        .HasName("IX_SiStatistics_ScenarioId");

                    b.HasIndex("SiStatSiId")
                        .HasName("IX_SiStatisticsSiID");

                    b.ToTable("SiStatistics");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Spare", b =>
                {
                    b.Property<int>("SpareId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("SpareID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK to Spare)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("SpareCategory")
                        .HasColumnType("varchar(50)")
                        .HasComment("Category the spare part belongs to (?)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<int?>("SpareChildType")
                        .HasColumnType("int");

                    b.Property<int?>("SpareCopiedFrom")
                        .HasColumnType("int")
                        .HasComment("For derived scenarios's you will need this field to keep track of changes");

                    b.Property<decimal?>("SpareCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost of the spare part. ");

                    b.Property<decimal?>("SpareDepreciationPct")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Depreciation percentage of the spare part. ");

                    b.Property<int>("SpareMrbId")
                        .HasColumnName("SpareMrbID")
                        .HasColumnType("int")
                        .HasComment("Risk ID the spare part belongs to (FK to MRB)");

                    b.Property<string>("SpareName")
                        .HasColumnType("varchar(60)")
                        .HasComment("Name of the spare part")
                        .HasMaxLength(60)
                        .IsUnicode(false);

                    b.Property<int?>("SpareNoOfItems")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValueSql("((1))")
                        .HasComment("Number of units of the spare part that are needed. (used in price calculation)");

                    b.Property<int?>("SpareObjectCount")
                        .HasColumnType("int")
                        .HasComment("Number of objects that can make use of the spare part.");

                    b.Property<decimal?>("SpareOrderLeadTime")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Lead time of the spare part. (The time it takes between ordering the part, and receiving it where it's needed)");

                    b.Property<decimal?>("SparePurchasePrice")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Purchase price of the spare part");

                    b.Property<int?>("SparePurchaseYear")
                        .HasColumnType("int")
                        .HasComment("Year the spare part was purchased.");

                    b.Property<string>("SpareReferenceId")
                        .HasColumnName("SpareReferenceID")
                        .HasColumnType("varchar(50)")
                        .HasComment("Reference ID of the spare part (not bound) (!)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<decimal?>("SpareReliability")
                        .HasColumnType("decimal(18, 3)");

                    b.Property<string>("SpareRemarks")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the spare part")
                        .IsUnicode(false);

                    b.Property<int?>("SpareStockNumber")
                        .HasColumnType("int")
                        .HasComment("Stock number of the spare part. ");

                    b.Property<string>("SpareSupplierId")
                        .HasColumnName("SpareSupplierID")
                        .HasColumnType("varchar(50)")
                        .HasComment("Supplier ID of the spare part, which can be used to order the spare part directly. ")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("SpareVendorId")
                        .HasColumnName("SpareVendorID")
                        .HasColumnType("varchar(50)")
                        .HasComment("The vendor that sells the spare part (! not an ID)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<decimal?>("SpareYearlyCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Yearly costs for the spare part (storage, depreciation, etc) (! does not seem to be used by AMprover software)");

                    b.HasKey("SpareId");

                    b.HasIndex("SpareMrbId");

                    b.ToTable("TblSpare");

                    b.HasComment(@"Contains spare parts, their costs and other data associated with storing them. 

Spare parts can be defined using the Spare parts grid control found in the Risk analysis screen, and can be added to any Risk.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Task", b =>
                {
                    b.Property<int>("TskId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("TskID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of Task)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int")
                        .HasComment("Cluster ID the task belongs to (FK to Cluster)");

                    b.Property<bool?>("TskClusterCostMember")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((1))")
                        .HasComment("Boolean that makes the task cluster cost a member (Cluster costs are spread evenly over the tasks that were set as cost member)");

                    b.Property<decimal?>("TskClusterCostPerUnit")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost per unit for the task calculated during clustering (?)");

                    b.Property<decimal?>("TskClusterCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Cost for the task calculated during clustering");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnName("TskCommonActionID")
                        .HasColumnType("int")
                        .HasComment("ID of the common task this task is a part of (FK to CommonTask)");

                    b.Property<int?>("TskCopiedFrom")
                        .HasColumnType("int")
                        .HasComment("Source ID the task was copied from (master task) (FK to Task) Will only be filled for tasks that were created as copies of other tasks. ");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Contains the costs associated with this task. (? total, estimated?)");

                    b.Property<DateTime?>("TskDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("TskDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<bool?>("TskDerived")
                        .HasColumnType("bit")
                        .HasComment("Is the task derived from another task");

                    b.Property<string>("TskDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the task")
                        .IsUnicode(false);

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Downtime needed to complete the task (in hours)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Time spent executing the task (in hours)");

                    b.Property<decimal?>("TskEstCostPerUnit")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Estimated cost per unit for this task");

                    b.Property<decimal?>("TskEstCosts")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Estimated cost for the task");

                    b.Property<DateTime?>("TskExecutionDate")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date when the task must be executed");

                    b.Property<int?>("TskExecutor")
                        .HasColumnType("int")
                        .HasComment("ID of the executor of the task (FK to LookupExecutor)");

                    b.Property<bool?>("TskExtraBool2")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<bool?>("TskExtraBool3")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<bool?>("TskExtraBool4")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<DateTime?>("TskFinishDate")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date when the task needs to have been executed");

                    b.Property<string>("TskFmecaEffect")
                        .HasColumnType("varchar(max)")
                        .HasComment("Effect that the task has for each FMECA column. This field contains an xml representation of an entire fmeca matrix.")
                        .IsUnicode(false);

                    b.Property<decimal?>("TskFmecaEffectPct")
                        .HasColumnType("numeric(18, 2)")
                        .HasComment("Effect that the task has on each FMECA column (?)");

                    b.Property<int>("TskFmecaVersion")
                        .HasColumnType("int")
                        .HasComment("Version of the FMECA matrix that is used. Allows tasks to be defined for different fmeca configurations.");

                    b.Property<string>("TskGeneralDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("General description of the task")
                        .IsUnicode(false);

                    b.Property<string>("TskInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int>("TskInitiator")
                        .HasColumnType("int")
                        .HasComment("ID of the initiator of the task (FK to LookupInitiator)");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18, 4)")
                        .HasComment("Interval of the task. The interval unit determines what the interval stands for (times yearly, monthly, etc) ");

                    b.Property<int>("TskIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("ID of the the interval unit of the task (FK to LookupIntervalUnit). The interval unit determines what the value stored in TskInterval really means.");

                    b.Property<string>("TskLcceffect")
                        .HasColumnName("TskLCCEffect")
                        .HasColumnType("varchar(max)")
                        .HasComment("? Effect that the task has for LCC calculations. This field contains an xml representation of an entire fmeca matrix, but now containing LCC effects for each field in the matrix.")
                        .IsUnicode(false);

                    b.Property<bool?>("TskMaster")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<string>("TskModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("TskMrbId")
                        .HasColumnName("TskMrbID")
                        .HasColumnType("int")
                        .HasComment("Risk ID the task belongs to (FK to MRB)");

                    b.Property<int?>("TskMxPolicy")
                        .HasColumnType("int")
                        .HasComment("ID of the maintenance policy of the task (FK to LookupMxPolicy)");

                    b.Property<string>("TskName")
                        .IsRequired()
                        .HasColumnType("varchar(60)")
                        .HasComment("Name of the task")
                        .HasMaxLength(60)
                        .IsUnicode(false);

                    b.Property<string>("TskNorm")
                        .HasColumnType("varchar(max)")
                        .HasComment("Norm for the task")
                        .IsUnicode(false);

                    b.Property<decimal?>("TskOptimalCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Optimal cost for the task");

                    b.Property<int?>("TskPartOf")
                        .HasColumnType("int")
                        .HasComment("Task ID of the parent task (FK to Task)");

                    b.Property<string>("TskPermit")
                        .HasColumnType("varchar(max)")
                        .HasComment("Permit(s) needed before being allowed to execute the task")
                        .IsUnicode(false);

                    b.Property<int?>("TskPriorityCode")
                        .HasColumnType("int")
                        .HasComment("? Does not seem to be used by AMprover software (!)");

                    b.Property<int?>("TskReferenceId")
                        .HasColumnName("TskReferenceID")
                        .HasColumnType("int");

                    b.Property<string>("TskRemark")
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the task")
                        .IsUnicode(false);

                    b.Property<bool?>("TskRemoved")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<string>("TskResponsible")
                        .HasColumnType("varchar(50)")
                        .HasComment("Person or department responsible for execution of the task. (Does not seem to be used by AMprover software) (!)")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<bool?>("TskSkipInLcc")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<int?>("TskSortOrder")
                        .HasColumnType("int")
                        .HasComment("Custom way to order the tasks. User can manually alter the order in which tasks will be executed by changing the numbers of this field. ");

                    b.Property<int?>("TskStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the task (master or copy)");

                    b.Property<string>("TskType")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasComment("Type of task (task, procedure etc.). The possible types are defined in Lookup, MeasureType. (?)")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.Property<int>("TskUnitType")
                        .HasColumnType("int")
                        .HasComment("Unit type ID used for the task (FK to LookupUserDefined)");

                    b.Property<decimal?>("TskUnits")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Number of units needed for the task");

                    b.Property<int?>("TskValidFromYear")
                        .HasColumnType("int")
                        .HasComment("The year when the common task becomes valid. (In LCC calculations, items with a ValidFromYear < the current year will not be part of the calculations.)");

                    b.Property<int?>("TskValidUntilYear")
                        .HasColumnType("int")
                        .HasComment("The last year this task is still valid");

                    b.Property<decimal?>("TskWorkInspCost")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Costs made for inspection during/for this task");

                    b.Property<int?>("TskWorkpackage")
                        .HasColumnType("int")
                        .HasComment("ID of the work package the task belongs to (FK to Workpackage)");

                    b.HasKey("TskId");

                    b.HasIndex("TskCluster")
                        .HasName("IX_TaskClusterID");

                    b.HasIndex("TskCommonActionId");

                    b.HasIndex("TskExecutor");

                    b.HasIndex("TskInitiator");

                    b.HasIndex("TskIntervalUnit");

                    b.HasIndex("TskMrbId")
                        .HasName("IX_TaskMrbID");

                    b.HasIndex("TskMxPolicy");

                    b.HasIndex("TskWorkpackage");

                    b.ToTable("TblTask");

                    b.HasComment(@"Contains maintenance tasks, which are linked to tasks stored in the customers maintenance information system. 
In some places within the code, tasks will be called actions. This is due to the naming conventions used in AMprover 2.6, references to actions will slowly be removed from the code. 
Tasks can be imported directly into the database (manually), or entered by hand in the preventive actions tab within the Risk analysis. 
");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("UserID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of User)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime?>("UserDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date of creation");

                    b.Property<DateTime?>("UserDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<int?>("UserDepartment")
                        .HasColumnType("int")
                        .HasComment("ID of the the department to which the user belongs (FK to Department)");

                    b.Property<string>("UserFirstName")
                        .HasColumnType("varchar(50)")
                        .HasComment("First name of the user")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("UserInitiatedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<bool?>("UserIsGroup")
                        .HasColumnType("bit");

                    b.Property<string>("UserLastName")
                        .HasColumnType("varchar(50)")
                        .HasComment("Surname of the user")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<bool?>("UserLocked")
                        .HasColumnType("bit");

                    b.Property<string>("UserLoginName")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the user")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("UserModifiedBy")
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record")
                        .HasMaxLength(30)
                        .IsUnicode(false);

                    b.Property<int?>("UserPartOfGroup")
                        .HasColumnType("int");

                    b.Property<string>("UserPassword")
                        .HasColumnType("varchar(100)")
                        .HasComment("Password of the user")
                        .HasMaxLength(100)
                        .IsUnicode(false);

                    b.Property<string>("UserRights")
                        .HasColumnType("varchar(max)")
                        .HasComment("XML structure with all individual UserRights")
                        .IsUnicode(false);

                    b.Property<bool?>("UserSetPassword")
                        .HasColumnType("bit");

                    b.Property<int?>("UserType")
                        .HasColumnType("int")
                        .HasComment("Four kind of types 0 =normal users 1 = read only users  2=Administrators");

                    b.HasKey("UserId");

                    b.HasIndex("UserLoginName")
                        .IsUnique()
                        .HasName("IX_User");

                    b.ToTable("User");

                    b.HasComment("Contains all data needed to define a user. This table is not in use from the Amprover software (!)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Workpackage", b =>
                {
                    b.Property<int>("WpId")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("WpID")
                        .HasColumnType("int")
                        .HasComment("Unique ID (PK of WorkPackage)")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("WpDescription")
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the work package")
                        .IsUnicode(false);

                    b.Property<int?>("WpExecutor")
                        .HasColumnType("int")
                        .HasComment("ID of the executor (FK to LookupExecutor)");

                    b.Property<decimal>("WpInterval")
                        .HasColumnType("decimal(18, 2)")
                        .HasComment("Interval of the work package. (a number that, combined with the interval unit, shows with what interval the work package needs to be executed)");

                    b.Property<int>("WpIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("ID of the the interval unit of the work package (FK to LookupIntervalUnit)");

                    b.Property<string>("WpName")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the work package")
                        .HasMaxLength(50)
                        .IsUnicode(false);

                    b.Property<string>("WpShortDescription")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasComment("Short key of the work package")
                        .HasMaxLength(20)
                        .IsUnicode(false);

                    b.HasKey("WpId")
                        .HasName("PK_Werkpakket");

                    b.HasIndex("WpExecutor");

                    b.HasIndex("WpIntervalUnit");

                    b.ToTable("TblWorkPackage");

                    b.HasComment("Contains workpackages, which are containers that are used to group tasks that can be more efficiently executed together. (for example daily and weekly tasks). Workpackages are created from the masterdata. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvSiItems", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Si", "BvSi")
                        .WithOne("BvSiItems")
                        .HasForeignKey("AMprover.Data.Entities.AM.BvSiItems", "BvSiId")
                        .HasConstraintName("FK_BvSiItems_Si")
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.BvWeightingModels", "BvWeightingModel")
                        .WithMany("BvSiItems")
                        .HasForeignKey("BvWeightingModelId")
                        .HasConstraintName("FK_BvSiItems_BvWeightingModels");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvWeightingModels", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.BvAspectSets", "BvModelAspectSet")
                        .WithMany("BvWeightingModels")
                        .HasForeignKey("BvModelAspectSetId")
                        .HasConstraintName("FK_BvWeightingModels_BvAspectSets");

                    b.HasOne("AMprover.Data.Entities.AM.Fmeca", "BvModelFmeca")
                        .WithMany("BvWeightingModels")
                        .HasForeignKey("BvModelFmecaId")
                        .HasConstraintName("FK_BvWeightingModels_Fmeca");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterCost", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "ClcCluster")
                        .WithMany("ClusterCost")
                        .HasForeignKey("ClcClusterId")
                        .HasConstraintName("FK_ClusterCost_Cluster");

                    b.HasOne("AMprover.Data.Entities.AM.CommonCost", "ClcCommonCost")
                        .WithMany("ClusterCost")
                        .HasForeignKey("ClcCommonCostId")
                        .HasConstraintName("FK_ClusterCost_CommonCost")
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Task", "ClcTask")
                        .WithMany("ClusterCost")
                        .HasForeignKey("ClcTaskId")
                        .HasConstraintName("FK_ClusterCost_Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterTaskPlan", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "CltpCluster")
                        .WithMany("ClusterTaskPlan")
                        .HasForeignKey("CltpClusterId")
                        .HasConstraintName("FK_ClusterTaskPlan_Cluster");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "CltpRisk")
                        .WithMany("ClusterTaskPlan")
                        .HasForeignKey("CltpRiskId")
                        .HasConstraintName("FK_ClusterTaskPlan_MRB");

                    b.HasOne("AMprover.Data.Entities.AM.Si", "CltpSi")
                        .WithMany("ClusterTaskPlan")
                        .HasForeignKey("CltpSiId")
                        .HasConstraintName("FK_ClusterTaskPlan_Si");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "CltpTask")
                        .WithMany("ClusterTaskPlan")
                        .HasForeignKey("CltpTaskId")
                        .HasConstraintName("FK_ClusterTaskPlan_Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonCost", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupInflationGroup", "CmnCostPriceGroupNavigation")
                        .WithMany("CommonCost")
                        .HasForeignKey("CmnCostPriceGroup")
                        .HasConstraintName("FK_CommonCost_LookupInflationGroup");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTask", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupExecutor", "CmnTaskExecutorNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskExecutor")
                        .HasConstraintName("FK_CommonTask_LookupExecutor");

                    b.HasOne("AMprover.Data.Entities.AM.LookupInitiator", "CmnTaskInitiatorNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskInitiator")
                        .HasConstraintName("FK_CommonTask_LookupInitiator");

                    b.HasOne("AMprover.Data.Entities.AM.LookupIntervalUnit", "CmnTaskIntervalUnitNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskIntervalUnit")
                        .HasConstraintName("FK_CommonTask_LookupIntervalUnit");

                    b.HasOne("AMprover.Data.Entities.AM.LookupMxPolicy", "CmnTaskMxPolicyNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskMxPolicy")
                        .HasConstraintName("FK_CommonTask_LookupMxPolicy");

                    b.HasOne("AMprover.Data.Entities.AM.Workpackage", "CmnTaskWorkPackageNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskWorkPackage")
                        .HasConstraintName("FK_CommonTask_Workpackage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTaskCost", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.CommonCost", "CtcCommonCost")
                        .WithMany("CommonTaskCost")
                        .HasForeignKey("CtcCommonCostId")
                        .HasConstraintName("FK_CommonTaskCost_CommonCost")
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.CommonTask", "CtcCommonTask")
                        .WithMany("CommonTaskCost")
                        .HasForeignKey("CtcCommonTaskId")
                        .HasConstraintName("FK_CommonTaskCost_CommonTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.FiltersSelectionList", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Filters", "SqlSel")
                        .WithMany("FiltersSelectionList")
                        .HasForeignKey("SqlSelId")
                        .HasConstraintName("FK_FiltersSelectionList_Filters")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lcc", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject")
                        .WithMany("ChildObjects")
                        .HasForeignKey("LccChildObject")
                        .HasConstraintName("FK_Lcc_Object");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject1")
                        .WithMany("ChildObjects1")
                        .HasForeignKey("LccChildObject1")
                        .HasConstraintName("FK_Lcc_Object1");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject2")
                        .WithMany("ChildObjects2")
                        .HasForeignKey("LccChildObject2")
                        .HasConstraintName("FK_Lcc_Object2");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject3")
                        .WithMany("ChildObjects3")
                        .HasForeignKey("LccChildObject3")
                        .HasConstraintName("FK_Lcc_Object3");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject4")
                        .WithMany("ChildObjects4")
                        .HasForeignKey("LccChildObject4")
                        .HasConstraintName("FK_Lcc_Object4");

                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "LccPartOfLcc")
                        .WithMany("LccChildren")
                        .HasForeignKey("LccPartOf");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany("LccItems")
                        .HasForeignKey("LccRiskObject");

                    b.HasOne("AMprover.Data.Entities.AM.Scenario", "LccScenario")
                        .WithMany()
                        .HasForeignKey("LccScenarioId");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lccdetail", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "Lcc")
                        .WithMany("Details")
                        .HasForeignKey("LccDetLccId")
                        .HasConstraintName("FK_LccDetail_Lcc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LcceffectDetail", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Lccdetail", "LccEfctLccDetail")
                        .WithMany("EffectDetails")
                        .HasForeignKey("LccEfctLccDetailId");

                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "LccEfctLcc")
                        .WithMany()
                        .HasForeignKey("LccEfctLccId");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mrb", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupFailMode", "FailureMode")
                        .WithMany("Risks")
                        .HasForeignKey("MrbFailureMode");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany("Risks")
                        .HasForeignKey("MrbRiskObject")
                        .HasConstraintName("FK_TblMRB_TblRiskObject_MrbRiskObject")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.MrbImage", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "MrbImageNavigation")
                        .WithOne("MrbImage")
                        .HasForeignKey("AMprover.Data.Entities.AM.MrbImage", "MrbImageId")
                        .HasConstraintName("FK_MrbImageID_MrB")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexFactor", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexFactOpexData")
                        .WithMany("OpexFactor")
                        .HasForeignKey("OpexFactOpexDataId")
                        .HasConstraintName("FK_OpexFactor_OpexData")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexToLcc", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexLccOpexDataId1Navigation")
                        .WithMany("OpexToLccOpexLccOpexDataId1Navigation")
                        .HasForeignKey("OpexLccOpexDataId1")
                        .HasConstraintName("FK_OpexToLCC_OpexData");

                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexLccOpexDataId2Navigation")
                        .WithMany("OpexToLccOpexLccOpexDataId2Navigation")
                        .HasForeignKey("OpexLccOpexDataId2")
                        .HasConstraintName("FK_OpexToLCC_OpexData1");

                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexLccOpexDataId3Navigation")
                        .WithMany("OpexToLccOpexLccOpexDataId3Navigation")
                        .HasForeignKey("OpexLccOpexDataId3")
                        .HasConstraintName("FK_OpexToLCC_OpexData2");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PickSi", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.SiLinkFilters", "PckSiLinkFilter")
                        .WithMany("PickSi")
                        .HasForeignKey("PckSiLinkFilterId")
                        .HasConstraintName("FK_PickSI_SiLinkFilters");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "PckSiMrb")
                        .WithMany("PickSi")
                        .HasForeignKey("PckSiMrbId")
                        .HasConstraintName("FK_PickMSI_MRB")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Si", "PckSiSi")
                        .WithMany("PickSi")
                        .HasForeignKey("PckSiSiId")
                        .HasConstraintName("FK_PickSI_Si")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Rams", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.RamsDiagram", "RamsDiagram")
                        .WithMany("Rams")
                        .HasForeignKey("RamsDiagramId")
                        .HasConstraintName("FK_RAMS_RamsDiagram")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Si", "RamsSi")
                        .WithMany("Rams")
                        .HasForeignKey("RamsSiId")
                        .HasConstraintName("FK_Rams_Si");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RiskObject", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Fmeca", "RiskObjFmeca")
                        .WithMany("RiskObject")
                        .HasForeignKey("RiskObjFmecaId")
                        .HasConstraintName("FK_RiskObject_tblFMECAV30")
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Object", "RiskObjObject")
                        .WithMany("RiskObject")
                        .HasForeignKey("RiskObjObjectId")
                        .HasConstraintName("FK_RiskObject_Object")
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Scenario", "RiskObjScenario")
                        .WithMany("RiskObject")
                        .HasForeignKey("RiskObjScenarioId")
                        .HasConstraintName("FK_RiskObject_Scenario")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Scenario", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Department", "ScenDepartmentNavigation")
                        .WithMany("Scenario")
                        .HasForeignKey("ScenDepartment")
                        .HasConstraintName("FK_Scenario_Department")
                        .IsRequired();
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiLinkFilters", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject1")
                        .WithMany("SiLinkFiltersSifChildObject1")
                        .HasForeignKey("SifChildObject1Id")
                        .HasConstraintName("FK_SiLinkFilters_Object1");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject2")
                        .WithMany("SiLinkFiltersSifChildObject2")
                        .HasForeignKey("SifChildObject2Id")
                        .HasConstraintName("FK_SiLinkFilters_Object2");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject3")
                        .WithMany("SiLinkFiltersSifChildObject3")
                        .HasForeignKey("SifChildObject3Id")
                        .HasConstraintName("FK_SiLinkFilters_Object3");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject4")
                        .WithMany("SiLinkFiltersSifChildObject4")
                        .HasForeignKey("SifChildObject4Id")
                        .HasConstraintName("FK_SiLinkFilters_Object4");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject")
                        .WithMany("SiLinkFiltersSifChildObject")
                        .HasForeignKey("SifChildObjectId")
                        .HasConstraintName("FK_SiLinkFilters_Object");

                    b.HasOne("AMprover.Data.Entities.AM.Filters", "SifFilterNavigation")
                        .WithMany("SiLinkFilters")
                        .HasForeignKey("SifFilterId")
                        .HasConstraintName("SiLinkFilters_SifFilterID")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "SifRisk")
                        .WithMany("SiLinkFilters")
                        .HasForeignKey("SifRiskId")
                        .HasConstraintName("FK_SiLinkFilters_MRB")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "SifRiskObject")
                        .WithMany("SiLinkFilters")
                        .HasForeignKey("SifRiskObjectId")
                        .HasConstraintName("FK_SiLinkFilters_RiskObject");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "SifTask")
                        .WithMany("SiLinkFilters")
                        .HasForeignKey("SifTaskId")
                        .HasConstraintName("FK_SiLinkFilters_Task")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Spare", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "SpareMrb")
                        .WithMany("Spares")
                        .HasForeignKey("SpareMrbId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Task", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "TskClusterNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskCluster")
                        .HasConstraintName("FK_Task_Cluster");

                    b.HasOne("AMprover.Data.Entities.AM.CommonTask", "TskCommonAction")
                        .WithMany("Task")
                        .HasForeignKey("TskCommonActionId")
                        .HasConstraintName("FK_Task_CommonTask");

                    b.HasOne("AMprover.Data.Entities.AM.LookupExecutor", "TskExecutorNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskExecutor")
                        .HasConstraintName("FK_Task_LookupExecutor");

                    b.HasOne("AMprover.Data.Entities.AM.LookupInitiator", "TskInitiatorNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskInitiator")
                        .HasConstraintName("FK_Task_LookupInitiator")
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.LookupIntervalUnit", "TskIntervalUnitNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskIntervalUnit")
                        .HasConstraintName("FK_Task_LookupIntervalUnit")
                        .IsRequired();

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "TskMrb")
                        .WithMany("Tasks")
                        .HasForeignKey("TskMrbId");

                    b.HasOne("AMprover.Data.Entities.AM.LookupMxPolicy", "TskMxPolicyNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskMxPolicy")
                        .HasConstraintName("FK_Task_LookupMxPolicy");

                    b.HasOne("AMprover.Data.Entities.AM.Workpackage", "TskWorkpackageNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskWorkpackage")
                        .HasConstraintName("FK_Task_Workpackage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Workpackage", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupExecutor", "WpExecutorNavigation")
                        .WithMany("Workpackage")
                        .HasForeignKey("WpExecutor")
                        .HasConstraintName("FK_Workpackage_LookupExecutor");

                    b.HasOne("AMprover.Data.Entities.AM.LookupIntervalUnit", "WpIntervalUnitNavigation")
                        .WithMany("Workpackage")
                        .HasForeignKey("WpIntervalUnit")
                        .HasConstraintName("FK_Workpackage_LookupIntervalUnit")
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
