using Microsoft.EntityFrameworkCore.Migrations;

namespace AMprover.Data.Migrations.AMproverAM;

public partial class AddFunctionalTreeSP : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        var sql = @"
	 	    	IF OBJECT_ID('GetFunctionalTreeReport', 'P') IS NOT NULL
                DROP PROC GetFunctionalTreeReport
                GO
     
                CREATE PROCEDURE [dbo].GetFunctionalTreeReport
                AS
                BEGIN
                    SET NOCOUNT ON;
                    SELECT 
                    ROW_NUMBER() OVER(ORDER BY TblScenario.ScenName, TblRiskObject.RiskObjName ASC) as Id,
                    TblScenario.ScenID as ScenarioID,
                    TblScenario.ScenDescr as ScenarioDescription,
                    TblScenario.ScenName as ScenarioName,
                    TblRiskObject.RiskObjID as RiskObjectId,
                    TblRiskObject.RiskObjName as RiskObjName,
                    TblRiskObject.RiskObjMRBstartPoint AS RiskObjStartPoint,
                    TblRiskObject.RiskObjResponsible as RiskObjResponsible,
                    TblObject.ObjName AS RiskObjObjName,
                    TblObject.ObjFunction AS RiskObjObjFunction,
                    TblObject.ObjLevel AS RiskObjObjLevel,
                    TblObject.ObjNewValue AS RiskObjObjNewValue,
                    TblObject.ObjProductionTime As RiskObjObjProductionTime,
                    Obj.ObjName AS ObjName,
                    Obj.ObjDescription AS ObjDescription,
                    Obj.ObjFunction AS ObjFunction,
                    Obj.ObjLevel AS ObjLevel,
                    Obj.ObjNewValue AS ObjNewValue,
                    Obj.ObjProductionTime As ObjProductionTime,
                    Obj1.ObjName AS Obj1Name,
                    Obj1.ObjDescription AS Obj1Description,
                    Obj1.ObjFunction AS Obj1Function,
                    Obj1.ObjLevel AS Obj1Level,
                    Obj1.ObjNewValue AS Obj1NewValue,
                    Obj1.ObjProductionTime As Obj1ProductionTime,
                    Obj2.ObjName AS Obj2Name,
                    Obj2.ObjDescription AS Obj2Description,
                    Obj2.ObjFunction AS Obj2Function,
                    Obj2.ObjLevel AS Obj2Level,
                    Obj2.ObjNewValue AS Obj2NewValue,
                    Obj2.ObjProductionTime As Obj2ProductionTime,
                    Obj3.ObjName AS Obj3Name,
                    Obj3.ObjDescription AS Obj3Description,
                    Obj3.ObjFunction AS Obj3Function,
                    Obj3.ObjLevel AS Obj3Level,
                    Obj3.ObjNewValue AS Obj3NewValue,
                    Obj3.ObjProductionTime As Obj3ProductionTime,
                    Obj4.ObjName AS Obj4Name,
                    Obj4.ObjDescription AS Obj4Description,
                    Obj4.ObjFunction AS Obj4Function,
                    Obj4.ObjLevel AS Obj4Level,
                    Obj4.ObjNewValue AS Obj4NewValue,
                    Obj4.ObjProductionTime As Obj4ProductionTime
                    from TblMRB
                    INNER JOIN TblRiskObject on MrbRiskObject = TblRiskObject.RiskObjId
                    INNER JOIN TblScenario on TblRiskObject.RiskObjScenarioID = TblScenario.ScenID
                    INNER JOIN TblObject ON TblRiskObject.RiskObjObjectID = TblObject.ObjID
                    LEFT OUTER JOIN TblObject As Obj ON TblMRB.MrbChildObject = Obj.ObjID
                    LEFT OUTER JOIN TblObject As Obj1 ON TblMRB.MrbChildObject1 = Obj1.ObjID
                    LEFT OUTER JOIN TblObject As Obj2 ON TblMRB.MrbChildObject2 = Obj2.ObjID
                    LEFT OUTER JOIN TblObject As Obj3 ON TblMRB.MrbChildObject3 = Obj3.ObjID
                    LEFT OUTER JOIN TblObject As Obj4 ON TblMRB.MrbChildObject4 = Obj4.ObjID
                    order by TblScenario.ScenName, TblRiskObject.RiskObjName
                END";

        migrationBuilder.Sql(sql);
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {

    }
}