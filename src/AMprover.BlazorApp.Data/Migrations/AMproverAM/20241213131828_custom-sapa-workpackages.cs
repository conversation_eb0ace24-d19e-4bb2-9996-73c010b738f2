using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb;

public partial class customsapaworkpackages : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            // Manual Change:
            migrationBuilder.Sql("delete from tblSapa");

            migrationBuilder.DropForeignKey(
                name: "FK_Sapa_WorkPackage",
                table: "TblSapa");

            migrationBuilder.RenameColumn(
                name: "SapaWorkPackageId",
                table: "TblSapa",
                newName: "SapaWorkpackageId");

            migrationBuilder.RenameIndex(
                name: "IX_TblSapa_SapaWorkPackageId",
                table: "TblSapa",
                newName: "IX_TblSapa_SapaWorkpackageId");

            migrationBuilder.AddColumn<int>(
                name: "TskSapaWorkpackage",
                table: "TblTask",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "TblSapaWorkpackage",
                columns: table => new
                {
                    SapaWpId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SapaWpKey = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    SapaWpName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    SapaWpDescription = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SapaWorkpackage", x => x.SapaWpId);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TblTask_TskSapaWorkpackage",
                table: "TblTask",
                column: "TskSapaWorkpackage");

            migrationBuilder.AddForeignKey(
                name: "FK_Sapa_WorkPackage",
                table: "TblSapa",
                column: "SapaWorkpackageId",
                principalTable: "TblSapaWorkpackage",
                principalColumn: "SapaWpId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Task_SapaWorkpackage",
                table: "TblTask",
                column: "TskSapaWorkpackage",
                principalTable: "TblSapaWorkpackage",
                principalColumn: "SapaWpId",
                onDelete: ReferentialAction.SetNull);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropForeignKey(
                name: "FK_Sapa_WorkPackage",
                table: "TblSapa");

            migrationBuilder.DropForeignKey(
                name: "FK_Task_SapaWorkpackage",
                table: "TblTask");

            migrationBuilder.DropTable(
                name: "TblSapaWorkpackage");

            migrationBuilder.DropIndex(
                name: "IX_TblTask_TskSapaWorkpackage",
                table: "TblTask");

            migrationBuilder.DropColumn(
                name: "TskSapaWorkpackage",
                table: "TblTask");

            migrationBuilder.RenameColumn(
                name: "SapaWorkpackageId",
                table: "TblSapa",
                newName: "SapaWorkPackageId");

            migrationBuilder.RenameIndex(
                name: "IX_TblSapa_SapaWorkpackageId",
                table: "TblSapa",
                newName: "IX_TblSapa_SapaWorkPackageId");

            migrationBuilder.AddForeignKey(
                name: "FK_Sapa_WorkPackage",
                table: "TblSapa",
                column: "SapaWorkPackageId",
                principalTable: "TblWorkPackage",
                principalColumn: "WpID",
                onDelete: ReferentialAction.Cascade);
        }
}