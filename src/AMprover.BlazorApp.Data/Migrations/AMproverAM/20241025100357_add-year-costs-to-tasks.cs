using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb;

public partial class addyearcoststotasks : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AddColumn<decimal>(
                name: "TskCostY1",
                table: "TblTask",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TskCostY2",
                table: "TblTask",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TskCostY3",
                table: "TblTask",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TskCostY4",
                table: "TblTask",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TskCostY5",
                table: "TblTask",
                type: "decimal(18,2)",
                nullable: true);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropColumn(
                name: "TskCostY1",
                table: "TblTask");

            migrationBuilder.DropColumn(
                name: "TskCostY2",
                table: "TblTask");

            migrationBuilder.DropColumn(
                name: "TskCostY3",
                table: "TblTask");

            migrationBuilder.DropColumn(
                name: "TskCostY4",
                table: "TblTask");

            migrationBuilder.DropColumn(
                name: "TskCostY5",
                table: "TblTask");
        }
}