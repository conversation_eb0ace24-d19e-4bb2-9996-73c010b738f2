using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb;

public partial class addlccageandstartyear : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AddColumn<int>(
                name: "LccAge",
                table: "TblLCC",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "LccStartYear",
                table: "TblLCC",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropColumn(
                name: "LccAge",
                table: "TblLCC");

            migrationBuilder.DropColumn(
                name: "LccStartYear",
                table: "TblLCC");
        }
}