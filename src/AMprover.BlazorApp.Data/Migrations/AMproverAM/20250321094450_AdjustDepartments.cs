using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations;

public partial class AdjustDepartments : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropColumn(
                name: "DepAccessRights",
                table: "TblDepartment");

            migrationBuilder.RenameColumn(
                name: "DepShortkey",
                table: "TblDepartment",
                newName: "DepShortKey");
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.RenameColumn(
                name: "DepShortKey",
                table: "TblDepartment",
                newName: "DepShortkey");

            migrationBuilder.AddColumn<string>(
                name: "DepAccessRights",
                table: "TblDepartment",
                type: "varchar(max)",
                unicode: false,
                nullable: true,
                comment: "Access rights held by members of the department");
        }
}