using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb;

public partial class Fix_ObjectIds_on_lcc_and_mrb : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            // Fix Collection and Installation on Lccs
            migrationBuilder.Sql(@"
                Update tblLcc
                Set 
                tblLcc.LccChildObject = tblRiskObject.RiskObjParentObjectID,
                tblLcc.LccChildObject1 = tblRiskObject.RiskObjObjectID
                from tblLcc
                inner join tblRiskObject on tblLcc.LccRiskObject = tblRiskObject.RiskObjID");

            // Fix Collection and Installation on Risks
            migrationBuilder.Sql(@"
                Update tblMrb
                set
                tblMrb.mrbChildObject = tblRiskObject.RiskObjParentObjectID,
                tblMrb.mrbChildObject1 = tblRiskObject.RiskObjObjectID
                from tblMrb
                inner join tblRiskObject on tblMrb.MrbRiskObject = TblRiskObject.RiskObjID");

            // Fix ScenarioId on Lccs
            migrationBuilder.Sql(@"
                Update tblLcc
                Set 
                tblLcc.LccScenarioID = tblRiskObject.RiskObjScenarioID
                from tblLcc
                inner join tblRiskObject on 
                (
	                tblLcc.LccScenarioID is not null
	                and tblLcc.LccScenarioID != TblRiskObject.RiskObjScenarioID
	                and tblLcc.LccRiskObject = TblRiskObject.RiskObjID
                )");
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {

        }
}