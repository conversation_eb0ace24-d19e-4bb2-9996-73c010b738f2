using Microsoft.EntityFrameworkCore.Migrations;

namespace AMprover.Data.Migrations;

public partial class CorrectLCCMapping : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.Sql(@"UPDATE TblLCC SET LccChildObject = NULL 
            WHERE LccChildObject NOT IN(SELECT ObjID FROM TblObject) 
            GO");

        migrationBuilder.Sql(@"UPDATE TblLCC SET LccChildObject1 = NULL 
            WHERE LccChildObject1 NOT IN(SELECT ObjID FROM TblObject) 
            GO");

        migrationBuilder.Sql(@"UPDATE TblLCC SET LccChildObject2 = NULL 
            WHERE LccChildObject2 NOT IN(SELECT ObjID FROM TblObject) 
            GO");

        migrationBuilder.Sql(@"UPDATE TblLCC SET LccChildObject3 = NULL 
            WHERE LccChildObject3 NOT IN(SELECT ObjID FROM TblObject) 
            GO");

        migrationBuilder.Sql(@"UPDATE TblLCC SET LccChildObject4 = NULL 
            WHERE LccChildObject4 NOT IN(SELECT ObjID FROM TblObject) 
            GO");

        migrationBuilder.CreateIndex(
            "IX_TblLCC_LccChildObject",
            "TblLCC",
            "LccChildObject");

        migrationBuilder.CreateIndex(
            "IX_TblLCC_LccChildObject1",
            "TblLCC",
            "LccChildObject1");

        migrationBuilder.CreateIndex(
            "IX_TblLCC_LccChildObject2",
            "TblLCC",
            "LccChildObject2");

        migrationBuilder.CreateIndex(
            "IX_TblLCC_LccChildObject3",
            "TblLCC",
            "LccChildObject3");

        migrationBuilder.CreateIndex(
            "IX_TblLCC_LccChildObject4",
            "TblLCC",
            "LccChildObject4");

        migrationBuilder.CreateIndex(
            "IX_TblLCC_LccRiskObject",
            "TblLCC",
            "LccRiskObject");

        migrationBuilder.AddForeignKey(
            "FK_Lcc_Object",
            "TblLCC",
            "LccChildObject",
            "TblObject",
            principalColumn: "ObjID",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            "FK_Lcc_Object1",
            "TblLCC",
            "LccChildObject1",
            "TblObject",
            principalColumn: "ObjID",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            "FK_Lcc_Object2",
            "TblLCC",
            "LccChildObject2",
            "TblObject",
            principalColumn: "ObjID",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            "FK_Lcc_Object3",
            "TblLCC",
            "LccChildObject3",
            "TblObject",
            principalColumn: "ObjID",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            "FK_Lcc_Object4",
            "TblLCC",
            "LccChildObject4",
            "TblObject",
            principalColumn: "ObjID",
            onDelete: ReferentialAction.Restrict);

        migrationBuilder.AddForeignKey(
            "FK_TblLCC_TblRiskObject_LccRiskObject",
            "TblLCC",
            "LccRiskObject",
            "TblRiskObject",
            principalColumn: "RiskObjID",
            onDelete: ReferentialAction.Restrict);
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            "FK_Lcc_Object",
            "TblLCC");

        migrationBuilder.DropForeignKey(
            "FK_Lcc_Object1",
            "TblLCC");

        migrationBuilder.DropForeignKey(
            "FK_Lcc_Object2",
            "TblLCC");

        migrationBuilder.DropForeignKey(
            "FK_Lcc_Object3",
            "TblLCC");

        migrationBuilder.DropForeignKey(
            "FK_Lcc_Object4",
            "TblLCC");

        migrationBuilder.DropForeignKey(
            "FK_TblLCC_TblRiskObject_LccRiskObject",
            "TblLCC");

        migrationBuilder.DropIndex(
            "IX_TblLCC_LccChildObject",
            "TblLCC");

        migrationBuilder.DropIndex(
            "IX_TblLCC_LccChildObject1",
            "TblLCC");

        migrationBuilder.DropIndex(
            "IX_TblLCC_LccChildObject2",
            "TblLCC");

        migrationBuilder.DropIndex(
            "IX_TblLCC_LccChildObject3",
            "TblLCC");

        migrationBuilder.DropIndex(
            "IX_TblLCC_LccChildObject4",
            "TblLCC");

        migrationBuilder.DropIndex(
            "IX_TblLCC_LccRiskObject",
            "TblLCC");
    }
}