using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class linkcritandsionsinamestep2 : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropForeignKey(
                name: "FK_TblCriticalityRanking_TblSi_CritSiId",
                table: "TblCriticalityRanking");

            migrationBuilder.DropIndex(
                name: "IX_TblCriticalityRanking_CritSiId",
                table: "TblCriticalityRanking");

            migrationBuilder.AlterColumn<string>(
                name: "CritSiName",
                table: "TblCriticalityRanking",
                type: "varchar(40)",
                unicode: false,
                maxLength: 40,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AlterColumn<string>(
                name: "CritSiName",
                table: "TblCriticalityRanking",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(40)",
                oldUnicode: false,
                oldMaxLength: 40,
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TblCriticalityRanking_CritSiId",
                table: "TblCriticalityRanking",
                column: "CritSiId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_TblCriticalityRanking_TblSi_CritSiId",
                table: "TblCriticalityRanking",
                column: "CritSiId",
                principalTable: "TblSi",
                principalColumn: "SiID",
                onDelete: ReferentialAction.Cascade);
        }
}