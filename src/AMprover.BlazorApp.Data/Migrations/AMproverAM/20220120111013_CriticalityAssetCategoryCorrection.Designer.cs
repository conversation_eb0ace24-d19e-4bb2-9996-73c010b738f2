// <auto-generated />
using System;
using AMprover.Data.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM
{
    [DbContext(typeof(AssetManagementDbContext))]
    [Migration("20220120111013_CriticalityAssetCategoryCorrection")]
    partial class CriticalityAssetCategoryCorrection
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("AMprover.Data.Entities.AM.Amprover3EditLog", b =>
                {
                    b.Property<DateTime>("LogDate")
                        .HasColumnType("datetime");

                    b.Property<int>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LogID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LogId"), 1L, 1);

                    b.Property<string>("LogModificationType")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("LogModifications")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("LogObjectId")
                        .HasColumnType("int")
                        .HasColumnName("LogObjectID");

                    b.Property<string>("LogObjectType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("LogObjectVarId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("LogObjectVarID");

                    b.Property<string>("LogUserId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("LogUserID");

                    b.ToTable("Amprover3_EditLog", (string)null);
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Amprover3MrbId", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("MrbId")
                        .HasColumnType("int");

                    b.ToTable("Amprover3_Mrb_ID", (string)null);
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Amprover3PickClusterSi", b =>
                {
                    b.Property<int?>("PckClClusterId")
                        .HasColumnType("int")
                        .HasColumnName("PckClClusterID");

                    b.Property<int>("PckClId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PckClID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PckClId"), 1L, 1);

                    b.Property<int?>("PckClSiCategory")
                        .HasColumnType("int");

                    b.Property<int?>("PckClSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckClSiID");

                    b.Property<string>("PckClSiType")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PckClTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PckClTaskID");

                    b.ToTable("Amprover3_PickClusterSI", (string)null);
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Amprover3PickSi", b =>
                {
                    b.Property<bool>("PckSiActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("PckSiDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PckSiDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("PckSiDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<DateTime?>("PckSiExecutionYear")
                        .HasColumnType("datetime");

                    b.Property<bool?>("PckSiExpand")
                        .HasColumnType("bit");

                    b.Property<int>("PckSiId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PckSiID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PckSiId"), 1L, 1);

                    b.Property<string>("PckSiInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int?>("PckSiItems")
                        .HasColumnType("int");

                    b.Property<int?>("PckSiLinkFilterId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiLinkFilterID");

                    b.Property<string>("PckSiModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int>("PckSiMrbId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiMrbID");

                    b.Property<string>("PckSiReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("PckSiReferenceID");

                    b.Property<int?>("PckSiSiCategory")
                        .HasColumnType("int");

                    b.Property<int>("PckSiSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiSiID");

                    b.Property<string>("PckSiSiType")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PckSiTaskId")
                        .HasColumnType("int");

                    b.Property<bool?>("PckSiXoutofN")
                        .HasColumnType("bit");

                    b.ToTable("Amprover3_PickSI", (string)null);
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Amprover3SiLinkFilters", b =>
                {
                    b.Property<int?>("SifCategory")
                        .HasColumnType("int");

                    b.Property<int?>("SifCompactCatId")
                        .HasColumnType("int")
                        .HasColumnName("SifCompactCatID");

                    b.Property<bool?>("SifCompactDetail")
                        .HasColumnType("bit");

                    b.Property<string>("SifDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("SifFilter")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("SifGroup")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("SifId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SifID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SifId"), 1L, 1);

                    b.Property<bool>("SifInheritParent")
                        .HasColumnType("bit");

                    b.Property<string>("SifName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("SifObjectId")
                        .HasColumnType("int")
                        .HasColumnName("SifObjectID");

                    b.Property<int?>("SifParentId")
                        .HasColumnType("int")
                        .HasColumnName("SifParentID");

                    b.Property<int?>("SifRiskId")
                        .HasColumnType("int")
                        .HasColumnName("SifRiskID");

                    b.Property<string>("SifSubGroup")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("SifTaskId")
                        .HasColumnType("int")
                        .HasColumnName("SifTaskID");

                    b.Property<int>("SifType")
                        .HasColumnType("int");

                    b.ToTable("Amprover3_SiLinkFilters", (string)null);
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BudgetCost", b =>
                {
                    b.Property<decimal?>("Accepted")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("Budget")
                        .HasColumnType("decimal(38,2)")
                        .HasColumnName("budget");

                    b.Property<decimal?>("BudgetSum")
                        .HasColumnType("decimal(38,2)")
                        .HasColumnName("budgetSum");

                    b.Property<int?>("Category")
                        .HasColumnType("int");

                    b.Property<decimal?>("CostsYear1")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("CostsYear2")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("CostsYear3")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("CostsYear4")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("CostsYear5")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("Postponed")
                        .HasColumnType("decimal(38,2)");

                    b.Property<int?>("PrioBudVersion")
                        .HasColumnType("int");

                    b.Property<int?>("PrioPartOf")
                        .HasColumnType("int");

                    b.Property<decimal?>("Proposed")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("Rejected")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("Risk")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("RiskDelta")
                        .HasColumnType("decimal(38,2)");

                    b.ToView("BudgetCost");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvAspectSets", b =>
                {
                    b.Property<int>("BvAspSetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("BvAspSetID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BvAspSetId"), 1L, 1);

                    b.Property<string>("BvAspAspects")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Unique aspects stored in XML. Stores the name, weight factor and the bound relevance set of the aspect.");

                    b.Property<DateTime?>("BvAspDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvAspModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("BvAspSetName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the aspect set");

                    b.HasKey("BvAspSetId")
                        .HasName("BvAspectID");

                    b.ToTable("TblBvAspectSets");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvRelevanceSets", b =>
                {
                    b.Property<int>("BvRelSetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("BvRelSetID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BvRelSetId"), 1L, 1);

                    b.Property<DateTime?>("BvRelDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvRelModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("BvRelRelevances")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Unique relevances stored in XML. Stores the name and the appreciation of the relevance.");

                    b.Property<string>("BvRelSetName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the relevance set");

                    b.HasKey("BvRelSetId");

                    b.ToTable("TblBvRelevanceSets");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvSiItems", b =>
                {
                    b.Property<int>("BvId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("BvID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BvId"), 1L, 1);

                    b.Property<int?>("BvBusinessvalue")
                        .HasColumnType("int");

                    b.Property<DateTime?>("BvDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("BvPerEffectSet")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("BvRelevanceSelectSet")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("BvSiId")
                        .HasColumnType("int")
                        .HasColumnName("BvSiID");

                    b.Property<int?>("BvWeightingModelId")
                        .HasColumnType("int")
                        .HasColumnName("BvWeightingModelID");

                    b.HasKey("BvId");

                    b.HasIndex("BvSiId")
                        .IsUnique()
                        .HasDatabaseName("IX_BvSiItems");

                    b.HasIndex("BvWeightingModelId");

                    b.ToTable("TblBvSiItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvWeightingModels", b =>
                {
                    b.Property<int>("BvModelId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("BvModelID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BvModelId"), 1L, 1);

                    b.Property<int?>("BvModelAspectSetId")
                        .HasColumnType("int")
                        .HasColumnName("BvModelAspectSetID")
                        .HasComment("ID of the aspect set used for this weighting model.");

                    b.Property<DateTime?>("BvModelDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BvModelEffectWeightSet")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Effect weight set stored in XML. Stores the aspect, effect (FMECA) column and the given value of the effect weight set.");

                    b.Property<int?>("BvModelFmecaId")
                        .HasColumnType("int")
                        .HasColumnName("BvModelFmecaID")
                        .HasComment("ID of the FMECA matrix used for this weighting model.");

                    b.Property<string>("BvModelModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("BvModelName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the weighting model");

                    b.HasKey("BvModelId")
                        .HasName("PK_BvWeightingModels");

                    b.HasIndex("BvModelAspectSetId");

                    b.HasIndex("BvModelFmecaId");

                    b.ToTable("BvWeightingModels");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Cluster", b =>
                {
                    b.Property<int>("ClustId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ClustID")
                        .HasComment("Unique ID (PK of Cluster)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ClustId"), 1L, 1);

                    b.Property<DateTime?>("ClustDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("ClustDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("ClustDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the cluster");

                    b.Property<decimal?>("ClustDisciplineCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs for disciplines that are essential for execution of the cluster (? what are disciplines in this context?)");

                    b.Property<bool?>("ClustDivideDownTime")
                        .HasColumnType("bit")
                        .HasComment("A bit used for the way the downtime is devided. false :The input in the cluster is devided over the tasks. True duration is the sum of the downtime in the tasks");

                    b.Property<bool?>("ClustDivideDuration")
                        .HasColumnType("bit")
                        .HasComment("A bit used for the way the duration is devided. false :The input in the cluster is devided over the tasks. True duration is the sum of the duration in the tasks");

                    b.Property<decimal?>("ClustDownTime")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Downtime needed to complete cluster of tasks (in hours)");

                    b.Property<decimal?>("ClustDuration")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Total time spent on cluster (in hours)");

                    b.Property<decimal?>("ClustEnergyCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Estimated energy costs made during execution of the cluster");

                    b.Property<decimal?>("ClustEstTaskCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Estimated task costs (?)");

                    b.Property<int?>("ClustExecutor")
                        .HasColumnType("int")
                        .HasComment("ID of the executor (FK to LookupExecutor)");

                    b.Property<string>("ClustInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Username of person that created this cluster");

                    b.Property<int?>("ClustInitiator")
                        .HasColumnType("int")
                        .HasComment("ID of the initiator (FK to LookupInitiator)");

                    b.Property<bool?>("ClustInterruptable")
                        .HasColumnType("bit")
                        .HasComment("Allow the cluster to be paused.");

                    b.Property<decimal?>("ClustInterval")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Cluster interval, interval at which the cluster will be executed");

                    b.Property<int?>("ClustIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("Interval unit ID (FK to LookupIntervalUnit)");

                    b.Property<int>("ClustLevel")
                        .HasColumnType("int")
                        .HasComment("Describes at what level of the tree the cluster is situated.");

                    b.Property<string>("ClustLocation")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique (technical) identifier of the related location");

                    b.Property<decimal?>("ClustMaterialCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs of materials that are essential for execution of the cluster");

                    b.Property<string>("ClustModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<string>("ClustName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of cluster");

                    b.Property<string>("ClustOrgId")
                        .HasMaxLength(12)
                        .IsUnicode(false)
                        .HasColumnType("varchar(12)")
                        .HasColumnName("ClustOrgID")
                        .HasComment("Organisation to which the cluster is bound");

                    b.Property<int?>("ClustPartOf")
                        .HasColumnType("int")
                        .HasComment("Cluster ID of the parent cluster (FK to self, Cluster)");

                    b.Property<int?>("ClustPriority")
                        .HasColumnType("int")
                        .HasComment("Priority of the cluster");

                    b.Property<string>("ClustReferenceId")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("ClustReferenceID");

                    b.Property<string>("ClustRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks allow for some extra information regarding this cluster");

                    b.Property<string>("ClustResponsible")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("People/departments who are responsible for execution of the cluster");

                    b.Property<int?>("ClustRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("ClustRiskObjectID")
                        .HasComment("Risk Object ID the cluster is referring to (FK to RiskObject)");

                    b.Property<int?>("ClustScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("ClustScenarioID")
                        .HasComment("Scenario ID the cluster is a part of (FK to Scenario)");

                    b.Property<string>("ClustSecondValues")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("?");

                    b.Property<int?>("ClustSequence")
                        .HasColumnType("int")
                        .HasComment("Sequence of the relationship between clusters.");

                    b.Property<decimal?>("ClustSharedCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("? Costs that are shared by items that belong to this cluster");

                    b.Property<int?>("ClustShiftEndDate")
                        .HasColumnType("int")
                        .HasComment("Amount of days that the end date of the cluster is shifted");

                    b.Property<int?>("ClustShiftStartDate")
                        .HasColumnType("int")
                        .HasComment("Amount of days that the start date of the cluster is shifted");

                    b.Property<string>("ClustShortKey")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("Short key of the cluster (needs to be unique within Cluster)");

                    b.Property<string>("ClustSiteId")
                        .HasMaxLength(12)
                        .IsUnicode(false)
                        .HasColumnType("varchar(12)")
                        .HasColumnName("ClustSiteID")
                        .HasComment("SiteID of the asset");

                    b.Property<int?>("ClustStatus")
                        .HasColumnType("int")
                        .HasComment("Cluster status, user can enter any string for status.  (? status domain --> There is no status domain, should there be one ?) ");

                    b.Property<decimal?>("ClustTaskCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Actual task costs for this cluster");

                    b.Property<string>("ClustTemplateType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("The template type of the cluster");

                    b.Property<decimal?>("ClustToolCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs for tools needed for completion of the cluster");

                    b.Property<decimal?>("ClustTotalCmnCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("? Total costs common to this cluster (?)");

                    b.Property<int?>("ClustWorkpackageId")
                        .HasColumnType("int")
                        .HasColumnName("ClustWorkpackageID")
                        .HasComment("Workpackage ID the cluster is a part of (FK to Workpackage)");

                    b.HasKey("ClustId");

                    b.HasIndex("ClustPartOf")
                        .HasDatabaseName("IX_ClusterPartOf");

                    b.HasIndex("ClustScenarioId");

                    b.ToTable("TblCluster");

                    b.HasComment("A cluster is a group of related preventive tasks, that can be grouped for entry into a maintenance information system. Most of the values stored in this table can be set from the ClusterControl. Some values get recalculated within SyncClusterData.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterCost", b =>
                {
                    b.Property<int>("ClcId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ClcID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ClcId"), 1L, 1);

                    b.Property<string>("ClcCalculationType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(8)
                        .IsUnicode(false)
                        .HasColumnType("varchar(8)")
                        .HasDefaultValueSql("('PxN')")
                        .HasComment("The way the common cost are calculated");

                    b.Property<int?>("ClcClusterId")
                        .HasColumnType("int")
                        .HasColumnName("ClcClusterID")
                        .HasComment("ID of the cluster to which the cluster cost is bound (Cluster)");

                    b.Property<int>("ClcCommonCostId")
                        .HasColumnType("int")
                        .HasColumnName("ClcCommonCostID")
                        .HasComment("ID of the common cost to which the cluster cost is bound (CommonCost)");

                    b.Property<decimal>("ClcCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost of the common cost");

                    b.Property<bool?>("ClcIsCommonTaskCost")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean to see if the cluster cost are referenced to a task");

                    b.Property<decimal>("ClcPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Price of the common cost");

                    b.Property<int?>("ClcPriceIndexYear")
                        .HasColumnType("int")
                        .HasComment("The year in which the cluster cost are indexed");

                    b.Property<decimal?>("ClcQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Quantity of the common cost");

                    b.Property<string>("ClcRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the common cost");

                    b.Property<int?>("ClcTaskId")
                        .HasColumnType("int")
                        .HasColumnName("ClcTaskID")
                        .HasComment("ID of the tasks to which the cluster cost is bound (Task)");

                    b.Property<string>("ClcType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("The Type of cluster cost");

                    b.Property<decimal?>("ClcUnits")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Number of units of the common cost");

                    b.HasKey("ClcId")
                        .HasName("PK_PickClusterCost");

                    b.HasIndex("ClcClusterId");

                    b.HasIndex("ClcCommonCostId");

                    b.HasIndex("ClcTaskId");

                    b.ToTable("TblClusterCost");

                    b.HasComment("Clustercosts are costs specific to a cluster. They can be defined ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterReport", b =>
                {
                    b.Property<int?>("ClcId")
                        .HasColumnType("int")
                        .HasColumnName("ClcID");

                    b.Property<decimal?>("ClustCostCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ClustCostDescr")
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)");

                    b.Property<decimal?>("ClustCostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustCostQuantity")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ClustCostRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ClustCostTaskId")
                        .HasColumnType("int");

                    b.Property<decimal?>("ClustCostUnits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ClustDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ClustDisciplineCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustDownTime")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustDuration")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustEnergyCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustEstTaskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ClustExecutor")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("ClustId")
                        .HasColumnType("int")
                        .HasColumnName("ClustID");

                    b.Property<string>("ClustInitiator")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("ClustInterval")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("ClustIntervalUnit")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int>("ClustLevel")
                        .HasColumnType("int");

                    b.Property<decimal?>("ClustMaterialCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ClustName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustName0")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustName1")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustName2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClustName3")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ClustPartOf")
                        .HasColumnType("int");

                    b.Property<string>("ClustRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("ClustResponsible")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("ClustSecondValues")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("ClustSharedCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ClustShortKey")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)");

                    b.Property<int?>("ClustStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("ClustTaskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustToolCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ClustTotalCmnCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CmnCostType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("MrbFailureCause")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("MrbFailureConsequences")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("MrbId")
                        .HasColumnType("int")
                        .HasColumnName("MrbID");

                    b.Property<string>("MrbObject0")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbObject2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbObject3")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("MrbObject4")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ParentClusterName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RiskObject")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RiskObjectDesc")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("Scenario")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("TskCostCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TskCostDescription")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<decimal?>("TskCostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskCostQuantity")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskCostTskId")
                        .HasColumnType("int")
                        .HasColumnName("TskCostTskID");

                    b.Property<decimal?>("TskCostUnits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool?>("TskDerived")
                        .HasColumnType("bit");

                    b.Property<string>("TskDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("TskExecutor")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("TskGeneralDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("TskId")
                        .HasColumnType("int")
                        .HasColumnName("TskID");

                    b.Property<string>("TskInitiator")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("TskIntervalUnit")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.Property<string>("TskName")
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<string>("TskPermit")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("TskPolicy")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)");

                    b.Property<string>("TskRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<bool?>("TskRemoved")
                        .HasColumnType("bit");

                    b.Property<int?>("TskSortOrder")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("TskWorkPackage")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.ToView("ClusterReport");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterSiTaskCollection", b =>
                {
                    b.Property<int?>("FailRateId")
                        .HasColumnType("int")
                        .HasColumnName("FailRateID");

                    b.Property<decimal?>("IntUnitCalculationKey")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomEffectAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbDirectCostAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbDirectCostBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("MrbFailureMode")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbRiskBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("Mrbcustomeffectbefore")
                        .HasColumnType("decimal(18,3)")
                        .HasColumnName("mrbcustomeffectbefore");

                    b.Property<int?>("NumberOfTasks")
                        .HasColumnType("int");

                    b.Property<int>("PckSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiID");

                    b.Property<int>("PckSiSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiSiID");

                    b.Property<int?>("SiCategory")
                        .HasColumnType("int");

                    b.Property<string>("SiName")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("SiQualityScore")
                        .HasColumnType("int");

                    b.Property<string>("SiUnitsTypeValues")
                        .HasMaxLength(150)
                        .IsUnicode(false)
                        .HasColumnType("varchar(150)");

                    b.Property<DateTime?>("SiYear")
                        .HasColumnType("datetime");

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnType("int")
                        .HasColumnName("TskCommonActionID");

                    b.Property<int?>("TskCopiedFrom")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("TskEstCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("TskExecutionDate")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TskFinishDate")
                        .HasColumnType("smalldatetime");

                    b.Property<decimal?>("TskFmecaEffectPct")
                        .HasColumnType("numeric(18,2)");

                    b.Property<int>("TskId")
                        .HasColumnType("int")
                        .HasColumnName("TskID");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int>("TskIntervalUnit")
                        .HasColumnType("int");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.Property<string>("TskName")
                        .IsRequired()
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<decimal?>("TskOptimalCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TskPermit")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("TskPriorityCode")
                        .HasColumnType("int");

                    b.Property<int?>("TskStatus")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int>("TskUnitType")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskUnits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskValidFromYear")
                        .HasColumnType("int");

                    b.Property<int?>("TskValidUntilYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskWorkInspCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskWorkpackage")
                        .HasColumnType("int");

                    b.ToView("ClusterSiTaskCollection");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterTaskPlan", b =>
                {
                    b.Property<int>("CltpId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CltpID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CltpId"), 1L, 1);

                    b.Property<decimal?>("CltpClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("?");

                    b.Property<decimal?>("CltpClusterCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cluster cost for the cluster task plan");

                    b.Property<int?>("CltpClusterId")
                        .HasColumnType("int")
                        .HasColumnName("CltpClusterID")
                        .HasComment("Cluster ID to which the cluster task plan is referenced (Cluster)");

                    b.Property<int?>("CltpCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("CltpCommonTaskID")
                        .HasComment("Common task ID to which the cluster task plan is referenced (CommonTask)");

                    b.Property<DateTime?>("CltpDateExecuted")
                        .HasColumnType("datetime")
                        .HasComment("Date when the cluster task plan is executed");

                    b.Property<DateTime?>("CltpDateGenerated")
                        .HasColumnType("datetime")
                        .HasComment("Date when the cluster task plan is generated");

                    b.Property<decimal?>("CltpDownTime")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Downtime needed in hours");

                    b.Property<decimal?>("CltpDuration")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Time spend in hours");

                    b.Property<int?>("CltpExecuteStatus")
                        .HasColumnType("int")
                        .HasComment("Execute status of the cluster task plan");

                    b.Property<DateTime?>("CltpExecutionDate")
                        .HasColumnType("datetime")
                        .HasComment("Date when the cluster task plan must be executed");

                    b.Property<bool?>("CltpInterruptable")
                        .HasColumnType("bit")
                        .HasComment("Allow the task plan to be paused.");

                    b.Property<int?>("CltpObjectId")
                        .HasColumnType("int")
                        .HasColumnName("CltpObjectID")
                        .HasComment("Object ID to which the cluster task plan is referenced (Object)");

                    b.Property<int?>("CltpPriority")
                        .HasColumnType("int")
                        .HasComment("Priority of the cluster task plan");

                    b.Property<int?>("CltpQualityScore")
                        .HasColumnType("int")
                        .HasComment("?");

                    b.Property<string>("CltpReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("CltpReferenceID")
                        .HasComment("?");

                    b.Property<string>("CltpRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the cluster task plan");

                    b.Property<int?>("CltpRiskId")
                        .HasColumnType("int")
                        .HasColumnName("CltpRiskID")
                        .HasComment("Risk ID to which the cluster task plan is referenced (MRB)");

                    b.Property<int?>("CltpSequence")
                        .HasColumnType("int")
                        .HasComment("Sequence of the relationship between cluster task plans.");

                    b.Property<int?>("CltpShiftEndDate")
                        .HasColumnType("int")
                        .HasComment("Amount of days that the end date of the cluster task plan is shifted");

                    b.Property<int?>("CltpShiftStartDate")
                        .HasColumnType("int")
                        .HasComment("Amount of days that the start date of the cluster task plan is shifted");

                    b.Property<int?>("CltpSiId")
                        .HasColumnType("int")
                        .HasColumnName("CltpSiID")
                        .HasComment("Significant item ID to which the cluster task plan is referenced (Cluster)");

                    b.Property<decimal?>("CltpSiUnits")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("?");

                    b.Property<int?>("CltpSlack")
                        .HasColumnType("int")
                        .HasComment("Interval of the slack for the cluster task plan");

                    b.Property<int?>("CltpSlackIntervalType")
                        .HasColumnType("int")
                        .HasComment("Interval unit ID of the slack for the cluster task plan (LookupIntervalUnit)");

                    b.Property<int?>("CltpTaskId")
                        .HasColumnType("int")
                        .HasColumnName("CltpTaskID")
                        .HasComment("Task ID to which the cluster task plan is referenced (Task)");

                    b.Property<decimal?>("CltpToolCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Tool cost for the cluster task plan");

                    b.Property<bool?>("CltpUseLastDateExecuted")
                        .HasColumnType("bit")
                        .HasComment("Use the last date executed as executing date for the cluster task plan");

                    b.HasKey("CltpId");

                    b.HasIndex("CltpClusterId");

                    b.HasIndex("CltpRiskId");

                    b.HasIndex("CltpSiId");

                    b.HasIndex("CltpTaskId");

                    b.ToTable("TblClusterTaskPlan");

                    b.HasComment("Cluster task plans contain groups of preventive measures, that can be imported into a maintenance information system. \r\n\r\nThe cluster task plans are generated from the clusters. Data in this table is generated using data from tables Cluster, ClusterCost, Task, PriorityTask, Mrb, and RiskObject.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CmnCostCalculation", b =>
                {
                    b.Property<decimal?>("CmnTaskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("CtcCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("CtcCommonTaskID");

                    b.Property<decimal>("CtcCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("CtcPriceIndexYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("InflPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.ToView("CmnCostCalculation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonCost", b =>
                {
                    b.Property<int>("CmnCostId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CmnCostID")
                        .HasComment("Unique ID (PK of CommonCost)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CmnCostId"), 1L, 1);

                    b.Property<string>("CmnCostCalculationType")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("The way the common cost must be used in calculations (P, PxN, PxNxU)");

                    b.Property<DateTime?>("CmnCostDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("CmnCostDescription")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Descriptive name of the common cost. This value is used for selecting the common costs in the screens that use them.");

                    b.Property<decimal?>("CmnCostExtraCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Extra cost for the common cost (currently not in use)");

                    b.Property<string>("CmnCostModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<decimal?>("CmnCostNumber")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Quantity number, used in calculation of costs. (when calculation type is not set to price-only)");

                    b.Property<string>("CmnCostOrgId")
                        .HasMaxLength(12)
                        .IsUnicode(false)
                        .HasColumnType("varchar(12)")
                        .HasColumnName("CmnCostOrgID")
                        .HasComment("Organisation to which the common cost is bound");

                    b.Property<decimal?>("CmnCostPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Price of one item. Is always used for price calculation.");

                    b.Property<int?>("CmnCostPriceGroup")
                        .HasColumnType("int")
                        .HasComment("Inflation group ID to which the common cost are bound. (FK to LookupUserDefined)");

                    b.Property<int?>("CmnCostPriceIndexYear")
                        .HasColumnType("int")
                        .HasComment("The year in which the common cost were indexed");

                    b.Property<string>("CmnCostReferenceCode")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique reference code or ID (customer)");

                    b.Property<string>("CmnCostRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks field for common costs. ");

                    b.Property<bool?>("CmnCostRotating")
                        .HasColumnType("bit")
                        .HasComment("Boolean to set the common cost as a rotating item");

                    b.Property<string>("CmnCostShortKey")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Short key of the common cost. Usually takes the first character of the CmnCostType. (?)");

                    b.Property<bool?>("CmnCostSpare")
                        .HasColumnType("bit")
                        .HasComment("Boolean to set the common cost as a spare part");

                    b.Property<int?>("CmnCostStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the common cost");

                    b.Property<DateTime?>("CmnCostStatusDate")
                        .HasColumnType("datetime")
                        .HasComment("Date the status was last modified.");

                    b.Property<string>("CmnCostSubSubType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Extra added sub sub type as a search field");

                    b.Property<string>("CmnCostSubType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Extra added sub type as a search field");

                    b.Property<string>("CmnCostType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasDefaultValueSql("('PxN')")
                        .HasComment("Common cost type domain. Specifies what caused the costs. (tools, disciplines, energy, etc.)");

                    b.Property<string>("CmnCostUnitType")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("The unit type of the common cost. Not currently used by AMprover software. (?)");

                    b.Property<decimal?>("CmnCostUnits")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Number of units, will be used in cost calculation (when calculation type is PxNxU)");

                    b.Property<string>("CmnCostVendorCode")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique reference code or ID of the vendor");

                    b.HasKey("CmnCostId")
                        .HasName("PK_ClusterCosts");

                    b.HasIndex("CmnCostPriceGroup");

                    b.ToTable("TblCommonCost");

                    b.HasComment("Master data table. Common costs define recurring costs, that are used for cost calculations. They define different costs for different types of items. (like materials, tools or use of specific disciplines.) Common costs can be added to common actions and clusters.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTask", b =>
                {
                    b.Property<int>("CmnTaskId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CmnTaskID")
                        .HasComment("Unique ID (PK of CommonTask)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CmnTaskId"), 1L, 1);

                    b.Property<bool?>("CmnTaskCostModifiable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean that enables modification of the common task costs during risk analysis");

                    b.Property<decimal?>("CmnTaskCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost of common task execution. (Cost per unit?) Is named action costs in the AMprover software.");

                    b.Property<DateTime?>("CmnTaskDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("CmnTaskDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("CmnTaskDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description for the common task");

                    b.Property<decimal?>("CmnTaskDownTime")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Downtime needed to complete the task (in hours)");

                    b.Property<decimal?>("CmnTaskDuration")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Time spent on task. Does not seem to be set from the AMprover software. (?)");

                    b.Property<int?>("CmnTaskExecutor")
                        .HasColumnType("int")
                        .HasComment("Executor ID of the common task (FK to LookupExecutor)");

                    b.Property<bool?>("CmnTaskExecutorModifiable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean that enables modification of the common task executor during risk analysis");

                    b.Property<string>("CmnTaskFieldRights")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("? Is not set by the AMprover software");

                    b.Property<string>("CmnTaskGeneralDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("General description for the common task. Does not seem to be filled by the AMprover software. (?)");

                    b.Property<string>("CmnTaskInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Username of person that created this cluster");

                    b.Property<int?>("CmnTaskInitiator")
                        .HasColumnType("int")
                        .HasComment("Initiator ID of the common task (FK to LookupInitiator)");

                    b.Property<bool?>("CmnTaskInitiatorModifiable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean that enables modification of the common task initiator during risk analysis");

                    b.Property<decimal?>("CmnTaskInterval")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,4)")
                        .HasDefaultValueSql("((1))")
                        .HasComment("Interval of the common task. The common task needs to be executed each time this interval passes. ");

                    b.Property<bool?>("CmnTaskIntervalModifiable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean that enables modification of the interval during risk analysis");

                    b.Property<int?>("CmnTaskIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("ID of the the interval unit of the common task (FK to LookupIntervalUnit)");

                    b.Property<int?>("CmnTaskMasterId")
                        .HasColumnType("int")
                        .HasColumnName("CmnTaskMasterID")
                        .HasComment("ID of the master common task (FK to CommonTask) Does not seem to be set by the AMprover software (?)");

                    b.Property<string>("CmnTaskModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<int?>("CmnTaskMxPolicy")
                        .HasColumnType("int")
                        .HasComment("Maintenance policy ID of the common task (FK to LookupMxPolicy)");

                    b.Property<string>("CmnTaskName")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("The name of the common task");

                    b.Property<string>("CmnTaskPermit")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("char(10)")
                        .IsFixedLength()
                        .HasComment("Permit that is needed for the common task. Never set in the AMprover software.");

                    b.Property<int?>("CmnTaskPriorityCode")
                        .HasColumnType("int")
                        .HasComment("Priority code of common task. Is not set by the AMprover software (?)");

                    b.Property<string>("CmnTaskReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("CmnTaskReferenceID")
                        .HasComment("Reference ID of the common task. Not bound to anything, user can enter any string. (?)");

                    b.Property<string>("CmnTaskRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the common task. Does not seem to be filled by the AMprover software. (?)");

                    b.Property<string>("CmnTaskResponsible")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Person or department that is responsible for executing the common task (never set in the AMprover software)");

                    b.Property<int?>("CmnTaskSiCategory")
                        .HasColumnType("int")
                        .HasComment("The si category value to which the common task is bound (FK to LookupUserDefined, UserdefinedFilter is SICategory)");

                    b.Property<int?>("CmnTaskSortOrder")
                        .HasColumnType("int")
                        .HasComment("Custom sequence to order the common tasks");

                    b.Property<string>("CmnTaskType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Type of common task (task, procedure etc.) Type domain is defined in Lookup table, Lookupfilter value is MeasureType. This is called Action type in the AMprover software.");

                    b.Property<int>("CmnTaskUnitType")
                        .HasColumnType("int")
                        .HasComment("The unit type value to which the cost of the common task are bound (FK to LookupUserDefined, FilterType UnitTypes)");

                    b.Property<int?>("CmnTaskValidFromYear")
                        .HasColumnType("int")
                        .HasComment("The starting year of the common, when it becomes valid. Does not seem to be set from the AMprover software. (?)");

                    b.Property<int?>("CmnTaskValidUntilYear")
                        .HasColumnType("int")
                        .HasComment("The year when the common task is no longer valid, and will no longer affect cost calculations. Does not seem to be set from the AMprover software. (?)");

                    b.Property<decimal?>("CmnTaskWorkInspCost")
                        .HasColumnType("decimal(18,0)")
                        .HasComment("Inspection cost of the common task. Is not set by the AMprover software. (?)");

                    b.Property<int?>("CmnTaskWorkPackage")
                        .HasColumnType("int")
                        .HasComment("ID of the work package of the common task (FK to Workpackage)");

                    b.Property<bool?>("CmnTaskWorkPackageModifiable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Boolean that enables modification of the work package during risk analysis");

                    b.HasKey("CmnTaskId");

                    b.HasIndex("CmnTaskExecutor");

                    b.HasIndex("CmnTaskInitiator");

                    b.HasIndex("CmnTaskIntervalUnit");

                    b.HasIndex("CmnTaskMxPolicy");

                    b.HasIndex("CmnTaskWorkPackage");

                    b.ToTable("TblCommonTask");

                    b.HasComment("Master data table. A common task is a task that has to be executed after a defined interval, each time the interval passes. Also named common action.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTaskCost", b =>
                {
                    b.Property<int>("CtcId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CtcID")
                        .HasComment("Unique ID (PK of CommonTaskCost)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CtcId"), 1L, 1);

                    b.Property<string>("CtcCalculationType")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasDefaultValueSql("('PxN')")
                        .HasComment("The way the common task cost must be calculated (P, PxN, PxNxU)");

                    b.Property<int>("CtcCommonCostId")
                        .HasColumnType("int")
                        .HasColumnName("CtcCommonCostID")
                        .HasComment("ID of the common cost the common task cost is bound to (FK to CommonCost)");

                    b.Property<int?>("CtcCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("CtcCommonTaskID")
                        .HasComment("Binds costs to a specific task. Currently selected task is added automatically when user adds a new common cost to an action. (FK to CommonTask) ");

                    b.Property<decimal>("CtcCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Calculated cost of the common task cost (determined by calculation type, and stored values for Price, Quantity and Units)");

                    b.Property<decimal>("CtcPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Price of the common task cost (price of a single unit)");

                    b.Property<int>("CtcPriceIndexYear")
                        .HasColumnType("int")
                        .HasComment("The year in which the common task cost are indexed");

                    b.Property<decimal?>("CtcQuantity")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Quantity of common cost items needed for this common task cost");

                    b.Property<decimal?>("CtcUnits")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Number of units needed for this common task cost");

                    b.HasKey("CtcId");

                    b.HasIndex("CtcCommonCostId");

                    b.HasIndex("CtcCommonTaskId");

                    b.ToTable("TblCommonTaskCost");

                    b.HasComment("Master data table. Contains costs related to common tasks. Defined within master data, as part of a common action. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Company", b =>
                {
                    b.Property<int>("CompanyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("CompanyID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CompanyId"), 1L, 1);

                    b.Property<string>("CompanyAdres")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Address of the company");

                    b.Property<string>("CompanyContact1")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Contact field of the company");

                    b.Property<string>("CompanyContact2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Contact field of the company");

                    b.Property<string>("CompanyCountry")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Country of the company");

                    b.Property<string>("CompanyDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the company");

                    b.Property<string>("CompanyEmail")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Email address of the company");

                    b.Property<string>("CompanyFax")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Fax number of the company");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("The name of the company");

                    b.Property<string>("CompanyPhone")
                        .HasMaxLength(15)
                        .IsUnicode(false)
                        .HasColumnType("varchar(15)")
                        .HasComment("Phone number of the company");

                    b.Property<string>("CompanyPlace")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("City of the company");

                    b.Property<string>("CompanyPostAdres")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Post address of the company");

                    b.Property<string>("CompanyZipCode")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("Zip code of the company");

                    b.HasKey("CompanyId");

                    b.ToTable("Company");

                    b.HasComment("Would contain full contact info for companies. Not used by AMprover software. (!)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CriticalityRanking", b =>
                {
                    b.Property<int>("CritId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CritId"), 1L, 1);

                    b.Property<string>("CritCategory")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<DateTime>("CritDateInitiated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CritDateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("CritDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("CritDownTimeAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CritFailureCause")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CritFailureConsequences")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CritFailureMode")
                        .HasColumnType("int");

                    b.Property<int?>("CritFmeca")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca1Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca2")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca2Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca3")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca3Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca4")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca4Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca5")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca5Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca6")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca6Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("CritFmeca7")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritFmeca7Value")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CritFmecaSelect")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CritFmecaVersion")
                        .HasColumnType("int");

                    b.Property<string>("CritInitiatedBy")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<int?>("CritKooN")
                        .HasColumnType("int");

                    b.Property<decimal?>("CritMTTR")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CritModifiedBy")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("CritName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("CritProbability")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CritReStrategy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CritRedundant")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("CritRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CritResponsible")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<int>("CritSiId")
                        .HasColumnType("int");

                    b.Property<int?>("CritStatus")
                        .HasColumnType("int");

                    b.Property<string>("CritTotal")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("CritTotalValue")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.HasKey("CritId");

                    b.HasIndex("CritSiId")
                        .IsUnique();

                    b.ToTable("TblCriticalityRanking");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DbScriptsExecuted", b =>
                {
                    b.Property<string>("ScriptName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("DateExecuted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.HasKey("ScriptName");

                    b.ToTable("DbScriptsExecuted");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Department", b =>
                {
                    b.Property<int>("DepId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("DepID")
                        .HasComment("Unique ID (PK of Department)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DepId"), 1L, 1);

                    b.Property<string>("DepAccessRights")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Access rights held by members of the department");

                    b.Property<string>("DepDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Full description of the department");

                    b.Property<string>("DepShortkey")
                        .IsRequired()
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)")
                        .HasComment("Short description of the department");

                    b.HasKey("DepId");

                    b.ToTable("TblDepartment");

                    b.HasComment("Defines different departments within a company. Defined from master data");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DerModified", b =>
                {
                    b.Property<int>("DerModId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("DerModID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DerModId"), 1L, 1);

                    b.Property<int?>("DerModCopiedFrom")
                        .HasColumnType("int")
                        .HasComment("For auditing reasons it is easier to know what the original object is");

                    b.Property<bool?>("DerModDeleted")
                        .HasColumnType("bit")
                        .HasComment("if this bit is set then the object is will be shown a if it has been deleted but the parent properties will be still be shown");

                    b.Property<string>("DerModModifications")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("xml string with the modifications");

                    b.Property<int?>("DerModObjectId")
                        .HasColumnType("int")
                        .HasColumnName("DerModObjectID")
                        .HasComment("The ID of the object whereto modifications have been applied");

                    b.Property<string>("DerModObjectKey")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("not used can be applied for objects that have no unique int key");

                    b.Property<int>("DerModObjectType")
                        .HasColumnType("int")
                        .HasComment("0=scenario 1=riskobject 2=risk 3 = task 4=siFilter 5=pickSi ");

                    b.HasKey("DerModId");

                    b.ToTable("DerModified");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Descriptions", b =>
                {
                    b.Property<int>("DescId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("DescID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DescId"), 1L, 1);

                    b.Property<string>("DescDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("DescExtraId")
                        .HasColumnType("int")
                        .HasColumnName("DescExtraID");

                    b.Property<string>("DescExtraType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int?>("DescFieldType")
                        .HasColumnType("int");

                    b.Property<int?>("DescRiskId")
                        .HasColumnType("int")
                        .HasColumnName("DescRiskID");

                    b.Property<int?>("DescRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("DescRiskObjectID");

                    b.Property<int?>("DescTaskId")
                        .HasColumnType("int")
                        .HasColumnName("DescTaskID");

                    b.HasKey("DescId");

                    b.ToTable("Descriptions");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DsRamsReportRams", b =>
                {
                    b.Property<double?>("RamsAvailabilityInput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsAvailabilityOutput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<string>("RamsClassDc")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasColumnName("RamsClassDC");

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<double?>("RamsDcd")
                        .HasColumnType("float")
                        .HasColumnName("RamsDCd");

                    b.Property<string>("RamsDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<DateTime?>("RamsDgDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDgDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("RamsDgDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsDgHorizon")
                        .HasColumnType("int");

                    b.Property<int>("RamsDgId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgID");

                    b.Property<string>("RamsDgInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("RamsDgModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("RamsDgName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("RamsDgReferenceID");

                    b.Property<string>("RamsDgRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("RamsDgRiskObject")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgScenario")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgSerialized")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RamsDgSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgSiID");

                    b.Property<string>("RamsDgSiRefId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("RamsDgSiRefID");

                    b.Property<int?>("RamsDgStatus")
                        .HasColumnType("int");

                    b.Property<int?>("RamsDgTestInterval")
                        .HasColumnType("int");

                    b.Property<bool?>("RamsDgWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsDgWantLCC");

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramRefID");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsFuncReliability")
                        .HasColumnType("float");

                    b.Property<string>("RamsFunctionalDemand")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit");

                    b.Property<string>("RamsInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int");

                    b.Property<string>("RamsModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn");

                    b.Property<double?>("RamsMttr")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTTR");

                    b.Property<string>("RamsName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsObjectID");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int");

                    b.Property<double?>("RamsPfd")
                        .HasColumnType("float")
                        .HasColumnName("RamsPFD");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int");

                    b.Property<string>("RamsRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsRiskId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskID");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskObjectID");

                    b.Property<int?>("RamsSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsSiID");

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsTechnReliability")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsWantLCC");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int");

                    b.Property<int?>("RamsXposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsXPosition");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int");

                    b.Property<int?>("RamsYposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsYPosition");

                    b.ToView("DsRamsReport_Rams");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DsRamsReportRamsDetails", b =>
                {
                    b.Property<double?>("RamsAvailabilityInput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsAvailabilityOutput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<string>("RamsClassDc")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasColumnName("RamsClassDC");

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<double?>("RamsDcd")
                        .HasColumnType("float")
                        .HasColumnName("RamsDCd");

                    b.Property<string>("RamsDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<DateTime?>("RamsDgDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDgDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("RamsDgDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsDgHorizon")
                        .HasColumnType("int");

                    b.Property<int>("RamsDgId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgID");

                    b.Property<string>("RamsDgInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("RamsDgModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("RamsDgName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("RamsDgReferenceID");

                    b.Property<string>("RamsDgRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("RamsDgRiskObject")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgScenario")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsDgSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgSiID");

                    b.Property<string>("RamsDgSiRefId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("RamsDgSiRefID");

                    b.Property<int?>("RamsDgStatus")
                        .HasColumnType("int");

                    b.Property<int?>("RamsDgTestInterval")
                        .HasColumnType("int");

                    b.Property<bool?>("RamsDgWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsDgWantLCC");

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramRefID");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsFuncReliability")
                        .HasColumnType("float");

                    b.Property<string>("RamsFunctionalDemand")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit");

                    b.Property<string>("RamsInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int");

                    b.Property<string>("RamsModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn");

                    b.Property<double?>("RamsMttr")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTTR");

                    b.Property<string>("RamsName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsObjectID");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int");

                    b.Property<double?>("RamsPfd")
                        .HasColumnType("float")
                        .HasColumnName("RamsPFD");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int");

                    b.Property<string>("RamsRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsRiskId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskID");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskObjectID");

                    b.Property<int?>("RamsSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsSiID");

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsTechnReliability")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsWantLCC");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int");

                    b.Property<int?>("RamsXposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsXPosition");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int");

                    b.Property<int?>("RamsYposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsYPosition");

                    b.ToView("DsRamsReport_RamsDetails");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.DsRamsReportRamsTable", b =>
                {
                    b.Property<double?>("RamsAvailabilityInput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsAvailabilityOutput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<string>("RamsClassDc")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasColumnName("RamsClassDC");

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<double?>("RamsDcd")
                        .HasColumnType("float")
                        .HasColumnName("RamsDCd");

                    b.Property<string>("RamsDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<DateTime?>("RamsDgDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDgDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("RamsDgDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsDgHorizon")
                        .HasColumnType("int");

                    b.Property<int>("RamsDgId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgID");

                    b.Property<string>("RamsDgInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("RamsDgModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("RamsDgName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("RamsDgReferenceID");

                    b.Property<string>("RamsDgRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("RamsDgRiskObject")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RamsDgScenario")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsDgSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgSiID");

                    b.Property<string>("RamsDgSiRefId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("RamsDgSiRefID");

                    b.Property<int?>("RamsDgStatus")
                        .HasColumnType("int");

                    b.Property<int?>("RamsDgTestInterval")
                        .HasColumnType("int");

                    b.Property<bool?>("RamsDgWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsDgWantLCC");

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramRefID");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsFuncReliability")
                        .HasColumnType("float");

                    b.Property<string>("RamsFunctionalDemand")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit");

                    b.Property<string>("RamsInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int");

                    b.Property<string>("RamsModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn");

                    b.Property<double?>("RamsMttr")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTTR");

                    b.Property<string>("RamsName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsObjectID");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int");

                    b.Property<double?>("RamsPfd")
                        .HasColumnType("float")
                        .HasColumnName("RamsPFD");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int");

                    b.Property<string>("RamsRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsRiskId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskID");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskObjectID");

                    b.Property<int?>("RamsSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsSiID");

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsTechnReliability")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsWantLCC");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int");

                    b.Property<int?>("RamsXposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsXPosition");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int");

                    b.Property<int?>("RamsYposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsYPosition");

                    b.Property<string>("SiDescription")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SiMiscTextField1")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<string>("SiName")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.ToView("DsRamsReport_RamsTable");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.EditLog", b =>
                {
                    b.Property<int>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LogID")
                        .HasComment("Unique ID (PK of EditLog)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LogId"), 1L, 1);

                    b.Property<DateTime>("LogDate")
                        .HasColumnType("datetime")
                        .HasComment("Date the modification occurred");

                    b.Property<string>("LogModificationType")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("Modification type (edit, delete etc.)");

                    b.Property<string>("LogModifications")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Describes the actual modification (column names, old value and new value)");

                    b.Property<int?>("LogObjectId")
                        .HasColumnType("int")
                        .HasColumnName("LogObjectID")
                        .HasComment("Object ID which was modified (?)");

                    b.Property<string>("LogObjectType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Describes the table that was modified (table name)");

                    b.Property<string>("LogObjectVarId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("LogObjectVarID")
                        .HasComment("?");

                    b.Property<string>("LogUserId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("LogUserID")
                        .HasComment("Name of the user who performed the modification (not an ID!)");

                    b.HasKey("LogId");

                    b.ToTable("EditLog");

                    b.HasComment("Contains the edit logs that are generated when a user makes changes to certain tables. The editlogs contain the columns changed, the old value and the new value. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ExportSimco", b =>
                {
                    b.Property<string>("CmnTaskReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("CmnTaskReferenceID");

                    b.Property<bool?>("PrioTskAutoSelected")
                        .HasColumnType("bit");

                    b.Property<decimal?>("PrioTskClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskCommonTaskID");

                    b.Property<decimal?>("PrioTskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("PrioTskDateDue")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioTskDateExecuted")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("PrioTskDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskExecutionYear")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskFromReference")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("PrioTskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskID");

                    b.Property<int?>("PrioTskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskObjectID");

                    b.Property<int?>("PrioTskOriginalId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskOriginalID");

                    b.Property<decimal?>("PrioTskPriorityCode")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskPriorityID");

                    b.Property<string>("PrioTskReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("PrioTskReferenceID");

                    b.Property<decimal?>("PrioTskRiskBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskRiskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskRiskID");

                    b.Property<bool?>("PrioTskSealed")
                        .HasColumnType("bit");

                    b.Property<string>("PrioTskSiDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskSiID");

                    b.Property<decimal?>("PrioTskSiUnits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskTaskID");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SiContractEnd")
                        .HasColumnType("datetime");

                    b.Property<string>("SiMiscTextField1")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("SiPartOf")
                        .HasColumnType("int");

                    b.Property<string>("SiVendor")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.ToView("ExportSimco");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ExportTasks", b =>
                {
                    b.Property<decimal?>("PrioTskClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskCommonTaskID");

                    b.Property<decimal?>("PrioTskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("PrioTskDateDue")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioTskDateExecuted")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("PrioTskDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskExecutionYear")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskFromReference")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskObjectID");

                    b.Property<decimal?>("PrioTskPriorityCode")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskPriorityID");

                    b.Property<string>("PrioTskReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("PrioTskReferenceID");

                    b.Property<decimal?>("PrioTskRiskBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskRiskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskRiskID");

                    b.Property<string>("PrioTskSiDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskSiID");

                    b.Property<decimal?>("PrioTskSiUnits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskTaskID");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int");

                    b.ToView("ExportTasks");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Filters", b =>
                {
                    b.Property<int>("SqlSelId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SqlSelID")
                        .HasComment("Unique ID (PK of Filters)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SqlSelId"), 1L, 1);

                    b.Property<string>("SqlSelGroup")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Main filter group. Describes the AMprover module the filter applies to. User cannot set this.");

                    b.Property<string>("SqlSelName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Descriptive name of the filter, user can enter any name. ");

                    b.Property<string>("SqlSelShortKey")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Shortkey of the filter, needs to be unique within the entire Filters. ");

                    b.Property<string>("SqlSelSubGroup")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Filter sub group. Is set by the code, using hardcoded values which are different for each subgroup of filters. Needs to be unique within the same main group. ");

                    b.HasKey("SqlSelId");

                    b.ToTable("TblFilters");

                    b.HasComment("Defines filters used throughout the program. (reports, priority box, clusters, life cycle costs). The filters are used to limit the amount of data the user has to go through to find the item they want. The filter can be set up on each of the columns of the related table. Filters are identified by the combination of SqlSelGroup and SqlSubSelGroup. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.FiltersSelectionList", b =>
                {
                    b.Property<int>("SqlSelId")
                        .HasColumnType("int")
                        .HasColumnName("SqlSelID");

                    b.Property<int>("Sequence")
                        .HasColumnType("int");

                    b.Property<string>("AndOr")
                        .HasMaxLength(3)
                        .IsUnicode(false)
                        .HasColumnType("varchar(3)");

                    b.Property<string>("Criterium")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FieldType")
                        .IsRequired()
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)");

                    b.Property<string>("FriendlyName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<short>("Ident")
                        .HasColumnType("smallint");

                    b.Property<bool>("IsAdvanced")
                        .HasColumnType("bit");

                    b.Property<string>("Selection")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("SortType")
                        .HasMaxLength(4)
                        .IsUnicode(false)
                        .HasColumnType("varchar(4)");

                    b.HasKey("SqlSelId", "Sequence");

                    b.ToTable("TblFiltersSelectionList");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Fmeca", b =>
                {
                    b.Property<int>("FmecaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("FmecaID")
                        .HasComment(" (PK of Fmeca)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FmecaId"), 1L, 1);

                    b.Property<string>("FmecaDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the FMECA matrix");

                    b.Property<string>("FmecaMatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Layout of the FMECA matrix, stored in XML. Stores the colors, and effects for each cell of the matrix.");

                    b.Property<string>("FmecaName")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Name for the FMECA matrix");

                    b.Property<string>("FmecaShortName")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasDefaultValueSql("('-')")
                        .HasComment("Short description of the FMECA matrix");

                    b.Property<int>("FmecaVersion")
                        .HasColumnType("int")
                        .HasComment("FMECA matrix version (default is 1 for AMprover 3.0)");

                    b.HasKey("FmecaId")
                        .HasName("PK_tblFMECA_1");

                    b.ToTable("TblFmeca");

                    b.HasComment("This table defines the rows, columns and cells of risk matrices. The cell contents, colors and values are stored in XML. The created risk matrices can be selected during risk analysis.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.FmecaSelect", b =>
                {
                    b.Property<int>("MfsId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MfsID")
                        .HasComment("Unique ID (PK of FmecaSelect)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MfsId"), 1L, 1);

                    b.Property<int>("MfsColIndex")
                        .HasColumnType("int")
                        .HasComment("Fmeca matrix column number (99 is mtbf)");

                    b.Property<double?>("MfsCustomAfter")
                        .HasColumnType("float")
                        .HasComment("Custom value after performing the FMECA");

                    b.Property<double?>("MfsCustomBefore")
                        .HasColumnType("float")
                        .HasComment("Custom value before performing the FMECA");

                    b.Property<string>("MfsDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description for the FMECA column.");

                    b.Property<int?>("MfsLccId")
                        .HasColumnType("int")
                        .HasColumnName("MfsLccID")
                        .HasComment("Reference to Lcc ID (FK to LCC)");

                    b.Property<int>("MfsReferenceId")
                        .HasColumnType("int")
                        .HasColumnName("MfsReferenceID")
                        .HasComment("Reference to risk ID (FK to Mrb)");

                    b.Property<int>("MfsRefrenceType")
                        .HasColumnType("int")
                        .HasComment("Used for mrb lcc and columncost per task. Is set to 999 by SyncMrb. ");

                    b.Property<int?>("MfsSelectAfter")
                        .HasColumnType("int")
                        .HasComment("Selected value after performing the FMECA");

                    b.Property<int?>("MfsSelectBefore")
                        .HasColumnType("int")
                        .HasComment("Select value before performing the FMECA");

                    b.Property<int?>("MfsTaskId")
                        .HasColumnType("int")
                        .HasColumnName("MfsTaskID")
                        .HasComment("Reference to task ID (FK to Task)");

                    b.Property<double?>("MfsValueAfter")
                        .HasColumnType("float")
                        .HasComment("Value after performing the FMECA");

                    b.Property<double?>("MfsValueBefore")
                        .HasColumnType("float")
                        .HasComment("Value before performing the FMECA");

                    b.HasKey("MfsId");

                    b.ToTable("FmecaSelect");

                    b.HasComment("Contains the monetary values (of each risk type) before and after the preventive actions defined during risk analysis.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lcc", b =>
                {
                    b.Property<int>("LccId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LccID")
                        .HasComment("Unique ID (PK of LCC)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LccId"), 1L, 1);

                    b.Property<decimal?>("LccAec")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("LccAEC")
                        .HasComment("Annual equivalent cost of the LCC");

                    b.Property<decimal?>("LccAverageOptimalCost")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Average optimal cost of the LCC");

                    b.Property<int?>("LccChildObject")
                        .HasColumnType("int")
                        .HasComment("Object ID of the LCC (FK to Object)");

                    b.Property<int?>("LccChildObject1")
                        .HasColumnType("int")
                        .HasComment("Object ID of the LCC (FK to Object)");

                    b.Property<int?>("LccChildObject2")
                        .HasColumnType("int")
                        .HasComment("Object ID of the LCC (FK to Object)");

                    b.Property<int?>("LccChildObject3")
                        .HasColumnType("int")
                        .HasComment("Object ID of the LCC (FK to Object)");

                    b.Property<int?>("LccChildObject4")
                        .HasColumnType("int")
                        .HasComment("Object ID of the LCC (FK to Object)");

                    b.Property<DateTime?>("LccDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<decimal?>("LccDiscountRate")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Discount rate for the LCC is a factror reflecting the time value of money that is used to convert cash flows occurring at different times, to a common time. ");

                    b.Property<decimal?>("LccEcoFunctional")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Functional eco score of the LCC");

                    b.Property<decimal?>("LccEcoTechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Technical eco score of the LCC");

                    b.Property<bool?>("LccExclude")
                        .HasColumnType("bit")
                        .HasComment("Boolean that excludes the LCC from calculation when true");

                    b.Property<decimal?>("LccInputAvailabilityTechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Availability of technical input for the LCC");

                    b.Property<decimal?>("LccInputReliabilityFunctional")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Reliability of functional input for the LCC");

                    b.Property<int?>("LccMaxYears")
                        .HasColumnType("int")
                        .HasComment("Number of years the LCC should be calculated for");

                    b.Property<decimal?>("LccMcRav")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Maintenance cost divided by the replacement value");

                    b.Property<string>("LccModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<decimal?>("LccMtbffunctional")
                        .HasColumnType("decimal(18,6)")
                        .HasColumnName("LccMTBFFunctional")
                        .HasComment("Output of the total functional MTBF (mean time between failure) of the LCC");

                    b.Property<decimal?>("LccMtbftechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasColumnName("LccMTBFTechnical")
                        .HasComment("Output of the total technical MTBF (mean time between failure) of the LCC");

                    b.Property<string>("LccName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("Name of the LCC");

                    b.Property<decimal?>("LccNpv")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("LccNPV")
                        .HasComment("Net present value of the LCC. NPV is the sum of discounted future cash flows, inlcuding both costs and benefits/revenues.");

                    b.Property<int?>("LccNpvyear")
                        .HasColumnType("int")
                        .HasColumnName("LccNPVyear")
                        .HasComment("Optimal year of the LCC (year where the AEC is on his lowest point)");

                    b.Property<decimal?>("LccOptimalAverageCorrectiveCost")
                        .HasColumnType("decimal(18,0)")
                        .HasComment("Average optimal cost for corrective maintenance of the LCC");

                    b.Property<decimal?>("LccOptimalAveragePreventiveCost")
                        .HasColumnType("decimal(18,0)")
                        .HasComment("Average optimal cost for preventive maintenance of the LCC");

                    b.Property<byte[]>("LccOptimalImage")
                        .HasColumnType("image")
                        .HasComment("Image containing the optimal cost graph of the LCC");

                    b.Property<decimal?>("LccOutputAvailabilityTechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Availability of technical output for the LCC");

                    b.Property<decimal?>("LccOutputReliabilityTechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Reliability of functional input for the LCC");

                    b.Property<decimal?>("LccOverallProductionCost")
                        .HasColumnType("decimal(18,0)")
                        .HasComment("Total production cost of the LCC");

                    b.Property<int?>("LccPartOf")
                        .HasColumnType("int")
                        .HasComment("LCC ID of the parent LCC (FK to LCC)");

                    b.Property<decimal?>("LccPotential")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Saving potential of the LCC");

                    b.Property<decimal?>("LccProductionCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Production cost of the LCC");

                    b.Property<decimal?>("LccProductivityFunctional")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Functional productivity of the LCC");

                    b.Property<decimal?>("LccProductivityTechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Technical productivity of the LCC");

                    b.Property<int?>("LccRamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsDiagramID")
                        .HasComment("RAMS diagram ID of the LCC (FK to RamsDiagram)");

                    b.Property<int?>("LccRamsId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsID")
                        .HasComment("RAMS ID of the LCC (FK to Rams)");

                    b.Property<byte[]>("LccRamsImage")
                        .HasColumnType("image")
                        .HasComment("Image containing the RAMS graph of the LCC");

                    b.Property<byte[]>("LccRealCostImage")
                        .HasColumnType("image")
                        .HasComment("Image containing the real cost graph of the LCC");

                    b.Property<string>("LccRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks field allowing for more detailed information about the LCC item");

                    b.Property<decimal?>("LccReplacementValue")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Replacement value of the LCC object");

                    b.Property<int?>("LccRiskObject")
                        .HasColumnType("int")
                        .HasComment("Risk object ID of the LCC (FK to RiskObject)");

                    b.Property<int?>("LccScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("LccScenarioID")
                        .HasComment("Scenario ID of the LCC (FK to Scenario)");

                    b.Property<int?>("LccSiId")
                        .HasColumnType("int")
                        .HasColumnName("LccSiID");

                    b.Property<decimal?>("LccTotalAverageCost")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Total average cost of the LCC");

                    b.Property<decimal?>("LccUtilizationFunctional")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Functional utilization (time) of the LCC");

                    b.Property<decimal?>("LccUtilizationTechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Technical utilization (time) of the LCC");

                    b.Property<DateTime?>("LccdateCalculated")
                        .HasColumnType("smalldatetime")
                        .HasColumnName("LCCDateCalculated");

                    b.HasKey("LccId");

                    b.HasIndex("LccChildObject");

                    b.HasIndex("LccChildObject1");

                    b.HasIndex("LccChildObject2");

                    b.HasIndex("LccChildObject3");

                    b.HasIndex("LccChildObject4");

                    b.HasIndex("LccPartOf")
                        .HasDatabaseName("IX_LCCPartOf");

                    b.HasIndex("LccRiskObject");

                    b.HasIndex("LccScenarioId");

                    b.HasIndex("LccSiId");

                    b.ToTable("TblLCC");

                    b.HasComment("Contains the life cycle costs for each defined risk object. Calculations can be based on risk and/or RAMS analysis. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lccdetail", b =>
                {
                    b.Property<int>("LccDetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LccDetID")
                        .HasComment("Unique ID (PK of LCCDetail)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LccDetId"), 1L, 1);

                    b.Property<decimal?>("LccDetActionCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost for actions of the LCC detail (? is this different than LCCDetTaskCost?) ");

                    b.Property<decimal?>("LccDetAec")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("LccDetAEC")
                        .HasComment("Annual equivalent costs. Is used to compare investment options where the natural replacement cycle cannot easily be related to the period of analysis. ");

                    b.Property<decimal?>("LccDetAvailabilityTechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Technical availability (time) of the LCC detail. The amount of time the riskobject is available for use. ");

                    b.Property<decimal?>("LccDetAverageAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Average cost after executing preventive actions ");

                    b.Property<decimal?>("LccDetAverageBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Average costs before executing preventive actions ");

                    b.Property<decimal?>("LccDetAverageOptimalCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Average optimal costs for the current year only");

                    b.Property<decimal?>("LccDetAverageRealCorrectiveCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Average costs of corrective measures for the current year only");

                    b.Property<decimal?>("LccDetAverageRealPreventiveCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Average costs of preventive measures for the current year only");

                    b.Property<decimal?>("LccDetCapexCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetCorrectiveCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs for corrective measures taken");

                    b.Property<decimal?>("LccDetDepreciation")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Depreciation cost of the LCC detail");

                    b.Property<decimal?>("LccDetDirectCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Direct cost of the LCC detail");

                    b.Property<decimal?>("LccDetEcoTechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Technical eco score of the LCC detail.");

                    b.Property<bool?>("LccDetExcluded")
                        .HasColumnType("bit")
                        .HasComment("Boolean that excludes the LCC detail from calculation when true.");

                    b.Property<decimal?>("LccDetFailureRate")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Failure rate of the LCC detail. Failure rate is the frequency with which an engineered system or component fails. Usually expressed in failures per hour. ");

                    b.Property<decimal?>("LccDetFmecaAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("FMECA cost after executing preventive actions ");

                    b.Property<decimal?>("LccDetFmecaBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("FMECA cost before executing preventive actions ");

                    b.Property<decimal?>("LccDetFmecaCustomAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Custom cost after executing preventive actions");

                    b.Property<decimal?>("LccDetFmecaCustomBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Custom FMECA costs before executing preventive actions");

                    b.Property<decimal?>("LccDetFmecaCustomRiskAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Custom risk cost after executing preventive actions");

                    b.Property<decimal?>("LccDetFmecaCustomRiskBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Custom risk before executing preventive actions");

                    b.Property<int?>("LccDetLccId")
                        .HasColumnType("int")
                        .HasColumnName("LccDetLccID")
                        .HasComment("LCC ID to which the LCC detail belongs (FK to LCC)");

                    b.Property<decimal?>("LccDetMaintenanceCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Maintenance cost of the LCC detail");

                    b.Property<decimal?>("LccDetModificationCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost for modifications of the LCC detail");

                    b.Property<decimal?>("LccDetOpexCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Opex cost of the LCC detail (operating expenditures)");

                    b.Property<decimal?>("LccDetOptimalCorrectiveCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Optimal costs of corrective measures for the current year only");

                    b.Property<decimal?>("LccDetOptimalPreventiveCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Optimal costs of preventive measures for the current year only");

                    b.Property<decimal?>("LccDetPreventiveCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs of preventive measures taken");

                    b.Property<decimal?>("LccDetProcedureCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost for procedures of the LCC detail");

                    b.Property<decimal?>("LccDetProductivityTechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Technical productivity time of the LCC detail. Productivity is an average measure of the efficiency of production. It is usually expressed as a ratio of production output to what is required to produce it. ");

                    b.Property<decimal?>("LccDetRealCorrectiveCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs of the corrective measures for the current year only");

                    b.Property<decimal?>("LccDetRealOpexCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Opex cost of the current year only (operating expenditures)");

                    b.Property<decimal?>("LccDetRealPreventiveCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs of the preventive measures for the current year only");

                    b.Property<decimal?>("LccDetRealTotalCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Real total costs for the current year only");

                    b.Property<decimal?>("LccDetReliability")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Reliability of the LCC detail");

                    b.Property<decimal?>("LccDetRiskAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Risk cost after executing preventive actions");

                    b.Property<decimal?>("LccDetRiskBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Risk cost before executing preventive actions");

                    b.Property<decimal?>("LccDetSpareCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost for spare parts needed for the LCC detail");

                    b.Property<decimal?>("LccDetTaskCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost for tasks of the LCC detail (? is this different than LCCDetActionCost?) ");

                    b.Property<decimal?>("LccDetTotalCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("The costs of this year and all previous years");

                    b.Property<decimal?>("LccDetTotalNpv")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("LccDetTotalNPV")
                        .HasComment("Total net present value of the LCC detail");

                    b.Property<decimal?>("LccDetUtilizationTechnichal")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Technical utilization time of the LCC detail. The time the riskobject is actually being used in production. In basic terms, utilization is a measure of the actual revenue earned by the assets against the potential revenue they could have earned.");

                    b.Property<int?>("LccDetYear")
                        .HasColumnType("int")
                        .HasComment("LCC detail year");

                    b.HasKey("LccDetId")
                        .HasName("PK_CalcLCCtotal");

                    b.HasIndex("LccDetLccId");

                    b.ToTable("TblLCCDetail");

                    b.HasComment("Contains the life cycle cost details for each risk object, for each year the LCC needs to be calculated. Can be calculated based on:\r\n- risk per fmeca effect column\r\n- risk per year\r\n- task per year\r\n(one or all of the above can be chosen from the LCC command center control, which generates the LCC details based on the users' preferences)\r\n\r\nAll of the LCCDetails combined will provide the data for the actual Life Cycle Costing calculation for each riskobject.  \r\n");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccDetailGrid", b =>
                {
                    b.Property<decimal?>("LccDetActionCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetAec")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("LccDetAEC");

                    b.Property<decimal?>("LccDetAvailabilityTechnical")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccDetAverageAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetAverageBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetAverageOptimalCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetAverageRealCorrectiveCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetAverageRealPreventiveCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetCorrectiveCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetDepreciation")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("LccDetDirectCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetEcoTechnical")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccDetFailureRate")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccDetFmecaAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetFmecaBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetFmecaCustomAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetFmecaCustomBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetFmecaCustomRiskAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetFmecaCustomRiskBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("LccDetId")
                        .HasColumnType("int")
                        .HasColumnName("LccDetID");

                    b.Property<int?>("LccDetLccId")
                        .HasColumnType("int")
                        .HasColumnName("LccDetLccID");

                    b.Property<decimal?>("LccDetMaintenanceCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetModificationCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetOpexCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetOptimalCorrectiveCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetOptimalPreventiveCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetPreventiveCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetProcedureCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetProductivityTechnical")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccDetRealCorrectiveCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetRealOpexCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetRealPreventiveCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetRealTotalCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetReliability")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("LccDetRiskAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetRiskBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetSpareCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetTaskCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetTotalCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccDetTotalNpv")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("LccDetTotalNPV");

                    b.Property<decimal?>("LccDetUtilizationTechnichal")
                        .HasColumnType("decimal(18,6)");

                    b.Property<int?>("LccDetYear")
                        .HasColumnType("int");

                    b.Property<string>("LccName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("LccProductionCost")
                        .HasColumnType("decimal(18,2)");

                    b.ToView("LccDetailGrid");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LcceffectDetail", b =>
                {
                    b.Property<int>("LccEfctId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LccEfctID")
                        .HasComment("Unique ID (PK of LCCEffectDetail)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LccEfctId"), 1L, 1);

                    b.Property<decimal?>("LccEfctActionCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("The costs of a task not specified (Task, action, modification or inspection)");

                    b.Property<decimal?>("LccEfctCorrectiveCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Total costs of corrective actions for this LCC effect detail");

                    b.Property<decimal?>("LccEfctCustomAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Custom cost of LCC effect detail, after executing preventive action");

                    b.Property<decimal?>("LccEfctCustomBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Custom cost of LCC effect detail, before executing preventive action");

                    b.Property<decimal?>("LccEfctCustomRiskAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Custom risk cost of the LCC effect detail, after executing preventive actions");

                    b.Property<decimal?>("LccEfctCustomRiskBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Custom risk cost of the LCC effect detail, before executing preventive actions");

                    b.Property<decimal?>("LccEfctDirectCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Direct cost of the LCC effect detail");

                    b.Property<int?>("LccEfctEffectColumn")
                        .HasColumnType("int")
                        .HasComment("Concerning FMECA column of the LCC effect detail (?)");

                    b.Property<string>("LccEfctEffectName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name for the FMECA column of the LCC effect detail");

                    b.Property<decimal?>("LccEfctFmecaAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("FMECA cost for this LCC effect detail, after executing preventive actions ");

                    b.Property<decimal?>("LccEfctFmecaBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("FMECA cost for this LCC effect detail, before executing preventive actions ");

                    b.Property<int?>("LccEfctLccDetailId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctLccDetailID")
                        .HasComment("LCC detail ID the LCC effect detail belongs to (FK to LCCDetail)");

                    b.Property<int?>("LccEfctLccId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctLccID")
                        .HasComment("LCC ID the LCC effect detail belongs to (FK to LCC)");

                    b.Property<decimal?>("LccEfctOptimalCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Optimal cost of the LCC effect detail");

                    b.Property<decimal?>("LccEfctPreventiveCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Total costs of preventive actions for this LCC effect detail");

                    b.Property<int?>("LccEfctRamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctRamsDiagramID")
                        .HasComment("RAMS diagram ID the LCC effect detail belongs to (FK to RamsDiagram)");

                    b.Property<int?>("LccEfctRamsId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctRamsID")
                        .HasComment("RAMS ID the LCC effect detail belongs to (FK to Rams)");

                    b.Property<decimal?>("LccEfctRiskAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Risk cost of LCC effect detail, after executing preventive action ");

                    b.Property<decimal?>("LccEfctRiskBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Risk cost of LCC effect detail, before executing preventive action ");

                    b.Property<int?>("LccEfctRiskId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctRiskID")
                        .HasComment("Risk ID the LCC effect detail belongs to (FK to MRB)");

                    b.Property<decimal?>("LccEfctSparePartCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("The maintenance costs (real expenses), these are the costs for storage and purchase of spare parts for this LCC effect detail");

                    b.Property<decimal?>("LccEfctTaskFmeca")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("?");

                    b.Property<decimal?>("LccEfctTaskFmecaCustom")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("?");

                    b.Property<int?>("LccEfctTaskId")
                        .HasColumnType("int")
                        .HasColumnName("LccEfctTaskID")
                        .HasComment("Task ID the LCC effect detail belongs to (FK to Task)");

                    b.Property<int?>("LccEfctType")
                        .HasColumnType("int")
                        .HasComment("Type of LCC effect detail, describes what caused the LCC effect detail (Type domain contains EffectDetail, RiskDetail, TaskDetail or RamsDetail)");

                    b.Property<int?>("LccEfctYear")
                        .HasColumnType("int")
                        .HasComment("Year the LCC effect detail contains costs for");

                    b.HasKey("LccEfctId");

                    b.HasIndex("LccEfctLccDetailId");

                    b.HasIndex("LccEfctLccId");

                    b.ToTable("TblLccEffectDetail");

                    b.HasComment("Contains the life cycle cost effect details. These details are generated from data gathered from LCC, LCCDetail, Task, Mrb and RiskObject. (?)\r\n\r\nLCC effect details are be generated from the LCC command centre control, based on the users' preferences.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccFmecaOfRiskObject", b =>
                {
                    b.Property<string>("FmecaMatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("FmecaVersion")
                        .HasColumnType("int");

                    b.Property<int>("RiskObjFmecaId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjFmecaID");

                    b.Property<int>("RiskObjId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjID");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.ToView("LccFmecaOfRiskObject");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccGrid", b =>
                {
                    b.Property<string>("ChildObject2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ChildObject3")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ChildObject4")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FmecaMatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("FmecaVersion")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccAec")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("LccAEC");

                    b.Property<decimal?>("LccAverageOptimalCost")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int?>("LccChildObject")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject1")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject2")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject3")
                        .HasColumnType("int");

                    b.Property<int?>("LccChildObject4")
                        .HasColumnType("int");

                    b.Property<DateTime?>("LccDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<decimal?>("LccDiscountRate")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("LccEcoFunctional")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccEcoTechnical")
                        .HasColumnType("decimal(18,6)");

                    b.Property<bool?>("LccExclude")
                        .HasColumnType("bit");

                    b.Property<int>("LccId")
                        .HasColumnType("int")
                        .HasColumnName("LccID");

                    b.Property<decimal?>("LccInputAvailabilityTechnical")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccInputReliabilityFunctional")
                        .HasColumnType("decimal(18,6)");

                    b.Property<int?>("LccMaxYears")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccMcRav")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("LccModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<decimal?>("LccMtbffunctional")
                        .HasColumnType("decimal(18,6)")
                        .HasColumnName("LccMTBFFunctional");

                    b.Property<decimal?>("LccMtbftechnical")
                        .HasColumnType("decimal(18,6)")
                        .HasColumnName("LccMTBFTechnical");

                    b.Property<string>("LccName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("LccNpv")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("LccNPV");

                    b.Property<int?>("LccNpvyear")
                        .HasColumnType("int")
                        .HasColumnName("LccNPVyear");

                    b.Property<decimal?>("LccOptimalAverageCorrectiveCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<decimal?>("LccOptimalAveragePreventiveCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<byte[]>("LccOptimalImage")
                        .HasColumnType("image");

                    b.Property<decimal?>("LccOutputAvailabilityTechnical")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccOutputReliabilityTechnical")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccOverallProductionCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<int?>("LccPartOf")
                        .HasColumnType("int");

                    b.Property<decimal?>("LccPotential")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccProductionCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("LccProductivityFunctional")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccProductivityTechnical")
                        .HasColumnType("decimal(18,6)");

                    b.Property<int?>("LccRamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsDiagramID");

                    b.Property<int?>("LccRamsId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsID");

                    b.Property<byte[]>("LccRamsImage")
                        .HasColumnType("image");

                    b.Property<byte[]>("LccRealCostImage")
                        .HasColumnType("image");

                    b.Property<string>("LccRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("LccReplacementValue")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int?>("LccRiskObject")
                        .HasColumnType("int");

                    b.Property<int?>("LccScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("LccScenarioID");

                    b.Property<int?>("LccSiId")
                        .HasColumnType("int")
                        .HasColumnName("LccSiID");

                    b.Property<decimal?>("LccTotalAverageCost")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("LccUtilizationFunctional")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal?>("LccUtilizationTechnical")
                        .HasColumnType("decimal(18,6)");

                    b.Property<DateTime?>("LccdateCalculated")
                        .HasColumnType("smalldatetime")
                        .HasColumnName("LCCDateCalculated");

                    b.Property<decimal?>("Obj2NewValue")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("Obj3NewValue")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("Obj4NewValue")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int?>("ObjAvailableTime")
                        .HasColumnType("int");

                    b.Property<int?>("ObjId")
                        .HasColumnType("int")
                        .HasColumnName("ObjID");

                    b.Property<string>("ObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("ObjNewValue")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int?>("ObjProductionTime")
                        .HasColumnType("int");

                    b.Property<int?>("ObjUsableTime")
                        .HasColumnType("int");

                    b.Property<int?>("ObjUtilizationTime")
                        .HasColumnType("int");

                    b.Property<string>("PartOfName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RiskObjNoOfInstallation")
                        .HasColumnType("int");

                    b.Property<decimal?>("RiskObjProdCostHour")
                        .HasColumnType("decimal(18,2)");

                    b.ToView("LccGrid");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccMrbCalc", b =>
                {
                    b.Property<string>("Failmode")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("failmode");

                    b.Property<int>("Failrateid")
                        .HasColumnType("int")
                        .HasColumnName("failrateid");

                    b.Property<string>("Fmecamatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("fmecamatrix");

                    b.Property<int?>("Fmecaversion")
                        .HasColumnType("int")
                        .HasColumnName("fmecaversion");

                    b.Property<decimal>("Mrbactioncosts")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrbactioncosts");

                    b.Property<decimal?>("Mrbcapcosts")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrbcapcosts");

                    b.Property<int?>("Mrbchildobject")
                        .HasColumnType("int")
                        .HasColumnName("mrbchildobject");

                    b.Property<int?>("Mrbchildobject1")
                        .HasColumnType("int")
                        .HasColumnName("mrbchildobject1");

                    b.Property<int?>("Mrbchildobject2")
                        .HasColumnType("int")
                        .HasColumnName("mrbchildobject2");

                    b.Property<int?>("Mrbchildobject3")
                        .HasColumnType("int")
                        .HasColumnName("mrbchildobject3");

                    b.Property<int?>("Mrbchildobject4")
                        .HasColumnType("int")
                        .HasColumnName("mrbchildobject4");

                    b.Property<decimal?>("Mrbcustomafter")
                        .HasColumnType("decimal(18,3)")
                        .HasColumnName("mrbcustomafter");

                    b.Property<decimal?>("Mrbcustombefore")
                        .HasColumnType("decimal(18,3)")
                        .HasColumnName("mrbcustombefore");

                    b.Property<decimal?>("Mrbdirectcostafter")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrbdirectcostafter");

                    b.Property<decimal?>("Mrbdirectcostbefore")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrbdirectcostbefore");

                    b.Property<decimal?>("Mrbdowntimeafter")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("mrbdowntimeafter");

                    b.Property<decimal?>("Mrbdowntimebefore")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("mrbdowntimebefore");

                    b.Property<decimal?>("Mrbeffectafter")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrbeffectafter");

                    b.Property<decimal?>("Mrbeffectbefore")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrbeffectbefore");

                    b.Property<string>("Mrbfailurecategorie1")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("mrbfailurecategorie1");

                    b.Property<string>("Mrbfailurecategorie2")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("mrbfailurecategorie2");

                    b.Property<int?>("Mrbfailuremode")
                        .HasColumnType("int")
                        .HasColumnName("mrbfailuremode");

                    b.Property<string>("Mrbfmecaselect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("mrbfmecaselect");

                    b.Property<int>("Mrbfmecaversion")
                        .HasColumnType("int")
                        .HasColumnName("mrbfmecaversion");

                    b.Property<int>("Mrbid")
                        .HasColumnType("int")
                        .HasColumnName("mrbid");

                    b.Property<decimal?>("Mrbmtbfafter")
                        .HasColumnType("decimal(18,3)")
                        .HasColumnName("mrbmtbfafter");

                    b.Property<decimal?>("Mrbmtbfbefore")
                        .HasColumnType("decimal(18,3)")
                        .HasColumnName("mrbmtbfbefore");

                    b.Property<string>("Mrbname")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("mrbname");

                    b.Property<decimal>("Mrboptimalcosts")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrboptimalcosts");

                    b.Property<decimal?>("Mrbriskafter")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrbriskafter");

                    b.Property<decimal?>("Mrbriskbefore")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrbriskbefore");

                    b.Property<int>("Mrbriskobject")
                        .HasColumnType("int")
                        .HasColumnName("mrbriskobject");

                    b.Property<decimal>("Mrbsparecosts")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrbsparecosts");

                    b.Property<decimal>("Mrbsparemanagecost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("mrbsparemanagecost");

                    b.Property<decimal?>("NewValue")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("NewValue1")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("NewValue2")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("NewValue3")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("NewValue4")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int?>("Riskobjnoofinstallation")
                        .HasColumnType("int")
                        .HasColumnName("riskobjnoofinstallation");

                    b.ToView("LccMrbCalc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccOpexCalc", b =>
                {
                    b.Property<int?>("Expr1")
                        .HasColumnType("int");

                    b.Property<int?>("Expr2")
                        .HasColumnType("int");

                    b.Property<int?>("OpexDataInflationGroupId")
                        .HasColumnType("int")
                        .HasColumnName("OpexDataInflationGroupID");

                    b.Property<int?>("OpexDataInflationGroupId2")
                        .HasColumnType("int")
                        .HasColumnName("OpexDataInflationGroupID2");

                    b.Property<int?>("OpexDataInflationGroupId3")
                        .HasColumnType("int")
                        .HasColumnName("OpexDataInflationGroupID3");

                    b.Property<int?>("OpexDataMethod")
                        .HasColumnType("int");

                    b.Property<string>("OpexDataName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("OpexDataName2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("OpexDataName3")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("OpexDataPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("OpexDataPercentage2")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("OpexDataPercentage3")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OpexLccColorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("OpexLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccID");

                    b.Property<int?>("OpexLccLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccLccID");

                    b.Property<string>("OpexLccName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("OpexLccOpexDataId1")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID1");

                    b.Property<int?>("OpexLccOpexDataId2")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID2");

                    b.Property<int?>("OpexLccOpexDataId3")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID3");

                    b.Property<decimal?>("OpexLccPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("OpexLccQuantity")
                        .HasColumnType("decimal(18,4)");

                    b.Property<bool?>("OpexLccShowInGraph")
                        .HasColumnType("bit");

                    b.Property<decimal?>("OpexLccUnits")
                        .HasColumnType("decimal(18,2)");

                    b.ToView("lccOpexCalc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccOutdated", b =>
                {
                    b.Property<DateTime?>("LccDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int>("LccId")
                        .HasColumnType("int")
                        .HasColumnName("LccID");

                    b.Property<int?>("LccRamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsDiagramID");

                    b.Property<int?>("LccRamsId")
                        .HasColumnType("int")
                        .HasColumnName("LccRamsID");

                    b.Property<DateTime?>("LccdateCalculated")
                        .HasColumnType("smalldatetime")
                        .HasColumnName("LCCDateCalculated");

                    b.Property<DateTime?>("MrbLastModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("MrbRiskObject")
                        .HasColumnType("int");

                    b.Property<int?>("RamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramID");

                    b.Property<DateTime?>("RamsLastModified")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TaskDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.ToView("LccOutdated");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccOutdatedOnRisk", b =>
                {
                    b.Property<int>("LccId")
                        .HasColumnType("int")
                        .HasColumnName("LccID");

                    b.Property<DateTime?>("LccdateCalculated")
                        .HasColumnType("smalldatetime")
                        .HasColumnName("LCCDateCalculated");

                    b.Property<DateTime?>("MrbLastModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int>("MrbRiskObject")
                        .HasColumnType("int");

                    b.ToView("LccOutdatedOnRisk");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccRamsCalc", b =>
                {
                    b.Property<string>("FmecaMatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("FmecaVersion")
                        .HasColumnType("int");

                    b.Property<double?>("RamsAvailabilityInput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsAvailabilityOutput")
                        .HasColumnType("float");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<string>("RamsClassDc")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasColumnName("RamsClassDC");

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<double?>("RamsDcd")
                        .HasColumnType("float")
                        .HasColumnName("RamsDCd");

                    b.Property<string>("RamsDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("RamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramID");

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramRefID");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<string>("RamsFunctionalDemand")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit");

                    b.Property<string>("RamsInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<bool>("RamsLccusePfd")
                        .HasColumnType("bit")
                        .HasColumnName("RamsLCCUsePFD");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int");

                    b.Property<string>("RamsModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn");

                    b.Property<double?>("RamsMttr")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTTR");

                    b.Property<string>("RamsName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RamsObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsObjectID");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int");

                    b.Property<double?>("RamsPfd")
                        .HasColumnType("float")
                        .HasColumnName("RamsPFD");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int");

                    b.Property<string>("RamsRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("RamsRiskId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskID");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskObjectID");

                    b.Property<int?>("RamsSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsSiID");

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18,0)");

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsWantLCC");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int");

                    b.Property<int?>("RamsXposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsXPosition");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int");

                    b.Property<int?>("RamsYposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsYPosition");

                    b.Property<int?>("RiskObjNoOfInstallation")
                        .HasColumnType("int");

                    b.Property<decimal?>("ValueObject")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("ValueRiskObject")
                        .HasColumnType("decimal(18,4)");

                    b.ToView("LccRamsCalc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LccSiCalc", b =>
                {
                    b.Property<string>("FailMode")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int>("FailRateId")
                        .HasColumnType("int")
                        .HasColumnName("FailRateID");

                    b.Property<string>("FmecaMatrix")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("FmecaVersion")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbActionCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbCapCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbDirectCostAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbDirectCostBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbDownTimeAfter")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("MrbDownTimeBefore")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MrbFailureCategorie1")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("MrbFailureCategorie2")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("MrbFailureMode")
                        .HasColumnType("int");

                    b.Property<string>("MrbFmecaSelect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("MrbFmecaVersion")
                        .HasColumnType("int");

                    b.Property<int>("MrbId")
                        .HasColumnType("int")
                        .HasColumnName("MrbID");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("MrbOptimalCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbRiskBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("MrbRiskObject")
                        .HasColumnType("int");

                    b.Property<decimal?>("MrbSpareCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbSpareManageCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PckSiSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiSiID");

                    b.ToView("LccSiCalc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Login", b =>
                {
                    b.Property<string>("LogInUserMachineName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Computer name used by logged in user");

                    b.Property<int>("LoginId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LoginID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LoginId"), 1L, 1);

                    b.Property<DateTime?>("LoginLoginDate")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date and time the login occurred");

                    b.Property<DateTime?>("LoginLogoutDate")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("LoginUserDomain")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Domain of the logged in user");

                    b.Property<int?>("LoginUserId")
                        .HasColumnType("int")
                        .HasColumnName("LoginUserID")
                        .HasComment("User name of the logged in user");

                    b.ToTable("Login");

                    b.HasComment("Contains data that shows which user logged in from where. This table is not used in the AMprover software (!)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lookup", b =>
                {
                    b.Property<int>("LookupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("LookupID")
                        .HasComment("Unique ID (PK for Lookup)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LookupId"), 1L, 1);

                    b.Property<string>("LookupFilter")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Domain name, each unique name defines a different lookup category");

                    b.Property<string>("LookupLongDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Long description");

                    b.Property<string>("LookupShortDescription")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Short description");

                    b.Property<int?>("LookupValue")
                        .HasColumnType("int")
                        .HasComment("Value that distinguishes lookup domain values, must be unique within the same category");

                    b.HasKey("LookupId");

                    b.HasIndex("LookupFilter", "LookupValue")
                        .IsUnique()
                        .HasDatabaseName("IX_Lookup")
                        .HasFilter("[LookupFilter] IS NOT NULL AND [LookupValue] IS NOT NULL");

                    b.ToTable("Lookup");

                    b.HasComment("Master data table that stores domains, that are used throughout the program. Domains are grouped by have the same name for field LookupFilter. A set of values can be created for each domain. Not editable by user. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupExecutor", b =>
                {
                    b.Property<int>("ExecutorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ExecutorID")
                        .HasComment("Unique ID (PK for LookupExecutor)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ExecutorId"), 1L, 1);

                    b.Property<string>("ExecutorDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the task executor (Which clarifies the name where needed)");

                    b.Property<string>("ExecutorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the task executor");

                    b.HasKey("ExecutorId")
                        .HasName("PK_LookupTaskExecutor");

                    b.ToTable("LookupExecutor");

                    b.HasComment("Master data table that contains all currently defined task executors. A task executor is the person or department that handles execution of a task. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupFailCat", b =>
                {
                    b.Property<int>("FailCatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("FailCatID")
                        .HasComment("Unique ID (PK for LookupFailCat)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FailCatId"), 1L, 1);

                    b.Property<string>("FailCatDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Failure category description");

                    b.Property<string>("FailCatGroup")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("The group the failure category belongs to (Group domain is defined in table Lookup)");

                    b.Property<string>("FailCatName")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Failure category name");

                    b.HasKey("FailCatId")
                        .HasName("PK_LookupFailCat");

                    b.HasIndex("FailCatName")
                        .IsUnique()
                        .HasDatabaseName("IX_LookupFailCat");

                    b.ToTable("LookupFailCat");

                    b.HasComment("Master data table that contains all defined failure categories. Each failure category defines a different failure behavior. User is allowed to change failure category names and descriptions.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupFailMode", b =>
                {
                    b.Property<int>("FailId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("FailID")
                        .HasComment("Unique ID (PK for LookupFailMode)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FailId"), 1L, 1);

                    b.Property<DateTime?>("FailDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("FailDescription")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)")
                        .HasComment("Description of the failure mode");

                    b.Property<int?>("FailDistributionId")
                        .HasColumnType("int")
                        .HasColumnName("FailDistributionID")
                        .HasComment("Distribution type determines what method of calculation will be used to calculate the costs associated with each failure mode. Distribution type values are defined in the Lookup table.");

                    b.Property<int?>("FailIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("Defines the position in the Weibull curve (when combined with Weibull location)");

                    b.Property<string>("FailMode")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Name of the failure mode");

                    b.Property<string>("FailModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<int>("FailRateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("FailRateID")
                        .HasDefaultValueSql("((3))")
                        .HasComment("Failure rate. This is the frequency at which a system or component fails. Possible values are defined in Lookup. Defaults to constant.");

                    b.Property<int?>("FailRiskTypeId")
                        .HasColumnType("int")
                        .HasColumnName("FailRiskTypeID")
                        .HasComment("Describes if the failure shows itself, or is invisible. Possible values are defined in Lookup.");

                    b.Property<decimal?>("FailShape")
                        .HasColumnType("decimal(10,2)")
                        .HasComment("Fail shape defines the slope of the current location on the Weibull curve");

                    b.Property<decimal?>("FailWeibullLocation")
                        .HasColumnType("decimal(10,2)")
                        .HasComment("Defines the location on the Weibull curve (starting point)");

                    b.HasKey("FailId");

                    b.ToTable("LookupFailMode");

                    b.HasComment("Master data table that defines failure modes. A failure mode describes a possible cause for the failure, if the failure shows itself, and what the frequency of the failure is. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupGridColumn", b =>
                {
                    b.Property<string>("ControlName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FieldName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ColumnName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ColumnHeader")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("ColumnWidth")
                        .HasColumnType("int");

                    b.Property<int>("DisplayIndex")
                        .HasColumnType("int");

                    b.Property<int>("FieldType")
                        .HasColumnType("int");

                    b.Property<bool>("Visible")
                        .HasColumnType("bit");

                    b.HasKey("ControlName", "FieldName", "ColumnName")
                        .HasName("PK_LookupGridColumn_ID");

                    b.ToTable("LookupGridColumn");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupInflationGroup", b =>
                {
                    b.Property<int>("InflId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("InflID")
                        .HasComment("Unique ID (PK of LookupInflationGroup)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("InflId"), 1L, 1);

                    b.Property<DateTime?>("InflDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("InflModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Last modification of this record was made by this user");

                    b.Property<string>("InflName")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Inflation group name. Describes what the inflation percentage belongs to.");

                    b.Property<decimal?>("InflPercentage")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Inflation percentage for this specific item.");

                    b.HasKey("InflId")
                        .HasName("PK_InflationGroups");

                    b.ToTable("LookupInflationGroup");

                    b.HasComment("Master data table that contains inflation groups. The inflation groups allow us to connect a specific inflation percentage to a variety of things. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupInitiator", b =>
                {
                    b.Property<int>("InitiatorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("InitiatorID")
                        .HasComment("Unique ID (PK of LookupInitiator)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("InitiatorId"), 1L, 1);

                    b.Property<string>("InitiatorDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the initiator");

                    b.Property<string>("InitiatorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the initiator");

                    b.HasKey("InitiatorId")
                        .HasName("PK_LookupTaskInitiator");

                    b.ToTable("LookupInitiator");

                    b.HasComment("Master data table that defines who (or what) will initiate a certain action. Is used for clusters and preventive actions. Editable by user.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupIntervalUnit", b =>
                {
                    b.Property<int>("IntUnitId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("IntUnitID")
                        .HasComment("Unique ID (PK of LookupIntervalUnit)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IntUnitId"), 1L, 1);

                    b.Property<decimal?>("IntUnitCalculationKey")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Number of times an interval unit occurs in a year");

                    b.Property<DateTime?>("IntUnitDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("IntUnitDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the interval unit");

                    b.Property<string>("IntUnitModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<string>("IntUnitName")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Name of the interval unit");

                    b.Property<string>("IntUnitShortKey")
                        .IsRequired()
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("Short code for the interval unit (usually consists of the first letter of the name)");

                    b.HasKey("IntUnitId")
                        .HasName("PK_LookupTaskIntervalUnit");

                    b.ToTable("LookupIntervalUnit");

                    b.HasComment("Master data table that holds values that are used in interval calculations. Editable by user.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupMxPolicy", b =>
                {
                    b.Property<int>("PolId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PolID")
                        .HasComment(" (PK of LookupMxPolicy)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PolId"), 1L, 1);

                    b.Property<string>("PolDescription")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Provides a more detailed description of the maintenance policy");

                    b.Property<string>("PolName")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)")
                        .HasComment("Name that describes the maintenance policy");

                    b.HasKey("PolId");

                    b.ToTable("LookupMxPolicy");

                    b.HasComment("Master data table that defines different maintenance policies. A maintenance policy can be described as the reason why maintenance should occur. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupSettings", b =>
                {
                    b.Property<string>("AmsProperty")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Unique ID and the name of the setting. (PK of LookupSettings)");

                    b.Property<DateTime?>("AmsDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("AmsDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<decimal?>("AmsDecimalValue")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Decimal value of the setting");

                    b.Property<string>("AmsInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user");

                    b.Property<int?>("AmsIntValue")
                        .HasColumnType("int")
                        .HasComment("Integer value of the setting");

                    b.Property<bool?>("AmsModifiable")
                        .HasColumnType("bit")
                        .HasComment("Boolean value that states if the setting is modifiable");

                    b.Property<string>("AmsModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<string>("AmsTextValue")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("The text value containing the values of the LookupSetting. Can be either null, a pipe ('|') delimited string, or XML. ");

                    b.HasKey("AmsProperty");

                    b.ToTable("LookupSettings");

                    b.HasComment("Master data table that defines settings for a variety of things within the Amprover software. (like grid column settings and program constants) Not editable by user. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupUserDefined", b =>
                {
                    b.Property<int>("UserDefinedId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("UserDefinedID")
                        .HasComment("Unique ID (PK of LookupUserDefined)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserDefinedId"), 1L, 1);

                    b.Property<DateTime?>("UserDefinedDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("UserDefinedFilter")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Filter that distinguishes user defined lookup categories (items of the same domain have the same name in this column)");

                    b.Property<string>("UserDefinedLongDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Long description");

                    b.Property<string>("UserDefinedModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User that made the last modification to this record");

                    b.Property<string>("UserDefinedShortDescription")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Short description");

                    b.Property<int?>("UserDefinedValue")
                        .HasColumnType("int")
                        .HasComment("Value for the user defined domain item");

                    b.HasKey("UserDefinedId");

                    b.HasIndex("UserDefinedFilter", "UserDefinedValue")
                        .IsUnique()
                        .HasDatabaseName("IX_LookupUserDefined")
                        .HasFilter("[UserDefinedFilter] IS NOT NULL AND [UserDefinedValue] IS NOT NULL");

                    b.ToTable("LookupUserDefined");

                    b.HasComment("Master data table that stores user defined domains. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mca", b =>
                {
                    b.Property<int>("McaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("McaID")
                        .HasComment("Unique ID (PK of Mca)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("McaId"), 1L, 1);

                    b.Property<int?>("McaCommercial")
                        .HasColumnType("int")
                        .HasComment("Commercial score for the multi criteria analyse");

                    b.Property<int?>("McaIntensity")
                        .HasColumnType("int")
                        .HasComment("Intensity score for the multi criteria analyse");

                    b.Property<int?>("McaQualityLevel")
                        .HasColumnType("int")
                        .HasComment("Level of the multi criteria analyse depending of the business value (0,1,2) ");

                    b.Property<int?>("McaQualityScore")
                        .HasColumnType("int")
                        .HasComment("Total score (business value) of the multi criteria analyse");

                    b.Property<int?>("McaRepresentative")
                        .HasColumnType("int")
                        .HasComment("Representative score for the multi criteria analyse");

                    b.Property<int>("McaSiId")
                        .HasColumnType("int")
                        .HasColumnName("McaSiID")
                        .HasComment("ID of the significant item the multi criteria analyse belongs to (FK to Si)");

                    b.Property<int?>("McaSocial")
                        .HasColumnType("int")
                        .HasComment("Social score for the multi criteria analyse");

                    b.Property<int?>("McaUsage")
                        .HasColumnType("int")
                        .HasComment("Usage score for the multi criteria analyse");

                    b.Property<int?>("McaUtilization")
                        .HasColumnType("int")
                        .HasComment("Utilization score for the multi criteria analyse");

                    b.HasKey("McaId");

                    b.HasIndex("McaSiId")
                        .IsUnique()
                        .HasDatabaseName("IX_Mca");

                    b.ToTable("Mca");

                    b.HasComment("Contains data for multiple criteria analysis. (MCA) MCA is a scientific procedure that allows for a rational choice based on more than one distinguishing criterium.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mrb", b =>
                {
                    b.Property<int>("Mrbid")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("MRBId");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Mrbid"), 1L, 1);

                    b.Property<decimal>("MrbActionCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Total cost per year for all tasks that are bound to the risk");

                    b.Property<decimal?>("MrbCapCosts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValueSql("((0))")
                        .HasComment("(?) not used in amprover software");

                    b.Property<int?>("MrbChildObject")
                        .HasColumnType("int")
                        .HasComment("ID of the object the risk belongs to (FK to Object)");

                    b.Property<int?>("MrbChildObject1")
                        .HasColumnType("int")
                        .HasComment("ID of the object (level 1) the risk belongs to (FK to Object)");

                    b.Property<int?>("MrbChildObject2")
                        .HasColumnType("int")
                        .HasComment("ID of the object (level 2) the risk belongs to (FK to Object)");

                    b.Property<int?>("MrbChildObject3")
                        .HasColumnType("int")
                        .HasComment("ID of the object (level 3) the risk belongs to (FK to Object)");

                    b.Property<int?>("MrbChildObject4")
                        .HasColumnType("int")
                        .HasComment("ID of the object (level 4) the risk belongs to (FK to Object)");

                    b.Property<int?>("MrbChildType")
                        .HasColumnType("int")
                        .HasComment("The way the risk is derived of another risk (eg child, master)");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18,3)")
                        .HasComment("FMECA custom value after executing preventive actions for the risk");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18,3)")
                        .HasComment("FMECA custom value before preventive actions for the risk are executed");

                    b.Property<decimal?>("MrbCustomEffectAfter")
                        .HasColumnType("decimal(18,3)")
                        .HasComment("FMECA custom effect after executing preventive actions for the risk");

                    b.Property<decimal?>("MrbCustomEffectBefore")
                        .HasColumnType("decimal(18,3)")
                        .HasComment("FMECA custom effect before executing preventive actions for the risk");

                    b.Property<DateTime?>("MrbDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("MrbDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("MrbDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the risk, contains the possible effects of the risk (?)");

                    b.Property<decimal?>("MrbDirectCostAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Direct cost of the risk after executing preventive actions");

                    b.Property<decimal?>("MrbDirectCostBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Direct costs of the risk before executing preventive actions");

                    b.Property<decimal?>("MrbDownTimeAfter")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,4)")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Down time after executing preventive actions (in hours) caused by the risk (not used) (!)");

                    b.Property<decimal?>("MrbDownTimeBefore")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,4)")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Down time after preventive actions (in hours) caused by the risk (not used) (!)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("FMECA effect after executing preventive actions for the risk");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("FMECA effect before preventive actions for the risk are executed (needed for Priority)");

                    b.Property<string>("MrbExclOptCb")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasDefaultValueSql("(N'false')")
                        .HasComment("?");

                    b.Property<string>("MrbFailureCategorie1")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Failure category 1, used for the risk (FK to LookupFailCat)");

                    b.Property<string>("MrbFailureCategorie2")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Failure category 2, used for the risk (FK to LookupFailCat)");

                    b.Property<string>("MrbFailureCause")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Cause of the risk  (does not seem to be used in the AMprover software !)");

                    b.Property<string>("MrbFailureConsequences")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Consequences, should the described risk occur ");

                    b.Property<int?>("MrbFailureMode")
                        .HasColumnType("int")
                        .HasComment("ID of the failure mode used for the risk (FK to LookupFailMode)");

                    b.Property<string>("MrbFmecaSelect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Selected values of the FMECA matrix before and after preventive actions. Values are stored in XML format. ");

                    b.Property<int>("MrbFmecaVersion")
                        .HasColumnType("int")
                        .HasComment("Version of the FMECA matrix used for this risk");

                    b.Property<string>("MrbFunction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MrbInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by this user with this username");

                    b.Property<int?>("MrbMasterId")
                        .HasColumnType("int")
                        .HasColumnName("MrbMasterID")
                        .HasComment("ID of the risk this risk was copied from (master risk) (FK to MRB)");

                    b.Property<string>("MrbModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18,3)")
                        .HasComment("MTBF in years after executing preventive actions for the risk");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18,3)")
                        .HasComment("MTBF in years before executing preventive actions for the risk");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name describing the risk");

                    b.Property<string>("MrbNorm")
                        .HasMaxLength(150)
                        .IsUnicode(false)
                        .HasColumnType("varchar(150)")
                        .HasComment("Norm for the risk (seems to not be in use) (!)");

                    b.Property<string>("MrbOpsProcedure")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("? Not used in amprover software");

                    b.Property<decimal>("MrbOptimalCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Optimal costs for preventive actions for the risk (based on risk after preventive action)");

                    b.Property<decimal?>("MrbPointsAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbPointsBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbPointsEffectAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbPointsEffectBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("MrbRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks for the risk, which provides a place to leave any extra information ");

                    b.Property<string>("MrbRemarks1")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the risk (not used) (!)");

                    b.Property<string>("MrbResponsible")
                        .HasMaxLength(150)
                        .IsUnicode(false)
                        .HasColumnType("varchar(150)")
                        .HasComment("Person or department responsible for the risk (? responsible for solving it, or monitoring?)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Risk cost after executing preventive actions");

                    b.Property<decimal?>("MrbRiskBefore")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValueSql("((0))")
                        .HasComment("Risk cost before executing preventive actions");

                    b.Property<int>("MrbRiskObject")
                        .HasColumnType("int")
                        .HasComment("ID of the risk object the risk belongs to (FK to RiskObject)");

                    b.Property<decimal>("MrbSpareCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs of spare parts that are needed for preventive/corrective measures");

                    b.Property<decimal>("MrbSpareManageCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs made to manage the spare parts that are needed for corrective/preventive measures (not used)");

                    b.Property<string>("MrbState")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("State of the risk (? Is this still used?)");

                    b.Property<int?>("MrbStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the risk (master, slave, or lock) (status is defined in Lookup)");

                    b.HasKey("Mrbid");

                    b.HasIndex("MrbChildObject1");

                    b.HasIndex("MrbChildObject2");

                    b.HasIndex("MrbChildObject3");

                    b.HasIndex("MrbChildObject4");

                    b.HasIndex("MrbFailureMode");

                    b.HasIndex("MrbRiskObject")
                        .HasDatabaseName("RiskObject");

                    b.HasIndex("MrbChildObject", "MrbChildObject1", "MrbChildObject2", "MrbChildObject3", "MrbChildObject4")
                        .HasDatabaseName("IX_MRB_ChildObject");

                    b.ToTable("TblMRB");

                    b.HasComment("Contains the data that defines risks, and their relations to other risks. It also contains everything needed to build the risk matrix and the monetary before/after values. (?)Risks can be defined within the Risk Analysis module. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.MrbImage", b =>
                {
                    b.Property<int>("MrbImageId")
                        .HasColumnType("int")
                        .HasColumnName("MrbImageID");

                    b.Property<DateTime>("MrbImageDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime")
                        .HasDefaultValueSql("(getdate())");

                    b.Property<byte[]>("MrbImageImageAfter")
                        .IsRequired()
                        .HasColumnType("image");

                    b.Property<byte[]>("MrbImageImageBefore")
                        .IsRequired()
                        .HasColumnType("image");

                    b.HasKey("MrbImageId");

                    b.ToTable("MrbImage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.MrbTaskCount", b =>
                {
                    b.Property<int?>("NumberOfTasks")
                        .HasColumnType("int");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.ToView("MrbTaskCount");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.MrbTree", b =>
                {
                    b.Property<int?>("ChildObject1Id")
                        .HasColumnType("int")
                        .HasColumnName("ChildObject1ID");

                    b.Property<string>("ChildObject1Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ChildObject2Id")
                        .HasColumnType("int")
                        .HasColumnName("ChildObject2ID");

                    b.Property<string>("ChildObject2Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ChildObject3Id")
                        .HasColumnType("int")
                        .HasColumnName("ChildObject3ID");

                    b.Property<string>("ChildObject3Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ChildObject4Id")
                        .HasColumnType("int")
                        .HasColumnName("ChildObject4ID");

                    b.Property<string>("ChildObject4Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ChildObjectId")
                        .HasColumnType("int")
                        .HasColumnName("ChildObjectID");

                    b.Property<string>("ChildObjectName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("MrbId")
                        .HasColumnType("int")
                        .HasColumnName("MrbID");

                    b.Property<string>("MrbName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RiskObjId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjID");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("ScenarioID");

                    b.Property<string>("ScenarioName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.ToView("MrbTree");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.NumberOfSiItems", b =>
                {
                    b.Property<int?>("Items")
                        .HasColumnType("int");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int");

                    b.Property<int?>("SiPartOf")
                        .HasColumnType("int");

                    b.Property<string>("UserDefinedShortDescription")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.ToView("NumberOfSiItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Object", b =>
                {
                    b.Property<int>("ObjId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ObjID")
                        .HasComment("Unique ID (PK of Object)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ObjId"), 1L, 1);

                    b.Property<int?>("ObjAvailableTime")
                        .HasColumnType("int")
                        .HasComment("Available time of the object. (Time the object is available for use) (Does not seem to be used by Amprover software)");

                    b.Property<DateTime?>("ObjDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("ObjDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Object description");

                    b.Property<string>("ObjFunction")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Function of the object. Provides a description that explains the functionality of this specific part of the model.");

                    b.Property<byte[]>("ObjImage")
                        .HasColumnType("image")
                        .HasComment(" (does not seem to be used by the AMprover software) (!)");

                    b.Property<int>("ObjLevel")
                        .HasColumnType("int")
                        .HasComment("Level (hierarchy) of the object. The level determines what the object actually is. (a whole system, an assembly, etc.)");

                    b.Property<string>("ObjModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<string>("ObjName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the object");

                    b.Property<decimal?>("ObjNewValue")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Replacement value of the object.");

                    b.Property<int?>("ObjProductionTime")
                        .HasColumnType("int")
                        .HasComment("Production time. (Time the object can be used in production?) (Does not seem to be used by Amprover software)");

                    b.Property<string>("ObjShortKey")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Short key of the object");

                    b.Property<int?>("ObjSiCategory")
                        .HasColumnType("int")
                        .HasComment("Relation to the kind of technical objects. SiCategories are defined in masterdata. (FK to LookupUserDefined) ");

                    b.Property<string>("ObjSiType")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("The type of the related technical objects (?)");

                    b.Property<int?>("ObjUsableTime")
                        .HasColumnType("int")
                        .HasComment("Usable time of the object. (Time the object can be used) (What's the difference with Utilization time ?) (Does not seem to be used by Amprover software)");

                    b.Property<int?>("ObjUtilizationTime")
                        .HasColumnType("int")
                        .HasComment("Utilization time of the object (time the object can be used for its specific function) (?) (Does not seem to be used by Amprover software)");

                    b.HasKey("ObjId");

                    b.ToTable("TblObject");

                    b.HasComment("Stores objects that are used for modelling risks. The object level determines what the object is. The levels and mapping to object type/name are defined in master data, by the items contained within the functional objects treenode. \r\n\r\nObjects define a part of the system we're modelling, and can describe an object, installation, system, assembly or component. The user can name 5 levels of objects. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexData", b =>
                {
                    b.Property<int>("OpexDataId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("OpexDataID")
                        .HasComment("Unique ID (PK of OpexData)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpexDataId"), 1L, 1);

                    b.Property<int?>("OpexDataCostType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("OpexDataDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<int?>("OpexDataInflationGroupId")
                        .HasColumnType("int")
                        .HasColumnName("OpexDataInflationGroupID")
                        .HasComment("ID of the inflation group that is used by the opex data (FK to LookupInflationGroup)");

                    b.Property<int?>("OpexDataMethod")
                        .HasColumnType("int")
                        .HasComment("Opex factor method value which is used for the 'opex data' (FK to Lookup)");

                    b.Property<string>("OpexDataModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<string>("OpexDataName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the 'opex data'");

                    b.Property<decimal?>("OpexDataPercentage")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Precentage used for the 'opex data'");

                    b.Property<decimal?>("OpexDataValue")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Value used for the 'opex data'");

                    b.HasKey("OpexDataId");

                    b.ToTable("OpexData");

                    b.HasComment("Contains data that defines the operating expenditures (opex) caused by specific failure causes. The operating expenditures can be defined in the masterdata, and are used by the LCC calculations. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexFactor", b =>
                {
                    b.Property<int>("OpexFactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("OpexFactID")
                        .HasComment("Unique ID (PK to OpexFactor)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpexFactId"), 1L, 1);

                    b.Property<DateTime?>("OpexFactDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<decimal?>("OpexFactLookupValue")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("? Defined in Masterdata.");

                    b.Property<string>("OpexFactModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<int?>("OpexFactOpexDataId")
                        .HasColumnType("int")
                        .HasColumnName("OpexFactOpexDataID")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<decimal?>("OpexFactValue")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("? Defined in Masterdata.");

                    b.HasKey("OpexFactId");

                    b.HasIndex("OpexFactOpexDataId");

                    b.ToTable("OpexFactor");

                    b.HasComment("Contains factors that are used in operating expenditure calculations. 0 or more of these factors can be defined for each Opex data item, from the masterdata. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexLccDetails", b =>
                {
                    b.Property<int?>("OpexLccDetOpexLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccDetOpexLccID");

                    b.Property<int?>("OpexLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccID");

                    b.ToView("OpexLccDetails");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexToLcc", b =>
                {
                    b.Property<int>("OpexLccId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("OpexLccID")
                        .HasComment("Unique ID (PK to OpexToLCC)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpexLccId"), 1L, 1);

                    b.Property<string>("OpexLccColorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the line color used for the 'opex to lcc' graph");

                    b.Property<int?>("OpexLccCycle")
                        .HasColumnType("int");

                    b.Property<int?>("OpexLccLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccLccID")
                        .HasComment("Lcc ID the 'opex to lcc' belongs to (FK to Lcc)");

                    b.Property<string>("OpexLccName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the 'opex to lcc'");

                    b.Property<int?>("OpexLccOpexDataId1")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID1")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<int?>("OpexLccOpexDataId2")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID2")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<int?>("OpexLccOpexDataId3")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccOpexDataID3")
                        .HasComment("Opex data ID which the 'opex to lcc' uses (FK to OpexData)");

                    b.Property<decimal?>("OpexLccPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Price for the 'opex to lcc' (price per unit/quantity)");

                    b.Property<decimal?>("OpexLccQuantity")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Physical quantity applicable for the 'opex to lcc'");

                    b.Property<int?>("OpexLccSequence")
                        .HasColumnType("int")
                        .HasComment("? Sequence used to keep the order of Opex to LCC items within the grid that is used to display them.");

                    b.Property<bool?>("OpexLccShowInGraph")
                        .HasColumnType("bit")
                        .HasComment("Boolean to show the 'opex to lcc' in the graph (shows up in graph when true)");

                    b.Property<int?>("OpexLccSumItem")
                        .HasColumnType("int")
                        .HasComment("?");

                    b.Property<decimal?>("OpexLccUnits")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Number of units applicable for the 'opex to lcc'");

                    b.HasKey("OpexLccId");

                    b.HasIndex("OpexLccOpexDataId1");

                    b.HasIndex("OpexLccOpexDataId2");

                    b.HasIndex("OpexLccOpexDataId3");

                    b.ToTable("OpexToLCC", (string)null);

                    b.HasComment("Contains operating expenditure (opex) data that is used by life cycle costs (LCC) calculation. These opex items can be found and defined from the LCC module, in the Opex tab (On the bottom half of the screen).");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexToLccDetail", b =>
                {
                    b.Property<int>("OpexLccDetId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("OpexLccDetID")
                        .HasComment("Unique ID (PK of OpexToLCCDetail)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpexLccDetId"), 1L, 1);

                    b.Property<decimal?>("OpexLccDetAec")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("OpexLccDetAEC")
                        .HasComment("Annual equivalent cost for the 'opex to lcc detail'");

                    b.Property<decimal?>("OpexLccDetCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs for this year of this 'opex to lcc detail'");

                    b.Property<int?>("OpexLccDetLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccDetLccID")
                        .HasComment("Lcc ID to which the 'opex to lcc detail' belongs (FK to Lcc)");

                    b.Property<decimal?>("OpexLccDetNpv")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("OpexLccDetNPV")
                        .HasComment("Net present value for the 'opex to lcc detail'");

                    b.Property<int?>("OpexLccDetOpexLccId")
                        .HasColumnType("int")
                        .HasColumnName("OpexLccDetOpexLccID")
                        .HasComment("Opex lcc ID to which the 'opex to lcc detail' belongs (FK to OpexToLcc)");

                    b.Property<decimal?>("OpexLccDetPv")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("OpexLccDetPV")
                        .HasComment("Present value for the 'opex to lcc detail'");

                    b.Property<int?>("OpexLccDetYear")
                        .HasColumnType("int")
                        .HasComment("Year for which the 'opex to lcc detail' contains opex data");

                    b.HasKey("OpexLccDetId");

                    b.ToTable("OpexToLccDetail");

                    b.HasComment("Contains operating expenditure (opex) data that is used to help calculate life cycle costing (LCC) detail costs. Will contain records for each LCC item, for each year of the LCC calculation. This provides a way to see and edit the costs per year of the life cycle of an item defined in the risk analysis. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PageNavigation", b =>
                {
                    b.Property<int>("PageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PageId"), 1L, 1);

                    b.Property<string>("PagePath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageQuery")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("PageId");

                    b.ToTable("TblPageNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PickSi", b =>
                {
                    b.Property<int>("PckSiId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PckSiID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PckSiId"), 1L, 1);

                    b.Property<int?>("PckSiLinkFilterId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiLinkFilterID");

                    b.Property<int>("PckSiMrbId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiMrbID");

                    b.Property<int>("PckSiSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiSiID");

                    b.HasKey("PckSiId")
                        .HasName("PK_PickMSI");

                    b.HasIndex("PckSiLinkFilterId");

                    b.HasIndex("PckSiMrbId");

                    b.HasIndex("PckSiSiId");

                    b.ToTable("TblPickSI");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PickSiFromCommonTask", b =>
                {
                    b.Property<int?>("CmnTaskSiCategory")
                        .HasColumnType("int");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ObjSiCategory")
                        .HasColumnType("int");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int");

                    b.Property<int>("SiId")
                        .HasColumnType("int")
                        .HasColumnName("SiID");

                    b.Property<string>("SiName")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<string>("SiType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int>("TskId")
                        .HasColumnType("int")
                        .HasColumnName("TskID");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.ToView("PickSiFromCommonTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Priority", b =>
                {
                    b.Property<int>("PrioId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PrioID")
                        .HasComment("Unique ID (PK of Priority)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PrioId"), 1L, 1);

                    b.Property<bool?>("PrioAutoGenerated")
                        .HasColumnType("bit")
                        .HasComment("Boolean that display if the priority was auto generated (true if it was generated)");

                    b.Property<DateTime?>("PrioDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("PrioDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("PrioDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the priority, can be used to add any remarks that could clarify the existance of the priority group. ");

                    b.Property<string>("PrioInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user with this username");

                    b.Property<string>("PrioModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<string>("PrioName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name describing the priority group");

                    b.Property<int?>("PrioPartOf")
                        .HasColumnType("int")
                        .HasComment("Priority ID of the parent priority (FK to Priority)");

                    b.Property<string>("PrioPath")
                        .HasMaxLength(250)
                        .IsUnicode(false)
                        .HasColumnType("varchar(250)")
                        .HasComment("Path to display the way the priority is linked to other priorities. Each linked item is an FK to Priority, they'll be separated by a '-' char.");

                    b.Property<string>("PrioRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks that should clarify the reason for the existance of the priority. ");

                    b.Property<string>("PrioResponsible")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("People or departments that are responsible for execution of the priority");

                    b.Property<string>("PrioShortKey")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("Short key of the priority");

                    b.Property<int?>("PrioSiCategory")
                        .HasColumnType("int")
                        .HasComment("Si category ID the chosen significant item belongs to (FK to LookupUserDefined)");

                    b.Property<int?>("PrioSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioSiID")
                        .HasComment("Significant item ID the priority is bound to (FK to Si)");

                    b.Property<string>("PrioStatus")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Priority status, user can enter any string here. (?)");

                    b.HasKey("PrioId");

                    b.HasIndex("PrioPartOf")
                        .HasDatabaseName("IX_PriorityPartOf");

                    b.ToTable("Priority");

                    b.HasComment("Contains priority groups, which are generated from data in PriorityTask and PriorityBudget. A priority group combines groups of tasks that belong together (like tasks that need to be performed in one geographical location). (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityBudget", b =>
                {
                    b.Property<int>("PrioBudId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PrioBudID")
                        .HasComment("Unique ID (PK to PriorityBudget)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PrioBudId"), 1L, 1);

                    b.Property<decimal?>("PrioBudAccepted")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Calculated value for the accepted costs of the priority budget");

                    b.Property<decimal?>("PrioBudBudget")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Budget for priority group (amount) (?)");

                    b.Property<decimal?>("PrioBudBudgetCostYear1")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Priority budget costs for year 1");

                    b.Property<decimal?>("PrioBudBudgetCostYear2")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Priority budget costs for year 2");

                    b.Property<decimal?>("PrioBudBudgetCostYear3")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Priority budget costs for year 3");

                    b.Property<decimal?>("PrioBudBudgetCostYear4")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Priority budget costs for year 4");

                    b.Property<decimal?>("PrioBudBudgetCostYear5")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Priority budget costs for year 5");

                    b.Property<decimal?>("PrioBudBudgetSumOfParts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Calculated value for budget, is built from a sum of all priority item budgets from this priority group. (?) ");

                    b.Property<decimal?>("PrioBudCostSelected")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Calculated value. Selected cost for the priority budget");

                    b.Property<DateTime?>("PrioBudDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("PrioBudDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<bool?>("PrioBudEnableTaskSelect")
                        .HasColumnType("bit")
                        .HasComment("? Boolean that determines if the selected costs are added or not. (?) (true adds them)");

                    b.Property<string>("PrioBudInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user with this username");

                    b.Property<string>("PrioBudModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<int?>("PrioBudOriginalId")
                        .HasColumnType("int")
                        .HasColumnName("PrioBudOriginalID")
                        .HasComment("Refers to itself, to keep track of where the priority budget was originally from (?) (FK to PriorityBudget)");

                    b.Property<decimal?>("PrioBudPostponed")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Calculated value for the postponed costs of the priority budget");

                    b.Property<int?>("PrioBudPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioBudPriorityID")
                        .HasComment("Priority item the priorityBudget refers to (FK to Priority)");

                    b.Property<decimal?>("PrioBudProposed")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Calculated value for the proposed costs of the priority budget");

                    b.Property<decimal?>("PrioBudRejected")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Calculated value for the rejected costs of the priority budget");

                    b.Property<string>("PrioBudRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks that can clarify priority budget");

                    b.Property<decimal?>("PrioBudRisk")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Risk costs for the current year");

                    b.Property<decimal?>("PrioBudRiskDelta")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("? Risk delta for the whole priority period, which is used for... (?) ");

                    b.Property<string>("PrioBudStatus")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Status of the priority budget (under review etc.)");

                    b.Property<bool?>("PrioBudTaskSelectDirty")
                        .HasColumnType("bit")
                        .HasComment("? Boolean that allows a different way of selecting task selection. (the dirty way) ");

                    b.Property<int?>("PrioBudVersion")
                        .HasColumnType("int")
                        .HasComment("Version (year and number) of the priority version which the priority budget is bound to (defined in PriorityVersion, but no FK)");

                    b.HasKey("PrioBudId");

                    b.HasIndex("PrioBudPriorityId", "PrioBudVersion")
                        .IsUnique()
                        .HasDatabaseName("IX_PriorityBudget")
                        .HasFilter("[PrioBudPriorityID] IS NOT NULL AND [PrioBudVersion] IS NOT NULL");

                    b.ToTable("PriorityBudget");

                    b.HasComment("Defines budgets for each item in the prioritygroup. Editable in the priority groups control. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityCost", b =>
                {
                    b.Property<int>("PrioCstVersion")
                        .HasColumnType("int")
                        .HasComment("Version (year and number) of the priority version which the priority cost is bound to (PriorityVersion)");

                    b.Property<int>("PrioCstId")
                        .HasColumnType("int")
                        .HasColumnName("PrioCstID")
                        .HasComment("Unique ID (PK of PriorityCost)");

                    b.Property<decimal>("PrioCstCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("The actual costs for this priority for this year ");

                    b.Property<decimal?>("PrioCstDirectCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("The direct cost is the price that can be completely attributed to the production of specific goods and services. These costs refer to materials, labor and expenses related to the production of a product. ");

                    b.Property<decimal?>("PrioCstFailRate")
                        .HasColumnType("decimal(18,6)")
                        .HasComment("Failure rate is the frequency with which an engineered system or component fails, expressed in failures per hour. (?)");

                    b.Property<int>("PrioCstNumberOfTasks")
                        .HasColumnType("int")
                        .HasComment("Number of tasks that are bound to this priority cost item");

                    b.Property<int>("PrioCstPrioId")
                        .HasColumnType("int")
                        .HasColumnName("PrioCstPrioID")
                        .HasComment("ID of the priority the priority cost is bound to (FK to Priority)");

                    b.Property<int?>("PrioCstPrioTskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioCstPrioTskID")
                        .HasComment("ID of the priority task the priority cost is bound to (FK to PriorityTask)");

                    b.Property<decimal?>("PrioCstRiskCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("The projected risk costs for this priority for this year (?)");

                    b.Property<decimal?>("PrioCstRiskDelta")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("?");

                    b.Property<int>("PrioCstYear")
                        .HasColumnType("int")
                        .HasComment("The year the priority costs have been calculated for. (?)");

                    b.HasKey("PrioCstVersion", "PrioCstId");

                    b.ToTable("PriorityCost");

                    b.HasComment("Defines the costs for each prioritygroup, for each year. (?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityGridData", b =>
                {
                    b.Property<decimal?>("PrioBudAccepted")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioBudBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear1")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear2")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear3")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear4")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioBudBudgetCostYear5")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioBudBudgetSumOfParts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioBudCostSelected")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("PrioBudDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioBudDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<bool?>("PrioBudEnableTaskSelect")
                        .HasColumnType("bit");

                    b.Property<int>("PrioBudId")
                        .HasColumnType("int")
                        .HasColumnName("PrioBudID");

                    b.Property<string>("PrioBudInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<string>("PrioBudModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int?>("PrioBudOriginalId")
                        .HasColumnType("int")
                        .HasColumnName("PrioBudOriginalID");

                    b.Property<decimal?>("PrioBudPostponed")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioBudPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioBudPriorityID");

                    b.Property<decimal?>("PrioBudProposed")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioBudRejected")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PrioBudRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("PrioBudStatus")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<bool?>("PrioBudTaskSelectDirty")
                        .HasColumnType("bit");

                    b.Property<int?>("PrioBudVersion")
                        .HasColumnType("int");

                    b.Property<int>("PrioId")
                        .HasColumnType("int")
                        .HasColumnName("PrioID");

                    b.Property<string>("PrioName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioPartOf")
                        .HasColumnType("int");

                    b.Property<int?>("PrioSiCategory")
                        .HasColumnType("int");

                    b.Property<int?>("PrioSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioSiID");

                    b.Property<string>("PrioStatus")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.ToView("PriorityGridData");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityTask", b =>
                {
                    b.Property<int>("PrioTskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskID")
                        .HasComment("Unique ID (PK of PriorityTask)");

                    b.Property<bool?>("PrioTskAutoSelected")
                        .HasColumnType("bit")
                        .HasComment("? Boolean that determines if a task will be auto-selected for postponement. Set to true for auto-select. ");

                    b.Property<decimal?>("PrioTskBudgetCostYear1")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Priority task costs for year 1");

                    b.Property<decimal?>("PrioTskBudgetCostYear2")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Priority task costs for year 2");

                    b.Property<decimal?>("PrioTskBudgetCostYear3")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Priority task costs for year 3");

                    b.Property<decimal?>("PrioTskBudgetCostYear4")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Priority task costs for year 4");

                    b.Property<decimal?>("PrioTskBudgetCostYear5")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Priority task costs for year 5");

                    b.Property<decimal?>("PrioTskClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cluster cost per unit of the priority task. Retrieved from CltpCLusterCostPerUnit or CmnTaskCosts.");

                    b.Property<int?>("PrioTskClusterPlanId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskClusterPlanID")
                        .HasComment("ID of the cluster plan the priority task is bound to (FK to ClusterTaskPlan)");

                    b.Property<int?>("PrioTskCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskCommonTaskID")
                        .HasComment("ID of the common action the priority task is bound to (FK to CommonTask)");

                    b.Property<decimal?>("PrioTskCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Original cost for the priority task as calculated by the clusterTaskPlan");

                    b.Property<DateTime?>("PrioTskDateDue")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date when the priority task is due (deadline for task completion)");

                    b.Property<DateTime?>("PrioTskDateExecuted")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date when the priority task was executed");

                    b.Property<DateTime?>("PrioTskDateGenerated")
                        .HasColumnType("smalldatetime")
                        .HasComment("The priority task was generated on this date and time.");

                    b.Property<string>("PrioTskDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the priority task");

                    b.Property<decimal?>("PrioTskDirectCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("? Direct costs associated with this priority task. (costs for materials, man hours, etc) Does not seem to be used in the AMprover software (!) ");

                    b.Property<decimal?>("PrioTskDownTime")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Downtime needed to complete task (in hours)");

                    b.Property<decimal?>("PrioTskDuration")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Time spent on task (in hours)");

                    b.Property<int?>("PrioTskExecuteStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the priority task (postponed, proposed etc.)");

                    b.Property<int?>("PrioTskExecutionYear")
                        .HasColumnType("int")
                        .HasComment("We need to keep track of modifications in the execution date. The year will be used when the execution is modified. (?)");

                    b.Property<string>("PrioTskFromReference")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("This is a composite field, containing several items, delimited by the '-' char. The field contains the following items:\r\nDate due, RiskID, PrioTaskID, SiID, TaskType, and TaskID.\r\n\r\nThe field will be used for importing corrective tasks based on an inspection. ");

                    b.Property<bool?>("PrioTskImported")
                        .HasColumnType("bit")
                        .HasComment("Boolean that sets the imported status for the priority task (Does not seem to be used in the AMprover software) (!)");

                    b.Property<decimal?>("PrioTskIntervalPerYear")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Number of times the task is performed per year");

                    b.Property<decimal?>("PrioTskNumberOfTimes")
                        .HasColumnType("decimal(18,3)")
                        .HasComment("? The amount of times a priority task will be executed. Based on the interval per year and the interval count.");

                    b.Property<int?>("PrioTskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskObjectID")
                        .HasComment("ID of the object the priority task is bound to (FK to Object)");

                    b.Property<int?>("PrioTskOriginalId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskOriginalID")
                        .HasComment("Task ID that is used to keep track of which task the priotask was originally created for. Seems to only be filled with the PrioTskID, is this still needed? (?)");

                    b.Property<decimal?>("PrioTskPostponePct")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("? Used in year cost calculation, as inflation correction factor.  ");

                    b.Property<decimal?>("PrioTskPriorityCode")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("? A code that defines the inspection priority. Defaults to 1. Value can be 1-100. (?)");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskPriorityID")
                        .HasComment("ID of the priority the priority task is bound to (FK to Priority)");

                    b.Property<int?>("PrioTskQualityScore")
                        .HasColumnType("int")
                        .HasComment("Business value of the bound significant item. Contains the quality score of the linked significant item. (if that's present)");

                    b.Property<string>("PrioTskReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("PrioTskReferenceID")
                        .HasComment("This is a composite field, containing several items, delimited by the '-' char. The field contains the following items:\r\nDate due, RiskID, PrioTaskID, SiID, TaskType, and TaskID.\r\n\r\nThe field will be used for importing corrective tasks based on an inspection. ");

                    b.Property<string>("PrioTskRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the priority task");

                    b.Property<decimal?>("PrioTskRiskBudget")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("The risk budget is defined as the costs per task. It can be calculated in different ways: By dividing the PrioTskRiskCost (or MrbRiskAfter) by the number of tasks, by looking at failrate type, failrate, age and effect after (and dividing that by number of tasks).");

                    b.Property<decimal?>("PrioTskRiskCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("? Filled with value of Mrb Risk after");

                    b.Property<decimal?>("PrioTskRiskDelta")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Calculated value that is used for determining priority of tasks. Calculated by subtracting the PrioTskRiskbudget from the MrbRiskbefore, and dividing that by number of tasks. The delta is the difference between the \"normal\" risk, and the risk after prioritizing tasks. (?)");

                    b.Property<decimal?>("PrioTskRiskFactor")
                        .HasColumnType("decimal(18,3)")
                        .HasComment("? A variable used in risk calculations. Determined by looking at failure type, fail rate, age and mtbf.");

                    b.Property<int?>("PrioTskRiskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskRiskID")
                        .HasComment("ID of the risk the priority task is bound to (FK to MRB)");

                    b.Property<bool?>("PrioTskSealed")
                        .HasColumnType("bit")
                        .HasComment("Boolean that seals the priority task. When this is true, the priority task is sealed. (which means it is no longer editable) ");

                    b.Property<int?>("PrioTskSelectionSeq")
                        .HasColumnType("int")
                        .HasComment("? Determines the selection sequence. A lower number will be processed sooner. ");

                    b.Property<int?>("PrioTskSequence")
                        .HasColumnType("int")
                        .HasComment("A calculation that uses fields: PrioTskRiskFactor, PrioTskQualityScore, PrioTskPriorityCode to determine what prioritytask should be executed first. (?)");

                    b.Property<string>("PrioTskSiDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Description of the bound significant item, is filled with the Si name");

                    b.Property<int?>("PrioTskSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskSiID")
                        .HasComment("ID of the significant item the priority task is bound to (FK to Si)");

                    b.Property<decimal?>("PrioTskSiUnits")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Number of significant item units for this priority task. Filled with value of CltpSiUnits.");

                    b.Property<int?>("PrioTskSlack")
                        .HasColumnType("int")
                        .HasComment("Slack for the priority task, which is a period of time a task can be postponed. Slack is measured in years. ");

                    b.Property<int?>("PrioTskSlackIntervalType")
                        .HasColumnType("int")
                        .HasComment("Interval unit ID of the slack for the priority task (FK to LookupIntervalUnit) The value is taken from CltpSlackIntervalType.");

                    b.Property<int?>("PrioTskTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskTaskID")
                        .HasComment("ID of the task the priority task is bound to (FK to Task)");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int")
                        .HasComment("Version (year and number) of the priority task (versions need to be present in PriorityVersion)");

                    b.HasKey("PrioTskId");

                    b.HasIndex("PrioTskPriorityId")
                        .HasDatabaseName("IX_PriorityTaskPriorityID");

                    b.HasIndex("PrioTskVersion")
                        .HasDatabaseName("IX_PriorityTaskVersion");

                    b.ToTable("PriorityTask");

                    b.HasComment("Contains the tasks that have been prioritized. This data is generated from the priority box command center. Data is gathered from Mrb, RIskObject, Task, and Priority.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityTaskCost", b =>
                {
                    b.Property<int?>("PrioTskExecuteStatus")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskPriorityID");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int");

                    b.Property<decimal?>("RiskBudget")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("RiskCost")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("RiskDelta")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("TaskCost")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("Year1")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("Year2")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("Year3")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("Year4")
                        .HasColumnType("decimal(38,2)");

                    b.Property<decimal?>("Year5")
                        .HasColumnType("decimal(38,2)");

                    b.ToView("PriorityTaskCost");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityTaskGridData", b =>
                {
                    b.Property<int?>("FailRateId")
                        .HasColumnType("int")
                        .HasColumnName("FailRateID");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomEffectAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomEffectBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("MrbName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbRiskBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("NumberOfTasks")
                        .HasColumnType("int");

                    b.Property<string>("PrioPath")
                        .HasMaxLength(250)
                        .IsUnicode(false)
                        .HasColumnType("varchar(250)");

                    b.Property<bool?>("PrioTskAutoSelected")
                        .HasColumnType("bit");

                    b.Property<decimal?>("PrioTskBudgetCostYear1")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear2")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear3")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear4")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskBudgetCostYear5")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskClusterPlanId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskClusterPlanID");

                    b.Property<int?>("PrioTskCommonTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskCommonTaskID");

                    b.Property<decimal?>("PrioTskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("PrioTskDateDue")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioTskDateExecuted")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("PrioTskDateGenerated")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("PrioTskDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("PrioTskDirectCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskDownTime")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskDuration")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskExecuteStatus")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskExecutionYear")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskFromReference")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("PrioTskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskID");

                    b.Property<bool?>("PrioTskImported")
                        .HasColumnType("bit");

                    b.Property<decimal?>("PrioTskIntervalPerYear")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("PrioTskNumberOfTimes")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("PrioTskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskObjectID");

                    b.Property<int?>("PrioTskOriginalId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskOriginalID");

                    b.Property<decimal?>("PrioTskPostponePct")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskPriorityCode")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskPriorityId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskPriorityID");

                    b.Property<int?>("PrioTskQualityScore")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("PrioTskReferenceID");

                    b.Property<string>("PrioTskRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("PrioTskRiskBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskRiskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskRiskDelta")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PrioTskRiskFactor")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("PrioTskRiskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskRiskID");

                    b.Property<bool?>("PrioTskSealed")
                        .HasColumnType("bit");

                    b.Property<int?>("PrioTskSelectionSeq")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskSequence")
                        .HasColumnType("int");

                    b.Property<string>("PrioTskSiDescription")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("PrioTskSiId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskSiID");

                    b.Property<decimal?>("PrioTskSiUnits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PrioTskSlack")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskSlackIntervalType")
                        .HasColumnType("int");

                    b.Property<int?>("PrioTskTaskId")
                        .HasColumnType("int")
                        .HasColumnName("PrioTskTaskID");

                    b.Property<int?>("PrioTskVersion")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SiContractEnd")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("SiMtbf")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("SiQualityScore")
                        .HasColumnType("int");

                    b.Property<decimal?>("SiRiskAfterCustom")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("SiRiskAfterValue")
                        .HasColumnType("decimal(18,0)");

                    b.Property<decimal?>("SiRiskBeforeCustom")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("SiRiskBeforeValue")
                        .HasColumnType("decimal(18,0)");

                    b.Property<int?>("SiRiskBvId")
                        .HasColumnType("int")
                        .HasColumnName("SiRiskBvID");

                    b.Property<decimal?>("SiRiskMtbfAfter")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("SiRiskMtbfBefore")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime?>("SiYear")
                        .HasColumnType("datetime");

                    b.Property<string>("TskType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("TskUnitType")
                        .HasColumnType("int");

                    b.ToView("PriorityTaskGridData");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PriorityVersion", b =>
                {
                    b.Property<int>("PrioVerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("PrioVerID")
                        .HasComment("Unique ID (PK of PriorityVersion)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PrioVerId"), 1L, 1);

                    b.Property<string>("PrioVerDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the priority version");

                    b.Property<string>("PrioVerName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the priority version");

                    b.Property<string>("PrioVerRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the priority version");

                    b.Property<int?>("PrioVerScenario")
                        .HasColumnType("int")
                        .HasComment("Scenario the priority version was created for.");

                    b.Property<bool?>("PrioVerSealed")
                        .HasColumnType("bit")
                        .HasComment("Boolean to seal the priority version. Setting this to true seals it. ");

                    b.Property<int?>("PrioVerVersion")
                        .HasColumnType("int")
                        .HasComment("Version (year and number) of the priority version");

                    b.HasKey("PrioVerId");

                    b.ToTable("PriorityVersion");

                    b.HasComment("Each priority calculation is created around a PriorityVersion. Defining a priority version allows the user to generate different priority groups, which enables the user to see the differences in the resulting risks and costs between several priority configurations. \r\n\r\nThe priority version is created from the priority box command center. A Priority version can be created for each scenario.\r\n(?)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PrioTaskCollection", b =>
                {
                    b.Property<decimal?>("CltpClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("CltpClusterCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("CltpClusterId")
                        .HasColumnType("int")
                        .HasColumnName("CltpClusterID");

                    b.Property<DateTime?>("CltpDateExecuted")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CltpDateGenerated")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("CltpDownTime")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("CltpDuration")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("CltpExecuteStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CltpExecutionDate")
                        .HasColumnType("datetime");

                    b.Property<int>("CltpId")
                        .HasColumnType("int")
                        .HasColumnName("CltpID");

                    b.Property<bool?>("CltpInterruptable")
                        .HasColumnType("bit");

                    b.Property<int?>("CltpObjectId")
                        .HasColumnType("int")
                        .HasColumnName("CltpObjectID");

                    b.Property<int?>("CltpPriority")
                        .HasColumnType("int");

                    b.Property<int?>("CltpQualityScore")
                        .HasColumnType("int");

                    b.Property<string>("CltpReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("CltpReferenceID");

                    b.Property<string>("CltpRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("CltpRiskId")
                        .HasColumnType("int")
                        .HasColumnName("CltpRiskID");

                    b.Property<int?>("CltpSequence")
                        .HasColumnType("int");

                    b.Property<int?>("CltpShiftEndDate")
                        .HasColumnType("int");

                    b.Property<int?>("CltpShiftStartDate")
                        .HasColumnType("int");

                    b.Property<int?>("CltpSiId")
                        .HasColumnType("int")
                        .HasColumnName("CltpSiID");

                    b.Property<decimal?>("CltpSiUnits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("CltpSlack")
                        .HasColumnType("int");

                    b.Property<int?>("CltpSlackIntervalType")
                        .HasColumnType("int");

                    b.Property<int?>("CltpTaskId")
                        .HasColumnType("int")
                        .HasColumnName("CltpTaskID");

                    b.Property<decimal?>("CltpToolCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool?>("CltpUseLastDateExecuted")
                        .HasColumnType("bit");

                    b.Property<decimal?>("IntUnitCalculationKey")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("MrbCustomAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbCustomBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbEffectAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbEffectBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbRiskAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MrbRiskBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("SiCategory")
                        .HasColumnType("int");

                    b.Property<string>("SiName")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("SiPartOf")
                        .HasColumnType("int");

                    b.Property<int?>("SiQualityScore")
                        .HasColumnType("int");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnType("int")
                        .HasColumnName("TskCommonActionID");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("TskName")
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<string>("TskType")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.ToView("PrioTaskCollection");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Rams", b =>
                {
                    b.Property<int>("RamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramID")
                        .HasComment("ID of the RAMS diagram this RAMS block belongs to (FK to RamsDiagram)");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID")
                        .HasComment("Unique ID (PK of Rams)");

                    b.Property<double?>("RamsAvailabilityInput")
                        .HasColumnType("float")
                        .HasComment("Availability of the input of the RAMS block. The value lies between 0 and 1.");

                    b.Property<double?>("RamsAvailabilityOutput")
                        .HasColumnType("float")
                        .HasComment("Availability of the output of the RAMS block. The value lies between 0 and 1.");

                    b.Property<double?>("RamsBeta")
                        .HasColumnType("float")
                        .HasComment("Weibull factor for this RAMS block. In reliability analysis, the weibull factor is used to clarify age-to-failure data. The weibull factor shows if the maintenance strategy should be better aimed at preventive or corrective maintenance. The value lies between 0 and 1.");

                    b.Property<byte[]>("RamsBitmap")
                        .HasColumnType("image")
                        .HasComment("Picture of the RAMS block. Was added to allow the user to store a picture of the physical structure the RAMS block represents. (Does not seem to be used in AMprover software) (!)");

                    b.Property<double?>("RamsBufferTime")
                        .HasColumnType("float");

                    b.Property<decimal?>("RamsCharacteristicLife")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("The characteristic life (Ƞ) is the moment where 63.2% of the units will fail");

                    b.Property<decimal?>("RamsCircuitDepCorrCost")
                        .HasColumnType("decimal(18,0)")
                        .HasComment("? Production and corrective costs dependent on the MTBF of this RAMS block. In RAMS containers, this cost will be determined by the sum of the underlying blocks.");

                    b.Property<string>("RamsClassDc")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)")
                        .HasColumnName("RamsClassDC")
                        .HasComment("Diagnostic Coverage (DC) class of the RAMS block. This class determines the level of detection of dangerous situations, and thus describes if a danger to this RAMS block would be detected. (dangerous detected, dangers undetected, etc.) ");

                    b.Property<bool?>("RamsCollapsed")
                        .HasColumnType("bit")
                        .HasComment("Boolean that collapses the RAMS blocks within the RAMS block to one block. This is used to keep track of the state of each of the RAMS blocks, so the user will see the same configuration when opening the same diagram.");

                    b.Property<bool?>("RamsCompleted")
                        .HasColumnType("bit")
                        .HasComment("Boolean that set the analysis of the block to complete. The user decides when a RAMS block is complete, and can set that using a checkbox.");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit")
                        .HasComment("Boolean that makes the RAMS block behave as a container. When creating a RAMS block with the \"add a new container\" toolstrip button, this boolean will be set.");

                    b.Property<bool?>("RamsCostLinked")
                        .HasColumnType("bit")
                        .HasComment("Boolean that, when set, lets RAMS blocks retrieve their costs from linked FMECA object. Can only be set when the block is linked to FMECA, and the cost owner bit is set. ");

                    b.Property<bool?>("RamsCostOwner")
                        .HasColumnType("bit")
                        .HasComment("The RamsCostOwner is a bit that is set for containers that will then be the owner of the cost. When this is set, input of costs to blocks belonging to that container will be blocked.");

                    b.Property<DateTime?>("RamsDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("RamsDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<double?>("RamsDcd")
                        .HasColumnType("float")
                        .HasColumnName("RamsDCd")
                        .HasComment("Diagnostic coverage of dangerous failures (DCD). This is a percentage for this RAMS block, that describes how many of the possibly dangerous failures that can occur would be detected. The value lies between 0 and 1. (?)");

                    b.Property<string>("RamsDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the RAMS block");

                    b.Property<int?>("RamsDiagramRefId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramRefID")
                        .HasComment("When a rams diagram is linked to another rams diagram, that reference will be stored here. When this reference is present, the rams block will not allow the user to alter any of the values used in calculations. ");

                    b.Property<int?>("RamsDistributionType")
                        .HasColumnType("int")
                        .HasComment("The distribution type specific to this RAMS block");

                    b.Property<double?>("RamsEcoFunct")
                        .HasColumnType("float")
                        .HasComment("Functional eco score of the RAMS block.  Eco score is a measurement of effect to the environment, and is measured with a score from 0 to 100. (closer to 100 means more environmentally friendly)");

                    b.Property<double?>("RamsEcoTechn")
                        .HasColumnType("float")
                        .HasComment("Technical eco score of the RAMS block. Eco score is a measurement of effect to the environment, and is measured with a score from 0 to 100. (closer to 100 means more environmentally friendly).");

                    b.Property<decimal?>("RamsFailCorrCost")
                        .HasColumnType("decimal(18,0)")
                        .HasComment("Costs of the corrective measures taken after failure of this RAMS block.");

                    b.Property<int?>("RamsFailureMode")
                        .HasColumnType("int")
                        .HasComment("ID of the failure mode used for the RAMS block (FK to LookupFailMode)");

                    b.Property<string>("RamsFunctionalDemand")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("? (Does not seem to be used by the AMprover software) Functional demand is defined as a demand for specific functionality, such as accessiblity and fire safety.");

                    b.Property<int?>("RamsHft")
                        .HasColumnType("int")
                        .HasColumnName("RamsHFT");

                    b.Property<bool?>("RamsIdentical")
                        .HasColumnType("bit")
                        .HasComment("Boolean that allows the software to set the properties of all the blocks within the RAMS block to values identical to the first RAMS block. This user can set this during design. ");

                    b.Property<string>("RamsInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user with this username");

                    b.Property<double?>("RamsLabdaFunctional")
                        .HasColumnType("float");

                    b.Property<double?>("RamsLabdaTechnical")
                        .HasColumnType("float");

                    b.Property<bool>("RamsLccusePfd")
                        .HasColumnType("bit")
                        .HasColumnName("RamsLCCUsePFD");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int")
                        .HasComment("RAMS link method determines how the RAMS is linked to Mrb items. (Rams link methods are defined in Lookup, though it's the enum value that gets stored.) (?)");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int")
                        .HasComment("Linktype shows what the RAMS block is linked to (RAMS, RiskObject, Object, Risk, or Notset). This value determines how a RAMS block will be processed during LCC calculations.  ");

                    b.Property<int>("RamsLinkedLeft")
                        .HasColumnType("int")
                        .HasComment("ID of the RAMS block on the left side of the RAMS block (FK to Rams). Is set to -1 when there is no RAMS block to the left. ");

                    b.Property<int>("RamsLinkedRight")
                        .HasColumnType("int")
                        .HasComment("ID of the RAMS block on the right side of the RAMS block (FK to Rams). Is set to -1 when there is no RAMS block to the right. ");

                    b.Property<string>("RamsModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<double?>("RamsMtbfFuncCalced")
                        .HasColumnType("float");

                    b.Property<double?>("RamsMtbfTecCalced")
                        .HasColumnType("float");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct")
                        .HasComment("Functional mean time between failure (MTBF) for the RAMS block");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn")
                        .HasComment("Technical mean time between failure (MTBF) for this RAMS block. ");

                    b.Property<double?>("RamsMttr")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTTR")
                        .HasComment("Mean time to repair (MTTR) for this RAMS block. (The time it takes to repair the block when a failure occurs)");

                    b.Property<string>("RamsName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the RAMS block");

                    b.Property<int?>("RamsObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsObjectID")
                        .HasComment("ID of the object the RAMS block belongs to (FK to Object)");

                    b.Property<int?>("RamsParallelBlocks")
                        .HasColumnType("int")
                        .HasComment("Number of parallel RAMS blocks in this RAMS block. Will only be visible for containers, and is set automatically during design of RAMS diagram.");

                    b.Property<int>("RamsPartOf")
                        .HasColumnType("int")
                        .HasComment("RAMS ID of the parent RAMS block (FK to Rams)");

                    b.Property<double?>("RamsPfd")
                        .HasColumnType("float")
                        .HasColumnName("RamsPFD")
                        .HasComment("Probability of failure on demand (PFD) for this RAMS block, which is the probability this RAMS block will fail when it is actually used. This value can be used to determine the Safety Integrity Level (SIL) ");

                    b.Property<decimal?>("RamsPreventiveCost")
                        .HasColumnType("decimal(18,0)")
                        .HasComment("Total costs of preventive measures taken for this RAMS block");

                    b.Property<double?>("RamsProductivityFunct")
                        .HasColumnType("float")
                        .HasComment("Functional productivity (time) of this RAMS block");

                    b.Property<double?>("RamsProductivityTechn")
                        .HasColumnType("float")
                        .HasComment("Technical productivity (time) of this RAMS block");

                    b.Property<int?>("RamsReadSequence")
                        .HasColumnType("int")
                        .HasComment("? The read sequence is used to rebuild a nested diagram in the correct order. The field is set automatically by methods DiagramToRows and DoDiagramToRows (in SyncRamsData).");

                    b.Property<double?>("RamsReliabilityFunctional")
                        .HasColumnType("float");

                    b.Property<double?>("RamsReliabilityTechnical")
                        .HasColumnType("float");

                    b.Property<string>("RamsRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks for this RAMS block");

                    b.Property<int?>("RamsRiskId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskID")
                        .HasComment("ID of the risk the RAMS block belongs to (FK to MRB)");

                    b.Property<int?>("RamsRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RamsRiskObjectID")
                        .HasComment("ID of the risk object the RAMS block belongs to (FK to RiskObject)");

                    b.Property<decimal?>("RamsSff")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("RamsSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsSiID")
                        .HasComment("ID of the significant item the RAMS block belongs to (FK to Si)");

                    b.Property<string>("RamsSil")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)");

                    b.Property<string>("RamsSilAc")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)")
                        .HasColumnName("RamsSilAC");

                    b.Property<int?>("RamsStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the RAMS block. The possible statuses are defined in Lookup. ");

                    b.Property<decimal?>("RamsTechnCorrCost")
                        .HasColumnType("decimal(18,0)")
                        .HasComment("Total costs of technical corrective measures taken for this RAMS block (?)");

                    b.Property<int?>("RamsTestInterval")
                        .HasColumnType("int");

                    b.Property<decimal?>("RamsTotalCost")
                        .HasColumnType("decimal(18,0)")
                        .HasComment("Total costs for this RAMS block");

                    b.Property<string>("RamsType")
                        .HasMaxLength(6)
                        .IsUnicode(false)
                        .HasColumnType("varchar(6)");

                    b.Property<double?>("RamsUtilizationFunct")
                        .HasColumnType("float")
                        .HasComment("Functional utilization (time) of this RAMS block. Asset utilization represents the total revenue earned for every dollar of assets a company owns. (?)");

                    b.Property<double?>("RamsUtilizationTechn")
                        .HasColumnType("float")
                        .HasComment("Technical utilization (time) of this RAMS block. Asset utilization represents the total revenue earned for every dollar of assets a company owns.  (?)");

                    b.Property<bool?>("RamsWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsWantLCC")
                        .HasComment("Boolean that makes the RAMS block available for LCC calculations. When a RAMS block is defined as a container, this value will be set. The user can also enable this by setting the checkbox.");

                    b.Property<decimal?>("RamsWeibullShape")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("The Weibull shape (β) defines the slope of the current location on the Weibull curve");

                    b.Property<int?>("RamsXooN")
                        .HasColumnType("int")
                        .HasComment("RAMS XooN is a number that shows how many RAMS blocks are contained within this RAMS block. Is only set to anything other than 1 for containers. (?)");

                    b.Property<int?>("RamsXposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsXPosition")
                        .HasComment("X-position of the RAMS block in the RAMS diagram. Used to more easily rebuild the RAMS blocks in the correct relative order.");

                    b.Property<int?>("RamsYear")
                        .HasColumnType("int")
                        .HasComment("? Year the object the RAMS block represents was first used.");

                    b.Property<int?>("RamsYposition")
                        .HasColumnType("int")
                        .HasColumnName("RamsYPosition")
                        .HasComment("Y-position of the RAMS block in the RAMS diagram. Used to more easily rebuild the RAMS blocks in the correct relative order.");

                    b.HasKey("RamsDiagramId", "RamsId")
                        .HasName("PK_RAMS");

                    b.HasIndex("RamsSiId");

                    b.ToTable("Rams");

                    b.HasComment("This table defines RAMS objects. RAMS is a set of methods which are used to visualize the performance of a system, looking specifically at the system Reliability, Availability, Maintainability and Safety. (RAMS) The RAMS objects are user defined from the RAMS controls.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RamsDiagram", b =>
                {
                    b.Property<int>("RamsDgId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("RamsDgID")
                        .HasComment("Unique ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RamsDgId"), 1L, 1);

                    b.Property<double>("RamsDgAvailableTime")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("float")
                        .HasDefaultValueSql("((8760))");

                    b.Property<bool>("RamsDgCalculateAvailability")
                        .HasColumnType("bit");

                    b.Property<bool>("RamsDgCalculationCompatibilityMode")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("RamsDgDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("RamsDgDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("RamsDgDescr")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the RAMS diagram");

                    b.Property<int?>("RamsDgHorizon")
                        .HasColumnType("int")
                        .HasComment("Period applied to the RAMS diagram. (?)");

                    b.Property<string>("RamsDgInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user");

                    b.Property<string>("RamsDgModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<string>("RamsDgName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the RAMS diagram");

                    b.Property<string>("RamsDgPageBreaks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal>("RamsDgPeriodFrom")
                        .HasColumnType("decimal(18,10)");

                    b.Property<decimal>("RamsDgPeriodTo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,10)")
                        .HasDefaultValueSql("((1))");

                    b.Property<string>("RamsDgPrerequisites")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("RamsDgReferenceId")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("RamsDgReferenceID")
                        .HasComment("Reference ID of the RAMS diagram (not bound and used) (!)");

                    b.Property<string>("RamsDgRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the RAMS diagram");

                    b.Property<int?>("RamsDgRiskObject")
                        .HasColumnType("int")
                        .HasComment("ID of the risk object linked to the RAMS diagram (FK to RiskObject)");

                    b.Property<int>("RamsDgScenId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgScenID")
                        .HasComment("ID of the scenario the RAMS diagram belongs to (FK to Scenario)");

                    b.Property<string>("RamsDgSerialized")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RamsDgSiId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDgSiID")
                        .HasComment("ID of the significant item bound to the RAMS diagram (FK to Si)");

                    b.Property<string>("RamsDgSiRefId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("RamsDgSiRefID")
                        .HasComment("Reference ID of the significant item bound to the RAMS diagram. (Does not seem to be in use in the AMprover software) (!)");

                    b.Property<int?>("RamsDgStatus")
                        .HasColumnType("int")
                        .HasComment("Rams status value of the RAMS diagram (FK to Lookup) (Does not seem to be used in AMprover software) (!)");

                    b.Property<int?>("RamsDgTestInterval")
                        .HasColumnType("int")
                        .HasComment("Test interval for the RAMS diagram. ");

                    b.Property<bool?>("RamsDgWantLcc")
                        .HasColumnType("bit")
                        .HasColumnName("RamsDgWantLCC")
                        .HasComment("Boolean that makes the RAMS diagram available for LCC calculations");

                    b.HasKey("RamsDgId")
                        .HasName("PK_RamsDetail_1");

                    b.ToTable("RamsDiagram");

                    b.HasComment("Stores the graphical representation of the RAMS analysis. Some specific properties can be set through the controls of the properties tab within the RAMS analysis.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RiskObject", b =>
                {
                    b.Property<int>("RiskObjId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("RiskObjID")
                        .HasComment("Unique ID (PK to RiskObject)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RiskObjId"), 1L, 1);

                    b.Property<string>("RiskObjAbmstate")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("RiskObjABMstate")
                        .HasComment("State of the risk object (master, revision etc.) (? what is ABM in this context?)");

                    b.Property<string>("RiskObjAmrbattended")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("RiskObjAMRBattended")
                        .HasComment("People who have attended the risk analysis session. (Does not seem to be used by AMprover software) (!)");

                    b.Property<string>("RiskObjAnalyseType")
                        .HasMaxLength(10)
                        .IsUnicode(false)
                        .HasColumnType("varchar(10)")
                        .HasComment("Analysis type of the risk object. The analysis type is defined from the masterdata, though the string value is stored here. (Lookup)");

                    b.Property<int?>("RiskObjCopiedFrom")
                        .HasColumnType("int")
                        .HasComment("When a risk object was copied from another risk object, this field contains the ID of the source risk object (master risk object) (FK to RiskObject)");

                    b.Property<DateTime?>("RiskObjDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<int>("RiskObjFmecaId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjFmecaID")
                        .HasComment("ID of the FMECA matrix used for this risk object. (FK to Fmeca)");

                    b.Property<byte[]>("RiskObjFuncDecomp")
                        .HasColumnType("image")
                        .HasComment("Picture of the functional decomposition of the risk object");

                    b.Property<int?>("RiskObjLccyear")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjLCCYear")
                        .HasComment("Optimal lifetime of the risk object (not used)");

                    b.Property<string>("RiskObjModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<string>("RiskObjMrbstartPoint")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("RiskObjMRBstartPoint")
                        .HasComment("Start point of the risk object. (Does not seem to be used in the AMprover software) (!) (?)");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the risk object");

                    b.Property<int?>("RiskObjNoOfInstallation")
                        .HasColumnType("int")
                        .HasComment("Number of objects contained within the risk object. (? Or the number of installations of its kind?) Used in LCC calculations.");

                    b.Property<int>("RiskObjObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjObjectID")
                        .HasComment("ID of the object, which is a level 1 risk object (FK to Object) (?)");

                    b.Property<int?>("RiskObjParentObjectId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjParentObjectID")
                        .HasComment("ID of the parent object, which is a level 0 risk object (FK to Object)");

                    b.Property<decimal?>("RiskObjProdCostHour")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Production costs (per hour) for this risk object");

                    b.Property<string>("RiskObjResponsible")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Person or department that are responsible for this risk object");

                    b.Property<decimal?>("RiskObjRforSpares")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,4)")
                        .HasDefaultValueSql("((0.95))")
                        .HasComment("Given reliability for the spare parts");

                    b.Property<int>("RiskObjScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjScenarioID")
                        .HasComment("ID of the scenario that contains the risk object (FK to Scenario)");

                    b.Property<int?>("RiskObjStatus")
                        .HasColumnType("int");

                    b.Property<int?>("RiskObjVolgNo")
                        .HasColumnType("int")
                        .HasComment("Serial number of the risk object. (Does not seem to be used by the AMprover software) (!)");

                    b.HasKey("RiskObjId");

                    b.HasIndex("RiskObjFmecaId");

                    b.HasIndex("RiskObjObjectId");

                    b.HasIndex("RiskObjParentObjectId");

                    b.HasIndex("RiskObjScenarioId");

                    b.ToTable("TblRiskObject");

                    b.HasComment("Stores riskobjects, which are defined within the risk analysis. A risk object is an asset, of which we want to know the risks that are present during normal usage. A risk object contains risks, or systems. (?)   ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Scenario", b =>
                {
                    b.Property<int>("ScenId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("ScenID")
                        .HasComment("Unique ID (PK of Scenario)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ScenId"), 1L, 1);

                    b.Property<int?>("ScenChildType")
                        .HasColumnType("int")
                        .HasComment("Determines the typeof relation the scenario has with its parent.\r\nIt can be a plain copy (version 3.15 and before) \r\nor\r\nDerived scenario for creating a scenario with overriden risks / tasks but keeping the parent risks/tasks\r\n");

                    b.Property<int?>("ScenCopiedFrom")
                        .HasColumnType("int")
                        .HasComment("Used when scenario was copied from another scenario. Contains the ID of the scenario which was the source of the copy (master scenario) (FK to Scenario)\r\nThis ID is used in the tree for building hierarchical scenario's");

                    b.Property<DateTime?>("ScenDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("ScenDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<int>("ScenDepartment")
                        .HasColumnType("int")
                        .HasComment("Department ID the scenario belongs to (FK to Department)");

                    b.Property<string>("ScenDescr")
                        .HasColumnType("text")
                        .HasComment("Description of the scenario");

                    b.Property<string>("ScenInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user");

                    b.Property<string>("ScenModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<string>("ScenName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the scenario");

                    b.Property<string>("ScenShrtKey")
                        .IsRequired()
                        .HasMaxLength(4)
                        .IsUnicode(false)
                        .HasColumnType("char(4)")
                        .IsFixedLength()
                        .HasComment("Short key of the scenario");

                    b.Property<string>("ScenStartPoint")
                        .HasColumnType("text")
                        .HasComment("Start point of the scenario");

                    b.Property<int?>("ScenStatus")
                        .HasColumnType("int");

                    b.HasKey("ScenId")
                        .HasName("PK_Scen");

                    b.HasIndex("ScenDepartment");

                    b.ToTable("TblScenario");

                    b.HasComment("Allows the user to define risks differently, which enables them to see the differences in outcome when taking a different approach to maintenance.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Si", b =>
                {
                    b.Property<int>("SiId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SiID")
                        .HasComment("Unique ID (PK of Si)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SiId"), 1L, 1);

                    b.Property<string>("SiAssetManager")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the responsible asset manager of the item");

                    b.Property<string>("SiAssetOwner")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the owner of the item");

                    b.Property<string>("SiAssetType")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Logical classification of the item (eg elevator)");

                    b.Property<int?>("SiBvId")
                        .HasColumnType("int")
                        .HasColumnName("SiBvID")
                        .HasComment("The ID of the businessvalue where the item belongs to");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int")
                        .HasComment("SI category value the significant item belongs to (stores value of SICategory item from LookupUserDefined, which can be defined in the master data) (! should be named SICategoryValue?)");

                    b.Property<DateTime?>("SiContractEnd")
                        .HasColumnType("datetime")
                        .HasComment("End of contract date for the significant item ");

                    b.Property<DateTime?>("SiDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<string>("SiDescription")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)")
                        .HasComment("Description of the significant item");

                    b.Property<decimal?>("SiHeight")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Height of the significant item");

                    b.Property<decimal?>("SiLength")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Length of the significant item");

                    b.Property<string>("SiLocation")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Geographical location of the item");

                    b.Property<bool?>("SiMiscBitField1")
                        .HasColumnType("bit")
                        .HasComment("Boolean that sets the significant item to SHE critical. (what is SHE critical?) ");

                    b.Property<DateTime?>("SiMiscDateField1")
                        .HasColumnType("datetime")
                        .HasComment("Miscellaneous date field for the significant item (not used)");

                    b.Property<decimal?>("SiMiscDecimalField1")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Miscellaneous decimal field for the significant item (not used) (!)");

                    b.Property<decimal?>("SiMiscDecimalField2")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Miscellaneous decimal field for the significant item (not used) (!)");

                    b.Property<string>("SiMiscTextField1")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

                    b.Property<string>("SiMiscTextField2")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

                    b.Property<string>("SiMiscTextField3")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

                    b.Property<string>("SiMiscTextField4")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

                    b.Property<string>("SiMiscTextField5")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

                    b.Property<string>("SiMiscTextField6")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

                    b.Property<string>("SiMiscTextField7")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

                    b.Property<string>("SiModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<decimal?>("SiMtbf")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Mean time between failures for this significant item. (?) (not used)");

                    b.Property<string>("SiName")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Name of the significant item");

                    b.Property<int?>("SiPartOf")
                        .HasColumnType("int")
                        .HasComment("Si ID of the parent significant item (FK to Si)");

                    b.Property<decimal?>("SiPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Price of the significant item. Used for LCC calculations.");

                    b.Property<int?>("SiQualityScore")
                        .HasColumnType("int")
                        .HasComment("Business value of the significant item (Quality scores are defined in Mca, but user can enter any number here) (?)");

                    b.Property<string>("SiReferenceId")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("SiReferenceID")
                        .HasComment("Reference ID of the significant item. This shows what this significant item is attached to. Each connected significant item is separated by a '-'. (?)");

                    b.Property<string>("SiRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the significant item");

                    b.Property<string>("SiSerialNumber")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Unique serial number of the item");

                    b.Property<string>("SiServiceManager")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the service manager of the item");

                    b.Property<string>("SiServiceProvider")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the service provider of the item");

                    b.Property<string>("SiSite")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int?>("SiStatus")
                        .HasColumnType("int");

                    b.Property<bool?>("SiSupplierBitField1")
                        .HasColumnType("bit")
                        .HasComment("Supplier boolean field (not used) (!)");

                    b.Property<DateTime?>("SiSupplierDateField1")
                        .HasColumnType("datetime")
                        .HasComment("Supplier date field (not used) (!)");

                    b.Property<int?>("SiSupplierIntField1")
                        .HasColumnType("int")
                        .HasComment("Supplier integer field (not used) (!)");

                    b.Property<string>("SiSupplierTextField1")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Supplier of the significant item.");

                    b.Property<string>("SiSupplierTextField2")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Supplier text field (not used) (!)");

                    b.Property<string>("SiSupplierTextField3")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasComment("Supplier text field (not used) (!)");

                    b.Property<decimal?>("SiTotalUnits")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Total number of units for this significant item");

                    b.Property<string>("SiType")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Type of significant item. User can enter any string here.");

                    b.Property<string>("SiUnitType")
                        .HasMaxLength(15)
                        .IsUnicode(false)
                        .HasColumnType("varchar(15)")
                        .HasComment("SI unit type which applies to the significant item (possible values are stored in Lookup)");

                    b.Property<decimal?>("SiUnits")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Number of units for this significant item");

                    b.Property<string>("SiUnitsTypeValues")
                        .HasMaxLength(150)
                        .IsUnicode(false)
                        .HasColumnType("varchar(150)")
                        .HasComment("Value of each unit type, stored in a sequence, items are separated by '|'");

                    b.Property<string>("SiVendor")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Vendor of the significant item");

                    b.Property<int?>("SiWarrantyPeriod")
                        .HasColumnType("int")
                        .HasComment("Number of years of warranty for the significant item. The warranty will run out this amount of years after the construction year. (SiYear) ");

                    b.Property<decimal?>("SiWidth")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Width of the significant item");

                    b.Property<DateTime?>("SiYear")
                        .HasColumnType("datetime")
                        .HasComment("Construction year of the significant item");

                    b.HasKey("SiId")
                        .HasName("PK_MSI");

                    b.HasIndex("SiCategory")
                        .HasDatabaseName("IX_Si_Category");

                    b.HasIndex("SiName")
                        .HasDatabaseName("IX_Si");

                    b.HasIndex("SiPartOf")
                        .HasDatabaseName("IX_tblSiPartOf");

                    b.ToTable("TblSi");

                    b.HasComment("Contains significant items (SI's). A significant item is an item which represents a physical object we want to measure the risks for. The significant item can be assigned to parts of the functional decomposition. The significant items and their  relationships are defined in the master data. Assignment of significant items to risk objects occurs in the Risk analysis.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiLinkFilters", b =>
                {
                    b.Property<int>("SifId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SifID")
                        .HasComment("Unique ID (PK of SiLinkFilters)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SifId"), 1L, 1);

                    b.Property<int?>("SifCategory")
                        .HasColumnType("int")
                        .HasComment("Sif category value the significant item belongs to (stores value of SICategory item from LookupUserDefined, which can be defined in the master data) (! should be named SifCategoryValue?)");

                    b.Property<int?>("SifChildObject1Id")
                        .HasColumnType("int")
                        .HasColumnName("SifChildObject1ID");

                    b.Property<int?>("SifChildObject2Id")
                        .HasColumnType("int")
                        .HasColumnName("SifChildObject2ID");

                    b.Property<int?>("SifChildObject3Id")
                        .HasColumnType("int")
                        .HasColumnName("SifChildObject3ID");

                    b.Property<int?>("SifChildObject4Id")
                        .HasColumnType("int")
                        .HasColumnName("SifChildObject4ID");

                    b.Property<int?>("SifChildObjectId")
                        .HasColumnType("int")
                        .HasColumnName("SifChildObjectID");

                    b.Property<string>("SifDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Contains a text representation of the defined filter. This representation is human readable, and was added to allow for some oversight while defining filters.");

                    b.Property<bool>("SifExcludeFromParent")
                        .HasColumnType("bit");

                    b.Property<int?>("SifFilterId")
                        .HasColumnType("int")
                        .HasColumnName("SifFilterID");

                    b.Property<string>("SifName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Descriptive name for the filter");

                    b.Property<int?>("SifObjectId")
                        .HasColumnType("int")
                        .HasColumnName("SifObjectID")
                        .HasComment("ID of the object the selected Si items are a part of. (FK to Object)");

                    b.Property<int?>("SifRiskId")
                        .HasColumnType("int")
                        .HasColumnName("SifRiskID")
                        .HasComment("ID of the risk object the selected Si items are a part of. (FK to RiskObject)");

                    b.Property<int?>("SifRiskObjectId")
                        .HasColumnType("int")
                        .HasColumnName("SifRiskObjectID");

                    b.Property<int?>("SifTaskId")
                        .HasColumnType("int")
                        .HasColumnName("SifTaskID");

                    b.HasKey("SifId");

                    b.HasIndex("SifChildObject1Id");

                    b.HasIndex("SifChildObject2Id");

                    b.HasIndex("SifChildObject3Id");

                    b.HasIndex("SifChildObject4Id");

                    b.HasIndex("SifChildObjectId");

                    b.HasIndex("SifFilterId")
                        .IsUnique()
                        .HasFilter("[SifFilterID] IS NOT NULL");

                    b.HasIndex("SifObjectId");

                    b.HasIndex("SifRiskId");

                    b.HasIndex("SifRiskObjectId");

                    b.HasIndex("SifTaskId");

                    b.ToTable("TblSiLinkFilters");

                    b.HasComment("Contains a variation of the filters stored in Filter, which are specific to (linking) significant items. These filters are used to limit the amount of data the user has to search through when linking significant items to functional objects. \r\n\r\nFilters are defined from the Risk analysis, switch to Si assignment tab, and click on the advanced button to start defining a new filter.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiRiskCalc", b =>
                {
                    b.Property<DateTime?>("BvDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("BvId")
                        .HasColumnType("int")
                        .HasColumnName("BvID");

                    b.Property<string>("BvPerEffectSet")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("BvWeightingModelId")
                        .HasColumnType("int")
                        .HasColumnName("BvWeightingModelID");

                    b.Property<DateTime?>("MrbDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("MrbFmecaSelect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("MrbFmecaVersion")
                        .HasColumnType("int");

                    b.Property<int>("PckSiActive")
                        .HasColumnType("int");

                    b.Property<int?>("PckSiDateModified")
                        .HasColumnType("int");

                    b.Property<int?>("PckSiExecutionYear")
                        .HasColumnType("int");

                    b.Property<int>("PckSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiID");

                    b.Property<int?>("PckSiItems")
                        .HasColumnType("int");

                    b.Property<int?>("PckSiLinkFilterId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiLinkFilterID");

                    b.Property<int?>("PckSiModifiedBy")
                        .HasColumnType("int");

                    b.Property<int>("PckSiMrbId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiMrbID");

                    b.Property<int>("PckSiSiId")
                        .HasColumnType("int")
                        .HasColumnName("PckSiSiID");

                    b.Property<decimal?>("SiMtbf")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("SifTaskId")
                        .HasColumnType("int")
                        .HasColumnName("SifTaskID");

                    b.ToView("SiRiskCalc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiRisks", b =>
                {
                    b.Property<decimal?>("SiRiskAfterCustom")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("SiRiskAfterValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("SiRiskBeforeCustom")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("SiRiskBeforeValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("SiRiskBvId")
                        .HasColumnType("int")
                        .HasColumnName("SiRiskBvID");

                    b.Property<int?>("SiRiskDateModified")
                        .HasColumnType("int");

                    b.Property<string>("SiRiskFmecaSelData")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("SiRiskId")
                        .HasColumnType("int")
                        .HasColumnName("SiRiskID");

                    b.Property<int?>("SiRiskModifiedBy")
                        .HasColumnType("int");

                    b.Property<decimal?>("SiRiskMtbfAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("SiRiskMtbfBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("SiRiskRiskAfter")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("SiRiskRiskBefore")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SiRiskRiskFactorBefore")
                        .HasColumnType("int");

                    b.Property<int>("SiRiskRiskId")
                        .HasColumnType("int")
                        .HasColumnName("SiRiskRiskID");

                    b.Property<int>("SiRiskRiskfactorAfter")
                        .HasColumnType("int");

                    b.Property<int>("SiRiskSiId")
                        .HasColumnType("int")
                        .HasColumnName("SiRiskSiID");

                    b.ToView("SiRisks");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiStatistics", b =>
                {
                    b.Property<int>("SiStatId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SiStatID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SiStatId"), 1L, 1);

                    b.Property<string>("SiStatColorListAfter")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("SiStatColorListBefore")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<DateTime?>("SiStatDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("SiStatFmecaId")
                        .HasColumnType("int")
                        .HasColumnName("SiStatFmecaID");

                    b.Property<string>("SiStatModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<decimal?>("SiStatRiskAfter")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,0)")
                        .HasDefaultValueSql("((0))");

                    b.Property<decimal?>("SiStatRiskBefore")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,0)")
                        .HasDefaultValueSql("((0))");

                    b.Property<int>("SiStatScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("SiStatScenarioID");

                    b.Property<int>("SiStatSiId")
                        .HasColumnType("int")
                        .HasColumnName("SiStatSiID");

                    b.Property<string>("SiStatSumListAfter")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<string>("SiStatSumListBefore")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.HasKey("SiStatId")
                        .HasName("PK_SiStatistics");

                    b.HasIndex("SiStatScenarioId")
                        .HasDatabaseName("IX_SiStatistics_ScenarioId");

                    b.HasIndex("SiStatSiId")
                        .HasDatabaseName("IX_SiStatisticsSiID");

                    b.ToTable("SiStatistics");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiStatisticsRiskQualification", b =>
                {
                    b.Property<decimal?>("RiskIndexAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("RiskIndexBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal>("RiskNumberAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal>("RiskNumberBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int>("SiCategory")
                        .HasColumnType("int");

                    b.Property<string>("SiDescription")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)");

                    b.Property<int>("SiId")
                        .HasColumnType("int")
                        .HasColumnName("SiID");

                    b.Property<string>("SiName")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<int>("SiStatId")
                        .HasColumnType("int")
                        .HasColumnName("SiStatID");

                    b.Property<int>("SiStatScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("SiStatScenarioID");

                    b.ToView("SiStatisticsRiskQualification");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.ClusterStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("ClusterLevel")
                        .HasColumnType("int");

                    b.Property<int?>("LowestStatus")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("ClusterStatus");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.ClusterTaskPlanWithObjects", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ClusterName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("CostPerUnit")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime?>("DateExecuted")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateGenerated")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("DownTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("Duration")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("ExecuteStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ExecutionDate")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("Interruptible")
                        .HasColumnType("bit");

                    b.Property<decimal?>("Interval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("IntervalUnit")
                        .HasColumnType("int");

                    b.Property<int?>("Priority")
                        .HasColumnType("int");

                    b.Property<int?>("QualityScore")
                        .HasColumnType("int");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Risk")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskId")
                        .HasColumnType("int");

                    b.Property<int?>("Sequence")
                        .HasColumnType("int");

                    b.Property<int?>("ShiftEndDate")
                        .HasColumnType("int");

                    b.Property<int?>("ShiftStartDate")
                        .HasColumnType("int");

                    b.Property<decimal?>("SiUnits")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("SignificantItem")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Slack")
                        .HasColumnType("int");

                    b.Property<string>("SlackInterval")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Task")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TaskId")
                        .HasColumnType("int");

                    b.Property<bool?>("UseLastDayExecuted")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.ToTable("ClusterTaskPlansWithObjects");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.ClusterTaskWithObjects", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Action")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Assembly")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Cluster")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ClusterCostMember")
                        .HasColumnType("bit");

                    b.Property<decimal?>("ClusterCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Collection")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Component")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Costs")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("DownTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("Duration")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("EstimatedCostPerUnit")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("EstimatedCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Executor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GeneralDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Initiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Installation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Interval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("IntervalUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Policy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Risk")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskId")
                        .HasColumnType("int");

                    b.Property<string>("RiskObject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Scenario")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("System")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UnitType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Units")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("WorkPackage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ClusterTasksWithObjects");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.ClusterTree", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("ClusterId")
                        .HasColumnType("int");

                    b.Property<int?>("ObjectId")
                        .HasColumnType("int");

                    b.Property<string>("ObjectShortKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObjectName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ScenarioChildTypeId")
                        .HasColumnType("int");

                    b.Property<int?>("ScenarioId")
                        .HasColumnType("int");

                    b.Property<string>("ScenarioName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ScenarioShortKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TaskWorkPackageId")
                        .HasColumnType("int");

                    b.Property<decimal?>("WorkPackageInterval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("WorkPackageIntervalUnitId")
                        .HasColumnType("int");

                    b.Property<string>("WorkPackageShortDescription")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ClusterTree");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.ClusterWorkPackageTree", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("ClusterId")
                        .HasColumnType("int");

                    b.Property<int?>("WorkPackageId")
                        .HasColumnType("int");

                    b.Property<decimal>("WorkPackageInterval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("WorkPackageIntervalUnitId")
                        .HasColumnType("int");

                    b.Property<string>("WorkPackageName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkPackageShortDescription")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ClusterWorkPackageTree");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.CommonActionReportItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Executor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FilterSiCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FilterUnitTypes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Initiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Interval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Policy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SiCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Task")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Unit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UnitType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkPackage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CommonActionReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.FunctionalTreeReportItem", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("Obj1Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Obj1Function")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Obj1Level")
                        .HasColumnType("int");

                    b.Property<string>("Obj1Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Obj1NewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("Obj1ProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("Obj2Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Obj2Function")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Obj2Level")
                        .HasColumnType("int");

                    b.Property<string>("Obj2Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Obj2NewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("Obj2ProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("Obj3Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Obj3Function")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Obj3Level")
                        .HasColumnType("int");

                    b.Property<string>("Obj3Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Obj3NewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("Obj3ProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("Obj4Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Obj4Function")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Obj4Level")
                        .HasColumnType("int");

                    b.Property<string>("Obj4Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Obj4NewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("Obj4ProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("ObjDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ObjFunction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ObjLevel")
                        .HasColumnType("int");

                    b.Property<string>("ObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ObjNewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("ObjProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("RiskObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObjObjFunction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskObjObjLevel")
                        .HasColumnType("int");

                    b.Property<string>("RiskObjObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("RiskObjObjNewValue")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("RiskObjObjProductionTime")
                        .HasColumnType("int");

                    b.Property<string>("RiskObjResponsible")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObjStartPoint")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RiskObjectId")
                        .HasColumnType("int");

                    b.Property<string>("ScenarioDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ScenarioId")
                        .HasColumnType("int");

                    b.Property<string>("ScenarioName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("FunctionalTreeReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.RiskAnalysisReportItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<decimal?>("ActionCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("CustomAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("CustomBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("DownTimeAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("DownTimeBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("EffectAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("EffectBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("FailMode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FailureCategory1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FailureCategory2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FailureCause")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FailureConsequences")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FmecaSelect")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("FmecaVersion")
                        .HasColumnType("int");

                    b.Property<decimal?>("MtbfAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MtbfBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Object3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("OptimalCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Remarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("RiskAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("RiskBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("RiskObjName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RiskObject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("RiskSpareCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Scenario")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SpareCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("SpareCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("SpareDepreciationPct")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("SpareID")
                        .HasColumnType("int");

                    b.Property<decimal?>("SpareManagementCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("SpareName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SpareNoOfItems")
                        .HasColumnType("int");

                    b.Property<int?>("SpareObjectCount")
                        .HasColumnType("int");

                    b.Property<decimal?>("SpareOrderLeadTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("SparePurchasePrice")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("SparePurchaseYear")
                        .HasColumnType("int");

                    b.Property<string>("SpareReferenceID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SpareRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SpareStockNumber")
                        .HasColumnType("int");

                    b.Property<string>("SpareSupplierID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SpareVendorID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("SpareYearlyCost")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskCluster")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskExecutor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskGeneralDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TskID")
                        .HasColumnType("int");

                    b.Property<string>("TskInitiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("TskIntervalUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskMxPolicy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TskWorkPackage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("RiskAnalysisReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.Reports.SignificantItemReportItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ContractEnd")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Height")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("Length")
                        .HasColumnType("decimal(18,3)");

                    b.Property<bool?>("MiscBitField1")
                        .HasColumnType("bit");

                    b.Property<string>("MiscTextField1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField3")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField4")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField5")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField6")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiscTextField7")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Mtbf")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PartOfSiName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(18,3)");

                    b.Property<int?>("QualityScore")
                        .HasColumnType("int");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Remarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierTextField1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TotalUnits")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UnitType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UnitTypeValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Units")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Vendor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("WarrantyPeriod")
                        .HasColumnType("int");

                    b.Property<decimal?>("Width")
                        .HasColumnType("decimal(18,3)");

                    b.Property<DateTime?>("Year")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("SignificantItemReportItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.RiskWithObjects", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Assembly")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Component")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Installation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RiskObjectId")
                        .HasColumnType("int");

                    b.Property<string>("System")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("RisksWithObjects");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SP.TaskWithObjects", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Action")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Costs")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("Executor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Initiator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Interval")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("IntervalUnit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Policy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RiskId")
                        .HasColumnType("int");

                    b.Property<string>("WorkPackage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("TasksWithObjects");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Spare", b =>
                {
                    b.Property<int>("SpareId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SpareID")
                        .HasComment("Unique ID (PK to Spare)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SpareId"), 1L, 1);

                    b.Property<string>("SpareCategory")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Category the spare part belongs to (?)");

                    b.Property<int?>("SpareChildType")
                        .HasColumnType("int");

                    b.Property<int?>("SpareCopiedFrom")
                        .HasColumnType("int")
                        .HasComment("For derived scenarios's you will need this field to keep track of changes");

                    b.Property<decimal?>("SpareCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost of the spare part. ");

                    b.Property<decimal?>("SpareDepreciationPct")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Depreciation percentage of the spare part. ");

                    b.Property<int>("SpareMrbId")
                        .HasColumnType("int")
                        .HasColumnName("SpareMrbID")
                        .HasComment("Risk ID the spare part belongs to (FK to MRB)");

                    b.Property<string>("SpareName")
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)")
                        .HasComment("Name of the spare part");

                    b.Property<int?>("SpareNoOfItems")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValueSql("((1))")
                        .HasComment("Number of units of the spare part that are needed. (used in price calculation)");

                    b.Property<int?>("SpareObjectCount")
                        .HasColumnType("int")
                        .HasComment("Number of objects that can make use of the spare part.");

                    b.Property<decimal?>("SpareOrderLeadTime")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Lead time of the spare part. (The time it takes between ordering the part, and receiving it where it's needed)");

                    b.Property<decimal?>("SparePurchasePrice")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Purchase price of the spare part");

                    b.Property<int?>("SparePurchaseYear")
                        .HasColumnType("int")
                        .HasComment("Year the spare part was purchased.");

                    b.Property<string>("SpareReferenceId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("SpareReferenceID")
                        .HasComment("Reference ID of the spare part (not bound) (!)");

                    b.Property<decimal?>("SpareReliability")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("SpareRemarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the spare part");

                    b.Property<int?>("SpareStockNumber")
                        .HasColumnType("int")
                        .HasComment("Stock number of the spare part. ");

                    b.Property<string>("SpareSupplierId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("SpareSupplierID")
                        .HasComment("Supplier ID of the spare part, which can be used to order the spare part directly. ");

                    b.Property<string>("SpareVendorId")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("SpareVendorID")
                        .HasComment("The vendor that sells the spare part (! not an ID)");

                    b.Property<decimal?>("SpareYearlyCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Yearly costs for the spare part (storage, depreciation, etc) (! does not seem to be used by AMprover software)");

                    b.HasKey("SpareId");

                    b.HasIndex("SpareMrbId");

                    b.ToTable("TblSpare");

                    b.HasComment("Contains spare parts, their costs and other data associated with storing them. \r\n\r\nSpare parts can be defined using the Spare parts grid control found in the Risk analysis screen, and can be added to any Risk.");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SyncRamsMrb", b =>
                {
                    b.Property<int>("MrbId")
                        .HasColumnType("int")
                        .HasColumnName("MrbID");

                    b.Property<decimal?>("MrbMtbfAfter")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal?>("MrbMtbfBefore")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("RamsContainer")
                        .HasColumnType("bit");

                    b.Property<int>("RamsDiagramId")
                        .HasColumnType("int")
                        .HasColumnName("RamsDiagramID");

                    b.Property<int>("RamsId")
                        .HasColumnType("int")
                        .HasColumnName("RamsID");

                    b.Property<int?>("RamsLinkMethod")
                        .HasColumnType("int");

                    b.Property<int?>("RamsLinkType")
                        .HasColumnType("int");

                    b.Property<double?>("RamsMtbffunct")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFFunct");

                    b.Property<double?>("RamsMtbftechn")
                        .HasColumnType("float")
                        .HasColumnName("RamsMTBFTechn");

                    b.ToView("SyncRamsMrb");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Task", b =>
                {
                    b.Property<int>("TskId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("TskID")
                        .HasComment("Unique ID (PK of Task)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TskId"), 1L, 1);

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int")
                        .HasComment("Cluster ID the task belongs to (FK to Cluster)");

                    b.Property<bool?>("TskClusterCostMember")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValueSql("((1))")
                        .HasComment("Boolean that makes the task cluster cost a member (Cluster costs are spread evenly over the tasks that were set as cost member)");

                    b.Property<decimal?>("TskClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost per unit for the task calculated during clustering (?)");

                    b.Property<decimal?>("TskClusterCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Cost for the task calculated during clustering");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnType("int")
                        .HasColumnName("TskCommonActionID")
                        .HasComment("ID of the common task this task is a part of (FK to CommonTask)");

                    b.Property<int?>("TskCopiedFrom")
                        .HasColumnType("int")
                        .HasComment("Source ID the task was copied from (master task) (FK to Task) Will only be filled for tasks that were created as copies of other tasks. ");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Contains the costs associated with this task. (? total, estimated?)");

                    b.Property<DateTime?>("TskDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Creation date");

                    b.Property<DateTime?>("TskDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<bool?>("TskDerived")
                        .HasColumnType("bit")
                        .HasComment("Is the task derived from another task");

                    b.Property<string>("TskDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the task");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Downtime needed to complete the task (in hours)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Time spent executing the task (in hours)");

                    b.Property<decimal?>("TskEstCostPerUnit")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Estimated cost per unit for this task");

                    b.Property<decimal?>("TskEstCosts")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Estimated cost for the task");

                    b.Property<DateTime?>("TskExecutionDate")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date when the task must be executed");

                    b.Property<int?>("TskExecutor")
                        .HasColumnType("int")
                        .HasComment("ID of the executor of the task (FK to LookupExecutor)");

                    b.Property<bool?>("TskExtraBool2")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<bool?>("TskExtraBool3")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<bool?>("TskExtraBool4")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<DateTime?>("TskFinishDate")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date when the task needs to have been executed");

                    b.Property<string>("TskFmecaEffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Effect that the task has for each FMECA column. This field contains an xml representation of an entire fmeca matrix.");

                    b.Property<decimal?>("TskFmecaEffectPct")
                        .HasColumnType("numeric(18,2)")
                        .HasComment("Effect that the task has on each FMECA column (?)");

                    b.Property<int>("TskFmecaVersion")
                        .HasColumnType("int")
                        .HasComment("Version of the FMECA matrix that is used. Allows tasks to be defined for different fmeca configurations.");

                    b.Property<string>("TskGeneralDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("General description of the task");

                    b.Property<string>("TskInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user");

                    b.Property<int>("TskInitiator")
                        .HasColumnType("int")
                        .HasComment("ID of the initiator of the task (FK to LookupInitiator)");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,4)")
                        .HasComment("Interval of the task. The interval unit determines what the interval stands for (times yearly, monthly, etc) ");

                    b.Property<int>("TskIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("ID of the the interval unit of the task (FK to LookupIntervalUnit). The interval unit determines what the value stored in TskInterval really means.");

                    b.Property<string>("TskLcceffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("TskLCCEffect")
                        .HasComment("? Effect that the task has for LCC calculations. This field contains an xml representation of an entire fmeca matrix, but now containing LCC effects for each field in the matrix.");

                    b.Property<bool?>("TskMaster")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<string>("TskModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID")
                        .HasComment("Risk ID the task belongs to (FK to MRB)");

                    b.Property<int?>("TskMxPolicy")
                        .HasColumnType("int")
                        .HasComment("ID of the maintenance policy of the task (FK to LookupMxPolicy)");

                    b.Property<string>("TskName")
                        .IsRequired()
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)")
                        .HasComment("Name of the task");

                    b.Property<string>("TskNorm")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Norm for the task");

                    b.Property<decimal?>("TskOptimalCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Optimal cost for the task");

                    b.Property<int?>("TskPartOf")
                        .HasColumnType("int")
                        .HasComment("Task ID of the parent task (FK to Task)");

                    b.Property<string>("TskPermit")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Permit(s) needed before being allowed to execute the task");

                    b.Property<int?>("TskPriorityCode")
                        .HasColumnType("int")
                        .HasComment("? Does not seem to be used by AMprover software (!)");

                    b.Property<int?>("TskReferenceId")
                        .HasColumnType("int")
                        .HasColumnName("TskReferenceID");

                    b.Property<string>("TskRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Remarks of the task");

                    b.Property<bool?>("TskRemoved")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<string>("TskResponsible")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Person or department responsible for execution of the task. (Does not seem to be used by AMprover software) (!)");

                    b.Property<bool?>("TskSkipInLcc")
                        .HasColumnType("bit")
                        .HasComment("Custom boolean value (does not seem to be used by AMprover software) (!)");

                    b.Property<int?>("TskSortOrder")
                        .HasColumnType("int")
                        .HasComment("Custom way to order the tasks. User can manually alter the order in which tasks will be executed by changing the numbers of this field. ");

                    b.Property<int?>("TskStatus")
                        .HasColumnType("int")
                        .HasComment("Status of the task (master or copy)");

                    b.Property<string>("TskType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Type of task (task, procedure etc.). The possible types are defined in Lookup, MeasureType. (?)");

                    b.Property<int>("TskUnitType")
                        .HasColumnType("int")
                        .HasComment("Unit type ID used for the task (FK to LookupUserDefined)");

                    b.Property<decimal?>("TskUnits")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Number of units needed for the task");

                    b.Property<int?>("TskValidFromYear")
                        .HasColumnType("int")
                        .HasComment("The year when the common task becomes valid. (In LCC calculations, items with a ValidFromYear < the current year will not be part of the calculations.)");

                    b.Property<int?>("TskValidUntilYear")
                        .HasColumnType("int")
                        .HasComment("The last year this task is still valid");

                    b.Property<decimal?>("TskWorkInspCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Costs made for inspection during/for this task");

                    b.Property<int?>("TskWorkpackage")
                        .HasColumnType("int")
                        .HasComment("ID of the work package the task belongs to (FK to Workpackage)");

                    b.HasKey("TskId");

                    b.HasIndex("TskCluster")
                        .HasDatabaseName("IX_TaskClusterID");

                    b.HasIndex("TskCommonActionId");

                    b.HasIndex("TskExecutor");

                    b.HasIndex("TskInitiator");

                    b.HasIndex("TskIntervalUnit");

                    b.HasIndex("TskMrbId")
                        .HasDatabaseName("IX_TaskMrbID");

                    b.HasIndex("TskMxPolicy");

                    b.HasIndex("TskWorkpackage");

                    b.ToTable("TblTask");

                    b.HasComment("Contains maintenance tasks, which are linked to tasks stored in the customers maintenance information system. \r\nIn some places within the code, tasks will be called actions. This is due to the naming conventions used in AMprover 2.6, references to actions will slowly be removed from the code. \r\nTasks can be imported directly into the database (manually), or entered by hand in the preventive actions tab within the Risk analysis. \r\n");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.TaskCostOnClusterModified", b =>
                {
                    b.Property<decimal?>("ClusterTaskCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(38,2)");

                    b.ToView("TaskCostOnClusterModified");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.TaskCostOnMrbModified", b =>
                {
                    b.Property<decimal?>("MrbCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("MrbDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(38,2)");

                    b.Property<DateTime?>("TskDate")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.ToView("TaskCostOnMrbModified");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.TaskEdit", b =>
                {
                    b.Property<string>("ObjLevel0Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjLevel2Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjLevel3Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjLevel4Name")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RiskObjId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjID");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("ScenChildType")
                        .HasColumnType("int");

                    b.Property<int?>("ScenId")
                        .HasColumnType("int")
                        .HasColumnName("ScenID");

                    b.Property<string>("ScenName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int");

                    b.Property<bool?>("TskClusterCostMember")
                        .HasColumnType("bit");

                    b.Property<decimal?>("TskClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskClusterCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnType("int")
                        .HasColumnName("TskCommonActionID");

                    b.Property<int?>("TskCopiedFrom")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("TskDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TskDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<bool?>("TskDerived")
                        .HasColumnType("bit");

                    b.Property<string>("TskDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("TskEstCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskEstCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("TskExecutionDate")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("TskExecutor")
                        .HasColumnType("int");

                    b.Property<bool?>("TskExtraBool2")
                        .HasColumnType("bit");

                    b.Property<bool?>("TskExtraBool3")
                        .HasColumnType("bit");

                    b.Property<bool?>("TskExtraBool4")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("TskFinishDate")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("TskFmecaEffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskFmecaEffectPct")
                        .HasColumnType("numeric(18,2)");

                    b.Property<int>("TskFmecaVersion")
                        .HasColumnType("int");

                    b.Property<string>("TskGeneralDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("TskId")
                        .HasColumnType("int")
                        .HasColumnName("TskID");

                    b.Property<string>("TskInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int>("TskInitiator")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int>("TskIntervalUnit")
                        .HasColumnType("int");

                    b.Property<string>("TskLcceffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("TskLCCEffect");

                    b.Property<bool?>("TskMaster")
                        .HasColumnType("bit");

                    b.Property<string>("TskModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.Property<int?>("TskMxPolicy")
                        .HasColumnType("int");

                    b.Property<string>("TskName")
                        .IsRequired()
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<string>("TskNorm")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskOptimalCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskPartOf")
                        .HasColumnType("int");

                    b.Property<string>("TskPermit")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("TskPriorityCode")
                        .HasColumnType("int");

                    b.Property<int?>("TskReferenceId")
                        .HasColumnType("int")
                        .HasColumnName("TskReferenceID");

                    b.Property<string>("TskRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<bool?>("TskRemoved")
                        .HasColumnType("bit");

                    b.Property<string>("TskResponsible")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("TskSkipInLcc")
                        .HasColumnType("bit");

                    b.Property<int?>("TskSortOrder")
                        .HasColumnType("int");

                    b.Property<int?>("TskStatus")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int>("TskUnitType")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskUnits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskValidFromYear")
                        .HasColumnType("int");

                    b.Property<int?>("TskValidUntilYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskWorkInspCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskWorkpackage")
                        .HasColumnType("int");

                    b.ToView("TaskEdit");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.TaskLookupGrid", b =>
                {
                    b.Property<string>("ClustName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ExecutorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("InitiatorName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("IntUnitName")
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<string>("MrbName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjectName2")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjectName3")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ObjectName4")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PolName")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)");

                    b.Property<int>("RiskObjId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjID");

                    b.Property<string>("RiskObjName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("RiskObjScenarioId")
                        .HasColumnType("int")
                        .HasColumnName("RiskObjScenarioID");

                    b.Property<int?>("TskCluster")
                        .HasColumnType("int");

                    b.Property<bool?>("TskClusterCostMember")
                        .HasColumnType("bit");

                    b.Property<decimal?>("TskClusterCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TskClusterCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskCommonActionId")
                        .HasColumnType("int")
                        .HasColumnName("TskCommonActionID");

                    b.Property<int?>("TskCopiedFrom")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskCosts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("TskDateInitiated")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TskDateModified")
                        .HasColumnType("smalldatetime");

                    b.Property<bool?>("TskDerived")
                        .HasColumnType("bit");

                    b.Property<string>("TskDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskDownTime")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("TskDuration")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("TskEstCostPerUnit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("TskExecutionDate")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("TskExecutor")
                        .HasColumnType("int");

                    b.Property<bool?>("TskExtraBool2")
                        .HasColumnType("bit");

                    b.Property<bool?>("TskExtraBool3")
                        .HasColumnType("bit");

                    b.Property<bool?>("TskExtraBool4")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("TskFinishDate")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("TskFmecaEffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<decimal?>("TskFmecaEffectPct")
                        .HasColumnType("numeric(18,2)");

                    b.Property<int>("TskFmecaVersion")
                        .HasColumnType("int");

                    b.Property<string>("TskGeneralDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int>("TskId")
                        .HasColumnType("int")
                        .HasColumnName("TskID");

                    b.Property<string>("TskInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int>("TskInitiator")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskInterval")
                        .HasColumnType("decimal(18,4)");

                    b.Property<int>("TskIntervalUnit")
                        .HasColumnType("int");

                    b.Property<string>("TskLcceffect")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("TskLCCEffect");

                    b.Property<bool?>("TskMaster")
                        .HasColumnType("bit");

                    b.Property<string>("TskModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)");

                    b.Property<int?>("TskMrbId")
                        .HasColumnType("int")
                        .HasColumnName("TskMrbID");

                    b.Property<int?>("TskMxPolicy")
                        .HasColumnType("int");

                    b.Property<string>("TskName")
                        .IsRequired()
                        .HasMaxLength(60)
                        .IsUnicode(false)
                        .HasColumnType("varchar(60)");

                    b.Property<decimal?>("TskOptimalCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TskPermit")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<int?>("TskPriorityCode")
                        .HasColumnType("int");

                    b.Property<string>("TskRemark")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)");

                    b.Property<bool?>("TskRemoved")
                        .HasColumnType("bit");

                    b.Property<string>("TskResponsible")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("TskSkipInLcc")
                        .HasColumnType("bit");

                    b.Property<int?>("TskSortOrder")
                        .HasColumnType("int");

                    b.Property<int?>("TskStatus")
                        .HasColumnType("int");

                    b.Property<string>("TskType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)");

                    b.Property<int>("TskUnitType")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskUnits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskValidFromYear")
                        .HasColumnType("int");

                    b.Property<int?>("TskValidUntilYear")
                        .HasColumnType("int");

                    b.Property<decimal?>("TskWorkInspCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TskWorkpackage")
                        .HasColumnType("int");

                    b.Property<string>("WpName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.ToView("TaskLookupGrid");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.User", b =>
                {
                    b.Property<int>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("UserID")
                        .HasComment("Unique ID (PK of User)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserId"), 1L, 1);

                    b.Property<DateTime?>("UserDateInitiated")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date of creation");

                    b.Property<DateTime?>("UserDateModified")
                        .HasColumnType("smalldatetime")
                        .HasComment("Date the record was last modified. (null if never modified)");

                    b.Property<int?>("UserDepartment")
                        .HasColumnType("int")
                        .HasComment("ID of the the department to which the user belongs (FK to Department)");

                    b.Property<string>("UserFirstName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("First name of the user");

                    b.Property<string>("UserInitiatedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("Created by user");

                    b.Property<bool?>("UserIsGroup")
                        .HasColumnType("bit");

                    b.Property<string>("UserLastName")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Surname of the user");

                    b.Property<bool?>("UserLocked")
                        .HasColumnType("bit");

                    b.Property<string>("UserLoginName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the user");

                    b.Property<string>("UserModifiedBy")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasComment("User name of person that made the last modification to this record");

                    b.Property<int?>("UserPartOfGroup")
                        .HasColumnType("int");

                    b.Property<string>("UserPassword")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)")
                        .HasComment("Password of the user");

                    b.Property<string>("UserRights")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("XML structure with all individual UserRights");

                    b.Property<bool?>("UserSetPassword")
                        .HasColumnType("bit");

                    b.Property<int?>("UserType")
                        .HasColumnType("int")
                        .HasComment("Four kind of types 0 =normal users 1 = read only users  2=Administrators");

                    b.HasKey("UserId");

                    b.HasIndex("UserLoginName")
                        .IsUnique()
                        .HasDatabaseName("IX_User");

                    b.ToTable("User");

                    b.HasComment("Contains all data needed to define a user. This table is not in use from the Amprover software (!)");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.VwSiFilter", b =>
                {
                    b.Property<int?>("PartOfCategory")
                        .HasColumnType("int");

                    b.Property<string>("PartOfName")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)");

                    b.Property<string>("Siassetmanager")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("siassetmanager");

                    b.Property<string>("Siassetowner")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("siassetowner");

                    b.Property<string>("Siassettype")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("siassettype");

                    b.Property<int?>("Sibvid")
                        .HasColumnType("int")
                        .HasColumnName("sibvid");

                    b.Property<int>("Sicategory")
                        .HasColumnType("int")
                        .HasColumnName("sicategory");

                    b.Property<DateTime?>("Sicontractend")
                        .HasColumnType("datetime")
                        .HasColumnName("sicontractend");

                    b.Property<DateTime?>("Sidatemodified")
                        .HasColumnType("smalldatetime")
                        .HasColumnName("sidatemodified");

                    b.Property<string>("Sidescription")
                        .HasMaxLength(100)
                        .IsUnicode(false)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("sidescription");

                    b.Property<decimal?>("Siheight")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("siheight");

                    b.Property<int>("Siid")
                        .HasColumnType("int")
                        .HasColumnName("siid");

                    b.Property<decimal?>("Silength")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("silength");

                    b.Property<string>("Silocation")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("silocation");

                    b.Property<bool?>("Simiscbitfield1")
                        .HasColumnType("bit")
                        .HasColumnName("simiscbitfield1");

                    b.Property<DateTime?>("Simiscdatefield1")
                        .HasColumnType("datetime")
                        .HasColumnName("simiscdatefield1");

                    b.Property<decimal?>("Simiscdecimalfield1")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("simiscdecimalfield1");

                    b.Property<decimal?>("Simiscdecimalfield2")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("simiscdecimalfield2");

                    b.Property<string>("Simisctextfield1")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield1");

                    b.Property<string>("Simisctextfield2")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield2");

                    b.Property<string>("Simisctextfield3")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield3");

                    b.Property<string>("Simisctextfield4")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield4");

                    b.Property<string>("Simisctextfield5")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield5");

                    b.Property<string>("Simisctextfield6")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield6");

                    b.Property<string>("Simisctextfield7")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("simisctextfield7");

                    b.Property<string>("Simodifiedby")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("simodifiedby");

                    b.Property<decimal?>("Simtbf")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("simtbf");

                    b.Property<string>("Siname")
                        .IsRequired()
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("siname");

                    b.Property<int?>("Sipartof")
                        .HasColumnType("int")
                        .HasColumnName("sipartof");

                    b.Property<decimal?>("Siprice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("siprice");

                    b.Property<int?>("Siqualityscore")
                        .HasColumnType("int")
                        .HasColumnName("siqualityscore");

                    b.Property<string>("Sireferenceid")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("sireferenceid");

                    b.Property<string>("Siremarks")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasColumnName("siremarks");

                    b.Property<string>("Siserialnumber")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("siserialnumber");

                    b.Property<string>("Siservicemanager")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("siservicemanager");

                    b.Property<string>("Siserviceprovider")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("siserviceprovider");

                    b.Property<string>("Sisite")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("sisite");

                    b.Property<int?>("Sistatus")
                        .HasColumnType("int")
                        .HasColumnName("sistatus");

                    b.Property<bool?>("Sisupplierbitfield1")
                        .HasColumnType("bit")
                        .HasColumnName("sisupplierbitfield1");

                    b.Property<DateTime?>("Sisupplierdatefield1")
                        .HasColumnType("datetime")
                        .HasColumnName("sisupplierdatefield1");

                    b.Property<int?>("Sisupplierintfield1")
                        .HasColumnType("int")
                        .HasColumnName("sisupplierintfield1");

                    b.Property<string>("Sisuppliertextfield1")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("sisuppliertextfield1");

                    b.Property<string>("Sisuppliertextfield2")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("sisuppliertextfield2");

                    b.Property<string>("Sisuppliertextfield3")
                        .HasMaxLength(40)
                        .IsUnicode(false)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("sisuppliertextfield3");

                    b.Property<decimal?>("Sitotalunits")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("sitotalunits");

                    b.Property<string>("Sitype")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("sitype");

                    b.Property<decimal?>("Siunits")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("siunits");

                    b.Property<string>("Siunitstypevalues")
                        .HasMaxLength(150)
                        .IsUnicode(false)
                        .HasColumnType("varchar(150)")
                        .HasColumnName("siunitstypevalues");

                    b.Property<string>("Siunittype")
                        .HasMaxLength(15)
                        .IsUnicode(false)
                        .HasColumnType("varchar(15)")
                        .HasColumnName("siunittype");

                    b.Property<string>("Sivendor")
                        .HasMaxLength(30)
                        .IsUnicode(false)
                        .HasColumnType("varchar(30)")
                        .HasColumnName("sivendor");

                    b.Property<int?>("Siwarrantyperiod")
                        .HasColumnType("int")
                        .HasColumnName("siwarrantyperiod");

                    b.Property<decimal?>("Siwidth")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("siwidth");

                    b.Property<DateTime?>("Siyear")
                        .HasColumnType("datetime")
                        .HasColumnName("siyear");

                    b.ToView("vwSiFilter");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Workpackage", b =>
                {
                    b.Property<int>("WpId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("WpID")
                        .HasComment("Unique ID (PK of WorkPackage)");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("WpId"), 1L, 1);

                    b.Property<string>("WpDescription")
                        .IsUnicode(false)
                        .HasColumnType("varchar(max)")
                        .HasComment("Description of the work package");

                    b.Property<int?>("WpExecutor")
                        .HasColumnType("int")
                        .HasComment("ID of the executor (FK to LookupExecutor)");

                    b.Property<decimal>("WpInterval")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Interval of the work package. (a number that, combined with the interval unit, shows with what interval the work package needs to be executed)");

                    b.Property<int>("WpIntervalUnit")
                        .HasColumnType("int")
                        .HasComment("ID of the the interval unit of the work package (FK to LookupIntervalUnit)");

                    b.Property<string>("WpName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)")
                        .HasComment("Name of the work package");

                    b.Property<string>("WpShortDescription")
                        .IsRequired()
                        .HasMaxLength(20)
                        .IsUnicode(false)
                        .HasColumnType("varchar(20)")
                        .HasComment("Short key of the work package");

                    b.HasKey("WpId")
                        .HasName("PK_Werkpakket");

                    b.HasIndex("WpExecutor");

                    b.HasIndex("WpIntervalUnit");

                    b.ToTable("TblWorkPackage");

                    b.HasComment("Contains workpackages, which are containers that are used to group tasks that can be more efficiently executed together. (for example daily and weekly tasks). Workpackages are created from the masterdata. ");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvSiItems", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Si", "BvSi")
                        .WithOne("BvSiItems")
                        .HasForeignKey("AMprover.Data.Entities.AM.BvSiItems", "BvSiId")
                        .IsRequired()
                        .HasConstraintName("FK_BvSiItems_Si");

                    b.HasOne("AMprover.Data.Entities.AM.BvWeightingModels", "BvWeightingModel")
                        .WithMany("BvSiItems")
                        .HasForeignKey("BvWeightingModelId")
                        .HasConstraintName("FK_BvSiItems_BvWeightingModels");

                    b.Navigation("BvSi");

                    b.Navigation("BvWeightingModel");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvWeightingModels", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.BvAspectSets", "BvModelAspectSet")
                        .WithMany("BvWeightingModels")
                        .HasForeignKey("BvModelAspectSetId")
                        .HasConstraintName("FK_BvWeightingModels_BvAspectSets");

                    b.HasOne("AMprover.Data.Entities.AM.Fmeca", "BvModelFmeca")
                        .WithMany("BvWeightingModels")
                        .HasForeignKey("BvModelFmecaId")
                        .HasConstraintName("FK_BvWeightingModels_Fmeca");

                    b.Navigation("BvModelAspectSet");

                    b.Navigation("BvModelFmeca");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Cluster", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "ClustPartOfCluster")
                        .WithMany("ClusterChildren")
                        .HasForeignKey("ClustPartOf");

                    b.HasOne("AMprover.Data.Entities.AM.Scenario", "Scenario")
                        .WithMany("Clusters")
                        .HasForeignKey("ClustScenarioId");

                    b.Navigation("ClustPartOfCluster");

                    b.Navigation("Scenario");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterCost", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "ClcCluster")
                        .WithMany("ClusterCost")
                        .HasForeignKey("ClcClusterId")
                        .HasConstraintName("FK_ClusterCost_Cluster");

                    b.HasOne("AMprover.Data.Entities.AM.CommonCost", "ClcCommonCost")
                        .WithMany("ClusterCost")
                        .HasForeignKey("ClcCommonCostId")
                        .IsRequired()
                        .HasConstraintName("FK_ClusterCost_CommonCost");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "ClcTask")
                        .WithMany("ClusterCost")
                        .HasForeignKey("ClcTaskId")
                        .HasConstraintName("FK_ClusterCost_Task");

                    b.Navigation("ClcCluster");

                    b.Navigation("ClcCommonCost");

                    b.Navigation("ClcTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.ClusterTaskPlan", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "CltpCluster")
                        .WithMany("ClusterTaskPlan")
                        .HasForeignKey("CltpClusterId")
                        .HasConstraintName("FK_ClusterTaskPlan_Cluster");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "CltpRisk")
                        .WithMany("ClusterTaskPlan")
                        .HasForeignKey("CltpRiskId")
                        .HasConstraintName("FK_ClusterTaskPlan_MRB");

                    b.HasOne("AMprover.Data.Entities.AM.Si", "CltpSi")
                        .WithMany("ClusterTaskPlan")
                        .HasForeignKey("CltpSiId")
                        .HasConstraintName("FK_ClusterTaskPlan_Si");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "CltpTask")
                        .WithMany("ClusterTaskPlan")
                        .HasForeignKey("CltpTaskId")
                        .HasConstraintName("FK_ClusterTaskPlan_Task");

                    b.Navigation("CltpCluster");

                    b.Navigation("CltpRisk");

                    b.Navigation("CltpSi");

                    b.Navigation("CltpTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonCost", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupInflationGroup", "CmnCostPriceGroupNavigation")
                        .WithMany("CommonCost")
                        .HasForeignKey("CmnCostPriceGroup")
                        .HasConstraintName("FK_CommonCost_LookupInflationGroup");

                    b.Navigation("CmnCostPriceGroupNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTask", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupExecutor", "CmnTaskExecutorNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskExecutor")
                        .HasConstraintName("FK_CommonTask_LookupExecutor");

                    b.HasOne("AMprover.Data.Entities.AM.LookupInitiator", "CmnTaskInitiatorNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskInitiator")
                        .HasConstraintName("FK_CommonTask_LookupInitiator");

                    b.HasOne("AMprover.Data.Entities.AM.LookupIntervalUnit", "CmnTaskIntervalUnitNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskIntervalUnit")
                        .HasConstraintName("FK_CommonTask_LookupIntervalUnit");

                    b.HasOne("AMprover.Data.Entities.AM.LookupMxPolicy", "CmnTaskMxPolicyNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskMxPolicy")
                        .HasConstraintName("FK_CommonTask_LookupMxPolicy");

                    b.HasOne("AMprover.Data.Entities.AM.Workpackage", "CmnTaskWorkPackageNavigation")
                        .WithMany("CommonTask")
                        .HasForeignKey("CmnTaskWorkPackage")
                        .HasConstraintName("FK_CommonTask_Workpackage");

                    b.Navigation("CmnTaskExecutorNavigation");

                    b.Navigation("CmnTaskInitiatorNavigation");

                    b.Navigation("CmnTaskIntervalUnitNavigation");

                    b.Navigation("CmnTaskMxPolicyNavigation");

                    b.Navigation("CmnTaskWorkPackageNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTaskCost", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.CommonCost", "CtcCommonCost")
                        .WithMany("CommonTaskCost")
                        .HasForeignKey("CtcCommonCostId")
                        .IsRequired()
                        .HasConstraintName("FK_CommonTaskCost_CommonCost");

                    b.HasOne("AMprover.Data.Entities.AM.CommonTask", "CtcCommonTask")
                        .WithMany("CommonTaskCost")
                        .HasForeignKey("CtcCommonTaskId")
                        .HasConstraintName("FK_CommonTaskCost_CommonTask");

                    b.Navigation("CtcCommonCost");

                    b.Navigation("CtcCommonTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CriticalityRanking", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Si", "CritSi")
                        .WithOne("SiCriticalityRanking")
                        .HasForeignKey("AMprover.Data.Entities.AM.CriticalityRanking", "CritSiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CritSi");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.FiltersSelectionList", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Filters", "SqlSel")
                        .WithMany("FiltersSelectionList")
                        .HasForeignKey("SqlSelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_FiltersSelectionList_Filters");

                    b.Navigation("SqlSel");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lcc", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject")
                        .WithMany("ChildObjects")
                        .HasForeignKey("LccChildObject")
                        .HasConstraintName("FK_Lcc_Object");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject1")
                        .WithMany("ChildObjects1")
                        .HasForeignKey("LccChildObject1")
                        .HasConstraintName("FK_Lcc_Object1");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject2")
                        .WithMany("ChildObjects2")
                        .HasForeignKey("LccChildObject2")
                        .HasConstraintName("FK_Lcc_Object2");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject3")
                        .WithMany("ChildObjects3")
                        .HasForeignKey("LccChildObject3")
                        .HasConstraintName("FK_Lcc_Object3");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject4")
                        .WithMany("ChildObjects4")
                        .HasForeignKey("LccChildObject4")
                        .HasConstraintName("FK_Lcc_Object4");

                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "LccPartOfLcc")
                        .WithMany("LccChildren")
                        .HasForeignKey("LccPartOf");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany("LccItems")
                        .HasForeignKey("LccRiskObject");

                    b.HasOne("AMprover.Data.Entities.AM.Scenario", "LccScenario")
                        .WithMany()
                        .HasForeignKey("LccScenarioId");

                    b.HasOne("AMprover.Data.Entities.AM.Si", "LccSi")
                        .WithMany("Lccs")
                        .HasForeignKey("LccSiId");

                    b.Navigation("ChildObject");

                    b.Navigation("ChildObject1");

                    b.Navigation("ChildObject2");

                    b.Navigation("ChildObject3");

                    b.Navigation("ChildObject4");

                    b.Navigation("LccPartOfLcc");

                    b.Navigation("LccScenario");

                    b.Navigation("LccSi");

                    b.Navigation("RiskObject");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lccdetail", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "Lcc")
                        .WithMany("Details")
                        .HasForeignKey("LccDetLccId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .HasConstraintName("FK_LccDetail_Lcc");

                    b.Navigation("Lcc");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LcceffectDetail", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Lccdetail", "LccEfctLccDetail")
                        .WithMany("EffectDetails")
                        .HasForeignKey("LccEfctLccDetailId")
                        .OnDelete(DeleteBehavior.ClientCascade)
                        .HasConstraintName("FK_TblLccEffectDetail_TblLCCDetail_LccEfctLccDetailID");

                    b.HasOne("AMprover.Data.Entities.AM.Lcc", "LccEfctLcc")
                        .WithMany()
                        .HasForeignKey("LccEfctLccId");

                    b.Navigation("LccEfctLcc");

                    b.Navigation("LccEfctLccDetail");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mrb", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Object", "ChildObject")
                        .WithMany("MrbChildObjects")
                        .HasForeignKey("MrbChildObject");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "Installation")
                        .WithMany("MrbChildObjects1")
                        .HasForeignKey("MrbChildObject1");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "System")
                        .WithMany("MrbChildObjects2")
                        .HasForeignKey("MrbChildObject2");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "Component")
                        .WithMany("MrbChildObjects3")
                        .HasForeignKey("MrbChildObject3");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "Assembly")
                        .WithMany("MrbChildObjects4")
                        .HasForeignKey("MrbChildObject4");

                    b.HasOne("AMprover.Data.Entities.AM.LookupFailMode", "FailureMode")
                        .WithMany("Risks")
                        .HasForeignKey("MrbFailureMode");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "RiskObject")
                        .WithMany("Risks")
                        .HasForeignKey("MrbRiskObject")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_TblMRB_TblRiskObject_MrbRiskObject");

                    b.Navigation("Assembly");

                    b.Navigation("ChildObject");

                    b.Navigation("Component");

                    b.Navigation("FailureMode");

                    b.Navigation("Installation");

                    b.Navigation("RiskObject");

                    b.Navigation("System");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.MrbImage", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "MrbImageNavigation")
                        .WithOne("MrbImage")
                        .HasForeignKey("AMprover.Data.Entities.AM.MrbImage", "MrbImageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_MrbImageID_MrB");

                    b.Navigation("MrbImageNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexFactor", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexFactOpexData")
                        .WithMany("OpexFactor")
                        .HasForeignKey("OpexFactOpexDataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("FK_OpexFactor_OpexData");

                    b.Navigation("OpexFactOpexData");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexToLcc", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexLccOpexDataId1Navigation")
                        .WithMany("OpexToLccOpexLccOpexDataId1Navigation")
                        .HasForeignKey("OpexLccOpexDataId1")
                        .HasConstraintName("FK_OpexToLCC_OpexData");

                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexLccOpexDataId2Navigation")
                        .WithMany("OpexToLccOpexLccOpexDataId2Navigation")
                        .HasForeignKey("OpexLccOpexDataId2")
                        .HasConstraintName("FK_OpexToLCC_OpexData1");

                    b.HasOne("AMprover.Data.Entities.AM.OpexData", "OpexLccOpexDataId3Navigation")
                        .WithMany("OpexToLccOpexLccOpexDataId3Navigation")
                        .HasForeignKey("OpexLccOpexDataId3")
                        .HasConstraintName("FK_OpexToLCC_OpexData2");

                    b.Navigation("OpexLccOpexDataId1Navigation");

                    b.Navigation("OpexLccOpexDataId2Navigation");

                    b.Navigation("OpexLccOpexDataId3Navigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.PickSi", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.SiLinkFilters", "PckSiLinkFilter")
                        .WithMany("PickSi")
                        .HasForeignKey("PckSiLinkFilterId")
                        .HasConstraintName("FK_PickSI_SiLinkFilters");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "PckSiMrb")
                        .WithMany("PickSi")
                        .HasForeignKey("PckSiMrbId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_PickMSI_MRB");

                    b.HasOne("AMprover.Data.Entities.AM.Si", "PckSiSi")
                        .WithMany("PickSi")
                        .HasForeignKey("PckSiSiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_PickSI_Si");

                    b.Navigation("PckSiLinkFilter");

                    b.Navigation("PckSiMrb");

                    b.Navigation("PckSiSi");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Rams", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.RamsDiagram", "RamsDiagram")
                        .WithMany("Rams")
                        .HasForeignKey("RamsDiagramId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_RAMS_RamsDiagram");

                    b.HasOne("AMprover.Data.Entities.AM.Si", "RamsSi")
                        .WithMany("Rams")
                        .HasForeignKey("RamsSiId")
                        .HasConstraintName("FK_Rams_Si");

                    b.Navigation("RamsDiagram");

                    b.Navigation("RamsSi");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RiskObject", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Fmeca", "RiskObjFmeca")
                        .WithMany("RiskObject")
                        .HasForeignKey("RiskObjFmecaId")
                        .IsRequired()
                        .HasConstraintName("FK_RiskObject_tblFMECAV30");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "RiskObjObject")
                        .WithMany("RiskObject")
                        .HasForeignKey("RiskObjObjectId")
                        .IsRequired()
                        .HasConstraintName("FK_RiskObject_Object");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "RiskObjParentObject")
                        .WithMany("ChildRiskObjects")
                        .HasForeignKey("RiskObjParentObjectId")
                        .HasConstraintName("FK_RiskObject_ParentObject");

                    b.HasOne("AMprover.Data.Entities.AM.Scenario", "RiskObjScenario")
                        .WithMany("RiskObject")
                        .HasForeignKey("RiskObjScenarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_RiskObject_Scenario");

                    b.Navigation("RiskObjFmeca");

                    b.Navigation("RiskObjObject");

                    b.Navigation("RiskObjParentObject");

                    b.Navigation("RiskObjScenario");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Scenario", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Department", "ScenDepartmentNavigation")
                        .WithMany("Scenario")
                        .HasForeignKey("ScenDepartment")
                        .IsRequired()
                        .HasConstraintName("FK_Scenario_Department");

                    b.Navigation("ScenDepartmentNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Si", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupUserDefined", "SiCategoryNavigation")
                        .WithMany("Si")
                        .HasForeignKey("SiCategory")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SiCategoryNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiLinkFilters", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject1")
                        .WithMany("SiLinkFiltersSifChildObject1")
                        .HasForeignKey("SifChildObject1Id")
                        .HasConstraintName("FK_SiLinkFilters_Object1");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject2")
                        .WithMany("SiLinkFiltersSifChildObject2")
                        .HasForeignKey("SifChildObject2Id")
                        .HasConstraintName("FK_SiLinkFilters_Object2");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject3")
                        .WithMany("SiLinkFiltersSifChildObject3")
                        .HasForeignKey("SifChildObject3Id")
                        .HasConstraintName("FK_SiLinkFilters_Object3");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject4")
                        .WithMany("SiLinkFiltersSifChildObject4")
                        .HasForeignKey("SifChildObject4Id")
                        .HasConstraintName("FK_SiLinkFilters_Object4");

                    b.HasOne("AMprover.Data.Entities.AM.Object", "SifChildObject")
                        .WithMany("SiLinkFiltersSifChildObject")
                        .HasForeignKey("SifChildObjectId")
                        .HasConstraintName("FK_SiLinkFilters_Object");

                    b.HasOne("AMprover.Data.Entities.AM.Filters", "SifFilterNavigation")
                        .WithOne("SiLinkFilters")
                        .HasForeignKey("AMprover.Data.Entities.AM.SiLinkFilters", "SifFilterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("SiLinkFilters_SifFilterID");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "SifRisk")
                        .WithMany("SiLinkFilters")
                        .HasForeignKey("SifRiskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("FK_SiLinkFilters_MRB");

                    b.HasOne("AMprover.Data.Entities.AM.RiskObject", "SifRiskObject")
                        .WithMany("SiLinkFilters")
                        .HasForeignKey("SifRiskObjectId")
                        .HasConstraintName("FK_SiLinkFilters_RiskObject");

                    b.HasOne("AMprover.Data.Entities.AM.Task", "SifTask")
                        .WithMany("SiLinkFilters")
                        .HasForeignKey("SifTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("FK_SiLinkFilters_Task");

                    b.Navigation("SifChildObject");

                    b.Navigation("SifChildObject1");

                    b.Navigation("SifChildObject2");

                    b.Navigation("SifChildObject3");

                    b.Navigation("SifChildObject4");

                    b.Navigation("SifFilterNavigation");

                    b.Navigation("SifRisk");

                    b.Navigation("SifRiskObject");

                    b.Navigation("SifTask");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Spare", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "SpareMrb")
                        .WithMany("Spares")
                        .HasForeignKey("SpareMrbId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SpareMrb");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Task", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.Cluster", "TskClusterNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskCluster")
                        .HasConstraintName("FK_Task_Cluster");

                    b.HasOne("AMprover.Data.Entities.AM.CommonTask", "TskCommonAction")
                        .WithMany("Task")
                        .HasForeignKey("TskCommonActionId")
                        .HasConstraintName("FK_Task_CommonTask");

                    b.HasOne("AMprover.Data.Entities.AM.LookupExecutor", "TskExecutorNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskExecutor")
                        .HasConstraintName("FK_Task_LookupExecutor");

                    b.HasOne("AMprover.Data.Entities.AM.LookupInitiator", "TskInitiatorNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskInitiator")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Task_LookupInitiator");

                    b.HasOne("AMprover.Data.Entities.AM.LookupIntervalUnit", "TskIntervalUnitNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskIntervalUnit")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Task_LookupIntervalUnit");

                    b.HasOne("AMprover.Data.Entities.AM.Mrb", "TskMrb")
                        .WithMany("Tasks")
                        .HasForeignKey("TskMrbId");

                    b.HasOne("AMprover.Data.Entities.AM.LookupMxPolicy", "TskMxPolicyNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskMxPolicy")
                        .HasConstraintName("FK_Task_LookupMxPolicy");

                    b.HasOne("AMprover.Data.Entities.AM.Workpackage", "TskWorkpackageNavigation")
                        .WithMany("Task")
                        .HasForeignKey("TskWorkpackage")
                        .HasConstraintName("FK_Task_Workpackage");

                    b.Navigation("TskClusterNavigation");

                    b.Navigation("TskCommonAction");

                    b.Navigation("TskExecutorNavigation");

                    b.Navigation("TskInitiatorNavigation");

                    b.Navigation("TskIntervalUnitNavigation");

                    b.Navigation("TskMrb");

                    b.Navigation("TskMxPolicyNavigation");

                    b.Navigation("TskWorkpackageNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Workpackage", b =>
                {
                    b.HasOne("AMprover.Data.Entities.AM.LookupExecutor", "WpExecutorNavigation")
                        .WithMany("Workpackage")
                        .HasForeignKey("WpExecutor")
                        .HasConstraintName("FK_Workpackage_LookupExecutor");

                    b.HasOne("AMprover.Data.Entities.AM.LookupIntervalUnit", "WpIntervalUnitNavigation")
                        .WithMany("Workpackage")
                        .HasForeignKey("WpIntervalUnit")
                        .IsRequired()
                        .HasConstraintName("FK_Workpackage_LookupIntervalUnit");

                    b.Navigation("WpExecutorNavigation");

                    b.Navigation("WpIntervalUnitNavigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvAspectSets", b =>
                {
                    b.Navigation("BvWeightingModels");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.BvWeightingModels", b =>
                {
                    b.Navigation("BvSiItems");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Cluster", b =>
                {
                    b.Navigation("ClusterChildren");

                    b.Navigation("ClusterCost");

                    b.Navigation("ClusterTaskPlan");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonCost", b =>
                {
                    b.Navigation("ClusterCost");

                    b.Navigation("CommonTaskCost");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.CommonTask", b =>
                {
                    b.Navigation("CommonTaskCost");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Department", b =>
                {
                    b.Navigation("Scenario");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Filters", b =>
                {
                    b.Navigation("FiltersSelectionList");

                    b.Navigation("SiLinkFilters");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Fmeca", b =>
                {
                    b.Navigation("BvWeightingModels");

                    b.Navigation("RiskObject");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lcc", b =>
                {
                    b.Navigation("Details");

                    b.Navigation("LccChildren");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Lccdetail", b =>
                {
                    b.Navigation("EffectDetails");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupExecutor", b =>
                {
                    b.Navigation("CommonTask");

                    b.Navigation("Task");

                    b.Navigation("Workpackage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupFailMode", b =>
                {
                    b.Navigation("Risks");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupInflationGroup", b =>
                {
                    b.Navigation("CommonCost");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupInitiator", b =>
                {
                    b.Navigation("CommonTask");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupIntervalUnit", b =>
                {
                    b.Navigation("CommonTask");

                    b.Navigation("Task");

                    b.Navigation("Workpackage");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupMxPolicy", b =>
                {
                    b.Navigation("CommonTask");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.LookupUserDefined", b =>
                {
                    b.Navigation("Si");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Mrb", b =>
                {
                    b.Navigation("ClusterTaskPlan");

                    b.Navigation("MrbImage");

                    b.Navigation("PickSi");

                    b.Navigation("SiLinkFilters");

                    b.Navigation("Spares");

                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Object", b =>
                {
                    b.Navigation("ChildObjects");

                    b.Navigation("ChildObjects1");

                    b.Navigation("ChildObjects2");

                    b.Navigation("ChildObjects3");

                    b.Navigation("ChildObjects4");

                    b.Navigation("ChildRiskObjects");

                    b.Navigation("MrbChildObjects");

                    b.Navigation("MrbChildObjects1");

                    b.Navigation("MrbChildObjects2");

                    b.Navigation("MrbChildObjects3");

                    b.Navigation("MrbChildObjects4");

                    b.Navigation("RiskObject");

                    b.Navigation("SiLinkFiltersSifChildObject");

                    b.Navigation("SiLinkFiltersSifChildObject1");

                    b.Navigation("SiLinkFiltersSifChildObject2");

                    b.Navigation("SiLinkFiltersSifChildObject3");

                    b.Navigation("SiLinkFiltersSifChildObject4");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.OpexData", b =>
                {
                    b.Navigation("OpexFactor");

                    b.Navigation("OpexToLccOpexLccOpexDataId1Navigation");

                    b.Navigation("OpexToLccOpexLccOpexDataId2Navigation");

                    b.Navigation("OpexToLccOpexLccOpexDataId3Navigation");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RamsDiagram", b =>
                {
                    b.Navigation("Rams");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.RiskObject", b =>
                {
                    b.Navigation("LccItems");

                    b.Navigation("Risks");

                    b.Navigation("SiLinkFilters");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Scenario", b =>
                {
                    b.Navigation("Clusters");

                    b.Navigation("RiskObject");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Si", b =>
                {
                    b.Navigation("BvSiItems");

                    b.Navigation("ClusterTaskPlan");

                    b.Navigation("Lccs");

                    b.Navigation("PickSi");

                    b.Navigation("Rams");

                    b.Navigation("SiCriticalityRanking");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.SiLinkFilters", b =>
                {
                    b.Navigation("PickSi");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Task", b =>
                {
                    b.Navigation("ClusterCost");

                    b.Navigation("ClusterTaskPlan");

                    b.Navigation("SiLinkFilters");
                });

            modelBuilder.Entity("AMprover.Data.Entities.AM.Workpackage", b =>
                {
                    b.Navigation("CommonTask");

                    b.Navigation("Task");
                });
#pragma warning restore 612, 618
        }
    }
}
