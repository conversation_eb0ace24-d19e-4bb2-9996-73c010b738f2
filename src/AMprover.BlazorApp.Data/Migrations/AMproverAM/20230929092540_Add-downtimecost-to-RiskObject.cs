using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class AdddowntimecosttoRiskObject : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AddColumn<decimal>(
                name: "RiskObjDownTimeCost",
                table: "TblRiskObject",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropColumn(
                name: "RiskObjDownTimeCost",
                table: "TblRiskObject");
        }
}