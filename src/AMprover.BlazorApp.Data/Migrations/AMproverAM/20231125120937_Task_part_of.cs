using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb;

public partial class Task_part_of : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            // TaskIds are not enforced previously so it can happen that the reference is not valid any longer. Clean out not existing references.
            migrationBuilder.Sql(
                @"UPDATE [dbo].[TblTask] 
                SET TskPartOf = NULL
                FROM TblTask A
                WHERE A.TskPartOf IS NOT NULL AND NOT EXISTS (SELECT TskID FROM [TblTask] B WHERE B.TskID = A.TskPartOf)
                  GO");
            
            migrationBuilder.CreateIndex(
                name: "IX_TblTask_TskPartOf",
                table: "TblTask",
                column: "TskPartOf");
            
            migrationBuilder.AddForeignKey(
                name: "FK_TblTask_TblTask_TskPartOf",
                table: "TblTask",
                column: "TskPartOf",
                principalTable: "TblTask",
                principalColumn: "TskID");
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropForeignKey(
                name: "FK_TblTask_TblTask_TskPartOf",
                table: "TblTask");

            migrationBuilder.DropIndex(
                name: "IX_TblTask_TskPartOf",
                table: "TblTask");
        }
}