using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class AddFiltersToClusterReport : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            //Cluster report SP
            var sqlClusterReport = @"
	 	    	IF OBJECT_ID('GetClusterReport', 'P') IS NOT NULL
                DROP PROC GetClusterReport
                GO
     
                CREATE PROCEDURE [dbo].[GetClusterReport]
                @FilterChildObject0 int = NULL,
                @FilterChildObject1 int = NULL,
                @FilterChildObject2 int = NULL,
                @FilterScenario int = NULL
                AS
                BEGIN
                    SET NOCOUNT ON;
				SELECT 
					Cluster.ClustID as ClustId
					,Cluster.ClustName
					,Cluster.ClustDescription
					,Cluster.ClustLevel
					,Cluster.ClustPartOf
					,Cluster.ClustInterval
					,ClustIntervalUnit
					,ClustInitiator
					,ClustExecutor
					,ClustEstTaskCosts
					,ClustTaskCosts
					,ClustSharedCosts
					,ClustDisciplineCosts
					,ClustMaterialCosts
					,ClustEnergyCosts
					,ClustToolCosts
					,ClustTotalCmnCost
					,ClustDownTime
					,ClustDuration
					,ClustStatus
					,ClustShortKey
					,ClustRemark
					,ClustResponsible
					,ClustSecondValues
					,(SELECT TblCommonCost.CmnCostDescription FROM TblCommonCost WHERE TblCommonCost.CmnCostID = TblClusterCost.ClcCommonCostID) AS ClustCostDescr
					,(SELECT TblCommonCost.CmnCostType FROM TblCommonCost WHERE TblCommonCost.CmnCostID = TblClusterCost.ClcCommonCostID) AS CmnCostType
					,TblClusterCost.ClcID as ClcId
					,TblClusterCost.ClcTaskID AS ClustCostTaskId
					,TblClusterCost.ClcQuantity AS ClustCostQuantity
					,TblClusterCost.ClcUnits AS ClustCostUnits
					,TblClusterCost.ClcPrice AS ClustCostPrice
					,TblClusterCost.ClcCost AS ClustCostCost
					,TblClusterCost.ClcRemarks AS ClustCostRemarks
					,TskID as TskId
					,TskName
					,TskGeneralDescription
					,TskRemark
					,TskDescription
					,TskInterval
					,TskIntervalUnit
					,TskWorkPackage
					,TskPolicy
					,TskExecutor
					,TskInitiator
					,TskMrbID as TskMrbId
					,TskCosts
					,TskDuration
					,TskDownTime
					,TskType
					,TskSortOrder
					,TskRemoved
					,TskDerived
					,TskPermit
					,TblClusterTaskCost.ClcTaskID AS TskCostTskId
					,TblClusterTaskCost.ClcUnits AS TskCostUnits
					,TblClusterTaskCost.ClcQuantity AS TskCostQuantity
					,TblClusterTaskCost.ClcPrice AS TskCostPrice
					,TblClusterTaskCost.ClcCost AS TskCostCost
					,(SELECT TblCommonCost.CmnCostDescription FROM TblCommonCost WHERE TblCommonCost.CmnCostID = TblClusterTaskCost.ClcCommonCostID) AS TskCostDescription
					,Scenario
					,RiskObject
					,RiskObjectDesc
					,RiskObjName
				    ,MrbObject0
					,MrbObject2
					,MrbObject3
					,MrbObject4
					,MrbID as MrbId
					,MrbFailureCause
					,MrbFailureConsequences
					,ParentClusterName
					,CASE 
						WHEN ClustLevel = 0 THEN ClustName
						WHEN ClustLevel = 1 THEN ParentClusterName
						WHEN ClustLevel = 2 THEN (SELECT clust0.ClustName FROM TblCluster clust2 INNER JOIN TblCluster clust1 ON clust2.ClustPartOf = clust1.clustid INNER JOIN TblCluster clust0 on clust0.ClustID = clust1.ClustPartOf WHERE Clust2.ClustID = Cluster.ClustID)
						WHEN ClustLevel = 3 THEN (SELECT Clust0.ClustName FROM TblCluster Clust3 INNER JOIN TblCluster clust2 ON Clust3.clustpartof = clust2.clustid INNER JOIN TblCluster clust1 ON clust2.ClustPartOf = clust1.clustid INNER JOIN TblCluster clust0 on clust0.ClustID = clust1.ClustPartOf WHERE Clust3.clustId = cluster.ClustID)
						WHEN ClustLevel > 3 THEN 'unsupported'
					END AS ClustName0
					,CASE 
						WHEN ClustLevel = 0 THEN NULL
						WHEN ClustLevel = 1 THEN ClustName
						WHEN ClustLevel = 2 THEN ParentClusterName
						WHEN ClustLevel = 3 THEN (SELECT clust0.ClustName FROM TblCluster clust2 INNER JOIN TblCluster clust1 ON clust2.ClustPartOf = clust1.clustid INNER JOIN TblCluster clust0 on clust0.ClustID = clust1.ClustPartOf WHERE Clust2.ClustID = Cluster.ClustID)
						WHEN ClustLevel > 3 THEN 'unsupported'
					END AS ClustName1
					,CASE 
						WHEN ClustLevel = 0 THEN NULL
						WHEN ClustLevel = 1 THEN NULL
						WHEN ClustLevel = 2 THEN ClustName
						WHEN ClustLevel = 3 THEN ParentClusterName
						WHEN ClustLevel > 3 THEN 'unsupported'
					END AS ClustName2
					,CASE 
						WHEN ClustLevel = 0 THEN NULL
						WHEN ClustLevel = 1 THEN NULL
						WHEN ClustLevel = 2 THEN NULL
						WHEN ClustLevel = 3 THEN ClustName
						WHEN ClustLevel > 3 THEN 'unsupported'
					END AS ClustName3
				    ,ChildObjectId
				    ,ChildObject1Id
				    ,ChildObject2Id
				FROM		
				(
				SELECT TblCluster.ClustID
						,TblCluster.ClustName
						,TblCluster.ClustDescription
						,TblCluster.ClustLevel
						,TblCluster.ClustPartOf
						,TblCluster.ClustInterval
						,IntUnitName AS ClustIntervalUnit
						,InitiatorName AS ClustInitiator
						,ExecutorName AS ClustExecutor
						,TblCluster.ClustEstTaskCosts
						,TblCluster.ClustTaskCosts
						,TblCluster.ClustSharedCosts
						,TblCluster.ClustDisciplineCosts
						,TblCluster.ClustMaterialCosts
						,TblCluster.ClustEnergyCosts
						,TblCluster.ClustToolCosts
						,TblCluster.ClustTotalCmnCost
						,TblCluster.ClustDownTime
						,TblCluster.ClustDuration
						,TblCluster.ClustStatus
						,TblCluster.ClustShortKey
						,TblCluster.ClustRemark
						,TblCluster.ClustResponsible
						,TblCluster.ClustSecondValues
						,Parent.ClustName As ParentClusterName
					FROM TblCluster LEFT JOIN TblCluster AS Parent ON TblCluster.ClustPartOf = Parent.ClustID
					LEFT JOIN LookupIntervalUnit ON LookupIntervalUnit.IntUnitID = TblCluster.ClustIntervalUnit
					LEFT JOIN LookupInitiator ON LookupInitiator.InitiatorID = TblCluster.ClustInitiator
					LEFT JOIN LookupExecutor ON LookupExecutor.ExecutorID = TblCluster.ClustExecutor
					WHERE (@FilterScenario IS NULL OR TblCluster.ClustScenarioID = @FilterScenario)
				) Cluster
				LEFT JOIN(
					SELECT
						TblTask.TskID
						,TblTask.TskName
						,TblTask.TskGeneralDescription
						,TblTask.TskRemark
						,TblTask.TskDescription
						,TblTask.TskInterval
						,IntUnitName AS TskIntervalUnit
						,WpName AS TskWorkPackage
						,PolName AS TskPolicy
						,ExecutorName AS TskExecutor
						,InitiatorName AS TskInitiator
						,TblTask.TskMrbID
						,TblTask.TskCosts
						,TblTask.TskDuration
						,TblTask.TskDownTime
						,TblTask.TskType
						,TblTask.TskSortOrder
						,TblTask.TskRemoved
						,TblTask.TskDerived
						,TblTask.TskPermit
						,TblTask.TskCluster
					FROM TblTask
					LEFT JOIN LookupIntervalUnit ON LookupIntervalUnit.IntUnitID = TblTask.TskIntervalUnit
					LEFT JOIN TblWorkpackage ON TblWorkpackage.WpID = TblTask.TskWorkpackage
					LEFT JOIN LookupExecutor ON LookupExecutor.ExecutorID = TblTask.TskExecutor
					LEFT JOIN LookupInitiator ON LookupInitiator.InitiatorID = TblTask.TskInitiator
					LEFT JOIN LookupMxPolicy ON LookupMxPolicy.PolID = TblTask.TskMxPolicy
					) AS Task ON Cluster.ClustID = Task.TskCluster
				LEFT JOIN TblClusterCost ON TblClusterCost.ClcClusterID = Cluster.ClustID
				LEFT JOIN TblClusterCost TblClusterTaskCost ON TblClusterTaskCost.ClcTaskID = Task.TskID
				LEFT JOIN
				(
					SELECT 
						MrbID
						,MrbName
						,TblMRB.MrbFailureCause
						,TblMRB.MrbFailureConsequences
						,RiskObjName
				        , MrbChildObject as ChildObjectId
				        , MrbChildObject1 as ChildObject1Id
				        , MrbChildObject2 as ChildObject2Id
						,(SELECT TblObject.ObjDescription FROM TblObject WHERE TblObject.ObjID = TblRiskObject.RiskObjObjectID) AS RiskObjectDesc
						,(SELECT ScenName FROM TblScenario WHERE TblScenario.scenId = TblRiskObject.RiskObjScenarioID) AS Scenario
						,(Select TblObject.ObjName FROM TblObject WHERE TblObject.ObjID = TblRiskObject.RiskObjObjectID) AS RiskObject
						,(SELECT ObjName FROM TblObject WHERE TblObject.ObjID = TblRiskObject.RiskObjParentObjectID) AS MrbObject0
						,(SELECT ObjName FROM TblObject WHERE TblObject.ObjId = TblMrb.MrbChildObject2) AS MrbObject2
						,(SELECT ObjName FROM TblObject WHERE TblObject.ObjId = TblMrb.MrbChildObject3) AS MrbObject3
						,(SELECT ObjName FROM TblObject WHERE TblObject.ObjId = TblMrb.MrbChildObject4) AS MrbObject4
						FROM TblMrb INNER JOIN TblRiskObject ON TblMRB.MrbRiskObject = TblRiskObject.RiskObjID
				) AS Mrb
					ON Task.TskMrbID = MRB.MrbID
                    WHERE (@FilterChildObject0 IS NULL OR ChildObjectId = @FilterChildObject0)
                    AND (@FilterChildObject1 IS NULL OR ChildObject1Id = @FilterChildObject1)
                    AND (@FilterChildObject2 IS NULL OR ChildObject2Id = @FilterChildObject2)
                END";

            migrationBuilder.Sql(sqlClusterReport);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        }
}