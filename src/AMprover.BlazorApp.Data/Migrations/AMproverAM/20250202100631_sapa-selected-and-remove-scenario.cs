using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AssetManagementDb;

public partial class sapaselectedandremovescenario : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            // Manual changes
            migrationBuilder.Sql("delete from tblSapa");
            migrationBuilder.Sql("delete from tblSapaCollection");

            migrationBuilder.DropForeignKey(
                name: "FK_TblSapaCollection_TblScenario_ScenarioScenId",
                table: "TblSapaCollection");

            migrationBuilder.DropIndex(
                name: "IX_TblSapaCollection_ScenarioScenId",
                table: "TblSapaCollection");

            migrationBuilder.DropColumn(
                name: "SapaCollScenarioId",
                table: "TblSapaCollection");

            migrationBuilder.DropColumn(
                name: "ScenarioScenId",
                table: "TblSapaCollection");

            migrationBuilder.DropColumn(
                name: "SapaScenarioId",
                table: "TblSapa");

            migrationBuilder.AlterColumn<int>(
                name: "SapaCollRiskObjId",
                table: "TblSapaCollection",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "SapaCollSelected",
                table: "TblSapaCollection",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<int>(
                name: "SapaRiskObjId",
                table: "TblSapa",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropColumn(
                name: "SapaCollSelected",
                table: "TblSapaCollection");

            migrationBuilder.AlterColumn<int>(
                name: "SapaCollRiskObjId",
                table: "TblSapaCollection",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<int>(
                name: "SapaCollScenarioId",
                table: "TblSapaCollection",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ScenarioScenId",
                table: "TblSapaCollection",
                type: "int",
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "SapaRiskObjId",
                table: "TblSapa",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<int>(
                name: "SapaScenarioId",
                table: "TblSapa",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TblSapaCollection_ScenarioScenId",
                table: "TblSapaCollection",
                column: "ScenarioScenId");

            migrationBuilder.AddForeignKey(
                name: "FK_TblSapaCollection_TblScenario_ScenarioScenId",
                table: "TblSapaCollection",
                column: "ScenarioScenId",
                principalTable: "TblScenario",
                principalColumn: "ScenID");
        }
}