using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class addfmecaselectionsonrisk : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AddColumn<int>(
                name: "MrbFmecaMtbfAfter",
                table: "TblMRB",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MrbFmecaMtbfBefore",
                table: "TblMRB",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MrbFmecaMtbfPmo",
                table: "TblMRB",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MrbFmecaSelectionAfter",
                table: "TblMRB",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MrbFmecaSelectionBefore",
                table: "TblMRB",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MrbFmecaSelectionPmo",
                table: "TblMRB",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DropColumn(
                name: "MrbFmecaMtbfAfter",
                table: "TblMRB");

            migrationBuilder.DropColumn(
                name: "MrbFmecaMtbfBefore",
                table: "TblMRB");

            migrationBuilder.DropColumn(
                name: "MrbFmecaMtbfPmo",
                table: "TblMRB");

            migrationBuilder.DropColumn(
                name: "MrbFmecaSelectionAfter",
                table: "TblMRB");

            migrationBuilder.DropColumn(
                name: "MrbFmecaSelectionBefore",
                table: "TblMRB");

            migrationBuilder.DropColumn(
                name: "MrbFmecaSelectionPmo",
                table: "TblMRB");
        }
}