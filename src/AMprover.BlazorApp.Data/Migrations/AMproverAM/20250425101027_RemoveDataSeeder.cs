using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace AMprover.Data.Migrations.MainDb;

/// <inheritdoc />
public partial class RemoveDataSeeder : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.DeleteData(
                table: "AspNetUserRoles",
                keyColumns: ["RoleId", "UserId"],
                keyValues: ["1461abf6-0f98-41ea-9365-9cbb52127abe", "1461abf6-0f98-41ea-9365-9cbb52127abe"]);

            migrationBuilder.DeleteData(
                table: "PortfolioAssignments",
                keyColumns: ["PortfolioId", "UserId"],
                keyValues: [1, "1461abf6-0f98-41ea-9365-9cbb52127abe"]);

            migrationBuilder.DeleteData(
                table: "PortfolioAssignments",
                keyColumns: ["PortfolioId", "UserId"],
                keyValues: [2, "1461abf6-0f98-41ea-9365-9cbb52127abe"]);

            migrationBuilder.DeleteData(
                table: "PortfolioAssignments",
                keyColumns: ["PortfolioId", "UserId"],
                keyValues: [3, "1461abf6-0f98-41ea-9365-9cbb52127abe"]);

            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "1461abf6-0f98-41ea-9365-9cbb52127abe");

            migrationBuilder.DeleteData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "1461abf6-0f98-41ea-9365-9cbb52127abe");

            migrationBuilder.DeleteData(
                table: "Portfolios",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Portfolios",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Portfolios",
                keyColumn: "Id",
                keyValue: 3);
        }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.InsertData(
                table: "AspNetRoles",
                columns: ["Id", "ConcurrencyStamp", "Name", "NormalizedName"],
                values: ["1461abf6-0f98-41ea-9365-9cbb52127abe", "f8a81495-fd9f-4c1a-be0d-3c1a95c56e60", "Administrators", "ADMINISTRATORS"
                ]);

            migrationBuilder.InsertData(
                table: "AspNetUsers",
                columns: ["Id", "AccessFailedCount", "Company", "ConcurrencyStamp", "Department", "Email", "EmailConfirmed", "LockoutEnabled", "LockoutEnd", "Name", "NormalizedEmail", "NormalizedUserName", "PasswordHash", "PhoneNumber", "PhoneNumberConfirmed", "SecurityStamp", "TwoFactorEnabled", "UserName"
                ],
                values: ["1461abf6-0f98-41ea-9365-9cbb52127abe", 0, null, "567904f6-4ff2-4333-9fc2-b756c382563d", null, "<EMAIL>", true, false, null, "Support Darestep", "<EMAIL>", "<EMAIL>", "AQAAAAEAACcQAAAAEKMFjr+NQMrzb0+2A8+F6X6gAN56PQcu5FIlhsJxhzFDD2OD9lXfdztjyARPStwxmg==", null, false, "", false, "<EMAIL>"
                ]);

            migrationBuilder.InsertData(
                table: "Portfolios",
                columns: ["Id", "CreatedOn", "DatabaseName", "Name"],
                values: new object[,]
                {
                    { 1, new DateTime(2023, 7, 27, 19, 16, 13, 267, DateTimeKind.Local).AddTicks(4990), "Mainnovation_AMprover_Dev_Demo", "AMprover 5 Demo" },
                    { 2, new DateTime(2023, 7, 27, 19, 16, 13, 267, DateTimeKind.Local).AddTicks(5000), "Mainnovation_AMprover_Dev_Waterschap_Noorderzijlvest", "Waterschap Noorderzijlvest" },
                    { 3, new DateTime(2023, 7, 27, 19, 16, 13, 267, DateTimeKind.Local).AddTicks(5010), "Mainnovation_AMprover_Dev_Training", "AMprover 5 Training" }
                });

            migrationBuilder.InsertData(
                table: "AspNetUserRoles",
                columns: ["RoleId", "UserId"],
                values: ["1461abf6-0f98-41ea-9365-9cbb52127abe", "1461abf6-0f98-41ea-9365-9cbb52127abe"]);

            migrationBuilder.InsertData(
                table: "PortfolioAssignments",
                columns: ["PortfolioId", "UserId"],
                values: new object[,]
                {
                    { 1, "1461abf6-0f98-41ea-9365-9cbb52127abe" },
                    { 2, "1461abf6-0f98-41ea-9365-9cbb52127abe" },
                    { 3, "1461abf6-0f98-41ea-9365-9cbb52127abe" }
                });
        }
}