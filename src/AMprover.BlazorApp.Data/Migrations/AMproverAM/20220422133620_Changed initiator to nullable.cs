using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class Changedinitiatortonullable : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AlterColumn<int>(
                name: "TskInitiator",
                table: "TblTask",
                type: "int",
                nullable: true,
                comment: "ID of the initiator of the task (FK to LookupInitiator)",
                oldClrType: typeof(int),
                oldType: "int",
                oldComment: "ID of the initiator of the task (FK to LookupInitiator)");
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AlterColumn<int>(
                name: "TskInitiator",
                table: "TblTask",
                type: "int",
                nullable: false,
                defaultValue: 0,
                comment: "ID of the initiator of the task (FK to LookupInitiator)",
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldComment: "ID of the initiator of the task (FK to LookupInitiator)");
        }
}