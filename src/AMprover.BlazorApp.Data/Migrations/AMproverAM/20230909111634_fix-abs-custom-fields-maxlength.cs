using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class fixabscustomfieldsmaxlength : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AlterColumn<string>(
                name: "SiSupplierTextField3",
                table: "TblSi",
                type: "varchar(255)",
                unicode: false,
                maxLength: 255,
                nullable: true,
                comment: "Supplier text field (not used) (!)",
                oldClrType: typeof(string),
                oldType: "varchar(40)",
                oldUnicode: false,
                oldMaxLength: 40,
                oldNullable: true,
                oldComment: "Supplier text field (not used) (!)");

            migrationBuilder.AlterColumn<string>(
                name: "SiSupplierTextField2",
                table: "TblSi",
                type: "varchar(255)",
                unicode: false,
                maxLength: 255,
                nullable: true,
                comment: "Supplier text field (not used) (!)",
                oldClrType: typeof(string),
                oldType: "varchar(40)",
                oldUnicode: false,
                oldMaxLength: 40,
                oldNullable: true,
                oldComment: "Supplier text field (not used) (!)");

            migrationBuilder.AlterColumn<string>(
                name: "SiSupplierTextField1",
                table: "TblSi",
                type: "varchar(255)",
                unicode: false,
                maxLength: 255,
                nullable: true,
                comment: "Supplier of the significant item.",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Supplier of the significant item.");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField7",
                table: "TblSi",
                type: "varchar(255)",
                unicode: false,
                maxLength: 255,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(40)",
                oldUnicode: false,
                oldMaxLength: 40,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField6",
                table: "TblSi",
                type: "varchar(255)",
                unicode: false,
                maxLength: 255,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(40)",
                oldUnicode: false,
                oldMaxLength: 40,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField5",
                table: "TblSi",
                type: "varchar(255)",
                unicode: false,
                maxLength: 255,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(40)",
                oldUnicode: false,
                oldMaxLength: 40,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField4",
                table: "TblSi",
                type: "varchar(255)",
                unicode: false,
                maxLength: 255,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(40)",
                oldUnicode: false,
                oldMaxLength: 40,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField3",
                table: "TblSi",
                type: "varchar(255)",
                unicode: false,
                maxLength: 255,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(40)",
                oldUnicode: false,
                oldMaxLength: 40,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField2",
                table: "TblSi",
                type: "varchar(255)",
                unicode: false,
                maxLength: 255,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(40)",
                oldUnicode: false,
                oldMaxLength: 40,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField1",
                table: "TblSi",
                type: "varchar(255)",
                unicode: false,
                maxLength: 255,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(40)",
                oldUnicode: false,
                oldMaxLength: 40,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AlterColumn<string>(
                name: "SiSupplierTextField3",
                table: "TblSi",
                type: "varchar(40)",
                unicode: false,
                maxLength: 40,
                nullable: true,
                comment: "Supplier text field (not used) (!)",
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldUnicode: false,
                oldMaxLength: 255,
                oldNullable: true,
                oldComment: "Supplier text field (not used) (!)");

            migrationBuilder.AlterColumn<string>(
                name: "SiSupplierTextField2",
                table: "TblSi",
                type: "varchar(40)",
                unicode: false,
                maxLength: 40,
                nullable: true,
                comment: "Supplier text field (not used) (!)",
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldUnicode: false,
                oldMaxLength: 255,
                oldNullable: true,
                oldComment: "Supplier text field (not used) (!)");

            migrationBuilder.AlterColumn<string>(
                name: "SiSupplierTextField1",
                table: "TblSi",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Supplier of the significant item.",
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldUnicode: false,
                oldMaxLength: 255,
                oldNullable: true,
                oldComment: "Supplier of the significant item.");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField7",
                table: "TblSi",
                type: "varchar(40)",
                unicode: false,
                maxLength: 40,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldUnicode: false,
                oldMaxLength: 255,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField6",
                table: "TblSi",
                type: "varchar(40)",
                unicode: false,
                maxLength: 40,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldUnicode: false,
                oldMaxLength: 255,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField5",
                table: "TblSi",
                type: "varchar(40)",
                unicode: false,
                maxLength: 40,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldUnicode: false,
                oldMaxLength: 255,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField4",
                table: "TblSi",
                type: "varchar(40)",
                unicode: false,
                maxLength: 40,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldUnicode: false,
                oldMaxLength: 255,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField3",
                table: "TblSi",
                type: "varchar(40)",
                unicode: false,
                maxLength: 40,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldUnicode: false,
                oldMaxLength: 255,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField2",
                table: "TblSi",
                type: "varchar(40)",
                unicode: false,
                maxLength: 40,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldUnicode: false,
                oldMaxLength: 255,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");

            migrationBuilder.AlterColumn<string>(
                name: "SiMiscTextField1",
                table: "TblSi",
                type: "varchar(40)",
                unicode: false,
                maxLength: 40,
                nullable: true,
                comment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ",
                oldClrType: typeof(string),
                oldType: "varchar(255)",
                oldUnicode: false,
                oldMaxLength: 255,
                oldNullable: true,
                oldComment: "Custom attribute field for this significant item. There are 7 fields that can be redefined by the user, which adds different custom fields to each significant item. ");
        }
}