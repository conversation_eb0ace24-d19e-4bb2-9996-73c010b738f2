using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AMprover.Data.Migrations.AMproverAM;

public partial class ChangeUserFieldsLengthContext : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AlterColumn<string>(
                name: "UserModifiedBy",
                table: "TblUser",
                type: "varchar(max)",
                unicode: false,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "UserInitiatedBy",
                table: "TblUser",
                type: "varchar(max)",
                unicode: false,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Created by user");

            migrationBuilder.AlterColumn<string>(
                name: "TskModifiedBy",
                table: "TblTask",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "TskInitiatedBy",
                table: "TblTask",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Created by user");

            migrationBuilder.AlterColumn<string>(
                name: "SiModifiedBy",
                table: "TblSi",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "ScenModifiedBy",
                table: "TblScenario",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "ScenInitiatedBy",
                table: "TblScenario",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Created by user");

            migrationBuilder.AlterColumn<string>(
                name: "RiskObjModifiedBy",
                table: "TblRiskObject",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "ObjModifiedBy",
                table: "TblObject",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "MrbModifiedBy",
                table: "TblMRB",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "MrbInitiatedBy",
                table: "TblMRB",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Created by this user with this username",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Created by this user with this username");

            migrationBuilder.AlterColumn<string>(
                name: "LccModifiedBy",
                table: "TblLCC",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "CmnTaskModifiedBy",
                table: "TblCommonTask",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "CmnTaskInitiatedBy",
                table: "TblCommonTask",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Username of person that created this cluster",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Username of person that created this cluster");

            migrationBuilder.AlterColumn<string>(
                name: "CmnCostModifiedBy",
                table: "TblCommonCost",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "ClustModifiedBy",
                table: "TblCluster",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "ClustInitiatedBy",
                table: "TblCluster",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Username of person that created this cluster",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Username of person that created this cluster");

            migrationBuilder.AlterColumn<string>(
                name: "BvModifiedBy",
                table: "TblBvSiItems",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BvRelModifiedBy",
                table: "TblBvRelevanceSets",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BvAspModifiedBy",
                table: "TblBvAspectSets",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "SiStatModifiedBy",
                table: "TblSiStatistics",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "RamsDgModifiedBy",
                table: "TblRamsDiagram",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "RamsDgInitiatedBy",
                table: "TblRamsDiagram",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Created by user");

            migrationBuilder.AlterColumn<string>(
                name: "RamsModifiedBy",
                table: "TblRams",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "RamsInitiatedBy",
                table: "TblRams",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Created by user with this username",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Created by user with this username");

            migrationBuilder.AlterColumn<string>(
                name: "PrioBudModifiedBy",
                table: "TblPriorityBudget",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "PrioBudInitiatedBy",
                table: "TblPriorityBudget",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Created by user with this username",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Created by user with this username");

            migrationBuilder.AlterColumn<string>(
                name: "PrioModifiedBy",
                table: "TblPriority",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "PrioInitiatedBy",
                table: "TblPriority",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Created by user with this username",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Created by user with this username");

            migrationBuilder.AlterColumn<string>(
                name: "OpexFactModifiedBy",
                table: "TblOpexFactor",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "OpexDataModifiedBy",
                table: "TblOpexData",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "UserDefinedModifiedBy",
                table: "LookupUserDefined",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "AmsModifiedBy",
                table: "LookupSettings",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "AmsInitiatedBy",
                table: "LookupSettings",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Created by user");

            migrationBuilder.AlterColumn<string>(
                name: "IntUnitModifiedBy",
                table: "LookupIntervalUnit",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "InflModifiedBy",
                table: "LookupInflationGroup",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Last modification of this record was made by this user",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Last modification of this record was made by this user");

            migrationBuilder.AlterColumn<string>(
                name: "FailModifiedBy",
                table: "LookupFailMode",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "LogUserID",
                table: "TblEditLog",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                comment: "Name of the user who performed the modification (not an ID!)",
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true,
                oldComment: "Name of the user who performed the modification (not an ID!)");

            migrationBuilder.AlterColumn<string>(
                name: "BvModelModifiedBy",
                table: "TblBvWeightingModels",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PckSiModifiedBy",
                table: "Amprover3_TblPickSI",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PckSiInitiatedBy",
                table: "Amprover3_TblPickSI",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "LogUserID",
                table: "Amprover3_TblEditLog",
                type: "varchar(50)",
                unicode: false,
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(30)",
                oldUnicode: false,
                oldMaxLength: 30,
                oldNullable: true);
        }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
            migrationBuilder.AlterColumn<string>(
                name: "UserModifiedBy",
                table: "User",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(max)",
                oldUnicode: false,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "UserInitiatedBy",
                table: "User",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(max)",
                oldUnicode: false,
                oldNullable: true,
                oldComment: "Created by user");

            migrationBuilder.AlterColumn<string>(
                name: "TskModifiedBy",
                table: "TblTask",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "TskInitiatedBy",
                table: "TblTask",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Created by user");

            migrationBuilder.AlterColumn<string>(
                name: "SiModifiedBy",
                table: "TblSi",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "ScenModifiedBy",
                table: "TblScenario",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "ScenInitiatedBy",
                table: "TblScenario",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Created by user");

            migrationBuilder.AlterColumn<string>(
                name: "RiskObjModifiedBy",
                table: "TblRiskObject",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "ObjModifiedBy",
                table: "TblObject",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "MrbModifiedBy",
                table: "TblMRB",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "MrbInitiatedBy",
                table: "TblMRB",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Created by this user with this username",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Created by this user with this username");

            migrationBuilder.AlterColumn<string>(
                name: "LccModifiedBy",
                table: "TblLCC",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "CmnTaskModifiedBy",
                table: "TblCommonTask",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "CmnTaskInitiatedBy",
                table: "TblCommonTask",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Username of person that created this cluster",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Username of person that created this cluster");

            migrationBuilder.AlterColumn<string>(
                name: "CmnCostModifiedBy",
                table: "TblCommonCost",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "ClustModifiedBy",
                table: "TblCluster",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "ClustInitiatedBy",
                table: "TblCluster",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Username of person that created this cluster",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Username of person that created this cluster");

            migrationBuilder.AlterColumn<string>(
                name: "BvModifiedBy",
                table: "TblBvSiItems",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BvRelModifiedBy",
                table: "TblBvRelevanceSets",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "BvAspModifiedBy",
                table: "TblBvAspectSets",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "SiStatModifiedBy",
                table: "SiStatistics",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "RamsDgModifiedBy",
                table: "RamsDiagram",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "RamsDgInitiatedBy",
                table: "RamsDiagram",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Created by user");

            migrationBuilder.AlterColumn<string>(
                name: "RamsModifiedBy",
                table: "Rams",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "RamsInitiatedBy",
                table: "TblRams",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Created by user with this username",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Created by user with this username");

            migrationBuilder.AlterColumn<string>(
                name: "PrioBudModifiedBy",
                table: "TblPriorityBudget",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "PrioBudInitiatedBy",
                table: "TblPriorityBudget",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Created by user with this username",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Created by user with this username");

            migrationBuilder.AlterColumn<string>(
                name: "PrioModifiedBy",
                table: "Priority",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "PrioInitiatedBy",
                table: "Priority",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Created by user with this username",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Created by user with this username");

            migrationBuilder.AlterColumn<string>(
                name: "OpexFactModifiedBy",
                table: "OpexFactor",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "OpexDataModifiedBy",
                table: "OpexData",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User name of person that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User name of person that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "UserDefinedModifiedBy",
                table: "LookupUserDefined",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "AmsModifiedBy",
                table: "LookupSettings",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "AmsInitiatedBy",
                table: "LookupSettings",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Created by user",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Created by user");

            migrationBuilder.AlterColumn<string>(
                name: "IntUnitModifiedBy",
                table: "LookupIntervalUnit",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "InflModifiedBy",
                table: "LookupInflationGroup",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Last modification of this record was made by this user",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Last modification of this record was made by this user");

            migrationBuilder.AlterColumn<string>(
                name: "FailModifiedBy",
                table: "LookupFailMode",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "User that made the last modification to this record",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "User that made the last modification to this record");

            migrationBuilder.AlterColumn<string>(
                name: "LogUserID",
                table: "EditLog",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                comment: "Name of the user who performed the modification (not an ID!)",
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true,
                oldComment: "Name of the user who performed the modification (not an ID!)");

            migrationBuilder.AlterColumn<string>(
                name: "BvModelModifiedBy",
                table: "BvWeightingModels",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PckSiModifiedBy",
                table: "Amprover3_PickSI",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PckSiInitiatedBy",
                table: "Amprover3_PickSI",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "LogUserID",
                table: "Amprover3_EditLog",
                type: "varchar(30)",
                unicode: false,
                maxLength: 30,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(50)",
                oldUnicode: false,
                oldMaxLength: 50,
                oldNullable: true);
        }
}