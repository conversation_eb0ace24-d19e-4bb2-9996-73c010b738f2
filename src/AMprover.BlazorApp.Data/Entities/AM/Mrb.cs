using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// Contains the data that defines risks, and their relations to other risks. 
/// It also contains everything needed to build the risk matrix and the monetary before/after values. 
/// Risks can be defined within the Risk Analysis module.
/// </summary>
[Table("TblMRB")]
public class Mrb
{
    /// <summary>
    /// Name describing the risk
    /// </summary>
    [Required]
    [StringLength(50)]
    [Unicode(false)]
    public string MrbName { get; set; }

    /// <summary>
    /// ID of the risk object the risk belongs to (FK to RiskObject)
    /// </summary>
    public int MrbRiskObject { get; set; }

    /// <summary>
    /// Navigation property for the risk object
    /// </summary>
    [ForeignKey("MrbRiskObject")]
    public virtual RiskObject RiskObject { get; set; }

    /// <summary>
    /// ID of the object the risk belongs to (FK to Object)
    /// </summary>
    public int? MrbChildObject { get; set; }

    /// <summary>
    /// Navigation property for the child object
    /// </summary>
    [ForeignKey("MrbChildObject")]
    [InverseProperty("MrbChildObjects")]
    public virtual Object ChildObject { get; set; }

    /// <summary>
    /// ID of the object (level 1) the risk belongs to (FK to Object)
    /// </summary>
    public int? MrbChildObject1 { get; set; }

    /// <summary>
    /// Navigation property for the installation object
    /// </summary>
    [ForeignKey("MrbChildObject1")]
    [InverseProperty("MrbChildObjects1")]
    public virtual Object Installation { get; set; }

    /// <summary>
    /// ID of the object (level 2) the risk belongs to (FK to Object)
    /// </summary>
    public int? MrbChildObject2 { get; set; }

    /// <summary>
    /// Navigation property for the system object
    /// </summary>
    [ForeignKey("MrbChildObject2")]
    [InverseProperty("MrbChildObjects2")]
    public virtual Object System { get; set; }

    /// <summary>
    /// ID of the object (level 3) the risk belongs to (FK to Object)
    /// </summary>
    public int? MrbChildObject3 { get; set; }

    /// <summary>
    /// Navigation property for the component object
    /// </summary>
    [ForeignKey("MrbChildObject3")]
    [InverseProperty("MrbChildObjects3")]
    public virtual Object Component { get; set; }

    /// <summary>
    /// ID of the object (level 4) the risk belongs to (FK to Object)
    /// </summary>
    public int? MrbChildObject4 { get; set; }

    /// <summary>
    /// Navigation property for the assembly object
    /// </summary>
    [ForeignKey("MrbChildObject4")]
    [InverseProperty("MrbChildObjects4")]
    public virtual Object Assembly { get; set; }

    /// <summary>
    /// Description of the risk, contains the possible effects of the risk (?)
    /// </summary>
    [Unicode(false)]
    public string MrbDescription { get; set; }

    /// <summary>
    /// ID of the failure mode used for the risk (FK to LookupFailMode)
    /// </summary>
    public int? MrbFailureMode { get; set; }

    /// <summary>
    /// Navigation property for the failure mode
    /// </summary>
    [ForeignKey("MrbFailureMode")]
    public virtual LookupFailMode FailureMode { get; set; }

    /// <summary>
    /// Cause of the risk (does not seem to be used in the AMprover software !)
    /// </summary>
    [Unicode(false)]
    public string MrbFailureCause { get; set; }

    /// <summary>
    /// Consequences, should the described risk occur
    /// </summary>
    [Unicode(false)]
    public string MrbFailureConsequences { get; set; }

    /// <summary>
    /// Failure category 1, used for the risk (FK to LookupFailCat)
    /// </summary>
    [StringLength(20)]
    [Unicode(false)]
    public string MrbFailureCategorie1 { get; set; }

    /// <summary>
    /// Failure category 2, used for the risk (FK to LookupFailCat)
    /// </summary>
    [StringLength(20)]
    [Unicode(false)]
    public string MrbFailureCategorie2 { get; set; }

    /// <summary>
    /// MTBF in years before executing preventive actions for the risk
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? MrbMtbfBefore { get; set; }

    /// <summary>
    /// MTBF in years after executing preventive actions for the risk
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? MrbMtbfAfter { get; set; }

    /// <summary>
    /// MTBF in years for PMO scenario
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? MrbMtbfPmo { get; set; }

    /// <summary>
    /// FMECA effect before preventive actions for the risk are executed (needed for Priority)
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbEffectBefore { get; set; }

    /// <summary>
    /// FMECA effect after executing preventive actions for the risk
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbEffectAfter { get; set; }

    /// <summary>
    /// FMECA custom value before preventive actions for the risk are executed
    /// </summary>
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCustomBefore { get; set; }

    /// <summary>
    /// FMECA custom value after executing preventive actions for the risk
    /// </summary>
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCustomAfter { get; set; }

    /// <summary>
    /// FMECA custom effect before executing preventive actions for the risk
    /// </summary>
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCustomEffectBefore { get; set; }

    /// <summary>
    /// FMECA custom effect after executing preventive actions for the risk
    /// </summary>
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCustomEffectAfter { get; set; }

    /// <summary>
    /// Risk cost before executing preventive actions
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbRiskBefore { get; set; }

    /// <summary>
    /// Risk cost after executing preventive actions
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbRiskAfter { get; set; }

    /// <summary>
    /// Risk cost for PMO scenario
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbRiskPmo { get; set; }

    /// <summary>
    /// Direct costs of the risk before executing preventive actions
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbDirectCostBefore { get; set; }

    /// <summary>
    /// Direct cost of the risk after executing preventive actions
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbDirectCostAfter { get; set; }

    /// <summary>
    /// Direct cost of the risk for PMO scenario
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbDirectCostPmo { get; set; }

    /// <summary>
    /// Down time after preventive actions (in hours) caused by the risk (not used) (!)
    /// </summary>
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? MrbDownTimeBefore { get; set; }

    /// <summary>
    /// Down time after executing preventive actions (in hours) caused by the risk (not used) (!)
    /// </summary>
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? MrbDownTimeAfter { get; set; }

    /// <summary>
    /// Total cost per year for all tasks that are bound to the risk
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbActionCosts { get; set; }

    /// <summary>
    /// Current action costs for the risk
    /// </summary>
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCurrentActionCosts { get; set; }

    /// <summary>
    /// Costs of spare parts that are needed for preventive/corrective measures
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbSpareCosts { get; set; }

    /// <summary>
    /// Costs made to manage the spare parts that are needed for corrective/preventive measures (not used)
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbSpareManageCost { get; set; }

    /// <summary>
    /// (?) not used in amprover software
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbCapCosts { get; set; }

    /// <summary>
    /// Optimal costs for preventive actions for the risk (based on risk after preventive action)
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbOptimalCosts { get; set; }

    /// <summary>
    /// Person or department responsible for the risk (? responsible for solving it, or monitoring?)
    /// </summary>
    [StringLength(150)]
    [Unicode(false)]
    public string MrbResponsible { get; set; }

    /// <summary>
    /// Remarks for the risk, which provides a place to leave any extra information
    /// </summary>
    [Unicode(false)]
    public string MrbRemarks { get; set; }

    /// <summary>
    /// Remarks of the risk (not used) (!)
    /// </summary>
    [Unicode(false)]
    public string MrbRemarks1 { get; set; }

    /// <summary>
    /// ? Not used in amprover software
    /// </summary>
    [Unicode(false)]
    public string MrbOpsProcedure { get; set; }

    /// <summary>
    /// Norm for the risk (seems to not be in use) (!)
    /// </summary>
    [StringLength(150)]
    [Unicode(false)]
    public string MrbNorm { get; set; }

    /// <summary>
    /// ?
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string MrbExclOptCb { get; set; }

    /// <summary>
    /// Selected values of the FMECA matrix before and after preventive actions. Values are stored in XML format.
    /// </summary>
    [Unicode(false)]
    public string MrbFmecaSelect { get; set; }

    /// <summary>
    /// FMECA selection values for the 'Before' scenario
    /// </summary>
    [StringLength(255)]
    public string MrbFmecaSelectionBefore { get; set; }

    /// <summary>
    /// FMECA selection values for the 'After' scenario
    /// </summary>
    [StringLength(255)]
    public string MrbFmecaSelectionAfter { get; set; }

    /// <summary>
    /// FMECA selection values for the 'PMO' scenario
    /// </summary>
    [StringLength(255)]
    public string MrbFmecaSelectionPmo { get; set; }

    /// <summary>
    /// FMECA MTBF selection for the 'Before' scenario
    /// </summary>
    public int? MrbFmecaMtbfBefore { get; set; }

    /// <summary>
    /// FMECA MTBF selection for the 'After' scenario
    /// </summary>
    public int? MrbFmecaMtbfAfter { get; set; }

    /// <summary>
    /// FMECA MTBF selection for the 'PMO' scenario
    /// </summary>
    public int? MrbFmecaMtbfPmo { get; set; }

    /// <summary>
    /// Version of the FMECA matrix used for this risk
    /// </summary>
    public int? MrbFmecaVersion { get; set; }

    /// <summary>
    /// Created by this user with this username
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string MrbInitiatedBy { get; set; }

    /// <summary>
    /// Creation date
    /// </summary>
    [Column(TypeName = "smalldatetime")]
    public DateTime? MrbDateInitiated { get; set; }

    /// <summary>
    /// User name of person that made the last modification to this record
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string MrbModifiedBy { get; set; }

    /// <summary>
    /// Date the record was last modified. (null if never modified)
    /// </summary>
    [Column(TypeName = "smalldatetime")]
    public DateTime? MrbDateModified { get; set; }

    /// <summary>
    /// ID of the risk this risk was copied from (master risk) (FK to MRB)
    /// </summary>
    [Column("MrbMasterID")]
    public int? MrbMasterId { get; set; }

    /// <summary>
    /// State of the risk (? Is this still used?)
    /// </summary>
    [StringLength(10)]
    [Unicode(false)]
    public string MrbState { get; set; }

    /// <summary>
    /// Status of the risk (master, slave, or lock) (status is defined in Lookup)
    /// </summary>
    public int? MrbStatus { get; set; }

    /// <summary>
    /// Navigation property for the status
    /// </summary>
    [ForeignKey("MrbStatus")]
    public virtual Lookup MrbStatusNavigation { get; set; }

    /// <summary>
    /// The way the risk is derived of another risk (eg child, master)
    /// </summary>
    public int? MrbChildType { get; set; }
    public int? MrbAdditionalDataId { get; set; }

    [Key]
    [Column("MRBId")]
    public int Mrbid { get; set; }

    /// <summary>
    /// ID used for importing risks, to match imported risks with existing risks
    /// </summary>
    public int? MrbImportId { get; set; }

    /// <summary>
    /// Navigation property for the master risk
    /// </summary>
    [ForeignKey(nameof(MrbMasterId))]
    public virtual Mrb MasterRisk { get; set; }

    /// <summary>
    /// Collection of risks that reference this risk as master
    /// </summary>
    public virtual ICollection<Mrb> ChildRisks { get; set; }

    /// <summary>
    /// Collection of tasks associated with this risk
    /// </summary>
    public virtual ICollection<Task> Tasks { get; set; }

    public int MrbSortOrder { get; set; }


    public virtual LookupAdditionalData MrbAdditionalData { get; set; }
    public virtual MrbImage MrbImage { get; set; }
    public virtual ICollection<Spare> Spares { get; set; } = new HashSet<Spare>();
    public virtual ICollection<PickSi> PickSis { get; set; } = new HashSet<PickSi>();

    /// <summary>
    /// Collection of attachments associated with this risk
    /// </summary>
    public virtual ICollection<Attachment> Attachments { get; set; } = new HashSet<Attachment>();

    /// <summary>
    /// Collection of SiLinkFilters associated with this risk
    /// </summary>
    public virtual ICollection<SiLinkFilters> SiLinkFilters { get; set; } = new HashSet<SiLinkFilters>();

    /// <summary>
    /// SAPA Index calculated as (RiskBefore - RiskAfter) / PreventiveCosts
    /// </summary>
    [NotMapped]
    public decimal? MrbSapaIndex => (MrbActionCosts + MrbSpareCosts + MrbSpareManageCost) == 0
        ? null
        : (MrbRiskBefore - MrbRiskAfter) / (MrbActionCosts + MrbSpareCosts + MrbSpareManageCost);

    /// <summary>
    /// Safety rating before executing preventive actions
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string MrbSafetyBefore { get; set; }

    /// <summary>
    /// Safety rating after executing preventive actions
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string MrbSafetyAfter { get; set; }

    /// <summary>
    /// Safety rating for PMO scenario
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string MrbSafetyPmo { get; set; }

    /// <summary>
    /// Market impact rating before executing preventive actions
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string MrbMarketBefore { get; set; }

    /// <summary>
    /// Market impact rating after executing preventive actions
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string MrbMarketAfter { get; set; }

    /// <summary>
    /// Market impact rating for PMO scenario
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string MrbMarketPmo { get; set; }

    /// <summary>
    /// Annual equivalent cost
    /// </summary>
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? MrbAec { get; set; }

    /// <summary>
    /// Function of the risk
    /// </summary>
    [Unicode(false)]
    public string MrbFunction { get; set; }

    /// <summary>
    /// Costs made to manage the spare parts that are needed for corrective/preventive measures for PMO scenario
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbSpareManageCostPmo { get; set; }
}
