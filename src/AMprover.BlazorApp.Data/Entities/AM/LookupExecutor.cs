using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("LookupExecutor")]
[Comment("Master data table that contains all currently defined task executors. A task executor is the person or department that handles execution of a task.")]
public class LookupExecutor
{
    [Key]
    [Column("ExecutorID")]
    [Comment("Unique ID (PK for LookupExecutor)")]
    public int ExecutorId { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    [Comment("Name of the task executor")]
    public string ExecutorName { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    [Comment("Description of the task executor (Which clarifies the name where needed)")]
    public string ExecutorDescription { get; set; }

    public virtual ICollection<CommonTask> CommonTask { get; set; } = new HashSet<CommonTask>();
    public virtual ICollection<Task> Task { get; set; } = new HashSet<Task>();
    public virtual ICollection<Workpackage> Workpackage { get; set; } = new HashSet<Workpackage>();
    public virtual ICollection<Sapa> Sapa { get; set; } = new HashSet<Sapa>();
}
