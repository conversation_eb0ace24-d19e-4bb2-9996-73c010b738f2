using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblPickSI")]
public class PickSi
{
    [Key]
    [Column("PckSiID")]
    public int PckSiId { get; set; }
    
    [Column("PckSiMrbID")]
    public int PckSiMrbId { get; set; }
    
    [Column("PckSiSiID")]
    public int PckSiSiId { get; set; }
    
    [Column("PckSiLinkFilterID")]
    public int? PckSiLinkFilterId { get; set; }

    [ForeignKey(nameof(PckSiLinkFilterId))]
    public virtual SiLinkFilters PckSiLinkFilter { get; set; }
    
    [ForeignKey(nameof(PckSiMrbId))]
    public virtual Mrb PckSiMrb { get; set; }
    
    [ForeignKey(nameof(PckSiSiId))]
    public virtual Si PckSiSi { get; set; }
}
