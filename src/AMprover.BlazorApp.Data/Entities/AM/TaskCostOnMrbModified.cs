using System;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Keyless]
public class TaskCostOnMrbModified
{
    [Column("TskMrbID")]
    public int? TskMrbId { get; set; }
    
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? TskCosts { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbCost { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? TskDate { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? MrbDateModified { get; set; }
}
