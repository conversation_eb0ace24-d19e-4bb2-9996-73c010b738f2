using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// Stores additional descriptions for various entities in the system
/// </summary>
[Table("Descriptions")]
public class Descriptions
{
    /// <summary>
    /// Unique ID (PK of Descriptions)
    /// </summary>
    [Key]
    [Column("DescID")]
    public int DescId { get; set; }
    
    /// <summary>
    /// ID of the related task, if this description is for a task
    /// </summary>
    [Column("DescTaskID")]
    public int? DescTaskId { get; set; }
    
    /// <summary>
    /// ID of the related risk, if this description is for a risk
    /// </summary>
    [Column("DescRiskID")]
    public int? DescRiskId { get; set; }
    
    /// <summary>
    /// ID of the related risk object, if this description is for a risk object
    /// </summary>
    [Column("DescRiskObjectID")]
    public int? DescRiskObjectId { get; set; }
    
    /// <summary>
    /// ID of an additional entity this description relates to
    /// </summary>
    [Column("DescExtraID")]
    public int? DescExtraId { get; set; }
    
    /// <summary>
    /// Type of the additional entity
    /// </summary>
    [StringLength(30)]
    [Unicode(false)]
    public string DescExtraType { get; set; }
    
    /// <summary>
    /// Type of the description field
    /// </summary>
    public int? DescFieldType { get; set; }
    
    /// <summary>
    /// The actual description text
    /// </summary>
    [Unicode(false)]
    public string DescDescription { get; set; }
    
    /// <summary>
    /// Navigation property for the related Task
    /// </summary>
    [ForeignKey("DescTaskId")]
    public virtual Task Task { get; set; }
    
    /// <summary>
    /// Navigation property for the related Risk
    /// </summary>
    [ForeignKey("DescRiskId")]
    public virtual Mrb Risk { get; set; }
    
    /// <summary>
    /// Navigation property for the related RiskObject
    /// </summary>
    [ForeignKey("DescRiskObjectId")]
    public virtual RiskObject RiskObject { get; set; }
}
