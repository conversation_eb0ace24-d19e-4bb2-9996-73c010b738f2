using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("TblCompany")]
[Comment("Would contain full contact info for companies. Not used by AMprover software. (!)")]
public class Company
{
    [Key]
    [Column("CompanyID")]
    [Comment("Unique ID")]
    public int CompanyId { get; set; }
    
    [Required]
    [StringLength(50)]
    [Unicode(false)]
    [Comment("The name of the company")]
    public string CompanyName { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Description of the company")]
    public string CompanyDescription { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Contact field of the company")]
    public string CompanyContact1 { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Contact field of the company")]
    public string CompanyContact2 { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Address of the company")]
    public string CompanyAdres { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Post address of the company")]
    public string CompanyPostAdres { get; set; }
    
    [StringLength(10)]
    [Unicode(false)]
    [Comment("Zip code of the company")]
    public string CompanyZipCode { get; set; }
    
    [StringLength(30)]
    [Unicode(false)]
    [Comment("City of the company")]
    public string CompanyPlace { get; set; }
    
    [StringLength(30)]
    [Unicode(false)]
    [Comment("Country of the company")]
    public string CompanyCountry { get; set; }
    
    [StringLength(15)]
    [Unicode(false)]
    [Comment("Phone number of the company")]
    public string CompanyPhone { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Fax number of the company")]
    public string CompanyFax { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Email address of the company")]
    public string CompanyEmail { get; set; }
}
