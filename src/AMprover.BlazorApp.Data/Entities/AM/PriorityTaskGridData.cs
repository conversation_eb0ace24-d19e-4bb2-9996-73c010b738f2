using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Keyless]
[Table("PriorityTaskGridData")]
public class PriorityTaskGridData
{
    [Column("PrioTskID")]
    public int PrioTskId { get; set; }
    
    [Column("PrioTskPriorityID")]
    public int? PrioTskPriorityId { get; set; }
    
    public int? PrioTskVersion { get; set; }
    
    [Column("PrioTskOriginalID")]
    public int? PrioTskOriginalId { get; set; }
    
    [Column("PrioTskRiskID")]
    public int? PrioTskRiskId { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string MrbName { get; set; }
    
    [Column("PrioTskObjectID")]
    public int? PrioTskObjectId { get; set; }
    
    [Column("PrioTskTaskID")]
    public int? PrioTskTaskId { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string PrioTskDescription { get; set; }
    
    [Column("PrioTskCommonTaskID")]
    public int? PrioTskCommonTaskId { get; set; }
    
    [Column("PrioTskSiID")]
    public int? PrioTskSiId { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string PrioTskSiDescription { get; set; }
    
    [Column("PrioTskClusterPlanID")]
    public int? PrioTskClusterPlanId { get; set; }
    
    public int? PrioTskQualityScore { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? PrioTskRiskFactor { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskPriorityCode { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskClusterCostPerUnit { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskSiUnits { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskDuration { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskDownTime { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? PrioTskDateGenerated { get; set; }
    
    [Column("PrioTskReferenceID")]
    [StringLength(50)]
    [Unicode(false)]
    public string PrioTskReferenceId { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? PrioTskDateExecuted { get; set; }
    
    public int? PrioTskExecuteStatus { get; set; }
    
    public int? PrioTskSlack { get; set; }
    
    public int? PrioTskSlackIntervalType { get; set; }
    
    [Unicode(false)]
    public string PrioTskRemarks { get; set; }
    
    public int? PrioTskSequence { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskBudgetCostYear1 { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskBudgetCostYear2 { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskBudgetCostYear3 { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskBudgetCostYear4 { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskBudgetCostYear5 { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? PrioTskNumberOfTimes { get; set; }
    
    [StringLength(250)]
    [Unicode(false)]
    public string PrioPath { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskPostponePct { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskCosts { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? PrioTskDateDue { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskRiskCosts { get; set; }
    
    public int? PrioTskExecutionYear { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? PrioTskIntervalPerYear { get; set; }
    
    public bool? PrioTskAutoSelected { get; set; }
    
    public int? PrioTskSelectionSeq { get; set; }
    
    public bool? PrioTskImported { get; set; }
    
    public bool? PrioTskSealed { get; set; }
    
    [StringLength(20)]
    [Unicode(false)]
    public string TskType { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string PrioTskFromReference { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskRiskBudget { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskRiskDelta { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbEffectBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbMtbfBefore { get; set; }
    
    public int? SiQualityScore { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCustomAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbRiskAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCustomBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbRiskBefore { get; set; }
    
    public int? NumberOfTasks { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbMtbfAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbEffectAfter { get; set; }
    
    public int? TskUnitType { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioTskDirectCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? SiMtbf { get; set; }
    
    [Column(TypeName = "datetime")]
    public DateTime? SiYear { get; set; }
    
    [Column(TypeName = "datetime")]
    public DateTime? SiContractEnd { get; set; }
    
    [Column("SiRiskBvID")]
    public int? SiRiskBvId { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? SiRiskBeforeValue { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? SiRiskBeforeCustom { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? SiRiskAfterValue { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? SiRiskAfterCustom { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? SiRiskMtbfBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? SiRiskMtbfAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCustomEffectAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCustomEffectBefore { get; set; }
    
    [Column("FailRateID")]
    public int? FailRateId { get; set; }
}
