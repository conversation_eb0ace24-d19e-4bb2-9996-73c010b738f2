using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// Stores objects that are used for modelling risks. The object level determines what the object is. 
/// The levels and mapping to object type/name are defined in master data, by the items contained within the functional objects treenode.
/// 
/// Objects define a part of the system we're modelling, and can describe an object, installation, system, assembly or component. 
/// The user can name 5 levels of objects.
/// </summary>
[Table("TblObject")]
public class Object
{
    /// <summary>
    /// Unique ID (PK of Object)
    /// </summary>
    [Key]
    [Column("ObjID")]
    public int ObjId { get; set; }

    /// <summary>
    /// Object description
    /// </summary>
    [Unicode(false)]
    public string ObjDescription { get; set; }

    /// <summary>
    /// Function of the object. Provides a description that explains the functionality of this specific part of the model.
    /// </summary>
    [Unicode(false)]
    public string ObjFunction { get; set; }

    /// <summary>
    /// Image of the object (does not seem to be used by the AMprover software)
    /// </summary>
    [Column(TypeName = "image")]
    public byte[] ObjImage { get; set; }

    /// <summary>
    /// Filter reference for the object
    /// </summary>
    [MaxLength(20)]
    public string ObjFilterRef { get; set; }

    /// <summary>
    /// Short key of the object
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Unicode(false)]
    public string ObjShortKey { get; set; }

    /// <summary>
    /// Name of the object
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Unicode(false)]
    public string ObjName { get; set; }

    /// <summary>
    /// Level (hierarchy) of the object. The level determines what the object actually is. (a whole system, an assembly, etc.)
    /// </summary>
    public int ObjLevel { get; set; }

    /// <summary>
    /// Replacement value of the object.
    /// </summary>
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? ObjNewValue { get; set; }

    /// <summary>
    /// Production time. (Time the object can be used in production?) (Does not seem to be used by Amprover software)
    /// </summary>
    public int? ObjProductionTime { get; set; }

    /// <summary>
    /// Usable time of the object. (Time the object can be used) (What's the difference with Utilization time?) 
    /// (Does not seem to be used by Amprover software)
    /// </summary>
    public int? ObjUsableTime { get; set; }

    /// <summary>
    /// Utilization time of the object (time the object can be used for its specific function)
    /// (Does not seem to be used by Amprover software)
    /// </summary>
    public int? ObjUtilizationTime { get; set; }

    /// <summary>
    /// Available time of the object. (Time the object is available for use) 
    /// (Does not seem to be used by Amprover software)
    /// </summary>
    public int? ObjAvailableTime { get; set; }

    /// <summary>
    /// Relation to the kind of technical objects. SiCategories are defined in masterdata. (FK to LookupUserDefined)
    /// </summary>
    public int? ObjSiCategory { get; set; }

    /// <summary>
    /// The type of the related technical objects
    /// </summary>
    [MaxLength(50)]
    [Unicode(false)]
    public string ObjSiType { get; set; }

    /// <summary>
    /// User name of person that made the last modification to this record
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string ObjModifiedBy { get; set; }

    /// <summary>
    /// Date the record was last modified. (null if never modified)
    /// </summary>
    [Column(TypeName = "smalldatetime")]
    public DateTime? ObjDateModified { get; set; }

    /// <summary>
    /// Collection of risk objects associated with this object
    /// </summary>
    [InverseProperty("RiskObjObject")]
    public virtual ICollection<RiskObject> RiskObject { get; set; } = new HashSet<RiskObject>();

    /// <summary>
    /// Collection of child risk objects associated with this object
    /// </summary>
    [InverseProperty("RiskObjParentObject")]
    public virtual ICollection<RiskObject> ChildRiskObjects { get; set; } = new HashSet<RiskObject>();

    /// <summary>
    /// Collections of SiLinkFilters associated with this object
    /// </summary>
    [InverseProperty("SifChildObject")]
    public virtual ICollection<SiLinkFilters> SiLinkFiltersSifChildObject { get; set; } = new HashSet<SiLinkFilters>();

    [InverseProperty("SifChildObject1")]
    public virtual ICollection<SiLinkFilters> SiLinkFiltersSifChildObject1 { get; set; } = new HashSet<SiLinkFilters>();

    [InverseProperty("SifChildObject2")]
    public virtual ICollection<SiLinkFilters> SiLinkFiltersSifChildObject2 { get; set; } = new HashSet<SiLinkFilters>();

    [InverseProperty("SifChildObject3")]
    public virtual ICollection<SiLinkFilters> SiLinkFiltersSifChildObject3 { get; set; } = new HashSet<SiLinkFilters>();

    [InverseProperty("SifChildObject4")]
    public virtual ICollection<SiLinkFilters> SiLinkFiltersSifChildObject4 { get; set; } = new HashSet<SiLinkFilters>();

    /// <summary>
    /// Collections of Lcc objects associated with this object
    /// </summary>
    [InverseProperty("ChildObject")]
    public virtual ICollection<Lcc> ChildObjects { get; set; } = new HashSet<Lcc>();
    [InverseProperty("ChildObject1")]
    public virtual ICollection<Lcc> ChildObjects1 { get; set; } = new HashSet<Lcc>();
    [InverseProperty("ChildObject2")]
    public virtual ICollection<Lcc> ChildObjects2 { get; set; } = new HashSet<Lcc>();
    [InverseProperty("ChildObject3")]
    public virtual ICollection<Lcc> ChildObjects3 { get; set; } = new HashSet<Lcc>();
    [InverseProperty("ChildObject4")]
    public virtual ICollection<Lcc> ChildObjects4 { get; set; } = new HashSet<Lcc>();


    /// <summary>
    /// Collections of Mrb objects associated with this object
    /// </summary>
    public virtual ICollection<Mrb> MrbChildObjects { get; set; } = new HashSet<Mrb>();
    public virtual ICollection<Mrb> MrbChildObjects1 { get; set; } = new HashSet<Mrb>();
    public virtual ICollection<Mrb> MrbChildObjects2 { get; set; } = new HashSet<Mrb>();
    public virtual ICollection<Mrb> MrbChildObjects3 { get; set; } = new HashSet<Mrb>();
    public virtual ICollection<Mrb> MrbChildObjects4 { get; set; } = new HashSet<Mrb>();
}
