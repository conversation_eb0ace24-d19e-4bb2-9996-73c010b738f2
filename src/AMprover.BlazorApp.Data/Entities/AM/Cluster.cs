using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// A cluster is a group of related preventive tasks, that can be grouped for entry into a maintenance information system.
/// Most of the values stored in this table can be set from the ClusterControl. Some values get recalculated within SyncClusterData.
/// </summary>
[Table("TblCluster")]
public class Cluster
{
    [Key] 
    [Column("ClustID")]
    public int ClustId { get; set; }
    
    [Required]
    [StringLength(50)]
    [Unicode(false)]
    public string ClustName { get; set; }
    
    [Unicode(false)]
    public string ClustDescription { get; set; }
    
    public int ClustLevel { get; set; }

    public int? ClustPartOf { get; set; }

    [ForeignKey("ClustPartOf")]
    public virtual Cluster ClustPartOfCluster { get; set; }
    public virtual ICollection<Cluster> ClusterChildren { get; set; } = new HashSet<Cluster>();

    [Column(TypeName = "decimal(18,4)")] 
    public decimal? ClustInterval { get; set; }
    
    public int? ClustIntervalUnit { get; set; }
    
    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustEstTaskCosts { get; set; }
    
    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustTaskCosts { get; set; }
    
    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustSharedCosts { get; set; }
    
    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustDisciplineCosts { get; set; }
    
    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustMaterialCosts { get; set; }
    
    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustEnergyCosts { get; set; }
    
    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustToolCosts { get; set; }
    
    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustTotalCmnCost { get; set; }
    
    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustDownTime { get; set; }
    
    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustDuration { get; set; }
    
    public int? ClustExecutor { get; set; }
    public int? ClustInitiator { get; set; }

    public int? ClustStatus { get; set; }

    [ForeignKey("ClustStatus")]
    public virtual Lookup ClustStatusNavigation { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string ClustShortKey { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ClustInitiatedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? ClustDateInitiated { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ClustModifiedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? ClustDateModified { get; set; }
    
    [Unicode(false)]
    public string ClustRemark { get; set; }
    
    [Unicode(false)]
    public string ClustResponsible { get; set; }
    
    [Unicode(false)]
    public string ClustSecondValues { get; set; }
    
    public int? ClustWorkpackageId { get; set; }
    
    public bool? ClustDivideDuration { get; set; }
    public bool? ClustDivideDownTime { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ClustLocation { get; set; }
    
    public bool? ClustInterruptable { get; set; }
    
    public int? ClustShiftStartDate { get; set; }
    public int? ClustShiftEndDate { get; set; }
    
    [StringLength(30)]
    [Unicode(false)]
    public string ClustTemplateType { get; set; }
    
    [StringLength(12)]
    [Unicode(false)]
    [Column("ClustOrgID")]
    public string ClustOrgId { get; set; }
    
    public int? ClustPriority { get; set; }
    public int? ClustSequence { get; set; }
    
    [Unicode(false)]
    [Column("ClustReferenceID")]
    public string ClustReferenceId { get; set; }

    [Column(TypeName = "decimal(18,2)")] 
    public decimal? ClustBudget { get; set; }

    [Column("ClustScenarioID")]
    public int? ClustScenarioId { get; set; }

    [ForeignKey("ClustScenarioId")]
    public virtual Scenario Scenario { get; set; }

    [Column("ClustRiskObjectID")]
    public int? ClustRiskObjectId { get; set; }

    [ForeignKey("ClustRiskObjectId")]
    public virtual RiskObject RiskObject { get; set; }
    
    [StringLength(12)]
    [Unicode(false)]
    [Column("ClustSiteID")]
    public string ClustSiteId { get; set; }

    public virtual ICollection<ClusterCost> ClusterCost { get; set; } = new HashSet<ClusterCost>();
    public virtual ICollection<ClusterTaskPlan> ClusterTaskPlan { get; set; } = new HashSet<ClusterTaskPlan>();
    public virtual ICollection<Task> Task { get; set; } = new HashSet<Task>();
}
