using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

public class LccDetailGrid
{
    [Key]
    [Column("LccDetID")]
    public int LccDetId { get; set; }
    
    [Column("LccDetLccID")]
    public int? LccDetLccId { get; set; }
    
    public int? LccDetYear { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string LccName { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetFmecaBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetAverageBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetRiskBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetFmecaCustomBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetFmecaCustomRiskBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetFmecaAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetAverageAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetRiskAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetFmecaCustomAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetFmecaCustomRiskAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? LccDetDepreciation { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetMaintenanceCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetTaskCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetActionCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetProcedureCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetModificationCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetSpareCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetTotalCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetPreventiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetCorrectiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? LccDetReliability { get; set; }
    
    [Column("LccDetTotalNPV", TypeName = "decimal(18, 2)")]
    public decimal? LccDetTotalNpv { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetRealTotalCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetRealPreventiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetRealCorrectiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetAverageRealPreventiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetAverageRealCorrectiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetOptimalPreventiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetOptimalCorrectiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetAverageOptimalCost { get; set; }
    
    [Column("LccDetAEC", TypeName = "decimal(18, 2)")]
    public decimal? LccDetAec { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccDetFailureRate { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccDetAvailabilityTechnical { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccDetUtilizationTechnichal { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccDetProductivityTechnical { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccDetEcoTechnical { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccProductionCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetDirectCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetOpexCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccDetRealOpexCost { get; set; }
}
