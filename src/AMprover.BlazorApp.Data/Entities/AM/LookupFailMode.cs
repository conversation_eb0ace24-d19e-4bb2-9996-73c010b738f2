using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("LookupFailMode")]
[Comment("Master data table that defines failure modes. A failure mode describes a possible cause for the failure, if the failure shows itself, and what the frequency of the failure is.")]
public class LookupFailMode
{
    [Key]
    [Column("FailID")]
    [Comment("Unique ID (PK for LookupFailMode)")]
    public int FailId { get; set; }

    [Required]
    [StringLength(40)]
    [Unicode(false)]
    [Comment("Name of the failure mode")]
    public string FailMode { get; set; }

    [StringLength(100)]
    [Unicode(false)]
    [Comment("Description of the failure mode")]
    public string FailDescription { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    [Comment("User that made the last modification to this record")]
    public string FailModifiedBy { get; set; }

    [Column(TypeName = "smalldatetime")]
    [Comment("Date the record was last modified. (null if never modified)")]
    public DateTime? FailDateModified { get; set; }

    [Column(TypeName = "decimal(10, 2)")]
    [Comment("Fail shape defines the slope of the current location on the Weibull curve")]
    public decimal? FailShape { get; set; }

    [Column(TypeName = "decimal(10, 2)")]
    [Comment("Defines the location on the Weibull curve (starting point)")]
    public decimal? FailWeibullLocation { get; set; }

    [Comment("Defines the position in the Weibull curve (when combined with Weibull location)")]
    public int? FailIntervalUnit { get; set; }

    [Column("FailRateID")]
    [Comment("Failure rate. This is the frequency at which a system or component fails. Possible values are defined in Lookup. Defaults to constant.")]
    [DefaultValue("((3))")]
    public int FailRateId { get; set; }

    [Column("FailRiskTypeID")]
    [Comment("Describes if the failure shows itself, or is invisible. Possible values are defined in Lookup.")]
    public int? FailRiskTypeId { get; set; }

    [Column("FailDistributionID")]
    [Comment("Distribution type determines what method of calculation will be used to calculate the costs associated with each failure mode. Distribution type values are defined in the Lookup table.")]
    public int? FailDistributionId { get; set; }

    public virtual ICollection<Mrb> Risks { get; set; } =
        new HashSet<Mrb>();
}