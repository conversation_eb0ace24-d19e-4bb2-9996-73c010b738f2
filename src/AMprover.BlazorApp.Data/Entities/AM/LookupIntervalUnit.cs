using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("LookupIntervalUnit")]
[Comment("Master data table that holds values that are used in interval calculations. Editable by user.")]
public class LookupIntervalUnit
{
    public LookupIntervalUnit()
    {
        CommonTask = new HashSet<CommonTask>();
        Task = new HashSet<Task>();
        Workpackage = new HashSet<Workpackage>();
    }

    [Key]
    [Column("IntUnitID")]
    [Comment("Unique ID (PK of LookupIntervalUnit)")]
    public int IntUnitId { get; set; }
    
    [Required]
    [StringLength(20)]
    [Unicode(false)]
    [Comment("Name of the interval unit")]
    public string IntUnitName { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    [Comment("Number of times an interval unit occurs in a year")]
    public decimal? IntUnitCalculationKey { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Description of the interval unit")]
    public string IntUnitDescription { get; set; }
    
    [Required]
    [StringLength(10)]
    [Unicode(false)]
    [Comment("Short code for the interval unit (usually consists of the first letter of the name)")]
    public string IntUnitShortKey { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("User that made the last modification to this record")]
    public string IntUnitModifiedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    [Comment("Date the record was last modified. (null if never modified)")]
    public DateTime? IntUnitDateModified { get; set; }

    public virtual ICollection<CommonTask> CommonTask { get; set; }
    public virtual ICollection<Task> Task { get; set; }
    public virtual ICollection<Workpackage> Workpackage { get; set; }
}
