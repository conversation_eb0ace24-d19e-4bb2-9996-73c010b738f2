using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Keyless]
public class TaskEdit
{
    [Column("TskID")]
    public int TskId { get; set; }
    
    [Required]
    [StringLength(60)]
    [Unicode(false)]
    public string TskName { get; set; }
    
    [Unicode(false)]
    public string TskDescription { get; set; }
    
    [Column("TskMrbID")]
    public int? TskMrbId { get; set; }
    
    public int? TskCluster { get; set; }
    
    public int TskInitiator { get; set; }
    
    public int? TskMxPolicy { get; set; }
    
    [Unicode(false)]
    public string TskGeneralDescription { get; set; }
    
    [Unicode(false)]
    public string TskRemark { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string TskResponsible { get; set; }
    
    public int? TskExecutor { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskCosts { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? TskInterval { get; set; }
    
    public int TskIntervalUnit { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string TskInitiatedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? TskDateInitiated { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string TskModifiedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? TskDateModified { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? TskDownTime { get; set; }
    
    public int? TskSortOrder { get; set; }
    
    public bool? TskExtraBool2 { get; set; }
    
    public bool? TskExtraBool3 { get; set; }
    
    public bool? TskExtraBool4 { get; set; }
    
    public int? TskCopiedFrom { get; set; }
    
    public int? TskWorkpackage { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? TskDuration { get; set; }
    
    public int? TskValidFromYear { get; set; }
    
    [Required]
    [StringLength(20)]
    [Unicode(false)]
    public string TskType { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskOptimalCost { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? TskFinishDate { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskWorkInspCost { get; set; }
    
    public int? TskValidUntilYear { get; set; }
    
    public int? TskPriorityCode { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? TskExecutionDate { get; set; }
    
    [Column("TskCommonActionID")]
    public int? TskCommonActionId { get; set; }
    
    [Unicode(false)]
    public string TskFmecaEffect { get; set; }
    
    [Column("TskLCCEffect")]
    [Unicode(false)]
    public string TskLcceffect { get; set; }
    
    [Column(TypeName = "numeric(18, 2)")]
    public decimal? TskFmecaEffectPct { get; set; }
    
    public int TskFmecaVersion { get; set; }
    
    public int TskUnitType { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskClusterCostPerUnit { get; set; }
    
    public bool? TskClusterCostMember { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskClusterCosts { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskEstCostPerUnit { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskEstCosts { get; set; }
    
    public int? TskPartOf { get; set; }
    
    [Unicode(false)]
    public string TskNorm { get; set; }
    
    [Unicode(false)]
    public string TskPermit { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskUnits { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ObjLevel3Name { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ObjLevel2Name { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ObjLevel4Name { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ScenName { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ObjLevel0Name { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string RiskObjName { get; set; }
    
    [Column("RiskObjID")]
    public int? RiskObjId { get; set; }
    
    [Column("ScenID")]
    public int? ScenId { get; set; }
    
    public int? ScenChildType { get; set; }
    
    public int? TskStatus { get; set; }
    
    public bool? TskRemoved { get; set; }
    
    [Column("TskReferenceID")]
    public int? TskReferenceId { get; set; }
    
    public bool? TskDerived { get; set; }
    
    public bool? TskMaster { get; set; }
    
    public bool? TskSkipInLcc { get; set; }
}
