using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// Contains spare parts, their costs and other data associated with storing them.
/// Spare parts can be defined using the Spare parts grid control found in the Risk analysis screen, and can be added to any Risk.
/// </summary>
[Table("TblSpare")]
public class Spare
{
    /// <summary>
    /// Unique ID (PK to Spare)
    /// </summary>
    [Key]
    [Column("SpareID")]
    public int SpareId { get; set; }
    
    /// <summary>
    /// Risk ID the spare part belongs to (FK to MRB)
    /// </summary>
    [Column("SpareMrbID")]
    public int SpareMrbId { get; set; }
    
    [ForeignKey(nameof(SpareMrbId))]
    public virtual Mrb SpareMrb { get; set; }

    /// <summary>
    /// Name of the spare part
    /// </summary>
    [StringLength(60)]
    [Unicode(false)]
    public string SpareName { get; set; }
    
    /// <summary>
    /// Remarks of the spare part
    /// </summary>
    [Unicode(false)]
    public string SpareRemarks { get; set; }
    
    /// <summary>
    /// Category the spare part belongs to (?)
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string SpareCategory { get; set; }
    
    /// <summary>
    /// Stock number of the spare part.
    /// </summary>
    public int? SpareStockNumber { get; set; }
    
    /// <summary>
    /// Lead time of the spare part. (The time it takes between ordering the part, and receiving it where it's needed)
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? SpareOrderLeadTime { get; set; }
    
    /// <summary>
    /// Purchase price of the spare part
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? SparePurchasePrice { get; set; }
    
    /// <summary>
    /// Yearly costs for the spare part (storage, depreciation, etc) (! does not seem to be used by AMprover software)
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? SpareYearlyCost { get; set; }
    
    /// <summary>
    /// Supplier ID of the spare part, which can be used to order the spare part directly.
    /// </summary>
    [Column("SpareSupplierID")]
    [StringLength(50)]
    [Unicode(false)]
    public string SpareSupplierId { get; set; }
    
    /// <summary>
    /// The vendor that sells the spare part (! not an ID)
    /// </summary>
    [Column("SpareVendorID")]
    [StringLength(50)]
    [Unicode(false)]
    public string SpareVendorId { get; set; }
    
    /// <summary>
    /// Reference ID of the spare part (not bound) (!)
    /// </summary>
    [Column("SpareReferenceID")]
    [StringLength(50)]
    [Unicode(false)]
    public string SpareReferenceId { get; set; }
    
    /// <summary>
    /// Number of units of the spare part that are needed. (used in price calculation)
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int? SpareNoOfItems { get; set; } = 1;
    
    /// <summary>
    /// Depreciation percentage of the spare part.
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? SpareDepreciationPct { get; set; }
    
    /// <summary>
    /// Cost of the spare part.
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? SpareCosts { get; set; }
    
    /// <summary>
    /// Year the spare part was purchased.
    /// </summary>
    public int? SparePurchaseYear { get; set; }
    
    /// <summary>
    /// Number of objects that can make use of the spare part.
    /// </summary>
    public int? SpareObjectCount { get; set; }
    
    [Column(TypeName = "decimal(18, 5)")]
    public decimal? SpareReliability { get; set; }
    
    /// <summary>
    /// For derived scenarios's you will need this field to keep track of changes
    /// </summary>
    public int? SpareCopiedFrom { get; set; }
    
    public int? SpareChildType { get; set; }
    
    public bool SpareDerived { get; set; }
    
    public bool SparePmo { get; set; }
}
