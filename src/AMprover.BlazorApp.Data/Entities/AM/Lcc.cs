using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// Contains the life cycle costs for each defined risk object. Calculations can be based on risk and/or RAMS analysis.
/// </summary>
[Table("TblLCC")]
public class Lcc
{
    /// <summary>
    /// Unique ID (PK of LCC)
    /// </summary>
    [Key] 
    [Column("LccID")]
    public int LccId { get; set; }
    
    /// <summary>
    /// Scenario ID of the LCC (FK to Scenario)
    /// </summary>
    [Column("LccScenarioID")]
    public int? LccScenarioId { get; set; }
    
    /// <summary>
    /// Navigation property for the scenario
    /// </summary>
    public virtual Scenario LccScenario { get; set; }

    /// <summary>
    /// Risk object ID of the LCC (FK to RiskObject)
    /// </summary>
    public int? LccRiskObject { get; set; }
    
    /// <summary>
    /// Navigation property for the risk object
    /// </summary>
    [ForeignKey("LccRiskObject")]
    public virtual RiskObject RiskObject { get; set; }

    /// <summary>
    /// Object ID of the LCC (FK to Object)
    /// </summary>
    public int? LccChildObject { get; set; }
    
    /// <summary>
    /// Navigation property for the child object
    /// </summary>
    [ForeignKey("LccChildObject")]
    [InverseProperty("ChildObjects")]
    public virtual Object ChildObject { get; set; }

    /// <summary>
    /// Object ID of the LCC (FK to Object)
    /// </summary>
    public int? LccChildObject1 { get; set; }
    
    /// <summary>
    /// Navigation property for the child object 1
    /// </summary>
    [ForeignKey("LccChildObject1")]
    [InverseProperty("ChildObjects1")]
    public virtual Object ChildObject1 { get; set; }

    /// <summary>
    /// Object ID of the LCC (FK to Object)
    /// </summary>
    public int? LccChildObject2 { get; set; }
    
    /// <summary>
    /// Navigation property for the child object 2
    /// </summary>
    [ForeignKey("LccChildObject2")]
    [InverseProperty("ChildObjects2")]
    public virtual Object ChildObject2 { get; set; }

    /// <summary>
    /// Object ID of the LCC (FK to Object)
    /// </summary>
    public int? LccChildObject3 { get; set; }
    
    /// <summary>
    /// Navigation property for the child object 3
    /// </summary>
    [ForeignKey("LccChildObject3")]
    [InverseProperty("ChildObjects3")]
    public virtual Object ChildObject3 { get; set; }

    /// <summary>
    /// Object ID of the LCC (FK to Object)
    /// </summary>
    public int? LccChildObject4 { get; set; }
    
    /// <summary>
    /// Navigation property for the child object 4
    /// </summary>
    [ForeignKey("LccChildObject4")]
    [InverseProperty("ChildObjects4")]
    public virtual Object ChildObject4 { get; set; }

    /// <summary>
    /// LCC ID of the parent LCC (FK to LCC)
    /// </summary>
    public int? LccPartOf { get; set; }
    
    /// <summary>
    /// Navigation property for the parent LCC
    /// </summary>
    [ForeignKey("LccPartOf")]
    public virtual Lcc LccPartOfLcc { get; set; }
    
    /// <summary>
    /// Navigation property for the child LCCs
    /// </summary>
    public virtual ICollection<Lcc> LccChildren { get; set; } = new List<Lcc>();

    /// <summary>
    /// Name of the LCC
    /// </summary>
    [Required]
    [StringLength(50)]
    public string LccName { get; set; }
    
    /// <summary>
    /// Discount rate for the LCC is a factror reflecting the time value of money that is used to convert cash flows occurring at different times, to a common time.
    /// </summary>
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? LccDiscountRate { get; set; }
    
    /// <summary>
    /// Net present value of the LCC. NPV is the sum of discounted future cash flows, inlcuding both costs and benefits/revenues.
    /// </summary>
    [Column("LccNPV", TypeName = "decimal(18, 4)")]
    public decimal? LccNpv { get; set; }
    
    /// <summary>
    /// Optimal year of the LCC (year where the AEC is on his lowest point)
    /// </summary>
    [Column("LccNPVyear")]
    public int? LccNpvyear { get; set; }
    
    /// <summary>
    /// Remarks field allowing for more detailed information about the LCC item
    /// </summary>
    [Unicode(false)]
    public string LccRemark { get; set; }
    
    /// <summary>
    /// Total average cost of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? LccTotalAverageCost { get; set; }
    
    /// <summary>
    /// Average optimal cost of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? LccAverageOptimalCost { get; set; }
    
    /// <summary>
    /// Saving potential of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccPotential { get; set; }
    
    /// <summary>
    /// Annual equivalent cost of the LCC
    /// </summary>
    [Column("LccAEC", TypeName = "decimal(18, 2)")]
    public decimal? LccAec { get; set; }
    
    /// <summary>
    /// Maintenance cost divided by the replacement value
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccMcRav { get; set; }
    
    /// <summary>
    /// Availability of technical input for the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccInputAvailabilityTechnical { get; set; }
    
    /// <summary>
    /// Reliability of functional input for the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccInputReliabilityFunctional { get; set; }
    
    /// <summary>
    /// Availability of technical output for the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccOutputAvailabilityTechnical { get; set; }
    
    /// <summary>
    /// Reliability of functional input for the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccOutputReliabilityTechnical { get; set; }
    
    /// <summary>
    /// Image containing the optimal cost graph of the LCC
    /// </summary>
    [Column(TypeName = "image")]
    public byte[] LccOptimalImage { get; set; }
    
    /// <summary>
    /// Image containing the real cost graph of the LCC
    /// </summary>
    [Column(TypeName = "image")]
    public byte[] LccRealCostImage { get; set; }
    
    /// <summary>
    /// Image containing the RAMS graph of the LCC
    /// </summary>
    [Column(TypeName = "image")]
    public byte[] LccRamsImage { get; set; }
    
    /// <summary>
    /// Replacement value of the LCC object
    /// </summary>
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? LccReplacementValue { get; set; }
    
    /// <summary>
    /// Output of the total technical MTBF (mean time between failure) of the LCC
    /// </summary>
    [Column("LccMTBFTechnical", TypeName = "decimal(18, 6)")]
    public decimal? LccMtbftechnical { get; set; }
    
    /// <summary>
    /// Output of the total functional MTBF (mean time between failure) of the LCC
    /// </summary>
    [Column("LccMTBFFunctional", TypeName = "decimal(18, 6)")]
    public decimal? LccMtbffunctional { get; set; }
    
    /// <summary>
    /// Average optimal cost for corrective maintenance of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? LccOptimalAverageCorrectiveCost { get; set; }
    
    /// <summary>
    /// Average optimal cost for preventive maintenance of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? LccOptimalAveragePreventiveCost { get; set; }
    
    /// <summary>
    /// Total production cost of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? LccOverallProductionCost { get; set; }
    
    /// <summary>
    /// Technical utilization (time) of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccUtilizationTechnical { get; set; }
    
    /// <summary>
    /// Functional utilization (time) of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccUtilizationFunctional { get; set; }
    
    /// <summary>
    /// Technical productivity of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccProductivityTechnical { get; set; }
    
    /// <summary>
    /// Functional productivity of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccProductivityFunctional { get; set; }
    
    /// <summary>
    /// Technical eco score of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccEcoTechnical { get; set; }
    
    /// <summary>
    /// Functional eco score of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccEcoFunctional { get; set; }
    
    /// <summary>
    /// Production cost of the LCC
    /// </summary>
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccProductionCost { get; set; }
    
    /// <summary>
    /// Number of years the LCC should be calculated for
    /// </summary>
    public int? LccMaxYears { get; set; }

    /// <summary>
    /// Start year for LCC calculations
    /// </summary>
    public int? LccStartYear { get; set; }

    /// <summary>
    /// Age of the LCC in years
    /// </summary>
    public int LccAge { get; set; }

    /// <summary>
    /// RAMS diagram ID of the LCC (FK to RamsDiagram)
    /// </summary>
    [Column("LccRamsDiagramID")]
    public int? LccRamsDiagramId { get; set; }

    /// <summary>
    /// RAMS ID of the LCC (FK to Rams)
    /// </summary>
    [Column("LccRamsID")]
    public int? LccRamsId { get; set; }

    /// <summary>
    /// Boolean that excludes the LCC from calculation when true
    /// </summary>
    public bool? LccExclude { get; set; }
    
    /// <summary>
    /// User name of person that made the last modification to this record
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string LccModifiedBy { get; set; }
    
    /// <summary>
    /// Date the record was last modified. (null if never modified)
    /// </summary>
    [Column(TypeName = "smalldatetime")]
    public DateTime? LccDateModified { get; set; }
    
    /// <summary>
    /// Date the LCC was calculated
    /// </summary>
    [Column("LCCDateCalculated", TypeName = "smalldatetime")]
    public DateTime? LccdateCalculated { get; set; }

    /// <summary>
    /// ID of the significant item
    /// </summary>
    [Column("LccSiID")]
    public int? LccSiId { get; set; }
    
    /// <summary>
    /// Navigation property for the significant item
    /// </summary>
    public virtual Si LccSi { get; set; }

    /// <summary>
    /// Navigation property for the LCC details
    /// </summary>
    public virtual ICollection<Lccdetail> Details { get; set; } = new List<Lccdetail>();

    /// <summary>
    /// Navigation property for the associated Vdmxl item
    /// </summary>
    [InverseProperty("Lcc")]
    public virtual Vdmxl VdmxlItem { get; set; }

    #region PMO fields

    /// <summary>
    /// PMO net present value
    /// </summary>
    [Precision(18, 4)] 
    public decimal? LccNPVPmo { get; set; }
    
    /// <summary>
    /// PMO NPV year
    /// </summary>
    public int? LccNPVyearPmo { get; set; }
    
    /// <summary>
    /// PMO total average cost
    /// </summary>
    [Precision(18, 4)] 
    public decimal? LccTotalAverageCostPmo { get; set; }
    
    /// <summary>
    /// PMO average optimal cost
    /// </summary>
    [Precision(18, 4)] 
    public decimal? LccAverageOptimalCostPmo { get; set; }
    
    /// <summary>
    /// PMO potential
    /// </summary>
    [Precision(18, 2)] 
    public decimal? LccPotentialPmo { get; set; }
    
    /// <summary>
    /// PMO annual equivalent cost
    /// </summary>
    [Precision(18, 2)] 
    public decimal? LccAECPmo { get; set; }
    
    /// <summary>
    /// PMO maintenance cost / replacement value
    /// </summary>
    [Precision(18, 2)] 
    public decimal? LccMcRavPmo { get; set; }
    
    /// <summary>
    /// PMO input availability technical
    /// </summary>
    [Precision(18, 6)] 
    public decimal? LccInputAvailabilityTechnicalPmo { get; set; }
    
    /// <summary>
    /// PMO output reliability technical
    /// </summary>
    [Precision(18, 6)] 
    public decimal? LccOutputReliabilityTechnicalPmo { get; set; }
    
    /// <summary>
    /// PMO MTBF technical
    /// </summary>
    [Precision(18, 6)] 
    public decimal? LccMTBFTechnicalPmo { get; set; }
    
    /// <summary>
    /// PMO optimal average corrective cost
    /// </summary>
    [Precision(18, 0)] 
    public decimal? LccOptimalAverageCorrectiveCostPmo { get; set; }
    
    /// <summary>
    /// PMO optimal average preventive cost
    /// </summary>
    [Precision(18, 0)] 
    public decimal? LccOptimalAveragePreventiveCostPmo { get; set; }

    #endregion
}
