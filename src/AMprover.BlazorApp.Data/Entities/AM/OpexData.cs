using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("TblOpexData")]
public class OpexData
{
    public OpexData()
    {
        OpexFactor = new HashSet<OpexFactor>();
        OpexToLccOpexLccOpexDataId1Navigation = new HashSet<OpexToLcc>();
        OpexToLccOpexLccOpexDataId2Navigation = new HashSet<OpexToLcc>();
        OpexToLccOpexLccOpexDataId3Navigation = new HashSet<OpexToLcc>();
    }

    [Key]
    [Column("OpexDataID")]
    /// <summary>
    /// Unique ID (PK of OpexData)
    /// </summary>
    public int OpexDataId { get; set; }
    
    [Column("OpexDataInflationGroupID")]
    /// <summary>
    /// ID of the inflation group that is used by the opex data (FK to LookupInflationGroup)
    /// </summary>
    public int? OpexDataInflationGroupId { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    /// <summary>
    /// Name of the 'opex data'
    /// </summary>
    public string OpexDataName { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    /// <summary>
    /// Value used for the 'opex data'
    /// </summary>
    public decimal? OpexDataValue { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    /// <summary>
    /// Percentage used for the 'opex data'
    /// </summary>
    public decimal? OpexDataPercentage { get; set; }
    
    /// <summary>
    /// Opex factor method value which is used for the 'opex data' (FK to Lookup)
    /// </summary>
    public int? OpexDataMethod { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    /// <summary>
    /// User name of person that made the last modification to this record
    /// </summary>
    public string OpexDataModifiedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    /// <summary>
    /// Date the record was last modified. (null if never modified)
    /// </summary>
    public DateTime? OpexDataDateModified { get; set; }
    
    public int? OpexDataCostType { get; set; }

    public virtual ICollection<OpexFactor> OpexFactor { get; set; }
    /// <summary>
    /// Collection of OpexToLcc entities where this OpexData is used as OpexLccOpexDataId1
    /// </summary>
    [InverseProperty("OpexLccOpexDataId1Navigation")]
    public virtual ICollection<OpexToLcc> OpexToLccOpexLccOpexDataId1Navigation { get; set; } = new HashSet<OpexToLcc>();
    /// <summary>
    /// Collection of OpexToLcc entities where this OpexData is used as OpexLccOpexDataId2
    /// </summary>
    [InverseProperty("OpexLccOpexDataId2Navigation")]
    public virtual ICollection<OpexToLcc> OpexToLccOpexLccOpexDataId2Navigation { get; set; } = new HashSet<OpexToLcc>();
    /// <summary>
    /// Collection of OpexToLcc entities where this OpexData is used as OpexLccOpexDataId3
    /// </summary>
    [InverseProperty("OpexLccOpexDataId3Navigation")]
    public virtual ICollection<OpexToLcc> OpexToLccOpexLccOpexDataId3Navigation { get; set; } = new HashSet<OpexToLcc>();
}
