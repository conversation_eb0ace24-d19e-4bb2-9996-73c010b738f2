using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("LookupMxPolicy")]
[Comment("Master data table that defines different maintenance policies. A maintenance policy can be described as the reason why maintenance should occur. (?)")]
public class LookupMxPolicy
{
    [Key]
    [Column("PolID")]
    [Comment(" (PK of LookupMxPolicy)")]
    public int PolId { get; set; }
    
    [StringLength(5)]
    [Unicode(false)]
    [Comment("Name that describes the maintenance policy")]
    public string PolName { get; set; }
    
    [StringLength(30)]
    [Unicode(false)]
    [Comment("Provides a more detailed description of the maintenance policy")]
    public string PolDescription { get; set; }
    
    public string PolEamPolicy { get; set; }

    public virtual ICollection<CommonTask> CommonTask { get; set; } = new HashSet<CommonTask>();
    public virtual ICollection<Task> Task { get; set; } = new HashSet<Task>();
}
