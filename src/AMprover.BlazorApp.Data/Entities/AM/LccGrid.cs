using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

public class LccGrid
{
    [Key]
    [Column("LccID")]
    public int LccId { get; set; }
    
    [Column("LccScenarioID")]
    public int? LccScenarioId { get; set; }
    
    public int? LccRiskObject { get; set; }
    
    public int? LccChildObject { get; set; }
    
    public int? LccChildObject1 { get; set; }
    
    public int? LccChildObject2 { get; set; }
    
    public int? LccChildObject3 { get; set; }
    
    public int? LccChildObject4 { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string LccName { get; set; }
    
    public int? LccPartOf { get; set; }
    
    [Column("LccNPV", TypeName = "decimal(18, 4)")]
    public decimal? LccNpv { get; set; }
    
    [Column("LccNPVyear")]
    public int? LccNpvyear { get; set; }
    
    [Unicode(false)]
    public string LccRemark { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? LccTotalAverageCost { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? LccAverageOptimalCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccPotential { get; set; }
    
    [Column("LccAEC", TypeName = "decimal(18, 2)")]
    public decimal? LccAec { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccMcRav { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccInputAvailabilityTechnical { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccInputReliabilityFunctional { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccOutputAvailabilityTechnical { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccOutputReliabilityTechnical { get; set; }
    
    [Column(TypeName = "image")]
    public byte[] LccOptimalImage { get; set; }
    
    [Column(TypeName = "image")]
    public byte[] LccRealCostImage { get; set; }
    
    [Column(TypeName = "image")]
    public byte[] LccRamsImage { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? LccReplacementValue { get; set; }
    
    [Column("LccMTBFTechnical", TypeName = "decimal(18, 6)")]
    public decimal? LccMtbftechnical { get; set; }
    
    [Column("LccMTBFFunctional", TypeName = "decimal(18, 6)")]
    public decimal? LccMtbffunctional { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? LccOptimalAverageCorrectiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? LccOptimalAveragePreventiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? LccOverallProductionCost { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccUtilizationTechnical { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccUtilizationFunctional { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccProductivityTechnical { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccProductivityFunctional { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccEcoTechnical { get; set; }
    
    [Column(TypeName = "decimal(18, 6)")]
    public decimal? LccEcoFunctional { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string RiskObjName { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string ObjName { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string ChildObject2 { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string ChildObject3 { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string ChildObject4 { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? RiskObjProdCostHour { get; set; }
    
    public int? ObjProductionTime { get; set; }
    
    public int? ObjUsableTime { get; set; }
    
    public int? ObjUtilizationTime { get; set; }
    
    public int? ObjAvailableTime { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? LccDiscountRate { get; set; }
    
    public int? RiskObjNoOfInstallation { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string PartOfName { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? LccProductionCost { get; set; }
    
    [Unicode(false)]
    public string FmecaMatrix { get; set; }
    
    public int? FmecaVersion { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? ObjNewValue { get; set; }
    
    [Column("ObjID")]
    public int? ObjId { get; set; }
    
    public int? LccMaxYears { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? Obj2NewValue { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? Obj3NewValue { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? Obj4NewValue { get; set; }
    
    [Column("LccRamsDiagramID")]
    public int? LccRamsDiagramId { get; set; }
    
    [Column("LccRamsID")]
    public int? LccRamsId { get; set; }
    
    public bool? LccExclude { get; set; }
    
    [Unicode(false)]
    public string LccModifiedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? LccDateModified { get; set; }
    
    [Column("LCCDateCalculated", TypeName = "smalldatetime")]
    public DateTime? LccdateCalculated { get; set; }
    
    [Column("LccSiID")]
    public int? LccSiId { get; set; }
}
