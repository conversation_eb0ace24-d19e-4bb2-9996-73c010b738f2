using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("LookupInflationGroup")]
[Comment("Master data table that contains inflation groups. The inflation groups allow us to connect a specific inflation percentage to a variety of things.")]
public class LookupInflationGroup
{
    [Key]
    [Column("InflID")]
    [Comment("Unique ID (PK of LookupInflationGroup)")]
    public int InflId { get; set; }
    
    [StringLength(30)]
    [Unicode(false)]
    [Comment("Inflation group name. Describes what the inflation percentage belongs to.")]
    public string InflName { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    [Comment("Inflation percentage for this specific item.")]
    public decimal? InflPercentage { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Last modification of this record was made by this user")]
    public string InflModifiedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    [Comment("Date the record was last modified. (null if never modified)")]
    public DateTime? InflDateModified { get; set; }

    public virtual ICollection<CommonCost> CommonCost { get; set; } = new HashSet<CommonCost>();
}
