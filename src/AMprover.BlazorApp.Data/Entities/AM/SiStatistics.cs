using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("TblSiStatistics")]
[Index(nameof(SiStatScenarioId), Name = "IX_SiStatistics_ScenarioId")]
[Index(nameof(SiStatSiId), Name = "IX_SiStatisticsSiID")]
public class SiStatistics
{
    [Key]
    [Column("SiStatID")]
    public int SiStatId { get; set; }
    
    [Column("SiStatScenarioID")]
    public int SiStatScenarioId { get; set; }
    
    [Column("SiStatSiID")]
    public int SiStatSiId { get; set; }
    
    [Unicode(false)]
    public string SiStatSumListBefore { get; set; }
    
    [Unicode(false)]
    public string SiStatSumListAfter { get; set; }
    
    [Unicode(false)]
    public string SiStatColorListBefore { get; set; }
    
    [Unicode(false)]
    public string SiStatColorListAfter { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? SiStatDateModified { get; set; }
    
    [Unicode(false)]
    [StringLength(50)]
    public string SiStatModifiedBy { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    [DefaultValue(0)]
    public decimal? SiStatRiskBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    [DefaultValue(0)]
    public decimal? SiStatRiskAfter { get; set; }
    
    [Column("SiStatFmecaID")]
    public int? SiStatFmecaId { get; set; }
}
