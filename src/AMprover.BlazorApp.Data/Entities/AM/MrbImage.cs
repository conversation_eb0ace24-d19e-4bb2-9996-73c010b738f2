using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblMrbImage")]
public class MrbImage
{
    /// <summary>
    /// Path to the image showing the risk before mitigation
    /// </summary>
    public string MrbImageImagePathBefore { get; set; }
    
    /// <summary>
    /// Path to the image showing the risk after mitigation
    /// </summary>
    public string MrbImageImagePathAfter { get; set; }
    
    /// <summary>
    /// Path to the image showing the PMO view
    /// </summary>
    public string MrbImageImagePathPmo { get; set; }
    
    /// <summary>
    /// Unique ID (PK of MrbImage), matches the MrbId of the related risk
    /// </summary>
    [Key]
    [Column("MrbImageID")]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public int MrbImageId { get; set; }
    
    /// <summary>
    /// Date when the image was created or last updated
    /// </summary>
    [Column(TypeName = "datetime")]
    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public DateTime MrbImageDate { get; set; }

    /// <summary>
    /// Navigation property for the related risk
    /// </summary>
    [ForeignKey("MrbImageId")]
    public virtual Mrb MrbImageNavigation { get; set; }
}
