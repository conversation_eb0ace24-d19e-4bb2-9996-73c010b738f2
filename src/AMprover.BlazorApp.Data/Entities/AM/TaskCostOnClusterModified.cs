using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Keyless]
[Table("TaskCostOnClusterModified")]
public class TaskCostOnClusterModified
{
    public int? TskCluster { get; set; }
    
    [Column(TypeName = "decimal(38, 2)")]
    public decimal? TskCosts { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClusterTaskCost { get; set; }
}
