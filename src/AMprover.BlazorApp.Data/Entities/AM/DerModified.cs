using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("TblDerModified")]
public class DerModified
{
    [Key]
    [Column("DerModID")]
    public int DerModId { get; set; }
    
    /// <summary>
    /// 0=scenario 1=riskobject 2=risk 3=task 4=siFilter 5=pickSi
    /// </summary>
    public int DerModObjectType { get; set; }
    
    /// <summary>
    /// If this bit is set then the object is will be shown as if it has been deleted but the parent properties will be still be shown
    /// </summary>
    public bool? DerModDeleted { get; set; }
    
    /// <summary>
    /// The ID of the object whereto modifications have been applied
    /// </summary>
    [Column("DerModObjectID")]
    public int? DerModObjectId { get; set; }
    
    /// <summary>
    /// For auditing reasons it is easier to know what the original object is
    /// </summary>
    public int? DerModCopiedFrom { get; set; }
    
    /// <summary>
    /// Not used can be applied for objects that have no unique int key
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string DerModObjectKey { get; set; }
    
    /// <summary>
    /// XML string with the modifications
    /// </summary>
    [Unicode(false)]
    public string DerModModifications { get; set; }
}
