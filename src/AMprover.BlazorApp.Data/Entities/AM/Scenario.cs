using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// Allows the user to define risks differently, which enables them to see the differences in outcome when taking a different approach to maintenance.
/// </summary>
[Table("TblScenario")]
public class Scenario
{
    public Scenario()
    {
        RiskObject = new HashSet<RiskObject>();
        Clusters = new HashSet<Cluster>();
    }

    /// <summary>
    /// Unique ID (PK of Scenario)
    /// </summary>
    [Key]
    [Column("ScenID")]
    public int ScenId { get; set; }
    
    /// <summary>
    /// Name of the scenario
    /// </summary>
    [Required]
    [StringLength(50)]
    [Unicode(false)]
    public string ScenName { get; set; }
    
    /// <summary>
    /// Description of the scenario
    /// </summary>
    [Column(TypeName = "text")]
    public string ScenDescr { get; set; }
    
    /// <summary>
    /// Created by user
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string ScenInitiatedBy { get; set; }
    
    /// <summary>
    /// Creation date
    /// </summary>
    [Column(TypeName = "smalldatetime")]
    public DateTime? ScenDateInitiated { get; set; }
    
    /// <summary>
    /// Short key of the scenario
    /// </summary>
    [Required]
    [StringLength(4)]
    [Unicode(false)]
    [Column(TypeName = "char(4)")]
    public string ScenShrtKey { get; set; }
    
    /// <summary>
    /// Start point of the scenario
    /// </summary>
    [Column(TypeName = "text")]
    public string ScenStartPoint { get; set; }
    
    /// <summary>
    /// Used when scenario was copied from another scenario. Contains the ID of the scenario which was the source of the copy (master scenario) (FK to Scenario)
    /// This ID is used in the tree for building hierarchical scenario's
    /// </summary>
    public int? ScenCopiedFrom { get; set; }
    
    /// <summary>
    /// User name of person that made the last modification to this record
    /// </summary>
    [StringLength(50)]
    [Unicode(false)]
    public string ScenModifiedBy { get; set; }
    
    /// <summary>
    /// Date the record was last modified. (null if never modified)
    /// </summary>
    [Column(TypeName = "smalldatetime")]
    public DateTime? ScenDateModified { get; set; }
    
    /// <summary>
    /// Determines the typeof relation the scenario has with its parent.
    /// It can be a plain copy (version 3.15 and before) 
    /// or
    /// Derived scenario for creating a scenario with overriden risks / tasks but keeping the parent risks/tasks
    /// </summary>
    public int? ScenChildType { get; set; }

    /// <summary>
    /// Status of the scenario
    /// </summary>
    public int? ScenStatus { get; set; }
    
    /// <summary>
    /// Navigation property for scenario status
    /// </summary>
    [ForeignKey("ScenStatus")]
    public virtual Lookup ScenStatusNavigation { get; set; }
    
    /// <summary>
    /// Collection of risk objects associated with this scenario
    /// </summary>
    public virtual ICollection<RiskObject> RiskObject { get; set; }

    /// <summary>
    /// Collection of clusters associated with this scenario
    /// </summary>
    public virtual ICollection<Cluster> Clusters { get; set; }

    public override string ToString()
    {
        return ScenName;
    }
}
