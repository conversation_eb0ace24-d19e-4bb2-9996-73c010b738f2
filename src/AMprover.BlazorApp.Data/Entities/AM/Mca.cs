using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// Contains data for multiple criteria analysis. (MCA) MCA is a scientific procedure 
/// that allows for a rational choice based on more than one distinguishing criterium.
/// </summary>
[Table("TblMca")]
public class Mca
{
    /// <summary>
    /// Unique ID (PK of Mca)
    /// </summary>
    [Key]
    [Column("McaID")]
    public int McaId { get; set; }
    
    /// <summary>
    /// ID of the significant item the multi criteria analyse belongs to (FK to Si)
    /// </summary>
    [Column("McaSiID")]
    public int McaSiId { get; set; }
    
    /// <summary>
    /// Commercial score for the multi criteria analyse
    /// </summary>
    public int? McaCommercial { get; set; }
    
    /// <summary>
    /// Utilization score for the multi criteria analyse
    /// </summary>
    public int? McaUtilization { get; set; }
    
    /// <summary>
    /// Social score for the multi criteria analyse
    /// </summary>
    public int? McaSocial { get; set; }
    
    /// <summary>
    /// Usage score for the multi criteria analyse
    /// </summary>
    public int? McaUsage { get; set; }
    
    /// <summary>
    /// Intensity score for the multi criteria analyse
    /// </summary>
    public int? McaIntensity { get; set; }
    
    /// <summary>
    /// Representative score for the multi criteria analyse
    /// </summary>
    public int? McaRepresentative { get; set; }
    
    /// <summary>
    /// Total score (business value) of the multi criteria analyse
    /// </summary>
    public int? McaQualityScore { get; set; }
    
    /// <summary>
    /// Level of the multi criteria analyse depending of the business value (0,1,2)
    /// </summary>
    public int? McaQualityLevel { get; set; }
    
    /// <summary>
    /// Navigation property for the significant item
    /// </summary>
    [ForeignKey(nameof(McaSiId))]
    public virtual Si Si { get; set; }
}
