using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Keyless]
public class LccSiCalc
{
    [Column("MrbID")]
    public int MrbId { get; set; }
    
    [Required]
    [MaxLength(50)]
    [Unicode(false)]
    public string MrbName { get; set; }
    
    public int MrbRiskObject { get; set; }
    
    public int? MrbFailureMode { get; set; }
    
    [MaxLength(20)]
    [Unicode(false)]
    public string MrbFailureCategorie1 { get; set; }
    
    [MaxLength(20)]
    [Unicode(false)]
    public string MrbFailureCategorie2 { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? MrbDownTimeAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    public decimal? MrbDownTimeBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbSpareCosts { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbCapCosts { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbActionCosts { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbOptimalCosts { get; set; }
    
    [Unicode(false)]
    public string MrbFmecaSelect { get; set; }
    
    public int MrbFmecaVersion { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbEffectBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCustomBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbMtbfBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbRiskBefore { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbEffectAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbMtbfAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbRiskAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 3)")]
    public decimal? MrbCustomAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbSpareManageCost { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbDirectCostAfter { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? MrbDirectCostBefore { get; set; }
    
    [Required]
    [MaxLength(40)]
    [Unicode(false)]
    public string FailMode { get; set; }
    
    [Column("PckSiSiID")]
    public int PckSiSiId { get; set; }
    
    [Unicode(false)]
    public string FmecaMatrix { get; set; }
    
    public int FmecaVersion { get; set; }
    
    [Column("FailRateID")]
    public int FailRateId { get; set; }
}
