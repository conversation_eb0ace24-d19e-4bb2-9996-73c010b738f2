using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

/// <summary>
/// View entity for cluster reports
/// </summary>
[Table("ClusterReport")]
[Keyless]
public class ClusterReport
{
    [Column("ClcID")]
    public int? ClcId { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustCostCost { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustDisciplineCosts { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustDownTime { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustDuration { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustEnergyCosts { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustEstTaskCosts { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string ClustExecutor { get; set; }

    [Column("ClustID")]
    public int? ClustId { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string ClustInitiator { get; set; }

    [Column(TypeName = "decimal(18, 4)")]
    public decimal? ClustInterval { get; set; }

    [StringLength(20)]
    [Unicode(false)]
    public string ClustIntervalUnit { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustMaterialCosts { get; set; }

    [Required]
    [StringLength(50)]
    [Unicode(false)]
    public string ClustName { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string ClustName0 { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string ClustName1 { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string ClustName2 { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string ClustName3 { get; set; }

    [Unicode(false)]
    public string ClustRemark { get; set; }

    [Unicode(false)]
    public string ClustResponsible { get; set; }

    [Unicode(false)]
    public string ClustSecondValues { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustSharedCosts { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string ClustShortKey { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustTaskCosts { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustToolCosts { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? ClustTotalCmnCost { get; set; }

    [StringLength(20)]
    [Unicode(false)]
    public string CmnCostType { get; set; }

    [Unicode(false)]
    public string MrbFailureCause { get; set; }

    [Unicode(false)]
    public string MrbFailureConsequences { get; set; }

    [Column("MrbID")]
    public int? MrbId { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string MrbObject0 { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string MrbObject2 { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string MrbObject3 { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string MrbObject4 { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string ParentClusterName { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string RiskObjName { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string RiskObject { get; set; }

    [Unicode(false)]
    public string RiskObjectDesc { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string Scenario { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskCostCost { get; set; }

    [StringLength(40)]
    [Unicode(false)]
    public string TskCostDescription { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskCostPrice { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskCostQuantity { get; set; }

    [Column("TskCostTskID")]
    public int? TskCostTskId { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskCostUnits { get; set; }

    [Column(TypeName = "decimal(18, 2)")]
    public decimal? TskCosts { get; set; }

    [Unicode(false)]
    public string TskDescription { get; set; }

    [Column(TypeName = "decimal(18, 4)")]
    public decimal? TskDownTime { get; set; }

    [Column(TypeName = "decimal(18, 4)")]
    public decimal? TskDuration { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string TskExecutor { get; set; }

    [Unicode(false)]
    public string TskGeneralDescription { get; set; }

    [Column("TskID")]
    public int? TskId { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string TskInitiator { get; set; }

    [Column(TypeName = "decimal(18, 4)")]
    public decimal? TskInterval { get; set; }

    [StringLength(20)]
    [Unicode(false)]
    public string TskIntervalUnit { get; set; }

    [Column("TskMrbID")]
    public int? TskMrbId { get; set; }

    [StringLength(60)]
    [Unicode(false)]
    public string TskName { get; set; }

    [Unicode(false)]
    public string TskPermit { get; set; }

    [StringLength(5)]
    [Unicode(false)]
    public string TskPolicy { get; set; }

    [Unicode(false)]
    public string TskRemark { get; set; }

    [StringLength(20)]
    [Unicode(false)]
    public string TskType { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string TskWorkPackage { get; set; }
}
