using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.AM;

[Table("TblAttachmentCategory")]
public class AttachmentCategory
{
    [Key]
    public int AtchCatId { get; set; }

    public string AtchCatTitle { get; set; }

    public string AtchCatDescription { get; set; }

    public virtual ICollection<Attachment> Attachments { get; set; }
}