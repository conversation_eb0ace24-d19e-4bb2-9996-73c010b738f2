using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Keyless]
[Table("PriorityGridData")]
public class PriorityGridData
{
    [Column("PrioID")]
    public int PrioId { get; set; }
    
    [Required]
    [StringLength(50)]
    [Unicode(false)]
    public string PrioName { get; set; }
    
    [Column("PrioSiID")]
    public int? PrioSiId { get; set; }
    
    public int? PrioSiCategory { get; set; }
    
    [StringLength(20)]
    [Unicode(false)]
    public string PrioStatus { get; set; }
    
    [Column("PrioBudID")]
    public int PrioBudId { get; set; }
    
    [Column("PrioBudPriorityID")]
    public int? PrioBudPriorityId { get; set; }
    
    public int? PrioBudVersion { get; set; }
    
    [Unicode(false)]
    public string PrioBudRemark { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudBudget { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudAccepted { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudRejected { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudPostponed { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudProposed { get; set; }
    
    [StringLength(20)]
    [Unicode(false)]
    public string PrioBudStatus { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string PrioBudInitiatedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? PrioBudDateInitiated { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string PrioBudModifiedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? PrioBudDateModified { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudBudgetCostYear1 { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudBudgetCostYear2 { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudBudgetCostYear3 { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudBudgetCostYear4 { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudBudgetCostYear5 { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudBudgetSumOfParts { get; set; }
    
    public int? PrioPartOf { get; set; }
    
    public bool? PrioBudTaskSelectDirty { get; set; }
    
    public bool? PrioBudEnableTaskSelect { get; set; }
    
    [Column(TypeName = "decimal(18, 2)")]
    public decimal? PrioBudCostSelected { get; set; }
    
    [Column("PrioBudOriginalID")]
    public int? PrioBudOriginalId { get; set; }
}
