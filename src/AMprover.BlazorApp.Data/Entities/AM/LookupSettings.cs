using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table("LookupSettings")]
[Comment("Master data table that defines settings for a variety of things within the Amprover software. (like grid column settings and program constants) Not editable by user.")]
public class LookupSettings
{
    [Key]
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Unique ID and the name of the setting. (PK of LookupSettings)")]
    public string AmsProperty { get; set; }
    
    [Unicode(false)]
    [Comment("The text value containing the values of the LookupSetting. Can be either null, a pipe ('|') delimited string, or XML.")]
    public string AmsTextValue { get; set; }
    
    [Column(TypeName = "decimal(18, 4)")]
    [Comment("Decimal value of the setting")]
    public decimal? AmsDecimalValue { get; set; }
    
    [Comment("Integer value of the setting")]
    public int? AmsIntValue { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("Created by user")]
    public string AmsInitiatedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    [Comment("Creation date")]
    public DateTime? AmsDateInitiated { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    [Comment("User that made the last modification to this record")]
    public string AmsModifiedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    [Comment("Date the record was last modified. (null if never modified)")]
    public DateTime? AmsDateModified { get; set; }
    
    [Comment("Boolean value that states if the setting is modifiable")]
    public bool? AmsModifiable { get; set; }
}
