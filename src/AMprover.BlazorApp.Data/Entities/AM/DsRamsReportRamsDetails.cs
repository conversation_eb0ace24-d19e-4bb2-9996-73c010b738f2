using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Table(nameof(DsRamsReportRamsDetails))]
[Keyless]
public class DsRamsReportRamsDetails
{
    [NotMapped]
    [Column("RamsDgID")]
    public int RamsDgId { get; set; }
    
    [Column("RamsDgReferenceID")]
    [MaxLength(30)]
    [Unicode(false)]
    public string RamsDgReferenceId { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string RamsDgName { get; set; }
    
    [Unicode(false)]
    public string RamsDgDescr { get; set; }
    
    [Unicode(false)]
    public string RamsDgRemark { get; set; }
    
    [Column("RamsDgSiRefID")]
    [MaxLength(50)]
    [Unicode(false)]
    public string RamsDgSiRefId { get; set; }
    
    [Column("RamsDgSiID")]
    public int? RamsDgSiId { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string RamsDgInitiatedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? RamsDgDateInitiated { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string RamsDgModifiedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? RamsDgDateModified { get; set; }
    
    public int? RamsDgStatus { get; set; }
    
    public int? RamsDgTestInterval { get; set; }
    
    public int? RamsDgHorizon { get; set; }
    
    [Column("RamsDgWantLCC")]
    public bool? RamsDgWantLcc { get; set; }
    
    [Column("RamsID")]
    public int RamsId { get; set; }
    
    [Column("RamsDiagramRefID")]
    public int? RamsDiagramRefId { get; set; }
    
    [Column("RamsRiskObjectID")]
    public int? RamsRiskObjectId { get; set; }
    
    [Column("RamsObjectID")]
    public int? RamsObjectId { get; set; }
    
    [Column("RamsRiskID")]
    public int? RamsRiskId { get; set; }
    
    [Unicode(false)]
    public string RamsDescr { get; set; }
    
    public double? RamsAvailabilityInput { get; set; }
    
    public double? RamsAvailabilityOutput { get; set; }
    
    [Column("RamsMTBFTechn")]
    public double? RamsMtbftechn { get; set; }
    
    [Column("RamsMTBFFunct")]
    public double? RamsMtbffunct { get; set; }
    
    [Column("RamsMTTR")]
    public double? RamsMttr { get; set; }
    
    [Unicode(false)]
    public string RamsFunctionalDemand { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? RamsTotalCost { get; set; }
    
    [Column(TypeName = "image")]
    public byte[] RamsBitmap { get; set; }
    
    [Unicode(false)]
    public string RamsRemark { get; set; }
    
    [Column("RamsPFD")]
    public double? RamsPfd { get; set; }
    
    [Column("RamsDCd")]
    public double? RamsDcd { get; set; }
    
    [Column("RamsClassDC")]
    [MaxLength(6)]
    [Unicode(false)]
    public string RamsClassDc { get; set; }
    
    public double? RamsBeta { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? RamsDateModified { get; set; }
    
    public double? RamsUtilizationTechn { get; set; }
    
    public double? RamsUtilizationFunct { get; set; }
    
    public double? RamsProductivityTechn { get; set; }
    
    public double? RamsProductivityFunct { get; set; }
    
    public double? RamsEcoTechn { get; set; }
    
    public double? RamsEcoFunct { get; set; }
    
    public int RamsPartOf { get; set; }
    
    public bool RamsContainer { get; set; }
    
    [Column("RamsXPosition")]
    public int? RamsXposition { get; set; }
    
    [Column("RamsYPosition")]
    public int? RamsYposition { get; set; }
    
    public int RamsLinkedLeft { get; set; }
    
    public int RamsLinkedRight { get; set; }
    
    public int? RamsYear { get; set; }
    
    public int? RamsXooN { get; set; }
    
    public int? RamsStatus { get; set; }
    
    public int? RamsParallelBlocks { get; set; }
    
    public int? RamsReadSequence { get; set; }
    
    public bool? RamsIdentical { get; set; }
    
    public bool? RamsCompleted { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? RamsPreventiveCost { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? RamsTechnCorrCost { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? RamsCircuitDepCorrCost { get; set; }
    
    [Column(TypeName = "decimal(18, 0)")]
    public decimal? RamsFailCorrCost { get; set; }
    
    public int? RamsLinkType { get; set; }
    
    public int? RamsLinkMethod { get; set; }
    
    public bool? RamsCollapsed { get; set; }
    
    [Column("RamsWantLCC")]
    public bool? RamsWantLcc { get; set; }
    
    public bool? RamsCostOwner { get; set; }
    
    public bool? RamsCostLinked { get; set; }
    
    [Unicode(false)]
    public string RamsInitiatedBy { get; set; }
    
    [Column(TypeName = "smalldatetime")]
    public DateTime? RamsDateInitiated { get; set; }
    
    [Unicode(false)]
    public string RamsModifiedBy { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string RamsName { get; set; }
    
    [Column("RamsSiID")]
    public int? RamsSiId { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string RamsDgScenario { get; set; }
    
    [MaxLength(50)]
    [Unicode(false)]
    public string RamsDgRiskObject { get; set; }
    
    public double? RamsFuncReliability { get; set; }
    
    public double? RamsTechnReliability { get; set; }
}
