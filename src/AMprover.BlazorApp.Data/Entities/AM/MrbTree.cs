using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace AMprover.Data.Entities.AM;

[Keyless]
[Table("MrbTree")]
public class MrbTree
{
    [Column("MrbID")]
    public int? MrbId { get; set; }
    
    [Column("ScenarioID")]
    public int? ScenarioId { get; set; }
    
    [Column("RiskObjID")]
    public int? RiskObjId { get; set; }
    
    [Column("ChildObjectID")]
    public int? ChildObjectId { get; set; }
    
    [Column("ChildObject1ID")]
    public int? ChildObject1Id { get; set; }
    
    [Column("ChildObject2ID")]
    public int? ChildObject2Id { get; set; }
    
    [Column("ChildObject3ID")]
    public int? ChildObject3Id { get; set; }
    
    [Column("ChildObject4ID")]
    public int? ChildObject4Id { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string MrbName { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ScenarioName { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string RiskObjName { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ChildObjectName { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ChildObject1Name { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ChildObject2Name { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ChildObject3Name { get; set; }
    
    [StringLength(50)]
    [Unicode(false)]
    public string ChildObject4Name { get; set; }
}
