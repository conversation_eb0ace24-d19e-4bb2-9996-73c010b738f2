using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AMprover.Data.Entities.Identity;

/// <summary>
/// A portfolio is a pointer to a databases contains all the data loaded into AMprover for the logged-in user, and holds all data for a company or location for example.
/// </summary>
public class Portfolio
{
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Key]
    public int Id { get; set; }

    public string Name { get; set; }
    public string DatabaseName { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)] // https://docs.microsoft.com/en-us/ef/core/modeling/generated-properties?tabs=data-annotations#value-generated-on-add-1
    public DateTime CreatedOn { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Computed)] // https://docs.microsoft.com/en-us/ef/core/modeling/generated-properties?tabs=data-annotations#value-generated-on-add-1
    public DateTime LastUpdatedOn { get; set; }

    public ICollection<PortfolioAssignment> UserAssignments { get; set; }

    //TODO: remove weird construction and work with model instead of db entity
    [NotMapped]
    public string Checked { get; set; }
}