using AMprover.BusinessLogic.Models.RiskAnalysis;
using AutoMapper;

namespace AMprover.BlazorApp.Configuration.AutoMapper;

public static class RiskObjectMapping
{
    public static void CreateRiskObjectMapping(this IMapperConfigurationExpression mc)
    {
            mc.CreateMap<Data.Entities.AM.RiskObject, RiskObjectModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.RiskObjId))
                .ForMember(dest => dest.AnalysisType, opt => opt.MapFrom(src => src.RiskObjAnalyseType))
                .ForMember(dest => dest.StatusNavigation, opt => opt.MapFrom(src => src.RiskObjStatusNavigation))
                .ForMember(dest => dest.DepartmentId, opt => opt.MapFrom(src => src.RiskObjDepartmentId))
                .ForMember(dest => dest.Department, opt => opt.MapFrom(src => src.RiskObjDepartment))
                // Ignore Risks to prevent circular reference
                .ForMember(dest => dest.Risks, opt => opt.Ignore())
                .ReverseMap()
                .ForMember(dest => dest.RiskObjDepartmentId, opt => opt.MapFrom(src => src.DepartmentId))
                .ForMember(dest => dest.RiskObjDepartment, opt => opt.Ignore())
                .ForMember(src => src.RiskObjFmeca, opt => opt.Ignore())
                .ForMember(src => src.RiskObjScenario, opt => opt.Ignore())
                .ForMember(src => src.RiskObjObject, opt => opt.Ignore())
                .ForMember(src => src.RiskObjParentObject, opt => opt.Ignore());

            mc.CreateMap<RiskObjectModel, RiskObjectModelFlat>();

            mc.CreateMap<Data.Entities.AM.RiskObject, RiskObjectModelFlat>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.RiskObjId))
                .ForMember(dest => dest.AnalysisType, opt => opt.MapFrom(src => src.RiskObjAnalyseType))
                .ReverseMap();

            mc.CreateMap<NewRiskObjectSettings, Data.Entities.AM.RiskObject>()
                .ForMember(dest => dest.RiskObjParentObjectId, opt => opt.MapFrom(src => src.CollectionId))
                .ForMember(dest => dest.RiskObjObjectId, opt => opt.MapFrom(src => src.InstallationId))
                .ForMember(dest => dest.RiskObjId, opt => opt.Ignore());
        }
}
