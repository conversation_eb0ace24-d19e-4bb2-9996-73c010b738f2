using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AutoMapper;

namespace AMprover.BlazorApp.Configuration.AutoMapper;

public static class RiskModelMapping
{
    public static void CreateRiskModelMapping(this IMapperConfigurationExpression mc)
    {
            mc.CreateMap<RiskModel, Data.Entities.AM.Mrb>()
                .ForMember(dest => dest.RiskObject, opt => opt.Ignore())
                .ForMember(dest => dest.ChildObject, opt => opt.Ignore())
                .ForMember(dest => dest.Installation, opt => opt.Ignore())
                .ForMember(dest => dest.System, opt => opt.Ignore())
                .ForMember(dest => dest.Component, opt => opt.Ignore())
                .ForMember(dest => dest.Assembly, opt => opt.Ignore())
                .ForMember(dest => dest.FailureMode, opt => opt.Ignore())
                .ForMember(dest => dest.Tasks, opt => opt.Ignore())
                .ForMember(dest => dest.Mrbid, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.MrbStatus, opt => opt.MapFrom(src => MappingConfiguration.GetIntFromStatus(src.ItemStatus)))
                .ForMember(dest => dest.MrbChildObject, opt => opt.MapFrom(src => src.CollectionId))
                .ForMember(dest => dest.MrbRiskObject, opt => opt.MapFrom(src => src.RiskObjectId))
                .ForMember(dest => dest.MrbChildObject1, opt => opt.MapFrom(src => src.InstallationId))
                .ForMember(dest => dest.MrbChildObject2, opt => opt.MapFrom(src => src.SystemId))
                .ForMember(dest => dest.MrbChildObject3, opt => opt.MapFrom(src => src.ComponentId))
                .ForMember(dest => dest.MrbChildObject4, opt => opt.MapFrom(src => src.AssemblyId))
                .ForMember(dest => dest.MrbSpareManageCost, opt => opt.MapFrom(src => src.SpareManagementCost))
                .ForMember(dest => dest.MrbSpareManageCostPmo, opt => opt.MapFrom(src => src.SpareManagementCostPmo))
                .ForMember(dest => dest.MrbFailureMode, opt => opt.MapFrom(src => src.FailureModeId))
                .ForMember(dest => dest.MrbFailureCategorie1, opt => opt.MapFrom(src => src.FailureCategory1))
                .ForMember(dest => dest.MrbFailureCategorie2, opt => opt.MapFrom(src => src.FailureCategory2))
                .ForMember(dest => dest.MrbFmecaSelect, opt => opt.MapFrom(src => src.GetMrbFmecaSelect()))
                .ForMember(dest => dest.MrbFmecaMtbfBefore, opt => opt.MapFrom(src => src.FmecaSelections.Before.MtbfSelected))
                .ForMember(dest => dest.MrbFmecaMtbfAfter, opt => opt.MapFrom(src => src.FmecaSelections.After.MtbfSelected))
                .ForMember(dest => dest.MrbFmecaMtbfPmo, opt => opt.MapFrom(src => src.FmecaSelections.Pmo.MtbfSelected))
                .ForMember(dest => dest.MrbFmecaSelectionBefore, opt => opt.MapFrom(src => string.Join(",", src.FmecaSelections.Before.Selected)))
                .ForMember(dest => dest.MrbFmecaSelectionAfter, opt => opt.MapFrom(src => string.Join(",", src.FmecaSelections.After.Selected)))
                .ForMember(dest => dest.MrbFmecaSelectionPmo, opt => opt.MapFrom(src => string.Join(",", src.FmecaSelections.Pmo.Selected)))
                .ForMember(dest => dest.MrbImage, opt => opt.Ignore())
                .ForMember(dest => dest.MrbAdditionalData, opt => opt.Ignore())
                .PreserveReferences()
                .ReverseMap()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Mrbid))
                .ForMember(dest => dest.RiskObject, opt => opt.MapFrom(src => src.RiskObject))
                .ForMember(dest => dest.Collection, opt => opt.MapFrom(src => src.ChildObject))
                .ForMember(dest => dest.Installation, opt => opt.MapFrom(src => src.Installation))
                .ForMember(dest => dest.System, opt => opt.MapFrom(src => src.System))
                .ForMember(dest => dest.Component, opt => opt.MapFrom(src => src.Component))
                .ForMember(dest => dest.Assembly, opt => opt.MapFrom(src => src.Assembly))
                .ForMember(dest => dest.FailureMode, opt => opt.MapFrom(src => src.FailureMode))
                .ForMember(dest => dest.Spares, opt => opt.MapFrom(src => src.Spares))
                .ForMember(dest => dest.Tasks, opt => opt.MapFrom(src => src.Tasks))
                .ForMember(dest => dest.FmecaSelections, opt => opt.MapFrom(src => src.ToFmecaSelections()))
                .ForMember(dest => dest.ItemStatus, opt => opt.MapFrom(src => MappingConfiguration.GetStatusFromFromInt(src.MrbStatus)))
                .AfterMap((_, r) => r.BuildRiskMatrix());

            mc.CreateMap<RiskModel, RiskTreeObject>().ReverseMap();

            mc.CreateMap<RiskModel, RiskModelFlat>().ReverseMap();

            mc.CreateMap<RiskModelFlat, Data.Entities.AM.Mrb>()
               .ForMember(dest => dest.Mrbid, opt => opt.MapFrom(src => src.Id))
               .ForMember(dest => dest.MrbChildObject, opt => opt.MapFrom(src => src.CollectionId))
               .ForMember(dest => dest.MrbRiskObject, opt => opt.MapFrom(src => src.RiskObjectId))
               .ForMember(dest => dest.MrbChildObject1, opt => opt.MapFrom(src => src.InstallationId))
               .ForMember(dest => dest.MrbChildObject2, opt => opt.MapFrom(src => src.SystemId))
               .ForMember(dest => dest.MrbChildObject3, opt => opt.MapFrom(src => src.ComponentId))
               .ForMember(dest => dest.MrbChildObject4, opt => opt.MapFrom(src => src.AssemblyId))
               .ForMember(dest => dest.MrbSpareManageCost, opt => opt.MapFrom(src => src.SpareManagementCost))
               .ForMember(dest => dest.MrbSpareManageCostPmo, opt => opt.MapFrom(src => src.SpareManagementCostPmo))
               .ForMember(dest => dest.MrbFailureMode, opt => opt.MapFrom(src => src.FailureModeId))
               .ForMember(dest => dest.MrbFailureCategorie1, opt => opt.MapFrom(src => src.FailureCategory1))
               .ForMember(dest => dest.MrbFailureCategorie2, opt => opt.MapFrom(src => src.FailureCategory2));

            mc.CreateMap<Data.Entities.AM.Mrb, RiskBaseModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Mrbid))
                .ReverseMap()
                .ForMember(dest => dest.Mrbid, opt => opt.MapFrom(src => src.Id));

            mc.CreateMap<Data.Entities.AM.Mrb, RiskWithObjectsModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Mrbid))
                .ForMember(dest => dest.CollectionId, opt => opt.MapFrom(src => src.MrbChildObject))
                .ForMember(dest => dest.InstallationId, opt => opt.MapFrom(src => src.MrbChildObject1))
                .ForMember(dest => dest.SystemId, opt => opt.MapFrom(src => src.MrbChildObject2))
                .ForMember(dest => dest.ComponentId, opt => opt.MapFrom(src => src.MrbChildObject3))
                .ForMember(dest => dest.AssemblyId, opt => opt.MapFrom(src => src.MrbChildObject4))
                .ForMember(dest => dest.RiskObjectId, opt => opt.MapFrom(src => src.MrbRiskObject))
                // Ignore RiskObject to prevent circular reference
                .ForMember(dest => dest.RiskObject, opt => opt.Ignore())
                .ForMember(dest => dest.ScenarioId, opt => opt.MapFrom(src => src.RiskObject.RiskObjScenarioId))
                .ReverseMap()
                .ForMember(dest => dest.Mrbid, opt => opt.MapFrom(src => src.Id));
        }

    private static string GetMrbFmecaSelect(this RiskModel model) =>
        XMLSerializer.Serialize(model.ToRiskData());
}
