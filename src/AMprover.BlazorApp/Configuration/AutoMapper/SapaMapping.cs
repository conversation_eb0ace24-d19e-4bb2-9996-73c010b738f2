using System.IO;
using System.Linq;
using AMprover.BusinessLogic.Models.Sapa;
using AMprover.Data.Entities.AM;
using AutoMapper;

namespace AMprover.BlazorApp.Configuration.AutoMapper;

public static class SapaMapping
{
    public static void CreateSapaMapping(this IMapperConfigurationExpression mc)
    {
            mc.CreateMap<SapaCollection, SapaCollectionModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.SapaCollId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.SapaCollName))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.SapaCollDescription))
                .ForMember(dest => dest.Remark, opt => opt.MapFrom(src => src.SapaCollRemark))
                .ForMember(dest => dest.Selected, opt => opt.MapFrom(src => src.SapaCollSelected))
                .ForMember(dest => dest.RiskObjId, opt => opt.MapFrom(src => src.SapaCollRiskObjId))
                .ReverseMap()
                .ForMember(dest => dest.RiskObject, opt => opt.Ignore());

            mc.CreateMap<Sapa, SapaModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.SapaId))
                .ForMember(dest => dest.RiskObjectId, opt => opt.MapFrom(src => src.SapaRiskObjId))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => MappingConfiguration.GetStatusFromFromInt(src.SapaStatusId)))
                .ForMember(dest => dest.Years, opt => opt.MapFrom(src => src.Years.OrderBy(x => x.SapaYearYear)))
                .ReverseMap()
                .ForMember(dest => dest.RiskObject, opt => opt.Ignore())
                .ForMember(dest => dest.Executor, opt => opt.Ignore())
                .ForMember(dest => dest.Initiator, opt => opt.Ignore())
                .ForMember(dest => dest.SapaStatusId, opt => opt.MapFrom(src => MappingConfiguration.GetIntFromStatus(src.Status)));

            mc.CreateMap<SapaYear, SapaYearModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.SapaYearId))
                .ReverseMap()
                .ForMember(dest => dest.Sapa, opt => opt.Ignore());

            mc.CreateMap<SapaDetail, SapaDetailModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.SapaDetId))
                .ForMember(dest => dest.RiskId, opt => opt.MapFrom(src => src.SapaDetMrbId))
                .ForMember(dest => dest.RiskName, opt => opt.MapFrom(src => src.Risk.MrbName))
                .ForMember(dest => dest.RiskObject, opt => opt.MapFrom(src => src.Risk.MrbChildObject))
                .ForMember(dest => dest.RiskObject1, opt => opt.MapFrom(src => src.Risk.MrbChildObject1))
                .ForMember(dest => dest.Installation, opt => opt.MapFrom(src => src.Risk.Installation.ObjName))
                .ForMember(dest => dest.RiskObject2, opt => opt.MapFrom(src => src.Risk.MrbChildObject2))
                .ForMember(dest => dest.System, opt => opt.MapFrom(src => src.Risk.System.ObjName))
                .ForMember(dest => dest.RiskObject3, opt => opt.MapFrom(src => src.Risk.MrbChildObject3))
                .ForMember(dest => dest.Component, opt => opt.MapFrom(src => src.Risk.Component.ObjName))
                .ForMember(dest => dest.RiskObject4, opt => opt.MapFrom(src => src.Risk.MrbChildObject4))
                .ForMember(dest => dest.Assembly, opt => opt.MapFrom(src => src.Risk.Assembly.ObjName))
                .ForMember(dest => dest.RiskBefore, opt => opt.MapFrom(src => src.Risk.MrbRiskBefore))
                .ForMember(dest => dest.RiskAfter, opt => opt.MapFrom(src => src.Risk.MrbRiskAfter))
                .ForMember(dest => dest.PreventiveCosts, opt => opt.MapFrom(src => src.Risk.MrbActionCosts))
                .ForMember(dest => dest.RiskSapaIndex, opt => opt.MapFrom(src => src.Risk.MrbSapaIndex))
                .ForMember(dest => dest.RiskSafetyBefore, opt => opt.MapFrom(src => src.Risk.MrbSafetyBefore))
                .ForMember(dest => dest.RiskSafetyAfter, opt => opt.MapFrom(src => src.Risk.MrbSafetyAfter))
                .ForMember(dest => dest.RiskMarketBefore, opt => opt.MapFrom(src => src.Risk.MrbMarketBefore))
                .ForMember(dest => dest.RiskMarketAfter, opt => opt.MapFrom(src => src.Risk.MrbMarketAfter))
                .ForMember(dest => dest.RiskStatus, opt => opt.MapFrom(src => src.Risk.MrbStatusNavigation.LookupShortDescription))
                .ForMember(dest => dest.RiskMtbfAfter, opt => opt.MapFrom(src => src.Risk.MrbMtbfAfter))
                .ForMember(dest => dest.RiskMtbfBefore, opt => opt.MapFrom(src => src.Risk.MrbMtbfBefore))
                .ForMember(dest => dest.RiskMtbfPmo, opt => opt.MapFrom(src => src.Risk.MrbMtbfPmo))
                .ForMember(dest => dest.RiskDescription, opt => opt.MapFrom(src => src.Risk.MrbDescription))
                .ForMember(dest => dest.RiskFailureCause, opt => opt.MapFrom(src => src.Risk.MrbFailureCause))
                .ForMember(dest => dest.RiskFailureConsequence, opt => opt.MapFrom(src => src.Risk.MrbFailureConsequences))
                .ForMember(dest => dest.RiskRemark, opt => opt.MapFrom(src => src.Risk.MrbRemarks))
                .ForMember(dest => dest.RiskRemark1, opt => opt.MapFrom(src => src.Risk.MrbRemarks1))
                .ForMember(dest => dest.RiskObjectName, opt => opt.MapFrom(src => src.Risk.RiskObject.RiskObjName))
                .ForMember(dest => dest.RiskObjId, opt => opt.MapFrom(src => src.Risk.RiskObject.RiskObjId))
                .ForMember(dest => dest.DepDescription, opt => opt.MapFrom(src => src.Risk.RiskObject.RiskObjDepartment.DepDescription))
                .ForMember(dest => dest.RiskMatrixBefore, opt => opt.MapFrom(src => File.ReadAllBytes(src.Risk.MrbImage.MrbImageImagePathBefore)))
                .ForMember(dest => dest.RiskMatrixAfter, opt => opt.MapFrom(src => File.ReadAllBytes(src.Risk.MrbImage.MrbImageImagePathAfter)))
                .ForMember(dest => dest.TaskId, opt => opt.MapFrom(src => src.SapaDetTskId))
                .ForMember(dest => dest.TaskName, opt => opt.MapFrom(src => src.Task.TskName))
                .ForMember(dest => dest.TaskDescription, opt => opt.MapFrom(src => src.Task.TskDescription))
                .ForMember(dest => dest.TaskRemark, opt => opt.MapFrom(src => src.Task.TskRemark))
                .ForMember(dest => dest.TaskGeneralDescription, opt => opt.MapFrom(src => src.Task.TskGeneralDescription))
                .ForMember(dest => dest.TaskPolicy, opt => opt.MapFrom(src => src.Task.TskMxPolicyNavigation.PolName))
                .ForMember(dest => dest.TaskInterval, opt => opt.MapFrom(src => src.Task.TskInterval))
                .ForMember(dest => dest.TaskIntervalUnit, opt => opt.MapFrom(src => src.Task.TskIntervalUnitNavigation.IntUnitName))
                .ForMember(dest => dest.TaskPartOf, opt => opt.MapFrom(src => src.Task.TskParent.TskName))
                .ForMember(dest => dest.TaskFmecaEffectPct, opt => opt.MapFrom(src => src.Task.TskFmecaEffectPct))
                .ForMember(dest => dest.TaskInitiator, opt => opt.MapFrom(src => src.Task.TskInitiatorNavigation.InitiatorName))
                .ForMember(dest => dest.TaskExecutor, opt => opt.MapFrom(src => src.Task.TskExecutorNavigation.ExecutorName))
                .ForMember(dest => dest.WorkPackageName, opt => opt.MapFrom(src => src.Task.TskSapaWorkpackageNavigation.SapaWpName))
                .ForMember(dest => dest.SapaWorkpackageId, opt => opt.MapFrom(src => src.Task.TskSapaWorkpackage))
                .ReverseMap()
                .ForMember(dest => dest.SapaYear, opt => opt.Ignore())
                .ForMember(dest => dest.Risk, opt => opt.Ignore())
                .ForMember(dest => dest.Task, opt => opt.Ignore());

            mc.CreateMap<SapaWorkpackage, SapaWorkpackageModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.SapaWpId))
                .ForMember(dest => dest.Key, opt => opt.MapFrom(src => src.SapaWpKey))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.SapaWpName))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.SapaWpDescription))
                .ReverseMap();
        }
}