<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="RoScenarioLbl" xml:space="preserve">
    <value>Szenario</value>
  </data>
  <data name="RoNameLbl" xml:space="preserve">
    <value>Navn</value>
  </data>
  <data name="RoMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;I denne &lt;b&gt;Verdi Risikoarrangørmodulen&lt;/b&gt; kan ulike Verdi Risikoanalyser organiseres.&lt;/p&gt;
 &lt;ol&gt;
&lt;li&gt;&lt;b&gt;Opprette&lt;/b&gt; en ny Verdi Risikoanalyse ved å velge &lt;b&gt;Ny Verdi Risikoanalyse&lt;/b&gt;, 
 &lt;ol type='a'&gt;
 &lt;li&gt;Et (unikt) &lt;b&gt;navn&lt;/b&gt; på Verdi Risikoanalyser genereres automatisk og kan endres etterpå.&lt;/b&gt;&lt;/li&gt;
&lt;li&gt;Velg et &lt;b&gt;Scenario&lt;/b&gt;&lt;/li&gt;
&lt;li&gt;Valgfritt – Velg en &lt;b&gt;Kategori&lt;/b&gt;&lt;/li&gt;
&lt;li&gt;Velg det &lt;b&gt;hierarkiske objektet som&lt;/b&gt; Verdi Risikoanalyser skal utføres på&lt;/li&gt;
&lt;li&gt;Velg &lt;b&gt;Verdi Risikomatrise&lt;/b&gt; du vil bruke i vurderingen&lt;/li&gt;
&lt;li&gt;Velg &lt;b&gt;Analysetypen&lt;/b&gt; du vil utføre&lt;/li&gt;
&lt;/ol&gt;
eller &lt;/li&gt;&lt;/br&gt;
&lt;li&gt;&lt;b&gt;Redigere&lt;/b&gt; en eksisterende Verdi Risikoanalyse
 &lt;ol type='a'&gt;
&lt;li&gt;Velg ett eller flere &lt;b&gt;Scenarioer&lt;/b&gt;&lt;/br&gt;
&lt;i&gt;I høyre skjerm får informasjon om det valgte scenariet&lt;/i&gt;&lt;/li&gt;
&lt;li&gt;Merke én eller flere &lt;b&gt;Kategorier&lt;/b&gt; (valgfritt)&lt;/li&gt;
&lt;li&gt;Velg &lt;b&gt;Verdi Risikoanalysetypen&lt;/b&gt; du vil redigere:&lt;/br&gt;
&lt;i&gt;I høyre skjerm gis informasjon om den valgte Verdi Risikoanalyser&lt;/i&gt;
&lt;ol type='i'&gt;
&lt;li&gt;&lt;b&gt;Beskrivelsen&lt;/b&gt; av Verdi Risikoanalyser genereres automatisk ved hjelp av det valgte hierarkiske objektet, den valgte Verdi Risikomatrisen og analysetypen&lt;/li&gt;
&lt;li&gt;En eventuell &lt;b&gt;Analysetype&lt;/b&gt; kan endres&lt;/li&gt;
&lt;i&gt;Forsiktig: Dette vil påvirke vurderingsdataene hvis en matrise med forskjellig størrelse velges&lt;/i&gt;&lt;/li&gt;
&lt;li&gt;&lt;b&gt;Statusen&lt;/b&gt; kan endres&lt;/li&gt;
&lt;li&gt;En eventuell &lt;b&gt;Kategori&lt;/b&gt; kan endres&lt;/li&gt;
&lt;li&gt;&lt;b&gt;Forutsetninger&lt;/b&gt; kan angis:&lt;/br&gt;
&lt;i&gt;Generelt: Utelat elementene du vil blokkjustere. F.eks. "Ingen vedlikehold utføres" for å rettferdiggjøre selve vedlikeholdet.&lt;/i&gt;&lt;/li&gt;
&lt;li&gt;Angi nøyaktig &lt;b&gt;Funksjon&lt;/b&gt; for det &lt;b&gt;hierarkiske objektet&lt;/b&gt;&lt;/li&gt;
&lt;li&gt;&lt;b&gt;Adresser deltakerne&lt;/b&gt; som vurderingen har funnet sted med (+tidsstempel)&lt;/li&gt;
&lt;/ol&gt;&lt;/li&gt;&lt;/ol&gt;&lt;/br&gt;
&lt;li&gt;I &lt;b&gt;målerens&lt;/b&gt; effektivitet av Verdi Risikoanalyser er gitt, kan du lese optimaliseringspotensialet til selve vurderingen&lt;/li&gt;
&lt;li&gt; Via handlingsknappen i rutenettet på fanen Verdi Risikovurdering kan du velge:
&lt;ol type = 'i'&gt;
&lt;li&gt;&lt;b&gt;Rediger&lt;/b&gt; for å åpne den valgte risikovurderingen&lt;/li&gt;       
&lt;li&gt;&lt;b&gt;Åpne LCV&lt;/b&gt; for å gå til LCV-modulen &lt;/li&gt;       
&lt;li&gt;&lt;b&gt;Kopier&lt;/b&gt; for å kopiere den valgte risikovurderingen&lt;/li&gt;       
&lt;li&gt;&lt;b&gt;Lim&lt;/b&gt; inn for å lime inn den kopierte risikovurderingen&lt;/li&gt;
&lt;li&gt;Du kan også &lt;b&gt;slette&lt;/b&gt; den valgte risikovurderingen&lt;/br&gt;
&lt;i&gt;Forsiktig: Den slettede Verdi Risikoanalyser blir slettet permanent&lt;/i&gt;&lt;/li&gt;       
&lt;/ol&gt; &lt;/li&gt;&lt;/li&gt; &lt;/ol&gt;</value>
  </data>
  <data name="RoHeaderTxt" xml:space="preserve">
    <value>Arrangør av Verdi Risiko</value>
  </data>
  <data name="RoMenuTitle" xml:space="preserve">
    <value>Tilnærming for Verdi Risikoarrangør</value>
  </data>
  <data name="RoNewBtnTxt" xml:space="preserve">
    <value>Ny Verdi Risikovurdering</value>
  </data>
  <data name="RoDescriptionLbl" xml:space="preserve">
    <value>Beskrivelse</value>
  </data>
  <data name="RoPreReqLbl" xml:space="preserve">
    <value>Forutsetninger</value>
  </data>
  <data name="RoChildTypeLbl" xml:space="preserve">
    <value>Underordnet type</value>
  </data>
  <data name="RoDepartmentLbl" xml:space="preserve">
    <value>Departement</value>
  </data>
  <data name="RoStatusLbl" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="RoMetadataLbl" xml:space="preserve">
    <value>Metadata</value>
  </data>
  <data name="RoInstructionTxt" xml:space="preserve">
    <value>Vennligst velg minst ett Scenario fra venstre!</value>
  </data>
  <data name="RoValueRiskTxt" xml:space="preserve">
    <value>Verdi Risikovurdering</value>
  </data>
  <data name="RoAnalysisTypeLbl" xml:space="preserve">
    <value>Analysetype</value>
  </data>
  <data name="RoValueRiskMatrixLbl" xml:space="preserve">
    <value>Matrise for Verdi Risiko</value>
  </data>
  <data name="RoEditTxt" xml:space="preserve">
    <value>Åpent</value>
  </data>
  <data name="RoDeleteTxt" xml:space="preserve">
    <value>Slett</value>
  </data>
  <data name="RoValueRisksTxt" xml:space="preserve">
    <value>Verdi Risikovurderinger</value>
  </data>
  <data name="RoAttendedByLbl" xml:space="preserve">
    <value>Deltatt av</value>
  </data>
  <data name="RoFunctionLbl" xml:space="preserve">
    <value>Funksjon</value>
  </data>
  <data name="RoValueRiskTabTxt" xml:space="preserve">
    <value>Verdi Risiko</value>
  </data>
  <data name="RoPreventiveCostLbl" xml:space="preserve">
    <value>Forebyggende kostnader</value>
  </data>
  <data name="RoCorrectiveCostLbl" xml:space="preserve">
    <value>Korrigerende kostnader</value>
  </data>
  <data name="RoDeleteStatementTxt" xml:space="preserve">
    <value>Slett Verdi Risikovurdering</value>
  </data>
  <data name="RoPrevActionTabTxt" xml:space="preserve">
    <value>Forebyggende tiltak</value>
  </data>
  <data name="RoPmoPrevActionTabTxt" xml:space="preserve">
    <value>Gjeldende handlinger</value>
  </data>
  <data name="UpdateExecutor" xml:space="preserve">
    <value>Executor</value>
  </data>
  <data name="UpdateWorkPackage" xml:space="preserve">
    <value>Arbeidspakke</value>
  </data>
  <data name="RoImportFinished" xml:space="preserve">
    <value>Importen er ferdig</value>
  </data>
  <data name="RoImportAnalyzeFile" xml:space="preserve">
    <value>Analyser fil</value>
  </data>
  <data name="RoImportReadyForImport" xml:space="preserve">
    <value>Alle radene er analysert, og du kan starte importen nå.</value>
  </data>
  <data name="RoImportRiskSelectFile" xml:space="preserve">
    <value>Velg Importer fil</value>
  </data>
  <data name="RoImportRiskTxt" xml:space="preserve">
    <value>Du kan importere Verdi Risikoer basert på følgende Verdirisikovurdering</value>
  </data>
  <data name="RoImportTaskNotAllowed" xml:space="preserve">
    <value>Importfil er ikke tillatt, vennligst sjekk importen. Har du importert en Handlingersfil? Refererer risikoimport-ID-en til import-ID-en i verdirisikoanalysen?</value>
  </data>
  <data name="RoImportTaskTxt" xml:space="preserve">
    <value>Du kan importere handlinger basert på følgende Verdi Risikovurdering</value>
  </data>
  <data name="RoImportUpdateReference" xml:space="preserve">
    <value>Funnet objekt av typen: '[type]' som ikke kunne matches med verdier i databasen, vennligst velg erstatninger.</value>
  </data>
  <data name="RoImportRiskGenerateObjLbl" xml:space="preserve">
    <value>Generere generiske objekter</value>
  </data>
  <data name="RoImportRiskGenerateFailModeLbl" xml:space="preserve">
    <value>Generer Feilmodi</value>
  </data>
  <data name="RoExportInfoTxt" xml:space="preserve">
    <value>&lt;p&gt;Mål: Eksportere tabellene Verdi Risiko, Forebyggende tiltak eller Reservedeler fra den valgte &lt;b&gt;Verdi Risikovurderingen&lt;/b&gt; til Excel. Den genererte eksporterte filen kan også brukes til importen.&lt;/p&gt;
&lt;ol&gt;
&lt;li&gt;Åpne Eksporter-modulen via &lt;b&gt;Eksporter&lt;/b&gt;&lt;/li&gt;
&lt;li&gt;I eksportmodulen velger du ønsket &lt;b&gt;Verdi Risikovurderingen&lt;/b&gt; via nedtrekksmenyen &lt;/li&gt;
&lt;li&gt;I de tre fanene vises data i rutenettformat.&lt;/li&gt;
&lt;li&gt;Via &lt;b&gt;Endre kolonner&lt;/b&gt; kan du redigere det nødvendige antallet kolonner for de tre rutenettene og eksportene. &lt;/li&gt;
&lt;li&gt;Via &lt;b&gt;Excel&lt;/b&gt; vil du eksportere filen til nedlastingene.&lt;/li&gt;
&lt;/ol&gt;
&lt;p&gt;ID-definisjoner i Eksporter-modulen: &lt;/p&gt;
&lt;ol type="I"&gt;
&lt;li&gt;&lt;u&gt;Risiko-ID&lt;/u&gt;: Dette er ID-nummeretsom er relatert til verdirisikoen i AMprover. &lt;/li&gt;
&lt;li&gt;&lt;u&gt;Import-ID&lt;/u&gt;: Dette er ID-nummeretsom brukes i modulen Import. &lt;/li&gt;
&lt;ol type = 'a'&gt;
&lt;li&gt;Import-IDen er tom når dataene ikke genereres via importen. &lt;/li&gt;
&lt;li&gt;Import-IDen har en ID når dataene genereres via importen. &lt;/li&gt;
&lt;i&gt;(nye importerte data bør i stedet referere til import-IDen til selve risiko-ID-en)&lt;/i&gt;
&lt;/ol&gt;
&lt;li&gt;&lt;u&gt;Action-ID&lt;/u&gt;: Dette er ID-nummeret til handlingene i listen over forebyggende tiltak. &lt;/li&gt;
&lt;li&gt;&lt;u&gt;Reserve-ID&lt;/u&gt;: Dette er ID-nummeret til reservedelen i listen over reservedeler. &lt;/li&gt;
&lt;/ol&gt;</value>
  </data>
  <data name="RoImportRisksInfoTxt" xml:space="preserve">
    <value>&lt;p&gt;Mål: Å importere en liste over nye risikoer, funksjonstreet og FME(C)A-informasjonen &lt;b&gt;til&lt;/b&gt; den valgte &lt;b&gt;Verdi Risikovurderingen&lt;/b&gt; i AMprover. &lt;/p&gt;
&lt;p&gt;&lt;u&gt;forberedelse:&lt;/u&gt;&lt;/p&gt;
&lt;p&gt;Før importen må tabellen import-verdi-risiko, som genereres via eksportalternativet, klargjøres med data.
Feltet "som skal importeres" må fylles ut. Ytterligere fokus bør være på feltet Risk-ID. Dette feltet – hvis det er tomt – må fylles ut med flere import-ID-numre. &lt;/p&gt;
&lt;ol type = 'I'&gt;
&lt;li&gt;&lt;u&gt;Alternativ 1&lt;/u&gt; "Importer": Hvis risiko-ID-ene i importfilen har nye import-ID-er, blir dataene importert som &lt;b&gt;nye poster&lt;/b&gt; i AMprover. &lt;/li&gt;
&lt;li&gt;&lt;u&gt;Alternativ 2&lt;/u&gt; "Oppdater": Hvis risiko-ID-ene i importfilen har eksisterende import-ID-er, importeres dataene og eksisterende poster i AMprover &lt;b&gt;oppdateres &lt;/b&gt;tilsvarende. &lt;/li&gt;
&lt;/ol&gt;
&lt;p&gt;&lt;u&gt;Handling: &lt;/u&gt;&lt;/p&gt;
&lt;ol &gt;
&lt;li&gt;Klikk på &lt;b&gt;Importer&lt;/b&gt; og velg alternativet Importer risiko
Et valgskjermbilde vil popup.
&lt;li&gt;Velg ønsket &lt;b&gt;Verdi Risikovurderingen&lt;/b&gt; via rullegardinmenyen, der dataene vil bli importert til. Dette er nivå 1 for &lt;b&gt;Generiske hierarkiobjekter&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Merk feltet &lt;b&gt;Generer generiske objekter&lt;/b&gt; bare hvis du vil generere nye objektnavn i modulen &lt;b&gt;Generiske hierarkiobjekter&lt;/b&gt; og dermed i Risikoer.&lt;/li&gt;
&lt;li&gt;Merk feltet &lt;b&gt;Generer feilmodi&lt;/b&gt; bare hvis du vil generere (legge til) nye feilmodi i &lt;b&gt;Feil modulen&lt;/b&gt;. &lt;/li&gt;
&lt;li&gt;Filen skal fylles med data. &lt;/li&gt;
&lt;li&gt;Klikk på &lt;b&gt;Velg importer fil&lt;/b&gt;.&lt;/li&gt;
&lt;i&gt;Filen vil bli analysert. Hvis det ikke oppstår noen problemer, kan filen importeres.&lt;/i&gt;
&lt;li&gt;Klikk på &lt;b&gt;Start import&lt;/b&gt;.&lt;/li&gt;&lt;/ol&gt;
&lt;ol type = 'I'&gt;
&lt;li&gt;Alternativ 1: "Importer": &lt;/li&gt;
&lt;ol type = 'a'&gt;
&lt;li&gt;Informasjonen &lt;b&gt;importeres&lt;/b&gt; deretter til den valgte &lt;b&gt;Verdi Risikovurderingen&lt;/b&gt; som &lt;b&gt;nye poster&lt;/b&gt;. &lt;/li&gt;
&lt;li&gt;Den arkiverte i Risk-ID vil være Import-ID og nye Risk-ID's vil bli generert. &lt;/li&gt;&lt;/ol&gt;
&lt;li&gt;Alternativ 2: "Oppdater": &lt;/li&gt;
&lt;ol type = 'a'&gt;
&lt;li&gt;Informasjonen i AMprover &lt;b&gt;oppdateres&lt;/b&gt; deretter til den valgte &lt;b&gt;Verdi Risikovurderingen&lt;/b&gt;. &lt;/li&gt;&lt;/ol&gt;&lt;/ol&gt;</value>
  </data>
  <data name="RoImportTasksTxt" xml:space="preserve">
    <value>&lt;p&gt;Mål: Å importere en liste over nye oppgaver/handlinger relatert til en risiko-ID i AMprover.&lt;/p&gt;
&lt;p&gt;&lt;u&gt;Forberedelse: &lt;/u&gt;&lt;/p&gt;
Før importen må import- handlinger -tabellen, som genereres via eksportalternativet, klargjøres med data.&lt;/br&gt;
Vær oppmerksom på antall tegn i handlingsfeltet, det må være mindre enn 60 tegn.&lt;/br&gt;
«Feltene som skal legges inn» må fylles ut. Ekstra oppmerksomhet bør rettes mot feltet Import-risiko-ID.  Import-IDen for en verdirisikoanalyse fylles automatisk ut med risiko-ID-en når du importerer en tilsvarende verdirisikofil. Hvis du bare importerer handlinger, må du angi denne import-IDen i kategorien &lt;b&gt;Diverse&lt;/b&gt; i modulverdien for verdirisikoanalyse. &lt;/br&gt;
PS. Feltet Pmo kan settes til 'True' hvis du vil importere oppgaver som PMO-oppgaver.&lt;/p&gt;
&lt;ol &gt;
&lt;li&gt;&lt;u&gt;Alternativ 1&lt;/u&gt; "Importer": Hvis risiko-ID-ene i importfilen har import-ID, blir dataene importert som nye poster i AMprover.&lt;/li&gt;
&lt;li&gt;&lt;u&gt;Alternativ 2 &lt;/u&gt; "Oppdater": Hvis risiko-ID-ene i importfilen har eksisterende risiko-ID-er, importeres dataene og eksisterende poster i AMprover oppdateres tilsvarende.&lt;/li&gt;&lt;/ol&gt;
&lt;p&gt;&lt;u&gt;handling: &lt;/u&gt;&lt;/p&gt;
&lt;ol &gt;
&lt;li&gt;Klikk på &lt;b&gt;Import&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;En valgskjerm vil popup.&lt;/li&gt;
&lt;li&gt;Velg ønsket &lt;b&gt;Verdi Risikovurdering&lt;/b&gt; via rullegardinmenyen, der dataene skal importeres til. Dette er nivå 1 for de &lt;b&gt;Generiske hierarkiobjekter&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Klikk på &lt;b&gt;Velg importer fil&lt;/b&gt;, og velg filmalen du vil importere. Filen skal fylles ut med handlinger-data.&lt;/li&gt;
&lt;li&gt;Navnet på filen vises i rammen. Bruk &lt;b&gt;X&lt;/b&gt; ved siden av filnavnet hvis du ikke vet hvilken fil som skal importeres.&lt;/li&gt;
&lt;li&gt;Klikk på &lt;b&gt;Analyser fil&lt;/b&gt;.&lt;/li&gt;
Filen vil bli &lt;li&gt;analysert, og vurderingsresultatene (datamengder) vil bli vist på grønn bakgrunn.&lt;/li&gt;
&lt;i&gt;Hvis bakgrunnen er rød, må du sjekke dataene.&lt;/i&gt;
&lt;li&gt;Hvis OK, klikker du på &lt;b&gt;Start import&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Hvis ting gikk som planlagt, vil du ha en popup, &lt;b&gt;Importerte XX-elementer.&lt;/b&gt;&lt;/li&gt;&lt;/ol&gt;</value>
  </data>
  <data name="RoImportTasksBtn" xml:space="preserve">
    <value>Import handlinger</value>
  </data>
  <data name="RoImportRisksBtn" xml:space="preserve">
    <value>Import Verdi Risiko</value>
  </data>
  <data name="RoImportSpareBtn" xml:space="preserve">
    <value>Import Reservedeler</value>
  </data>
  <data name="RoImportSpareNotAllowed" xml:space="preserve">
    <value>Importfil er ikke tillatt, vennligst sjekk importen. Har du importert en reservedelsfil? Refererer risikoimport-ID-en til import-ID-en i verdirisikoanalysen?</value>
  </data>
  <data name="RoImportSpareTxt" xml:space="preserve">
    <value>&lt;p&gt;Mål: Å importere en liste over nye Reservedeler relatert til en risiko-ID i AMprover.&lt;/p&gt;
&lt;p&gt;&lt;u&gt;Forberedelse: &lt;/u&gt;&lt;/p&gt;
Før importen må import- Reservedeler-tabellen, som genereres via eksportalternativet, klargjøres med data.&lt;/br&gt;
Vær oppmerksom på antall tegn i ‘spare’ feltet, det må være mindre enn 60 tegn.&lt;/br&gt;
«Feltene som skal legges inn» må fylles ut. Ekstra oppmerksomhet bør rettes mot feltet Import-risiko-ID.  Import-IDen for en verdirisikoanalyse fylles automatisk ut med risiko-ID-en når du importerer en tilsvarende verdirisikofil. Hvis du bare importerer Reservedeler, må du angi denne import-IDen i kategorien &lt;b&gt;Diverse&lt;/b&gt; i modulverdien for risikoanalyse. &lt;/br&gt;
PS. Feltet Pmo kan settes til 'True' hvis du vil importere Reservedeler som PMO- Reservedeler.&lt;/p&gt;
&lt;ol &gt;
&lt;li&gt;&lt;u&gt;Alternativ 1&lt;/u&gt; "Importer": Hvis risiko-ID-ene i importfilen har import-ID, blir dataene importert som nye poster i AMprover.&lt;/li&gt;
&lt;li&gt;&lt;u&gt;Alternativ 2 &lt;/u&gt; "Oppdater": Hvis risiko-ID-ene i importfilen har eksisterende risiko-ID-er, importeres dataene og eksisterende poster i AMprover oppdateres tilsvarende.&lt;/li&gt;&lt;/ol&gt;
&lt;p&gt;&lt;u&gt;handling: &lt;/u&gt;&lt;/p&gt;
&lt;ol &gt;
&lt;li&gt;Klikk på &lt;b&gt;Import&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;En valgskjerm vil popup.&lt;/li&gt;
&lt;li&gt;Velg ønsket &lt;b&gt;Verdi Risikovurdering&lt;/b&gt; via rullegardinmenyen, der dataene skal importeres til. Dette er nivå 1 for de &lt;b&gt;Generiske hierarkiobjekter&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Klikk på &lt;b&gt;Velg importer fil&lt;/b&gt;, og velg filmalen du vil importere. Filen skal fylles ut med reservedeler-data.&lt;/li&gt;
&lt;li&gt;Navnet på filen vises i rammen. Bruk &lt;b&gt;X&lt;/b&gt; ved siden av filnavnet hvis du ikke vet hvilken fil som skal importeres.&lt;/li&gt;
&lt;li&gt;Klikk på &lt;b&gt;Analyser fil&lt;/b&gt;.&lt;/li&gt;
Filen vil bli &lt;li&gt;analysert, og vurderingsresultatene (datamengder) vil bli vist på grønn bakgrunn.&lt;/li&gt;
&lt;i&gt;Hvis bakgrunnen er rød, må du sjekke dataene.&lt;/i&gt;
&lt;li&gt;Hvis OK, klikker du på &lt;b&gt;Start import&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Hvis ting gikk som planlagt, vil du ha en popup, &lt;b&gt;Importerte XX-elementer.&lt;/b&gt;&lt;/li&gt;&lt;/ol&gt;</value>
  </data>
  <data name="RoDownTimeCostLbl" xml:space="preserve">
    <value>Nedetid / timer</value>
  </data>
  <data name="RoPmoGraphLbl" xml:space="preserve">
    <value>Resultat PMO-analyse</value>
  </data>
  <data name="RoActionCosts" xml:space="preserve">
    <value>Handlinger kostnader</value>
  </data>
  <data name="RoDirectRiskCosts" xml:space="preserve">
    <value>Direkte risikokostnad</value>
  </data>
  <data name="RoValueRiskCosts" xml:space="preserve">
    <value>Kostnader Verdi Risiko</value>
  </data>
</root>