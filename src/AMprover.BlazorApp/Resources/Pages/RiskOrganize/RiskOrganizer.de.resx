<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="RoMenuTitle" xml:space="preserve">
    <value>Ansatz für Wert Risk Organizer</value>
  </data>
  <data name="RoMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;In diesem &lt;b&gt;Wert Risiko Organisator &lt;/b&gt;Modul können verschiedene Wert Risiko Bewertungen organisiert werden. &lt;/p&gt;
&lt;ol&gt;
&lt;li&gt;&lt;b&gt;Erstellen&lt;/b&gt; Sie eine neue Wert Risiko Bewertung, indem Sie &lt;b&gt;Neue Wert Risiko Bewertung&lt;/b&gt; auswählen.
&lt;ol type='a'&gt;
&lt;li&gt;Ein (eindeutiger) &lt;b&gt;Name&lt;/b&gt; für die Wert Risiko Bewertung wird automatisch generiert und kann nachträglich geändert werden.&lt;/li&gt;
&lt;li&gt;Wählen Sie ein &lt;b&gt;Szenario&lt;/b&gt; aus&lt;/li&gt;
&lt;li&gt;Optional - Wählen Sie eine &lt;b&gt;Kategorie&lt;/b&gt; aus&lt;/li&gt;
&lt;li&gt;Wählen Sie das &lt;b&gt;Hierarchische Objekt&lt;/b&gt; aus, um die Wert Risiko Bewertung durchzuführen&lt;/li&gt;
&lt;li&gt;Wählen Sie die &lt;b&gt;Wert Risiko Matrix&lt;/b&gt; aus, die Sie innerhalb der Bewertung verwenden möchten&lt;/li&gt;
&lt;li&gt;Wählen Sie den &lt;b&gt;Bewertungsart&lt;/b&gt; aus, den Sie durchführen möchten&lt;/li&gt;&lt;/br&gt;
&lt;/ol&gt;
oder &lt;/li&gt;&lt;/br&gt;
&lt;li&gt;&lt;b&gt;Bearbeiten&lt;/b&gt; einer vorhandenen Wert Risiko Bewertung
&lt;ol type='a'&gt;
&lt;li&gt;Wählen Sie ein oder mehrere &lt;b&gt;Szenarien&lt;/b&gt; aus&lt;/br&gt;
&lt;i&gt;Auf dem rechten Bildschirm werden Informationen über das gewählte Szenario gegeben&lt;/i&gt;&lt;/li&gt;
&lt;li&gt;Wählen Sie eine oder mehrere &lt;b&gt;Kategorien&lt;/b&gt; aus (optional) &lt;/br&gt;
&lt;li&gt;Wählen Sie die &lt;b&gt;Bewertungsart&lt;/b&gt; aus, die Sie bearbeiten möchten: &lt;/li&gt;
&lt;i&gt;Im rechten Bildschirm werden Informationen über die ausgewählte Wert Risiko Bewertung angezeigt&lt;/i&gt;&lt;/li&gt;
&lt;ol type='i'&gt;
&lt;li&gt;Die &lt;b&gt;Name&lt;/b&gt;  der Wert Risiko Bewertung wird automatisch dar gestellt mit Hilfe der gewählten
Hierarchisches Objekt, die gewählte Wert Risiko Matrix und der Analyseart&lt;/li&gt;
&lt;li&gt;Ein eventueller &lt;b&gt;Bewertungsart&lt;/b&gt; kann geändert werden&lt;/li&gt;
&lt;li&gt;Eine eventuelle &lt;b&gt;Wert Risiko Matrix &lt;/b&gt;Auswahl kann geändert werden&lt;/br&gt;
&lt;i&gt;Vorsicht: Dies wirkt sich auf die Analysedaten aus, wenn eine Matrix mit unterschiedlicher Größe gewählt wird&lt;/i&gt;&lt;/li&gt;
&lt;li&gt;Der &lt;b&gt;Status&lt;/b&gt; kann geändert werden&lt;/li&gt;
&lt;li&gt;Eine eventuelle &lt;b&gt;Kategorie&lt;/b&gt; kann geändert werden&lt;/li&gt;
&lt;li&gt;Die &lt;b&gt;Bedingungen&lt;/b&gt; kann wie folgt festgelegt werden: &lt;/br&gt;
&lt;i&gt;Allgemein: Schließen Sie die Elemente aus, die Sie begründen möchten. Zb. "Es wird keine Wartung durchgeführt", um die Wartung selbst zu rechtfertigen. &lt;/i&gt;&lt;/li&gt;
&lt;li&gt;Geben Sie die genaue &lt;b&gt;Funktion&lt;/b&gt; des hierarchischen Objekts an&lt;/li&gt;
&lt;li&gt;&lt;b&gt;Ansprache der Teilnehmer&lt;/b&gt;, mit denen das Bewertung stattgefunden hat (+Zeitstempel)&lt;/li&gt;
&lt;/ol&gt;&lt;/li&gt;&lt;/ol&gt;&lt;/br&gt;
&lt;li&gt;&lt;b&gt;Im Gauge&lt;/b&gt; ist die Wirksamkeit der Wert Risiko Bewertung angegeben, Sie können das Optimierungspotenzial der Analyse selbst ablesen&lt;/li&gt;
&lt;li&gt; Über die Aktionsschaltfläche im Raster der Registerkarte Wert Risiko Bewertung können Sie Folgendes auswählen:
                        &lt;ol type='i'&gt;
&lt;li&gt;&lt;b&gt;Bearbeiten&lt;/b&gt;, um die ausgewählte Risikobewertung zu öffnen&lt;/li&gt;       
&lt;li&gt;&lt;b&gt;Öffnen Sie LCV&lt;/b&gt;, um zum LCV-Modul zu gelangen &lt;/li&gt;       
&lt;li&gt;&lt;b&gt;Kopieren&lt;/b&gt;, um die gewählte Wert Risiko Bewertung zu kopieren&lt;/li&gt;       
&lt;li&gt;&lt;b&gt;Einfügen&lt;/b&gt;, um die kopierte Wert Risiko Bewertung einzufügen&lt;/li&gt;
&lt;li&gt;Sie können auch die gewählte Wert Risiko Bewertung &lt;b&gt;Löschen&lt;/b&gt;&lt;/br&gt;
&lt;i&gt;Achtung: Die gelöschte Wert Risiko Bewertung wird dauerhaft gelöscht&lt;/i&gt;&lt;/li&gt;       
&lt;/ol&gt; &lt;/li&gt;&lt;/li&gt; &lt;/ol&gt;</value>
  </data>
  <data name="RoScenarioLbl" xml:space="preserve">
    <value>Szenario</value>
  </data>
  <data name="RoHeaderTxt" xml:space="preserve">
    <value>Wert Risiko Organisator</value>
  </data>
  <data name="RoNewBtnTxt" xml:space="preserve">
    <value>Neue Wert Risiko Bewertung</value>
  </data>
  <data name="RoNameLbl" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="RoDescriptionLbl" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="RoPreReqLbl" xml:space="preserve">
    <value>Voraussetzungen</value>
  </data>
  <data name="RoChildTypeLbl" xml:space="preserve">
    <value>Kind Typ</value>
  </data>
  <data name="RoDepartmentLbl" xml:space="preserve">
    <value>Abteilung</value>
  </data>
  <data name="RoStatusLbl" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="RoMetadataLbl" xml:space="preserve">
    <value>Metadaten</value>
  </data>
  <data name="RoInstructionTxt" xml:space="preserve">
    <value>Bitte wählen Sie links mindestens 1 Szenario aus!</value>
  </data>
  <data name="RoValueRiskTxt" xml:space="preserve">
    <value>Wert Risiko Bewertung</value>
  </data>
  <data name="RoAnalysisTypeLbl" xml:space="preserve">
    <value>Analyseart</value>
  </data>
  <data name="RoValueRiskMatrixLbl" xml:space="preserve">
    <value>Wert Risiko Matrix</value>
  </data>
  <data name="RoEditTxt" xml:space="preserve">
    <value>Öffnen</value>
  </data>
  <data name="RoDeleteTxt" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="RoValueRisksTxt" xml:space="preserve">
    <value>Wert Risiko Bewertung</value>
  </data>
  <data name="RoAttendedByLbl" xml:space="preserve">
    <value>Beteiligt an</value>
  </data>
  <data name="RoFunctionLbl" xml:space="preserve">
    <value>Funktion</value>
  </data>
  <data name="RoValueRiskTabTxt" xml:space="preserve">
    <value>Wert Risikos</value>
  </data>
  <data name="RoCorrectiveCostLbl" xml:space="preserve">
    <value>Korrektur Kosten</value>
  </data>
  <data name="RoPreventiveCostLbl" xml:space="preserve">
    <value>Präventive Kosten</value>
  </data>
  <data name="RoDeleteStatementTxt" xml:space="preserve">
    <value>Wert Risiko Bewertung löschen</value>
  </data>
  <data name="RoPrevActionTabTxt" xml:space="preserve">
    <value>Präventivmaßnahmen</value>
  </data>
  <data name="RoPmoPrevActionTabTxt" xml:space="preserve">
    <value>Aktuelle Maßnahmen</value>
  </data>
  <data name="UpdateExecutor" xml:space="preserve">
    <value>Ausführende</value>
  </data>
  <data name="UpdateWorkPackage" xml:space="preserve">
    <value>Arbeitspaket</value>
  </data>
  <data name="RoImportFinished" xml:space="preserve">
    <value>Import Abgeschlossen</value>
  </data>
  <data name="RoImportAnalyzeFile" xml:space="preserve">
    <value>Datei analysieren</value>
  </data>
  <data name="RoImportReadyForImport" xml:space="preserve">
    <value>Alle Zeilen wurden analysiert und Sie können nun mit dem Import beginnen.</value>
  </data>
  <data name="RoImportRiskSelectFile" xml:space="preserve">
    <value>Wählen Sie Datei importieren</value>
  </data>
  <data name="RoImportRiskTxt" xml:space="preserve">
    <value>Sie können Wert Risiken auf der Grundlage der folgenden Wert Risiko Bewertung importieren</value>
  </data>
  <data name="RoImportTaskNotAllowed" xml:space="preserve">
    <value>Das Importieren von Dateien ist nicht erlaubt, bitte überprüfen Sie Ihren Import. Haben Sie eine Maßnahmendatei importiert? Bezieht sich die Risiko-Import-ID auf die Import-ID in der Wert-Risiko-Analyse?</value>
  </data>
  <data name="RoImportTaskTxt" xml:space="preserve">
    <value>Sie können Maßnahmen auf der Grundlage der folgenden Wert Risiko Bewertung importieren</value>
  </data>
  <data name="RoImportUpdateReference" xml:space="preserve">
    <value>Gefundenes Objekt des Typs: '[type]', das nicht mit Werten in der Datenbank abgeglichen werden konnte, bitte Ersatz auswählen.</value>
  </data>
  <data name="RoImportRiskGenerateObjLbl" xml:space="preserve">
    <value>Generische Objekte generieren</value>
  </data>
  <data name="RoImportRiskGenerateFailModeLbl" xml:space="preserve">
    <value>Generieren von Fehlermodi</value>
  </data>
  <data name="RoExportInfoTxt" xml:space="preserve">
    <value>&lt;p&gt;Ziel: Exportieren der Tabellen "Wert Risikos", "Präventivmaßnahmen" oder "Ersatzteile" aus der ausgewählten &lt;b&gt;Wert Risiko Bewertung&lt;/b&gt; nach Excel. Die generierte exportierte Datei kann auch für den Import verwendet werden.&lt;/p&gt;
&lt;ol&gt;
&lt;li&gt;Öffnen Sie das Export-Modul über &lt;b&gt;Export&lt;/b&gt;&lt;/li&gt;
&lt;li&gt;Wählen Sie im Exportmodul über das Pulldown-Menü die gewünschte &lt;b&gt;Wert Risiko Bewertung&lt;/b&gt; aus &lt;/li&gt;
&lt;li&gt;Auf den drei Registerkarten werden die Daten in Rasterformat angezeigt.&lt;/li&gt;
&lt;li&gt;Über Spalten ändern können Sie die benötigte Anzahl von &lt;b&gt;Spalten Ändern&lt;/b&gt; für die drei Raster und die Exporte bearbeiten. &lt;/li&gt;
&lt;li&gt;Über &lt;b&gt;Excel&lt;/b&gt; exportieren Sie die Datei in die Downloads.&lt;/li&gt;
&lt;/ol&gt;
&lt;p&gt;ID-Definitionen im Export-Modul: &lt;/p&gt;
&lt;ol type="I"&gt;
&lt;li&gt;&lt;u&gt;Risk-ID&lt;/u&gt;: Dies ist die ID-Nummer, die sich auf das Wert-Risiko in AMprover bezieht. &lt;/li&gt;
&lt;li&gt;&lt;u&gt;Import-ID&lt;/u&gt;: Dies ist die ID-Nummer, die im Import-Modul verwendet wird. &lt;/li&gt;
&lt;ol type = 'a'&gt;
&lt;li&gt;Die Import-ID ist leer, wenn die Daten nicht über den Import generiert werden. &lt;/li&gt;
&lt;li&gt;Die Import-ID hat eine ID, wenn die Daten über den Import generiert werden. &lt;/li&gt;
&lt;i&gt;(neu importierte Daten sollten sich auf die Import-ID beziehen und nicht auf die Risiko-ID selbst)&lt;/i&gt;
&lt;/ol&gt;
&lt;li&gt;&lt;u&gt;Aktions-ID&lt;/u&gt;: Dies ist die ID-Nummer der Maßnahmen in der Liste der vorbeugenden Maßnahmen. &lt;/li&gt;
&lt;li&gt;&lt;u&gt;Spare-ID&lt;/u&gt;: Dies ist die ID-Nummer des Ersatzteils in der Liste der Ersatzteile. &lt;/li&gt;
&lt;/ol&gt;</value>
  </data>
  <data name="RoImportRisksInfoTxt" xml:space="preserve">
    <value>&lt;p&gt;Ziel: Importieren einer Liste neuer Risiken, des Funktionsbaums und der FME(C)A-Informationen &lt;b&gt;in&lt;/b&gt; die gewählte &lt;b&gt;Wert Risiko Bewertung&lt;/b&gt; in AMprover. &lt;/p&gt;
&lt;p&gt;&lt;u&gt;Präparat:&lt;/u&gt;&lt;/p&gt;
&lt;p&gt;Vor dem Import muss die Import-Value-Risk-Tabelle, die über die Exportoption generiert wird, mit richtigen Daten aufbereitet werden.
Das "zu importieren Feld" muss ausgefüllt werden. Ein weiterer Schwerpunkt sollte auf dem Feld Risk-ID liegen. Dieses Feld muss – falls leer – mit zusätzlichen Import-ID-Nummern ausgefüllt werden. &lt;/p&gt;
&lt;ol type=‘I'&gt;
&lt;li&gt;&lt;u&gt;Option 1&lt;/u&gt; "Import": Wenn die Risk-ID's in der Importdatei neue Import-IDs haben, werden die Daten als &lt;b&gt;neue Datensätze&lt;/b&gt; in AMprover importiert. &lt;/li&gt;
&lt;li&gt;&lt;u&gt;Option 2&lt;/u&gt; "Aktualisieren": Wenn die Risiko-IDs in der Importdatei vorhandene Import-IDs haben, werden die Daten importiert und vorhandene Datensätze in AMprover entsprechend &lt;b&gt;aktualisiert&lt;/b&gt;. &lt;/li&gt;
&lt;/ol&gt;
&lt;p&gt;&lt;u&gt;Aktion: &lt;/u&gt;&lt;/p&gt;
&lt;ol &gt;
&lt;li&gt;Klicken Sie auf &lt;b&gt;Importieren&lt;/b&gt; und wählen Sie die Option Risiken importieren
Es öffnet sich ein Auswahlbildschirm.
&lt;li&gt;Wählen Sie über das Pulldown-Menü die gewünschte &lt;b&gt;Wert Risiko Bewertung&lt;/b&gt; aus, in die die Daten importiert werden sollen. Dies ist die Ebene 1 der &lt;b&gt;Generische Hierarchieobjekte&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Markieren Sie das Feld &lt;b&gt;Generische Objekte generieren&lt;/b&gt;, wenn Sie im Modul &lt;b&gt;Generische Hierarchieobjekte&lt;/b&gt; und damit im Modul Risiken neue Objektnamen generieren möchten &lt;/li&gt;
&lt;li&gt;Markieren Sie das Feld &lt;b&gt;Generieren von Fehlermodi&lt;/b&gt;, wenn Sie neue Fehlermodi im &lt;b&gt;Ausfällemodul&lt;/b&gt; erzeugen (hinzufügen) möchten. &lt;/li&gt;
&lt;li&gt;Die Datei sollte mit Daten gefüllt sein. &lt;/li&gt;
&lt;li&gt;Klicken Sie auf &lt;b&gt;Wählen Sie Datei importieren&lt;/b&gt;.&lt;/li&gt;
&lt;i&gt;Die Datei wird analysiert. Wenn keine Probleme auftreten, kann die Datei importiert werden&lt;/i&gt;
&lt;li&gt;Klicken Sie auf &lt;b&gt;Import starten&lt;/b&gt;.&lt;/li&gt;&lt;/ol&gt;
&lt;ol type='I'&gt;
&lt;li&gt;Option 1: "Importieren": &lt;/li&gt;
&lt;ol type='a'&gt;
&lt;li&gt;Die Informationen werden dann als &lt;b&gt;neue Datensätze&lt;/b&gt; in die ausgewählte &lt;b&gt;Wert Risiko bewertung&lt;/b&gt; &lt;b&gt;importiert&lt;/b&gt;. &lt;/li&gt;
&lt;li&gt;Die in der Risiko-ID hinterlegte ist die Import-ID und es werden neue Risiko-IDs generiert. &lt;/li&gt;&lt;/ol&gt;
&lt;li&gt;Option 2: " Aktualisieren": &lt;/li&gt;
&lt;ol type='a'&gt;
&lt;li&gt;Die Informationen in AMprover werden dann auf die ausgewählte &lt;b&gt;Wert Risiko Bewertung&lt;/b&gt; &lt;b&gt;aktualisiert&lt;/b&gt;. &lt;/li&gt;&lt;/ol&gt;&lt;/ol&gt;</value>
  </data>
  <data name="RoImportTasksTxt" xml:space="preserve">
    <value>&lt;p&gt;Ziel: Importieren einer Liste neuer Maßnahmen, die sich auf eine Risiko-ID in AMprover beziehen.&lt;/p&gt;
&lt;p&gt;&lt;u&gt;Präparat: &lt;/u&gt;&lt;/p&gt;
Vor dem Import muss die Import-Action-Tabelle, die über die Exportoption generiert wird, mit Daten aufbereitet werden.&lt;/br&gt;
Achten Sie auf die Anzahl der Zeichen im Feld Aktion, die kleiner als 60 sein muss.&lt;/br&gt;
Die "einzutragenden Felder" müssen ausgefüllt werden. Besonderes Augenmerk sollte auf das Feld Import-Risk-ID gelegt werden.  Die Import-ID einer Wert-Risiko-Analyse wird beim Import einer entsprechenden Wert -Risk-Datei automatisch mit der Risiko-ID aufgefüllt. Wenn Sie nur Aktionen importieren, müssen Sie diese Import-ID auf der Registerkarte "&lt;b&gt;Verschiedenes"&lt;/b&gt; im Wert des Risikoanalysemoduls eingeben. &lt;/br&gt;
PS. Das Feld TaskPmo kann auf 'True' gesetzt werden, wenn Sie Aufgaben als PMO-Aufgaben importieren möchten.&lt;/p&gt;
&lt;ol &gt;
&lt;li&gt;&lt;u&gt;Option 1&lt;/u&gt; "Import": Wenn die Risk-ID's in der Importdatei Import-ID haben, werden die Daten als neue Datensätze in AMprover importiert.&lt;/li&gt;
&lt;li&gt;&lt;u&gt;Option 2 &lt;/u&gt; "Update": Wenn die Risk-ID's in der Importdatei bereits Risk-ID's haben, werden die Daten importiert und bestehende Datensätze in AMprover entsprechend aktualisiert.&lt;/li&gt;&lt;/ol&gt;
&lt;p&gt;&lt;u&gt;Aktion: &lt;/u&gt;&lt;/p&gt;
&lt;ol &gt;
&lt;li&gt;Klicken Sie auf &lt;b&gt;Import&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Es öffnet sich ein Auswahlbildschirm.&lt;/li&gt;
&lt;li&gt;Wählen Sie über das Pulldown-Menü die gewünschte &lt;b&gt;Wert Risiko Bewertung&lt;/b&gt; aus, in die die Daten importiert werden sollen. Dies ist die Ebene 1 der &lt;b&gt;Generische Hierarchieobjekte&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Klicken Sie auf &lt;b&gt;Wählen Sie Datei Importieren &lt;/b&gt; und wählen Sie die Dateivorlage aus, die Sie importieren möchten. Die Datei sollte mit Aktionsdaten gefüllt werden.&lt;/li&gt;
&lt;li&gt;Der Name der Datei wird im Rahmen angezeigt. Verwenden Sie &lt;b&gt;X&lt;/b&gt; neben dem Dateinamen, wenn Sie nicht wissen, welche Datei importiert werden soll.&lt;/li&gt;
&lt;li&gt;Klicken Sie auf &lt;b&gt;Datei analysieren&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Die Datei wird analysiert und die Bewertungsergebnisse (Datenmengen) werden auf grünem Hintergrund angezeigt.&lt;/li&gt;
&lt;i&gt;Wenn der Hintergrund rot ist, müssen Sie die Daten überprüfen.&lt;/i&gt;
&lt;li&gt;Wenn OK, klicken Sie auf &lt;b&gt;Import starten.&lt;/b&gt;&lt;/li&gt;
&lt;li&gt;Wenn alles wie geplant gelaufen ist, wird ein Popup-Fenster mit dem Titel "&lt;b&gt;XX Elemente erfolgreich importiert" angezeigt.&lt;/b&gt;&lt;/li&gt;&lt;/ol&gt;</value>
  </data>
  <data name="RoImportTasksBtn" xml:space="preserve">
    <value>Import Maßnahmen</value>
  </data>
  <data name="RoImportRisksBtn" xml:space="preserve">
    <value>Import Wert Risiken</value>
  </data>
  <data name="RoImportSpareBtn" xml:space="preserve">
    <value>Import Ersatzteile</value>
  </data>
  <data name="RoImportSpareNotAllowed" xml:space="preserve">
    <value>Das Importieren von Dateien ist nicht erlaubt, bitte überprüfen Sie Ihren Import. Haben Sie eine Ersatzteildatei importiert? Bezieht sich die Risiko-Import-ID auf die Import-ID in der Wert-Risiko-Analyse?</value>
  </data>
  <data name="RoImportSpareTxt" xml:space="preserve">
    <value>&lt;p&gt;Ziel: Importieren einer Liste neuer Ersatzteile, die sich auf eine Risiko-ID in AMprover beziehen.&lt;/p&gt;
&lt;p&gt;&lt;u&gt;Präparat: &lt;/u&gt;&lt;/p&gt;
Vor dem Import muss die Import-Ersatzteile-Tabelle, die über die Exportoption generiert wird, mit Daten aufbereitet werden.&lt;/br&gt;
Achten Sie auf die Anzahl der Zeichen im Feld Spares, die kleiner als 60 Zeichen sein muss.&lt;/br&gt;
Die "einzutragenden Felder" müssen ausgefüllt werden. Besonderes Augenmerk sollte auf das Feld Import-Risk-ID gelegt werden.  Die Import-ID einer Wert-Risiko-Analyse wird beim Import einer entsprechenden Wert-Risk-Datei automatisch mit der Risiko-ID aufgefüllt. Wenn Sie nur Ersatzteile importieren, müssen Sie diese Import-ID auf der Registerkarte "&lt;b&gt;Verschiedenes"&lt;/b&gt; im Wert des Risikoanalysemoduls eingeben. &lt;/br&gt;
PS. Das Feld Pmo kann auf 'True' gesetzt werden, wenn Sie Ersatzteile als PMO- Ersatzteile importieren möchten.&lt;/p&gt;
&lt;ol &gt;
&lt;li&gt;&lt;u&gt;Option 1&lt;/u&gt; "Import": Wenn die Risk-ID's in der Importdatei Import-ID haben, werden die Daten als neue Datensätze in AMprover importiert.&lt;/li&gt;
&lt;li&gt;&lt;u&gt;Option 2 &lt;/u&gt; "Update": Wenn die Risk-ID's in der Importdatei bereits Risk-ID's haben, werden die Daten importiert und bestehende Datensätze in AMprover entsprechend aktualisiert.&lt;/li&gt;&lt;/ol&gt;
&lt;p&gt;&lt;u&gt;Aktion: &lt;/u&gt;&lt;/p&gt;
&lt;ol &gt;
&lt;li&gt;Klicken Sie auf &lt;b&gt;Import&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Es öffnet sich ein Auswahlbildschirm.&lt;/li&gt;
&lt;li&gt;Wählen Sie über das Pulldown-Menü die gewünschte &lt;b&gt;Wert Risiko Bewertung&lt;/b&gt; aus, in die die Daten importiert werden sollen. Dies ist die Ebene 1 der &lt;b&gt;Generische Hierarchieobjekte&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Klicken Sie auf &lt;b&gt;Wählen Sie Datei Importieren &lt;/b&gt; und wählen Sie die Dateivorlage aus, die Sie importieren möchten. Die Datei sollte mit Ersatzteile daten gefüllt werden.&lt;/li&gt;
&lt;li&gt;Der Name der Datei wird im Rahmen angezeigt. Verwenden Sie &lt;b&gt;X&lt;/b&gt; neben dem Dateinamen, wenn Sie nicht wissen, welche Datei importiert werden soll.&lt;/li&gt;
&lt;li&gt;Klicken Sie auf &lt;b&gt;Datei analysieren&lt;/b&gt;.&lt;/li&gt;
&lt;li&gt;Die Datei wird analysiert und die Bewertungsergebnisse (Datenmengen) werden auf grünem Hintergrund angezeigt.&lt;/li&gt;
&lt;i&gt;Wenn der Hintergrund rot ist, müssen Sie die Daten überprüfen.&lt;/i&gt;
&lt;li&gt;Wenn OK, klicken Sie auf &lt;b&gt;Import starten.&lt;/b&gt;&lt;/li&gt;
&lt;li&gt;Wenn alles wie geplant gelaufen ist, wird ein Popup-Fenster mit dem Titel "&lt;b&gt;XX Elemente erfolgreich importiert" angezeigt.&lt;/b&gt;&lt;/li&gt;&lt;/ol&gt;</value>
  </data>
  <data name="RoDownTimeCostLbl" xml:space="preserve">
    <value>Ausfallzeit/ Stunde</value>
  </data>
  <data name="RoPmoGraphLbl" xml:space="preserve">
    <value>Ergebnis PMO-Analyse</value>
  </data>
  <data name="RoActionCosts" xml:space="preserve">
    <value>Maßnahmen Kosten</value>
  </data>
  <data name="RoDirectRiskCosts" xml:space="preserve">
    <value>Direkte Risikokosten</value>
  </data>
  <data name="RoValueRiskCosts" xml:space="preserve">
    <value>Wert Risiko Kosten</value>
  </data>
</root>