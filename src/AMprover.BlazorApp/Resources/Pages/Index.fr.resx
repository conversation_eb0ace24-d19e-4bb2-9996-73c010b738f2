<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="IndexDataPreparationToolTip" xml:space="preserve">
    <value>Cette icône vous permet d'accéder à la partie &lt;b&gt;Préparation des données&lt;/b&gt; de la base de données. Vous pouvez ajouter des données sur la société et modifier les paramètres de la base de données AMprover.</value>
    <comment>Shows tooltip of the data preparation block</comment>
  </data>
  <data name="IndexValueDriverAnalysisToolTip" xml:space="preserve">
    <value>Cette icône vous permet d'accéder à un autre site Web lié à &lt;b&gt;My VDMxl&lt;/b&gt;.</value>
  </data>
  <data name="IndexMatrixToolTip" xml:space="preserve">
    <value>Cette icône vous permet d'accéder aux modèles de &lt;b&gt;matrice de valeur risques&lt;/b&gt;. Vous pouvez ensuite ajouter et construire de nouvelles matrices de risques utilisées pour définir la politique de votre entreprise.</value>
  </data>
  <data name="IndexCriticalityRankingToolTip" xml:space="preserve">
    <value>Cette icône vous amènera à la grille avec les assets, qui sont classés en fonction de la &lt;b&gt;Criticité&lt;/b&gt; attribuée.</value>
  </data>
  <data name="IndexRcmFmecaToolTip" xml:space="preserve">
    <value>Cette icône vous permet d'accéder au module &lt;b&gt;Valeur Risk Organizer&lt;/b&gt;. &lt;br/&gt;Dans ce module, vous serez en mesure d'organiser et d'évaluer les risques et de désigner les contre-mesures appropriées.</value>
  </data>
  <data name="IndexPmoToolTip" xml:space="preserve">
    <value>Cette icône vous amène au module &lt;b&gt;Valeur Risk Organizer&lt;/b&gt;.&lt;br/&gt;Dans ce module, vous pourrez organiser et optimiser les tâches de maintenance en évaluant le risque.</value>
  </data>
  <data name="IndexHazopTooltip" xml:space="preserve">
    <value>Cette icône vous permet d'accéder au module &lt;b&gt;Valeur Risk Organizer&lt;/b&gt;. &lt;br/&gt;Dans ce module, vous serez en mesure d'organiser et d'évaluer le risque de danger et le risque opérationnel et de désigner les mesures de protection appropriées.</value>
  </data>
  <data name="IndexValueForecastToolTip" xml:space="preserve">
    <value>Under Construction</value>
  </data>
  <data name="IndexReportsToolTip" xml:space="preserve">
    <value>Cette icône vous permet d'accéder aux &lt;b&gt;rapports&lt;/b&gt;.</value>
  </data>
  <data name="IndexUpLoadToEamToolTip" xml:space="preserve">
    <value>Cette icône vous permet d'accéder à la page &lt;b&gt;Transférer&lt;/b&gt; des workpackages configurés.</value>
  </data>
  <data name="IndexLccToolTip" xml:space="preserve">
    <value>Cette icône vous permet d'accéder au &lt;b&gt;module LCV&lt;/b&gt;.&lt;br/&gt;Vous pouvez générer et valider les calculs LCV basés sur les données d'analyse de valeur risque ou de RAMS.</value>
  </data>
  <data name="IndexSparePartToolTip" xml:space="preserve">
    <value>Cette icône vous permet d'accéder au rapport sur les &lt;b&gt;pièces de rechange&lt;/b&gt;. &lt;br/&gt;Vous pouvez modifier les pièces de rechange dans les modules de risque.</value>
  </data>
  <data name="IndexClusterToolTip" xml:space="preserve">
    <value>Cette icône vous amène au module &lt;b&gt;Cluster&lt;/b&gt;.&lt;br/&gt;Vous pouvez générer des lots de travaux en grappes sur la base des critères prédéfinis.</value>
  </data>
  <data name="IndexLinkPmToAbsToolTip" xml:space="preserve">
    <value>Cette icône vous amène au module &lt;b&gt;Affecter des assets&lt;/b&gt;.&lt;br/&gt;Vous pouvez affecter plusieurs actifs aux listes d'actions de configuration (maintenance) via la structure hiérarchique.</value>
  </data>
  <data name="IndexDataPrepTxt" xml:space="preserve">
    <value>Préparation des données</value>
  </data>
  <data name="IndexAnalyseTxt" xml:space="preserve">
    <value>Analyser</value>
  </data>
  <data name="IndexFocusTxt" xml:space="preserve">
    <value>Priorités</value>
  </data>
  <data name="IndexVdmTxt" xml:space="preserve">
    <value>Analyse leviers de valeurs</value>
  </data>
  <data name="IndexValueRiskMatrixTxt" xml:space="preserve">
    <value>Matrice Valeurs/Risques</value>
  </data>
  <data name="IndexCritRankTxt" xml:space="preserve">
    <value>Analyse de criticité</value>
  </data>
  <data name="IndexRcmFmecaTxt" xml:space="preserve">
    <value>Étude RCM/AMDEC</value>
  </data>
  <data name="IndexHazopTxt" xml:space="preserve">
    <value>Étude HAZOP</value>
  </data>
  <data name="IndexPmoTxt" xml:space="preserve">
    <value>Étude PMO</value>
  </data>
  <data name="IndexRAMSTxt" xml:space="preserve">
    <value>Étude RAMS</value>
  </data>
  <data name="IndexLccTxt" xml:space="preserve">
    <value>Étude LCV</value>
  </data>
  <data name="IndexOptTxt" xml:space="preserve">
    <value>Optimiser</value>
  </data>
  <data name="IndexSparePartTxt" xml:space="preserve">
    <value>Étude pièces de rechange</value>
  </data>
  <data name="IndexWorkPackagesTxt" xml:space="preserve">
    <value>Cluster</value>
  </data>
  <data name="IndexLinkPmAbsTxt" xml:space="preserve">
    <value>Lier plans PM à ABS</value>
  </data>
  <data name="IndexDeployTxt" xml:space="preserve">
    <value>Déployer</value>
  </data>
  <data name="IndexSummaryTxt" xml:space="preserve">
    <value>Rapports</value>
  </data>
  <data name="IndexLtapTxt" xml:space="preserve">
    <value>Plan équipement long terme</value>
  </data>
  <data name="IndexValForecastTxt" xml:space="preserve">
    <value>Prévision des valeurs</value>
  </data>
  <data name="IndexEamUploadTxt" xml:space="preserve">
    <value>Exporter vers l'EAM</value>
  </data>
  <data name="IndexLinkVrOmAbsTxt" xml:space="preserve">
    <value>Valeur/ Risque sur ABS</value>
  </data>
</root>