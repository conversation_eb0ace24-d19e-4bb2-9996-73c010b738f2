<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="IndexDataPreparationToolTip" xml:space="preserve">
    <value>Dit icoon brengt je naar de &lt;b&gt;Datavoorbereiding&lt;/b&gt; deel van de applicatie.&lt;br/&gt;Je kan bedrijfsdata toevoegen of instellingen wijzigen.</value>
    <comment>Shows tooltip of the data preparation block</comment>
  </data>
  <data name="IndexValueDriverAnalysisToolTip" xml:space="preserve">
    <value>Dit pictogram brengt u naar een andere website gerelateerd aan &lt;b&gt;My VDMxl&lt;/b&gt;.</value>
  </data>
  <data name="IndexMatrixToolTip" xml:space="preserve">
    <value>Dit icoon brengt u naar de &lt;b&gt;Waarde Risicomatrix&lt;/b&gt; sjablonen. &lt;br/&gt;U kunt dan nieuwe Risicomatrices toevoegen en opstellen, die gebruikt worden voor het definiëren van uw bedrijfsbeleid.</value>
  </data>
  <data name="IndexCriticalityRankingToolTip" xml:space="preserve">
    <value>Dit icoon brengt u naar het grid met assets, die zijn gerangschikt volgens de toegewezen &lt;b&gt;Kriticiteit&lt;/b&gt;.</value>
  </data>
  <data name="IndexRcmFmecaToolTip" xml:space="preserve">
    <value>Dit icoon brengt u naar de &lt;b&gt;Waarde Risk Organizer&lt;/b&gt; module. &lt;br/&gt;In deze module kunt u de risico's organiseren en beoordelen en de juiste beheersmaatregelen nemen.</value>
  </data>
  <data name="IndexPmoToolTip" xml:space="preserve">
    <value>Dit icoon brengt u naar de &lt;b&gt;Waarde Risk Organizer&lt;/b&gt; module.&lt;br/&gt;In deze module kunt u de onderhoudstaken organiseren en optimaliseren door het risico in te schatten.</value>
  </data>
  <data name="IndexHazopTooltip" xml:space="preserve">
    <value>Met dit icoon komt u in de &lt;b&gt;Waarde Risk Organizer&lt;/b&gt; module.&lt;br/&gt;In deze module kunt u de risico's en operationele risico's organiseren en beoordelen en de juiste voorzorgsmaatregelen nemen.</value>
  </data>
  <data name="IndexValueForecastToolTip" xml:space="preserve">
    <value>Under Construction</value>
  </data>
  <data name="IndexReportsToolTip" xml:space="preserve">
    <value>Dit icoon brengt u naar de &lt;b&gt;Rapporten&lt;/b&gt;.</value>
  </data>
  <data name="IndexUpLoadToEamToolTip" xml:space="preserve">
    <value>Dit icoon brengt u naar de &lt;b&gt;Upload&lt;/b&gt; pagina van de opgezette Werkpakketten.</value>
  </data>
  <data name="IndexLccToolTip" xml:space="preserve">
    <value>Dit icoon brengt u naar de &lt;b&gt;LCV module&lt;/b&gt;.&lt;br/&gt;U kunt LCV berekeningen genereren en valideren, gebaseerd op de Waarde Risico of RAMS analyse gegevens.</value>
  </data>
  <data name="IndexSparePartToolTip" xml:space="preserve">
    <value>Dit icoon brengt u naar het &lt;b&gt;Spare Part&lt;/b&gt; rapport. &lt;br/&gt;U kunt de reserveonderdelen in de risicomodules wijzigen.</value>
  </data>
  <data name="IndexClusterToolTip" xml:space="preserve">
    <value>Dit icoon brengt u naar de &lt;b&gt;Cluster&lt;/b&gt; module.&lt;br/&gt;U kunt geclusterde Werkpaketten genereren op basis van de vooraf ingestelde criteria.</value>
  </data>
  <data name="IndexLinkPmToAbsToolTip" xml:space="preserve">
    <value>Dit icoon brengt u naar de &lt;b&gt;Assets toewijs&lt;/b&gt; module.&lt;br/&gt;U kunt verschillende assets toewijzen aan de setup (Onderhoud) Action lijsten via de Hieracrhy Structure.</value>
  </data>
  <data name="IndexDataPrepTxt" xml:space="preserve">
    <value>Datavoorbereiding</value>
  </data>
  <data name="IndexVdmTxt" xml:space="preserve">
    <value>Waarde Drijver Analyse</value>
  </data>
  <data name="IndexFocusTxt" xml:space="preserve">
    <value>Focus</value>
  </data>
  <data name="IndexValueRiskMatrixTxt" xml:space="preserve">
    <value>Waarde Risico Matrix</value>
  </data>
  <data name="IndexCritRankTxt" xml:space="preserve">
    <value>Kriticiteit Ranking</value>
  </data>
  <data name="IndexAnalyseTxt" xml:space="preserve">
    <value>Analyseer</value>
  </data>
  <data name="IndexRcmFmecaTxt" xml:space="preserve">
    <value>RCM/FMECA Studie</value>
  </data>
  <data name="IndexPmoTxt" xml:space="preserve">
    <value>PMO Studie</value>
  </data>
  <data name="IndexHazopTxt" xml:space="preserve">
    <value>HAZOP Studie</value>
  </data>
  <data name="IndexRAMSTxt" xml:space="preserve">
    <value>RAMS Studie</value>
  </data>
  <data name="IndexOptTxt" xml:space="preserve">
    <value>Optimaliseer</value>
  </data>
  <data name="IndexLccTxt" xml:space="preserve">
    <value>LCV Studie</value>
  </data>
  <data name="IndexSparePartTxt" xml:space="preserve">
    <value>Reservedelen Studie</value>
  </data>
  <data name="IndexWorkPackagesTxt" xml:space="preserve">
    <value>Clustering</value>
  </data>
  <data name="IndexLinkPmAbsTxt" xml:space="preserve">
    <value>Link PM plannen aan ABS</value>
  </data>
  <data name="IndexDeployTxt" xml:space="preserve">
    <value>Implementeer</value>
  </data>
  <data name="IndexSummaryTxt" xml:space="preserve">
    <value>Rapporten</value>
  </data>
  <data name="IndexLtapTxt" xml:space="preserve">
    <value>Levensduur Plan</value>
  </data>
  <data name="IndexValForecastTxt" xml:space="preserve">
    <value>Waarde prognose</value>
  </data>
  <data name="IndexEamUploadTxt" xml:space="preserve">
    <value>Export naar EAM</value>
  </data>
  <data name="IndexLinkVrOmAbsTxt" xml:space="preserve">
    <value>Waarde Risico op ABS</value>
  </data>
</root>