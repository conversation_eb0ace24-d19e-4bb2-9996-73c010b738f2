<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="RmtCopyBtn" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="RmtSaveBtn" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="RmtHeaderTxt" xml:space="preserve">
    <value>Value Risk Matrix</value>
  </data>
  <data name="RmtSubHeaderTxt" xml:space="preserve">
    <value>In the &lt;b&gt;Value Risk Matrix&lt;/b&gt; Page the specific company policy for values related to risks can be modelled into a Value Risk matrix</value>
  </data>
  <data name="RmtShortNameLbl" xml:space="preserve">
    <value>Short name</value>
  </data>
  <data name="RmtFmecaNameLbl" xml:space="preserve">
    <value>FMECA name</value>
  </data>
  <data name="RmtDefaultLbl" xml:space="preserve">
    <value>Default Matrix</value>
  </data>
  <data name="RmtDescriptionLbl" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="RmtsSubHeaderTxt" xml:space="preserve">
    <value>&lt;b&gt;Value Risk Matrices&lt;/b&gt; will be used to get insight in the effects and impact of the value and risk for your valuable assets.</value>
  </data>
  <data name="RmtsHeaderTxt" xml:space="preserve">
    <value>Value Risk Matrices</value>
  </data>
  <data name="RmtsMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;Objective: set up one or several Value Risk Matrices. They must be appointed to Level 1 of the Generic Hierarchy Objects in the &lt;b&gt;Value Risk Organizer&lt;/b&gt; Module.  
Start with a new matrix template by clicking on &lt;b&gt;New&lt;/b&gt;. The standard layout will be used as the template. 
&lt;ol&gt;
&lt;li&gt;Use &lt;b&gt;New&lt;/b&gt; to open a value risk matrix template.&lt;/li&gt;
&lt;li&gt;Use &lt;b&gt;Open&lt;/b&gt; to edit a value risk matrix.&lt;/li&gt;
&lt;li&gt;Use &lt;b&gt;Change Columns&lt;/b&gt; to edit the layout of the tab/table.&lt;/li&gt;
&lt;/ol&gt;&lt;/p&gt;</value>
  </data>
  <data name="RmtMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;Objective: set up the layout of a &lt;b&gt;Value Risk Matrix&lt;/b&gt;. The Value Risk matrix in AMprover must be compatible with the Value Risk matrix of the asset owner and in line with the maintenance management strategies. 
&lt;ol&gt;
&lt;li&gt;Give a name and description of the Value Risk matrix. &lt;/li&gt;
&lt;li&gt;Use &lt;b&gt;Save&lt;/b&gt; to save updates in the Value Risk matrix. &lt;/li&gt;
&lt;li&gt;Add Effect columns and Probability columns by hovering over the upper cell and clicking on the (+). &lt;/li&gt;
&lt;li&gt;Add an Impact row by hovering over the lower/left cell and clicking on the (+) &lt;/li&gt;
&lt;li&gt;You have the Value Risk matrix information in 4 tabs: (Description, Impact value, Custom value, Points) that you can update. The last two are optional to be used.&lt;/li&gt;
&lt;li&gt;To update the content of each cell in you must hover over the cell and click on the edit button that pops up. In the popup you can update the cell in each tab including the colour. &lt;/li&gt;
&lt;li&gt;If you update the &lt;u&gt;impact values&lt;/u&gt;  that are used in the &lt;b&gt;Value Risk Assessment&lt;/b&gt; maintain the Logarithmic distribution of the impact values. &lt;/li&gt;
&lt;/ol&gt;&lt;/p&gt;</value>
  </data>
  <data name="RskMtrxNoDefaultTitle" xml:space="preserve">
    <value>No default selected</value>
  </data>
  <data name="RskMtrxNoDefaultTxt" xml:space="preserve">
    <value>You currently don't have a risk Matrix that is marked as the 'Default Matrix'. Please Edit another matrix and set it as default before creating a new Matrix.</value>
  </data>
</root>