<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="CaeNameLbl" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="CaeReferenceIdLbl" xml:space="preserve">
    <value>Reference Id</value>
  </data>
  <data name="CaePolicyLbl" xml:space="preserve">
    <value>Strategy</value>
  </data>
  <data name="CaeActionTypeLbl" xml:space="preserve">
    <value>Action Type</value>
  </data>
  <data name="CaeDescriptionLbl" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="CaeRemarksLbl" xml:space="preserve">
    <value>Remarks</value>
  </data>
  <data name="CaeModifiableLbl" xml:space="preserve">
    <value>Modifiable</value>
  </data>
  <data name="CaeCostLbl" xml:space="preserve">
    <value>Cost</value>
  </data>
  <data name="CaeUnitTypeLbl" xml:space="preserve">
    <value>Unit Type</value>
  </data>
  <data name="CaeInitiatorLbl" xml:space="preserve">
    <value>Initiator</value>
  </data>
  <data name="CaeExecutorLbl" xml:space="preserve">
    <value>Executor</value>
  </data>
  <data name="CaeWorkPackageLbl" xml:space="preserve">
    <value>WorkPackage</value>
  </data>
  <data name="CaeIntervalLbl" xml:space="preserve">
    <value>Interval</value>
  </data>
  <data name="CaeGeneralItemsLbl" xml:space="preserve">
    <value>General Items</value>
  </data>
  <data name="CaePermitLbl" xml:space="preserve">
    <value>Permit</value>
  </data>
  <data name="CaeResponsibleLbl" xml:space="preserve">
    <value>Responsible</value>
  </data>
  <data name="CaeDownTimeLbl" xml:space="preserve">
    <value>DownTime</value>
  </data>
  <data name="CaeDurationLbl" xml:space="preserve">
    <value>Duration</value>
  </data>
  <data name="CaePropertiesLbl" xml:space="preserve">
    <value>Properties</value>
  </data>
  <data name="CaeInitiatedByLbl" xml:space="preserve">
    <value>Initiated by</value>
  </data>
  <data name="CaeOnLbl" xml:space="preserve">
    <value>On</value>
  </data>
  <data name="CaSubHeaderTxt" xml:space="preserve">
    <value>&lt;b&gt;Common Actions&lt;/b&gt; can be used to define standard actions, which can be used to set up task plans.</value>
  </data>
  <data name="CaHeaderTxt" xml:space="preserve">
    <value>Common actions</value>
  </data>
  <data name="CaMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;Objective: Common actions list are predefined actions and action settings that can be used in the Preventive action tab within the &lt;b&gt;Value Risk Assessment&lt;/b&gt; module. In that module you can select a common action and update it according to your specific assessment results and the required preventive actions parameters.  &lt;/br&gt;
&lt;ol&gt;
&lt;li&gt;Use &lt;b&gt;New&lt;/b&gt; to add a record.&lt;/li&gt;
&lt;li&gt;Use &lt;b&gt;Edit&lt;/b&gt; to edit a record line.&lt;/li&gt;
&lt;li&gt;Use &lt;b&gt;Open as popup&lt;/b&gt;  to edit the contents of a record. &lt;/li&gt;
&lt;li&gt;Use &lt;b&gt;Change Columns&lt;/b&gt;  to edit the layout of the tab/table. &lt;/li&gt;
&lt;/ol&gt;&lt;/p&gt;</value>
  </data>
  <data name="CaeSortOrderLbl" xml:space="preserve">
    <value>Sort Order</value>
  </data>
  <data name="CaCommonActionInUse" xml:space="preserve">
    <value>Common Action is in use</value>
  </data>
  <data name="CaCommonActionInUseDetails" xml:space="preserve">
    <value>The common action is currently in use by {0} Tasks. Are you sure you want to delete the common action and sever all dependencies?</value>
  </data>
  <data name="CaImportExportTabHeader" xml:space="preserve">
    <value>Import / Export</value>
  </data>
  <data name="CaEditPopupHeader" xml:space="preserve">
    <value>Common action details</value>
  </data>
  <data name="CaImportGenerateExecutor" xml:space="preserve">
    <value>Generate missing executors</value>
  </data>
  <data name="CaImportGenerateInitiator" xml:space="preserve">
    <value>Generate missing initiators</value>
  </data>
  <data name="CaImportGenerateUnitType" xml:space="preserve">
    <value>Generate missing UnitTypes</value>
  </data>
  <data name="CaImportPopupText" xml:space="preserve">
    <value>Import Common Actions</value>
  </data>
  <data name="CaImportAnalyzeFile" xml:space="preserve">
    <value>Analyze File</value>
  </data>
  <data name="CaImportGenerateStrategy" xml:space="preserve">
    <value>Generate missing strategies</value>
  </data>
  <data name="CaImportPrevious" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="CaImportSelectFile" xml:space="preserve">
    <value>Select File</value>
  </data>
  <data name="CaImportStartImport" xml:space="preserve">
    <value>Start Import</value>
  </data>
  <data name="CaeFilterRefLbl" xml:space="preserve">
    <value>Filter ref.</value>
  </data>
</root>