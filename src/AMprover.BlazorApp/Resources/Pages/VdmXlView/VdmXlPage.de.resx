<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="VdmXlNameLbl" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="VdmXlOptLifeTimeTxt" xml:space="preserve">
    <value>Opt. Lebensdauer</value>
  </data>
  <data name="VdmXlRecalculateBtn" xml:space="preserve">
    <value>Neuberechnen</value>
  </data>
  <data name="VdmXlRisksTxt" xml:space="preserve">
    <value>Wert Risiko</value>
  </data>
  <data name="VdmXlYearsLbl" xml:space="preserve">
    <value>Jahre</value>
  </data>
  <data name="VdmXlTimeUnitTxt" xml:space="preserve">
    <value>Jahre</value>
  </data>
  <data name="VdmXlMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;In dieser &lt;b&gt;VDM&lt;sup&gt;XL &lt;/sup&gt;-Übersicht &lt;/b&gt; werden die Werttreiber auf Basis der Lebenszykluswert Berechnungen berechnet.&lt;/p&gt;
&lt;p&gt;Richten Sie eine VDM&lt;b&gt;&lt;sup&gt;XL&lt;/sup&gt;-Berechnung&lt;/b&gt; ein, indem Sie auf die Schaltfläche &lt;b&gt;Neu VdmXl&lt;/b&gt; klicken, um diese Übersicht zu generieren. &lt;/br&gt; (Gleichzeitig wird eine LCV-Berechnung erstellt)&lt;/p&gt;
&lt;ol&gt;
&lt;li&gt;Die Übersicht wird für die optimale Lebensdauer des gewählten Assets berechnet&lt;/li&gt;
&lt;li&gt;der &lt;b&gt;Kapitalwert&lt;/b&gt; (NPV) aller Werttreiber wird dargestellt&lt;/li&gt;
&lt;li&gt;Der Kapitalwert wird für die&lt;/li&gt;
&lt;ol type='a'&gt;
&lt;li&gt;Das Risiko ohne Maßnahmen&lt;/li&gt;
&lt;li&gt;Die PMO-Situation&lt;/li&gt;
&lt;li&gt;Der Wert Risiken bei getroffenen (optimierten) Maßnahmen&lt;/li&gt;
&lt;/ol&gt;&lt;/ol&gt;
&lt;p&gt;&lt;b&gt;Nb.&lt;/b&gt; Um eine gute Übersicht zu gewährleisten, ist es notwendig, dass in der zugehörigen FMECA-Matrix, in der Überschrift der Effektspalten in den&lt;b&gt; Spalteneigenschaften&lt;/b&gt;, die Option &lt;b&gt;Korrekturkostenkalkulation&lt;/b&gt; gesetzt ist.&lt;/p&gt;
Auswählen hiermit:&lt;ol &gt;
&lt;li&gt;&lt;b&gt;Circuit affected Cost&lt;/b&gt; für Utilization.&lt;/li&gt;
&lt;li&gt;&lt;b&gt;Direct Cost&lt;/b&gt; für Opex kosten.&lt;/li&gt;</value>
  </data>
  <data name="VdmXlMenuTitle" xml:space="preserve">
    <value>Vorgehensweise für VDM&lt;sup&gt;XL&lt;/sup&gt; im Überblick</value>
  </data>
  <data name="VdmXlVRPmoTxt" xml:space="preserve">
    <value>Vor optimierten Aktionen (PMO)</value>
  </data>
  <data name="VdmXlVRwithActionsTxt" xml:space="preserve">
    <value>Wert Risiko mit Maßnahmen</value>
  </data>
  <data name="VdmXlVRwoActionsTxt" xml:space="preserve">
    <value>Wert Risiko ohne Maßnahmen</value>
  </data>
  <data name="VdmXlNewVdmXl" xml:space="preserve">
    <value>Neu VdmXl</value>
  </data>
  <data name="VdmXlWHeaderTxt" xml:space="preserve">
    <value>Erstellen neue VdmXL</value>
  </data>
  <data name="VdmXlHeaderTxt" xml:space="preserve">
    <value>VDM&lt;sup&gt;XL&lt;/sup&gt; - NPV - ansicht über die Lebensdauer</value>
  </data>
  <data name="VdmXlTaskDownTimelbl" xml:space="preserve">
    <value>Ausfallzeit Maßnahmen</value>
  </data>
</root>