<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ClustMenuTitle" xml:space="preserve">
    <value>Ansatz für die Erstellung von Arbeitspakete</value>
  </data>
  <data name="ClustMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;In diesem Cluster-Modul können aus den in der &lt;b&gt;Wert Risiko Bewertung&lt;/b&gt; eingerichteten Aktionen &lt;b&gt;Arbeitspakete&lt;/b&gt; generiert werden.&lt;/p&gt;
        &lt;ol&gt;
            &lt;li&gt;
               &lt;b&gt;Arbeitspakete&lt;/b&gt; können vorab im Modul Wert Risiko Bewertung eingerichtet werden
                &lt;ol type='a'&gt;
                    &lt;li&gt;Angeben &lt;b&gt;von Arbeitspakete&lt;/b&gt; im &lt;b&gt;Detail "Vorbeugende Maßnahmen&lt;/b&gt;" im Modul "Wert Risiko Bewertung"&lt;/li&gt;
                    &lt;li&gt;Erwarten Sie qualitative &lt;b&gt;Arbeitspakete mit einer gründlichen&lt;/b&gt; Einrichtung im &lt;b&gt;Datenvorbereitungsteil&lt;/b&gt; von AMprover&lt;/li&gt;
                    &lt;li&gt;Zb. 'Monatliche Inspektion - rund' oder '2 jährliche bearbeitungszeit mechanisch' &lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;
                Wählen Sie die Schaltfläche &lt;b&gt;Cluster Konzept&lt;/b&gt; für Szenarien oder Wert Risiko Bewertungen:
                &lt;ol type='a'&gt;
                    &lt;li&gt;&lt;b&gt;Cluster Konzept neu generieren&lt;/b&gt;, um alle Aktionen neuen Clustern zuzuweisen. &lt;br /&gt;&lt;b&gt;NB.&lt;/b&gt; &lt;u&gt;Alle Berechnungen werden gelöscht!&lt;/u&gt;&lt;/li&gt;
                    &lt;li&gt;&lt;b&gt;Aktualisierung bestehenden Konzepts&lt;/b&gt; für angepasste oder hinzugefügte Aktionen&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
&lt;li&gt;Angeben der Charakteristika des Clusters, z. B. &lt;b&gt;Intervall&lt;/b&gt;, &lt;b&gt;Ausfallzeit&lt;/b&gt; und &lt;b&gt;Clustereigenschaften&lt;/b&gt;&lt;/li&gt;
            &lt;li&gt;Ordnen Sie eventuelle Aktionen neu an, indem Sie den richtigen Cluster auf der Registerkarte &lt;b&gt;Cluster Konzept&lt;/b&gt; im Aktionsraster auswählen.&lt;/li&gt;
            &lt;li&gt;Cluster als Ganzes oder als einzelne Aktionen im &lt;b&gt;Berechnungsraster &lt;/b&gt;auf der Registerkarte &lt;b&gt;Cluster Konzept&lt;/b&gt; berechnen&lt;/li&gt;
            &lt;li&gt;Um Arbeitspakete zu generieren, die in der ABS-Struktur verknüpft sind, muss die hierarchische Struktur (spezifiziert im Modul Wert Risiko Bewertungen) mit dem Gliederung der Aktiva (ABS) in &lt;b&gt;Link PM-Pläne mit ABS&lt;/b&gt; verknüpft werden.&lt;/li&gt;
            &lt;li&gt;
                Wählen Sie die Schaltfläche &lt;b&gt;Aufgaben Plan&lt;/b&gt; aus für
                &lt;ol type='a'&gt;
                    &lt;li&gt;&lt;b&gt;Aufgaben Plan neu generieren, &lt;/b&gt; um alle Aufgabenpläne für seine spezifischen Assets neu zu generieren&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
        &lt;/ol&gt;</value>
  </data>
  <data name="ClustHeaderTxt" xml:space="preserve">
    <value>Cluster</value>
  </data>
  <data name="ClustClusterStatusLbl" xml:space="preserve">
    <value>Cluster Status</value>
  </data>
  <data name="ClustIntervalUnitLbl" xml:space="preserve">
    <value>Einheit</value>
  </data>
  <data name="ClustIntervalLbl" xml:space="preserve">
    <value>Intervall</value>
  </data>
  <data name="ClustDownTimeLbl" xml:space="preserve">
    <value>Ausfallzeit (Stunden)</value>
  </data>
  <data name="ClustShortKeyLbl" xml:space="preserve">
    <value>Kurzname</value>
  </data>
  <data name="ClustPartLevelLbl" xml:space="preserve">
    <value>Teil des Niveaus</value>
  </data>
  <data name="ClustPartNameLbl" xml:space="preserve">
    <value>Teil des Cluster</value>
  </data>
  <data name="ClustDurationLbl" xml:space="preserve">
    <value>Dauer (Stunden)</value>
  </data>
  <data name="ClustDescriptionLbl" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="ClustInitiatorLbl" xml:space="preserve">
    <value>Initiator</value>
  </data>
  <data name="ClustExecutorLbl" xml:space="preserve">
    <value>Ausführende</value>
  </data>
  <data name="ClustResponsibleLbl" xml:space="preserve">
    <value>Verantwortlich</value>
  </data>
  <data name="ClustRemarksTab" xml:space="preserve">
    <value>Bemerkungen</value>
  </data>
  <data name="ClustConceptCostsTab" xml:space="preserve">
    <value>Cluster Kosten</value>
  </data>
  <data name="ClustDiscUnitLbl" xml:space="preserve">
    <value>Disziplinen</value>
  </data>
  <data name="ClustTotEstCostLbl" xml:space="preserve">
    <value>Geschätzte Gesamt Kosten</value>
  </data>
  <data name="ClustEnergyUnitLbl" xml:space="preserve">
    <value>Energie</value>
  </data>
  <data name="ClustTotTaskCostLbl" xml:space="preserve">
    <value>Gesamt Kosten</value>
  </data>
  <data name="ClustMaterialUnitLbl" xml:space="preserve">
    <value>Materialien</value>
  </data>
  <data name="ClustToolUnitLbl" xml:space="preserve">
    <value>Werkzeuge</value>
  </data>
  <data name="ClustTotalCostUnitLbl" xml:space="preserve">
    <value>Gesamt Kosten</value>
  </data>
  <data name="ClustSharedCostUnitLbl" xml:space="preserve">
    <value>Gemeinsame Kosten</value>
  </data>
  <data name="ClustUsedByTab" xml:space="preserve">
    <value>Verschiedenes</value>
  </data>
  <data name="ClustCreatedByLbl" xml:space="preserve">
    <value>Initiiert von</value>
  </data>
  <data name="ClustOnLbl" xml:space="preserve">
    <value>An</value>
  </data>
  <data name="ClustModifiedByLbl" xml:space="preserve">
    <value>Geändert von</value>
  </data>
  <data name="ClustTaskConceptTab" xml:space="preserve">
    <value>Cluster Konzept</value>
  </data>
  <data name="ClustClusterCostTab" xml:space="preserve">
    <value>Cluster Kosten</value>
  </data>
  <data name="ClustTaskPlanTab" xml:space="preserve">
    <value>Aufgaben Plan</value>
  </data>
  <data name="ClustClustersTab" xml:space="preserve">
    <value>Clusters</value>
  </data>
  <data name="ClustExtraTab" xml:space="preserve">
    <value>Extra</value>
  </data>
  <data name="ClustLocationIdLbl" xml:space="preserve">
    <value>Standort ID</value>
  </data>
  <data name="ClustOrganizationIdLbl" xml:space="preserve">
    <value>Organisation ID</value>
  </data>
  <data name="ClustTemplateTypeLbl" xml:space="preserve">
    <value>Vorlage Typ</value>
  </data>
  <data name="ClustInterruptableLbl" xml:space="preserve">
    <value>Unterbrechbar</value>
  </data>
  <data name="ClustSequenceLbl" xml:space="preserve">
    <value>Sequenz</value>
  </data>
  <data name="ClustShiftEndDateLbl" xml:space="preserve">
    <value>Enddatum des Schlupfs (Tage)</value>
  </data>
  <data name="ClustPriorityLbl" xml:space="preserve">
    <value>Priorität</value>
  </data>
  <data name="ClustShiftStartDateLbl" xml:space="preserve">
    <value>Slack Startdatum (Tage)</value>
  </data>
  <data name="ClustRegenerateClustConceptBtn" xml:space="preserve">
    <value>Cluster Konzept neu generieren</value>
  </data>
  <data name="ClustRegenerateClustConceptByRiskObjectBtn" xml:space="preserve">
    <value>Clusterkonzept auf Wert Risiko Bewertung neu generieren</value>
  </data>
  <data name="ClustUpdateClustConceptByRiskObjectBtn" xml:space="preserve">
    <value>Clust. Konz. nach Wert Risiko Bewertung aktualisieren</value>
  </data>
  <data name="ClustRegenerateClustConceptByScenarioBtn" xml:space="preserve">
    <value>Clusterkonzept nach Szenario neu generieren</value>
  </data>
  <data name="ClustUpdateClustConceptByScenarioBtn" xml:space="preserve">
    <value>Clusterkonzept nach Szenario aktualisieren</value>
  </data>
  <data name="ClustUpdateClustConceptBtn" xml:space="preserve">
    <value>Aktualisierung bestehenden Konzepts</value>
  </data>
  <data name="ClustRefreshClusterDataBtn" xml:space="preserve">
    <value>Clusterdaten aktualisieren</value>
  </data>
  <data name="ClustRefreshClusterCostBtn" xml:space="preserve">
    <value>Kosten Aktualisierung Clusters (nur Kosten)</value>
  </data>
  <data name="ClustClusterConceptTab" xml:space="preserve">
    <value>Cluster Konzept</value>
  </data>
  <data name="ClustRegenerateTaskPlanBtn" xml:space="preserve">
    <value>Aufgaben Plan neu generieren</value>
  </data>
  <data name="ClustUpdateExistingTaskPlanBtn" xml:space="preserve">
    <value>Aktualisierung bestehenden Aufgaben Plänen</value>
  </data>
  <data name="ClustNewBtn" xml:space="preserve">
    <value>Neues</value>
  </data>
  <data name="ClustSaveBtn" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="CwClustNameLbl" xml:space="preserve">
    <value>Cluster Name</value>
  </data>
  <data name="CwCreateBtn" xml:space="preserve">
    <value>Erstellen</value>
  </data>
  <data name="CwHeaderTxt" xml:space="preserve">
    <value>Erstellen neue Cluster</value>
  </data>
  <data name="CwPartOfClustLbl" xml:space="preserve">
    <value>Teil des Clusters</value>
  </data>
  <data name="ClustPropTab" xml:space="preserve">
    <value>Clustereigenschaften</value>
  </data>
  <data name="CvmRegenerateClusterTxt" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie alle Clusterkonzepte neu generieren möchten?</value>
  </data>
  <data name="CvmUpdateClustConcepTxt" xml:space="preserve">
    <value>Möchten Sie wirklich alle Clusterkonzepte aktualisieren?</value>
  </data>
  <data name="CvmRefreshClustdataTxt" xml:space="preserve">
    <value>Möchten Sie wirklich alle Clusterdaten aktualisieren?</value>
  </data>
  <data name="CvmTaskPlanDetailsTxt" xml:space="preserve">
    <value>Details Aufgaben Plan</value>
  </data>
  <data name="CvmRefreshClusterCostTxt" xml:space="preserve">
    <value>Möchten Sie wirklich alle Cluster Kosten aktualisieren?</value>
  </data>
  <data name="CvmRegenerateTaskPlanTxt" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie alle Aufgabenpläne neu generieren möchten?</value>
  </data>
  <data name="CvmUpdateExistingTaskPlanTxt" xml:space="preserve">
    <value>Möchten Sie wirklich alle Aufgabenpläne aktualisieren?</value>
  </data>
  <data name="CvmUpdatingClosterConceptTxt" xml:space="preserve">
    <value>Aktualisierung bestehender Clusterkonzepte...</value>
  </data>
  <data name="CvmRefreshingClustdataTxt" xml:space="preserve">
    <value>Auffrischung der Clusterdaten...</value>
  </data>
  <data name="CvmRefreshingClustCostTxt" xml:space="preserve">
    <value>Auffrischung der Cluster Kosten...</value>
  </data>
  <data name="CvmRefreshingClustConceptTxt" xml:space="preserve">
    <value>Cluster Konzepten neu generieren...</value>
  </data>
  <data name="CvmRefreshClustPlanTxt" xml:space="preserve">
    <value>Aufgabenpläne neu generieren...</value>
  </data>
  <data name="CvmUpdatingTaskPlansTxt" xml:space="preserve">
    <value>Aktualisierung bestehender Aufgabenpläne...</value>
  </data>
  <data name="CvmLoadingTxt" xml:space="preserve">
    <value>Laden</value>
  </data>
  <data name="CvmLoadingTxtTxt" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="CvmClusterCostDetailsTxt" xml:space="preserve">
    <value>Details Cluster Kosten</value>
  </data>
  <data name="CCCWidgetTxtEnter" xml:space="preserve">
    <value>Bestätigen mit:</value>
  </data>
  <data name="CCCWidgetTxtWarning" xml:space="preserve">
    <value>Zur Bestätigung:  Cluster Konzept kosten Berechnungen werden gelöscht.</value>
  </data>
  <data name="CCCWidgetTxtIAgree" xml:space="preserve">
    <value>ICH STIMME ZU</value>
  </data>
  <data name="CvmUpdateClustConceptTxt" xml:space="preserve">
    <value>Das folgende Wert Risiko Bewertung wird aktualisiert</value>
  </data>
  <data name="CvmUpdateClustConceptScenTxt" xml:space="preserve">
    <value>Das folgende Szenario wird aktualisiert</value>
  </data>
  <data name="CvmSelection" xml:space="preserve">
    <value>Auswahl</value>
  </data>
  <data name="ClustHeaderPmoTxt" xml:space="preserve">
    <value>Cluster - PMO-Ansicht (Aktuelle Maßnahmen werden angezeigt)</value>
  </data>
  <data name="CvmRefreshingClustConceptUpdateTxt" xml:space="preserve">
    <value>Cluster Konzepten aktualisieren...</value>
  </data>
</root>