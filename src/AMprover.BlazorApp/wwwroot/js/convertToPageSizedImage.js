// Page size definitions in inches
const pageSizes = {
    letter: { width: 8.5, height: 11 },
    a4: { width: 8.27, height: 11.69 },
    legal: { width: 8.5, height: 14 },
    a3: { width: 11.69, height: 16.54 },
    tabloid: { width: 11, height: 17 }
};

window.convertToPageSizedImage = function(divClassName, fileName, pageSize = 'a4', dpi = 300, orientation = 'portrait', customWidth = null, customHeight = null) {
    const parentElement = document.getElementsByClassName(divClassName)[0];
    if (!parentElement) {
        console.error(`Element with class name ${divClassName} not found`);
        return Promise.reject(`Element with class name "${divClassName}" not found`);
    }

    // Calculate page dimensions in pixels
    let pageWidth, pageHeight;

    if (pageSize === 'custom') {
        if (!customWidth || !customHeight) {
            console.error('Custom dimensions are required when pageSize is "custom"');
            return Promise.reject('Custom dimensions are required when pageSize is "custom"');
        }
        pageWidth = parseInt(customWidth);
        pageHeight = parseInt(customHeight);
    } else {
        const dimensions = pageSizes[pageSize];
        if (!dimensions) {
            console.error(`Invalid page size: ${pageSize}. Valid options: ${Object.keys(pageSizes).join(', ')}, custom`);
            return Promise.reject(`Invalid page size: ${pageSize}`);
        }
        pageWidth = Math.round(dimensions.width * dpi);
        pageHeight = Math.round(dimensions.height * dpi);

        // Apply orientation
        if (orientation === 'landscape') {
            [pageWidth, pageHeight] = [pageHeight, pageWidth];
        }
    }

    // Save the original styles
    const originalStyles = {
        width: parentElement.style.width,
        height: parentElement.style.height,
        overflow: parentElement.style.overflow,
        position: parentElement.style.position
    };

    // Set styles for capturing
    parentElement.style.overflow = 'visible';
    parentElement.style.position = 'relative';

    // Get the actual dimensions of the content
    const contentWidth = parentElement.scrollWidth;
    const contentHeight = parentElement.scrollHeight;

    console.log(`Content dimensions: ${contentWidth}x${contentHeight}`);
    console.log(`Page dimensions: ${pageWidth}x${pageHeight}`);

    // Check for page break elements first
    const pageBreaks = parentElement.querySelectorAll('.rams-page-break');
    if (pageBreaks.length > 0) {
        console.log(`Found ${pageBreaks.length} page breaks, splitting at page break points`);
        return capturePageBreakPages(parentElement, fileName, originalStyles, pageBreaks);
    }

    // Check if content fits in one page
    if (contentWidth <= pageWidth && contentHeight <= pageHeight) {
        console.log('Content fits in one page');
        return captureSinglePage(parentElement, fileName, originalStyles, pageWidth, pageHeight, contentWidth, contentHeight);
    } else {
        console.log('Content requires multiple pages');
        return captureMultiplePages(parentElement, fileName, originalStyles, pageWidth, pageHeight, contentWidth, contentHeight);
    }
}

function captureSinglePage(element, fileName, originalStyles, pageWidth, pageHeight, contentWidth, contentHeight) {
    const options = {
        allowTaint: true,
        backgroundColor: '#ffffff',
        scale: 1,
        useCORS: true,
        width: contentWidth,
        height: contentHeight,
        scrollX: 0,
        scrollY: 0
    };

    return html2canvas(element, options).then(canvas => {
        downloadCanvas(canvas, `${fileName}.png`);
        restoreStyles(element, originalStyles);
        console.log('Single page capture completed');
        return { success: true, filesGenerated: 1, message: 'Single page capture completed' };
    }).catch(error => {
        console.error('Error capturing single page:', error);
        restoreStyles(element, originalStyles);
        return Promise.reject(error);
    });
}

function captureMultiplePages(element, fileName, originalStyles, pageWidth, pageHeight, contentWidth, contentHeight) {
    return new Promise((resolve, reject) => {
        // Calculate number of slices needed
        const horizontalSlices = Math.ceil(contentWidth / pageWidth);
        const verticalSlices = Math.ceil(contentHeight / pageHeight);
        const totalSlices = horizontalSlices * verticalSlices;

        console.log(`Will create ${horizontalSlices}x${verticalSlices} = ${totalSlices} slices`);

        let currentSlice = 0;
        let completedSlices = 0;

        function captureSlice(row, col) {
            const sliceX = col * pageWidth;
            const sliceY = row * pageHeight;
            const sliceWidth = Math.min(pageWidth, contentWidth - sliceX);
            const sliceHeight = Math.min(pageHeight, contentHeight - sliceY);

            console.log(`Capturing slice ${currentSlice + 1}/${totalSlices}: row=${row}, col=${col}, x=${sliceX}, y=${sliceY}, size=${sliceWidth}x${sliceHeight}`);

            const options = {
                allowTaint: true,
                backgroundColor: '#ffffff',
                scale: 1,
                useCORS: true,
                x: sliceX,
                y: sliceY,
                width: sliceWidth,
                height: sliceHeight,
                scrollX: 0,
                scrollY: 0
            };

            html2canvas(element, options).then(canvas => {
                // Create filename with row and column info
                let sliceName;
                if (totalSlices > 1) {
                    sliceName = `${fileName}_page${currentSlice + 1}of${totalSlices}.png`;
                } else {
                    sliceName = `${fileName}.png`;
                }

                downloadCanvas(canvas, sliceName);
                completedSlices++;
                currentSlice++;

                // Move to next slice
                const nextCol = col + 1;
                const nextRow = nextCol >= horizontalSlices ? row + 1 : row;
                const newCol = nextCol >= horizontalSlices ? 0 : nextCol;

                if (nextRow < verticalSlices) {
                    setTimeout(() => {
                        captureSlice(nextRow, newCol);
                    }, 500); // Small delay between captures
                } else {
                    // All slices completed
                    restoreStyles(element, originalStyles);
                    console.log('All slices captured successfully');
                    resolve({
                        success: true,
                        filesGenerated: completedSlices,
                        message: `${completedSlices} slices captured successfully`
                    });
                }
            }).catch(error => {
                console.error(`Error capturing slice ${currentSlice + 1}:`, error);
                restoreStyles(element, originalStyles);
                reject(error);
            });
        }

        // Start capturing from top-left
        captureSlice(0, 0);
    });
}

function capturePageBreakPages(element, fileName, originalStyles, pageBreaks) {
    return new Promise((resolve, reject) => {
        const pageBreakArray = Array.from(pageBreaks);
        const totalPages = pageBreakArray.length + 1;
        let completedPages = 0;

        console.log(`Will create ${totalPages} pages based on page breaks`);

        function capturePageSection(pageIndex) {
            console.log(`Capturing page ${pageIndex + 1} of ${totalPages}`);

            // Get the X positions of page breaks to determine crop areas (horizontal flow)
            let startX = 0;
            let endX = element.scrollWidth;

            if (pageIndex > 0 && pageBreakArray[pageIndex - 1]) {
                // Start after the previous page break
                const prevPageBreak = pageBreakArray[pageIndex - 1];
                const rect = prevPageBreak.getBoundingClientRect();
                const containerRect = element.getBoundingClientRect();
                startX = rect.right - containerRect.left + element.scrollLeft;
            }

            if (pageIndex < pageBreakArray.length && pageBreakArray[pageIndex]) {
                // End at the next page break
                const nextPageBreak = pageBreakArray[pageIndex];
                const rect = nextPageBreak.getBoundingClientRect();
                const containerRect = element.getBoundingClientRect();
                endX = rect.left - containerRect.left + element.scrollLeft;
            }

            console.log(`Page ${pageIndex + 1}: X range ${startX} to ${endX}`);

            // Hide all page breaks for cleaner capture
            const hiddenPageBreaks = [];
            pageBreakArray.forEach(pb => {
                if (pb.style.display !== 'none') {
                    hiddenPageBreaks.push({element: pb, originalDisplay: pb.style.display});
                    pb.style.display = 'none';
                }
            });

            // Wait for layout to update
            setTimeout(() => {
                // Capture the full diagram first
                const options = {
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    scale: 1,
                    useCORS: true,
                    scrollX: 0,
                    scrollY: 0,
                    width: element.scrollWidth,
                    height: element.scrollHeight
                };

                html2canvas(element, options).then(fullCanvas => {
                    // Restore page breaks
                    hiddenPageBreaks.forEach(item => {
                        item.element.style.display = item.originalDisplay;
                    });

                    if (fullCanvas.width === 0 || fullCanvas.height === 0) {
                        console.warn(`Page ${pageIndex + 1} generated empty canvas`);
                        completedPages++;
                        if (completedPages === totalPages) {
                            restoreStyles(element, originalStyles);
                            resolve({ success: true, filesGenerated: totalPages, message: 'Page break capture completed' });
                        } else {
                            setTimeout(() => capturePageSection(pageIndex + 1), 100);
                        }
                        return;
                    }

                    // Create a cropped canvas for this page
                    const croppedCanvas = document.createElement('canvas');
                    const ctx = croppedCanvas.getContext('2d');

                    const cropWidth = Math.max(1, endX - startX);
                    croppedCanvas.width = cropWidth;
                    croppedCanvas.height = fullCanvas.height;

                    // Fill with white background
                    ctx.fillStyle = '#ffffff';
                    ctx.fillRect(0, 0, croppedCanvas.width, croppedCanvas.height);

                    // Draw the cropped portion (horizontal crop)
                    ctx.drawImage(
                        fullCanvas,
                        startX, 0, cropWidth, fullCanvas.height,  // source (x, y, width, height)
                        0, 0, croppedCanvas.width, croppedCanvas.height  // destination
                    );

                    const pageName = `${fileName}_page${pageIndex + 1}of${totalPages}.png`;
                    downloadCanvas(croppedCanvas, pageName);
                    console.log(`Successfully captured page ${pageIndex + 1} (${croppedCanvas.width}x${croppedCanvas.height})`);

                    completedPages++;

                    if (completedPages === totalPages) {
                        restoreStyles(element, originalStyles);
                        console.log('Page break capture completed');
                        resolve({ success: true, filesGenerated: totalPages, message: 'Page break capture completed' });
                    } else {
                        // Capture next page
                        setTimeout(() => capturePageSection(pageIndex + 1), 100);
                    }
                }).catch(error => {
                    console.error(`Error capturing page ${pageIndex + 1}:`, error);
                    // Restore page breaks on error
                    hiddenPageBreaks.forEach(item => {
                        item.element.style.display = item.originalDisplay;
                    });
                    restoreStyles(element, originalStyles);
                    reject(error);
                });
            }, 100); // Wait for layout update
        }

        // Start with first page
        capturePageSection(0);
    });
}

function downloadCanvas(canvas, filename) {
    const imgData = canvas.toDataURL('image/png');
    const tempLink = document.createElement('a');
    tempLink.href = imgData;
    tempLink.setAttribute('download', filename);
    tempLink.click();
}

function restoreStyles(element, originalStyles) {
    element.style.width = originalStyles.width;
    element.style.height = originalStyles.height;
    element.style.overflow = originalStyles.overflow;
    element.style.position = originalStyles.position;
}