@media print {
    @page {
        margin: 1cm;
    }
    
    body {
        counter-reset: page;
    }
    
    @page {
        @bottom-center {
            content: "Page " counter(page);
        }
    }
    
    .page-break {
        page-break-after: always;
        counter-increment: page;
    }

    /* RAMS page break specific styling */
    .rams-page-break {
        page-break-after: always;
        counter-increment: page;
        display: none !important; /* Hide the page break element itself in print */
    }

    /* Ensure content after page break starts on new page */
    .rams-page-break + * {
        page-break-before: always;
    }
    
    .sidebar, #sidebarCollapse, .menu.ml-auto, .header-navigation,
    button.rz-button, table thead tr:nth-child(2), .rz-datatable-thead tr:nth-child(2), .task-message  {
        display: none !important;
    }

    .treeview-row-selected{
        -webkit-print-color-adjust: exact !important; /* Chrome, Safari */
        color-adjust: exact !important; /* Firefox */
        print-color-adjust: exact !important; /* Future standard */
        background-color: #62bc47 !important; /* Force the background color */
    }

    .rz-chart.rz-scheme-pastel, .sapa-graph, .sapa-graph-layout {
        scale: 0.7;
    }

    h2 {
        font-size: 18px;
        font-weight: bold;
    }

    .rz-tabview-panel, .rz-tabview-title, .rz-tabview, .rz-textbox, .form-control, .am-dropdown-header, .treeview-container {
        font-size: 0.8rem;
        overflow: hidden;
    }

    .rz-grid-table thead th .rz-column-title, .rz-cell-data, .sapa-side-tab-content {
        font-size: 0.6rem;
    }

    .rz-data-grid tbody tr td .rz-cell-data {
        font-size: 0.5rem;
        line-height: 8px;
    }

    /* Align remaining header elements to the left */
    .top-row {
        display: flex;
        justify-content: flex-start !important;
        align-items: center;
    }

    /* Ensure the logo and text elements stay aligned properly */
    .top-row img, .top-row .menu-title, .top-row .menu-catchphrase {
        margin-right: 10px;
        float: left;
    }

    /* Remove any auto margins that might push elements right */
    .top-row > * {
        margin-left: 0 !important;
        margin-right: 10px;
    }

    /* Page settings */
    @page {
        size: landscape;
        margin: 0.5cm;
    }
}