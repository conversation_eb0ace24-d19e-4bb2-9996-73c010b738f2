<CascadingAuthenticationState>
    @* <ApplicationInsightsTracker /> *@
    <Router AppAssembly="@typeof(Program).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
                <NotAuthorized>
                    @* Applied for a double-lock. NonAuthorized is expected to be handled via the MainLayout.razor
                        But somehow the use also lands at this point in code, so reuse the NonAuthorizedHanler that is present on MainLayout.razor as well.
                        Assumption here: Coming here is applicable when your ARE authentication but NOT authorized (enough). And that MainLayout is applicable for user that are NOT authenticated at all. *@
                    <NonAuthorizedHandler />
                </NotAuthorized>
            </AuthorizeRouteView>
        </Found>
        <NotFound>
            <LayoutView Layout="@typeof(MainLayout)">
                <p>Sorry, there's nothing at this address.</p>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>
