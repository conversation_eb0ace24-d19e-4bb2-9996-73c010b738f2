.treeview-container {
    width: 100%;
    height: 250px;
    resize: vertical;
    overflow-y: auto;
    margin-bottom: 20px;
    border-radius: 4px;
    padding: 8px 0px 14px 8px;
    border: solid 1px #e9ecef;
}

.treeview-content {
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    padding-right: 8px;
}

.noselect {
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Old versions of Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version, currently  supported by Chrome, Edge, Opera and Firefox */
}

.treeview-row-selected {
    background-color: #62BC47;
    cursor: pointer;
    border-radius: 3px;
    padding: 2px;
    color: whitesmoke;
    margin: 1px 0px;

    &:hover {
        background-color: #9DD58B;
    }
}

.treeview-row {
    cursor: pointer;
    border-radius: 3px;
    padding: 2px;
    margin: 1px 0px;

    &:hover {
        background-color: #9DD58B;
        color: whitesmoke;
    }
}

.treeview-expand {
    width: 20px;
    text-align: center;
    font-weight: bold;
    float: left;
}

.treenode-filtered-icon {
    margin-left: -32px;
    margin-right: 10px;
    position: relative
}

.treenode-icon {
    margin-left: 13px;
    margin-right: 5px;
    font-size: 14px;
    position: relative
}

.treenode-text {
    margin-left: 3px;

    &.disabled{
        color:grey;
    }
}
