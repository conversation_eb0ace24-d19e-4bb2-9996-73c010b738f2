@page "/rams"
@using AMprover.BlazorApp.Helpers
@using AMprover.BusinessLogic.Enums.Rams

<style>
    .content {
        margin-bottom: 0;
    }

    .main {
        overflow: hidden;
    }
</style>

<h2>@Localizer["RamsHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-3 text-center">
    </div>
    <div class="col-5">
        <RadzenStack Orientation="Orientation.Horizontal" Gap="0.4rem" Wrap="FlexWrap.Wrap"
                     class="btn-mainbar my-2 mr-2" JustifyContent="JustifyContent.End">
            <RadzenButton Icon="add_circle_outline" Text=@Localizer["RamsNewBtnTxt"] Click=@OpenNewRamsWidget
                          ButtonStyle=ButtonStyle.Secondary />

            <RadzenButton Icon="delete" Text=@Localizer["RamsDeleteBtnTxt"] Click=DeleteDiagram
                          ButtonStyle=ButtonStyle.Danger
                          Visible="CurrentDiagram != null"
                          Disabled="CurrentDiagram == null || !AppState.CanEdit" />
        </RadzenStack>
    </div>
</div>

<div class="row rams">
    <!-- Left Panel - Rams diagram picker -->
    @if (!ShowHidePanelMode)
    {
        <div class="col-md-3 diagram-container-width-left">
            <div class="row">
                <div class="col-sm-12 treeview risk-assessment">
                    <RadzenTabs @ref=@TreeTab class="hide-tabs-nav">
                        <Tabs>
                            <RadzenTabsItem>
                                <TreeComponent TItem=RamsTreeObject
                                               Treeview=RamsTree
                                               NodeClickCallback=ClickTreeNode />
                            </RadzenTabsItem>
                        </Tabs>
                    </RadzenTabs>
                </div>
            </div>

            <!-- Selected rams block and container editor -->
            <div class="row">
                <div class="col-sm-12">
                    <RadzenTabs @ref=@EditorTab @bind-SelectedIndex=@SelectedTabIndex>
                        <Tabs>
                            <RadzenTabsItem Text="Diagram">
                                @if (CurrentDiagram != null)
                                {
                                    <div class="col-sm-12">
                                        <div class="row">
                                            <div class="col-sm-2">
                                                <AMproverNumberInput TValue="int" Label="Id"
                                                                     @bind-Value=@CurrentDiagram.Id Disabled="true"/>
                                            </div>
                                            <div class="col-sm-10">
                                                <AMproverTextBox Label=@Localizer["RamsNameTxt"]
                                                                 @bind-Value=@CurrentDiagram.Name Placeholder="Name"
                                                                 Change=@SaveRamsDiagram/>
                                            </div>
                                        </div>
                                        <AMproverDictionaryDropdown Disabled=@DisabledDropdowns Label="Scenario"
                                                                    Data=@ScenarioDict.ToNullableDictionary()
                                                                    @bind-Value=@CurrentDiagram.ScenId
                                                                    Change=@SaveRamsDiagram AllowClear="true"/>
                                        <div class="row">
                                            <div class="col-sm-8">
                                                <AMproverDictionaryDropdown Label=@Localizer["RamsRiskObjectTxt"]
                                                                            Data=@RiskObjectDict.ToNullableDictionary()
                                                                            @bind-Value=@CurrentDiagram.RiskObject
                                                                            Change=@SaveRamsDiagram
                                                                            AllowClear="true"/>
                                            </div>
                                            <div class="col-sm-4">
                                                <div class="form-group">
                                                    <RadzenButton Icon="download" class="my-2 mx-1"
                                                                  Text=@Localizer["RamsLoadDiagramTxt"]
                                                                  Click=@GenerateDiagramFromRiskObject
                                                                  Size=ButtonSize.Small
                                                                  ButtonStyle=ButtonStyle.Secondary
                                                                  Disabled=!AppState.CanEdit />
                                                </div>
                                            </div>
                                        </div>
                                        <RadzenTabs class="rams-tab">
                                            <Tabs>
                                                <RadzenTabsItem Text=@Localizer["RamsGeneralTxt"]>
                                                    <AMproverTextArea TValue="string" Label=@Localizer["RamsDescrTxt"]
                                                                      @bind-Value=@CurrentDiagram.Descr
                                                                      Change=@SaveRamsDiagram/>
                                                    <AMproverTextArea TValue="string" Label=@Localizer["RamsPrereqTxt"]
                                                                      @bind-Value=@CurrentDiagram.Prerequisites
                                                                      Change=@SaveRamsDiagram/>

                                                    <!-- Reliability (from to) year(s) -->
                                                    <div class="row">
                                                        <div class="col-sm-4">
                                                            <AMproverNumberInput TValue="decimal"
                                                                                 Label="Reliability from:"
                                                                                 Format="N2"
                                                                                 @bind-Value=@CurrentDiagram.PeriodFrom/>
                                                        </div>
                                                        <div class="col-sm-4">
                                                            <AMproverNumberInput TValue="decimal"
                                                                                 Label=@Localizer["RamsToYearsTxt"]
                                                                                 Format="N2"
                                                                                 @bind-Value=@CurrentDiagram.PeriodTo/>
                                                        </div>
                                                        <div class="col-sm-4">
                                                            <!-- Available (hours/year)-->
                                                            <AMproverNumberInput TValue="double"
                                                                                 Label=@Localizer["RamsAvailHoursTxt"]
                                                                                 Format="N0"
                                                                                 @bind-Value=@CurrentDiagram.AvailableTime
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                    </div>

                                                    <!-- Tabs: Properties, Descriptions, remarks -->
                                                    <AMproverTextArea TValue="string" Label=@Localizer["RamsRemarksTxt"]
                                                                      @bind-Value=@CurrentDiagram.Remark
                                                                      Change=@SaveRamsDiagram/>

                                                    <!-- create lcc calculation of this diagram -->
                                                    <Radzen.Blazor.RadzenCheckBox @bind-Value=@CurrentDiagram.WantLcc
                                                                                  TValue="bool?"
                                                                                  Change=@CreateLcvCalculation/> @Localizer["RamsLccCalcTxt"]
                                                    <br/>
                                                    <!-- automatically calculate availability -->
                                                    <Radzen.Blazor.RadzenCheckBox
                                                        @bind-Value=@CurrentDiagram.CalculateAvailability TValue="bool"
                                                        Change=@(() => SaveRamsDiagram())/> @Localizer["RamsAutoCalcTxt"]
                                                    <br/><br/>

                                                    <!-- test interval (hrs), horizon (int) -->
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="int?"
                                                                                 Label=@Localizer["RamsTestIntervalTxt"]
                                                                                 @bind-Value=@CurrentDiagram.TestInterval
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="int?"
                                                                                 Label=@Localizer["RamsHorizonTxt"]
                                                                                 @bind-Value=@CurrentDiagram.Horizon
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                    </div>
                                                </RadzenTabsItem>

                                                <RadzenTabsItem Text=@Localizer["RamsRequirementsTxt"]>
                                                    <p><strong>@Localizer["RamsProgramOfDemandsTxt"]</strong></p>

                                                    <!-- Reliability and Availability -->
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double?" Format="F4"
                                                                                 Label=@Localizer["RamsReliabilityTxt"]
                                                                                 @bind-Value=@CurrentDiagram.RequirementReliability
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double?" Format="F4"
                                                                                 Label=@Localizer["RamsAvailabilityTxt"]
                                                                                 @bind-Value=@CurrentDiagram.RequirementAvailability
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                    </div>

                                                    <!-- Capacity -->
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double?" Format="F2"
                                                                                 Label=@Localizer["RamsCapacityTxt"]
                                                                                 @bind-Value=@CurrentDiagram.RequirementCapacity
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <div class="neg-margin-small">
                                                                <label>@Localizer["RamsCapacityUnitTxt"]</label>
                                                            </div>
                                                            <div class="neg-margin-small">
                                                                <AMDropdown TKeyType="int?"
                                                                    Data=@UnitTypeDict
                                                                    @bind-Value=@CurrentDiagram.RequirementCapacityUnit
                                                                    Change=@SaveRamsDiagram
                                                                    AllowClear="true"
                                                                    Disabled="true" />
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Quality and Ecological Footprint -->
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverTextBox Label=@Localizer["RamsQualityTxt"]
                                                                             @bind-Value=@CurrentDiagram.RequirementQuality
                                                                             Change=@SaveRamsDiagram/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverTextBox
                                                                Label=@Localizer["RamsEcologicalFootprintTxt"]
                                                                @bind-Value=@CurrentDiagram.RequirementEcologicalFootprint
                                                                Change=@SaveRamsDiagram/>
                                                        </div>
                                                    </div>

                                                    <!-- Budget -->
                                                    <p><strong>Budget</strong></p>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label="Capex"
                                                                                 @bind-Value=@CurrentDiagram.BudgetCapex
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label="Opex"
                                                                                 @bind-Value=@CurrentDiagram.BudgetOpex
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                    </div>

                                                    <!-- LCC Lifetime -->
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="int?"
                                                                                 Label=@Localizer["RamsLifeTimeTxt"]
                                                                                 @bind-Value=@CurrentDiagram.LccLifetime
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                    </div>
                                                </RadzenTabsItem>

                                                <RadzenTabsItem Text=@Localizer["RamsRealizationTxt"]>
                                                    <p><strong>@Localizer["RamsRealizationTxt"]</strong></p>

                                                    <!-- Reliability and Availability -->
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double?" Format="F4"
                                                                                 Label=@Localizer["RamsReliabilityTxt"]
                                                                                 @bind-Value=@CurrentDiagram.RealizationReliability
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double?" Format="F4"
                                                                                 Label=@Localizer["RamsAvailabilityTxt"]
                                                                                 @bind-Value=@CurrentDiagram.RealizationAvailability
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                    </div>

                                                    <!-- Capacity -->
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double?" Format="F2"
                                                                                 Label=@Localizer["RamsCapacityTxt"]
                                                                                 @bind-Value=@CurrentDiagram.RealizationCapacity
                                                                                 Disabled="true"/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <div class="neg-margin-small">
                                                                <label>@Localizer["RamsCapacityUnitTxt"]</label>
                                                            </div>
                                                            <div class="neg-margin-small">
                                                                <AMDropdown Data=@UnitTypeDict
                                                                            @bind-Value=@CurrentDiagram.RealizationCapacityUnit
                                                                            AllowClear="true"
                                                                            Disabled="true" />
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Quality and Ecological Footprint -->
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverTextBox Label=@Localizer["RamsQualityTxt"]
                                                                             @bind-Value=@CurrentDiagram.RealizationQuality
                                                                             Change=@SaveRamsDiagram/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverTextBox
                                                                Label=@Localizer["RamsEcologicalFootprintTxt"]
                                                                @bind-Value=@CurrentDiagram.RealizationEcologicalFootprint
                                                                Change=@SaveRamsDiagram/>
                                                        </div>
                                                    </div>

                                                    <!-- Budget -->
                                                    <p><strong>Budget</strong></p>

                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label="Capex"
                                                                                 @bind-Value=@CurrentDiagram.RealizationCapex
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label="Opex"
                                                                                 @bind-Value=@CurrentDiagram.RealizationOpex
                                                                                 Change=@SaveRamsDiagram/>
                                                        </div>
                                                    </div>
                                                </RadzenTabsItem>
                                            </Tabs>
                                        </RadzenTabs>
                                    </div>
                                }
                            </RadzenTabsItem>
                            <RadzenTabsItem Text="Group" @ref=@EditorTabGroup
                                            Disabled=@DisableGroupTab>
                                @if (SelectedGroup != null)
                                {
                                    <div class="col-sm-12">

                                        @if (XooNData.Count > 1)
                                        {
                                            <div class="form-group neg-margin">
                                                <div class="row"
                                                     style="background-color:#e9ecef; padding: 10px 10px 5px 10px">
                                                    <div class="col-sm-3">
                                                    </div>
                                                    <div class="col-sm-2 bold">
                                                        <label>Group:</label>
                                                    </div>
                                                    <div class="col-sm-4 neg-margin-small">
                                                        <RadzenDropDown TValue="int?" TextProperty="Value"
                                                                        ValueProperty="Key"
                                                                        Data=@XooNData @bind-Value=@SelectedGroup.XooN
                                                                        Change=@SaveRamsComponentAndRecalculate
                                                                        AllowClear="true"/>
                                                    </div>
                                                    <div class="col-sm-3">
                                                        <label>@($"out of {XooNData.Count}")</label>
                                                    </div>
                                                </div>
                                            </div>
                                        }

                                        <div class="row">
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Label="Id" @bind-Value=@SelectedGroup.Id
                                                                     Disabled="true"/>
                                            </div>
                                            <div class="col-sm-9">
                                                <AMproverTextBox Label=@Localizer["RamsNameTxt"]
                                                                 @bind-Value=@SelectedGroup.Name Placeholder="Name"
                                                                 Change=@SaveRamsComponent/>
                                            </div>
                                        </div>

                                        <AMproverTextArea Label=@Localizer["RamsDescrTxt"] Placeholder="Description"
                                                          @bind-Value=@SelectedGroup.Descr
                                                          Change=@SaveRamsComponent/>

                                        <div class="form-group">
                                            <div class="neg-margin-small">
                                                <label>Status:</label>
                                            </div>
                                            <div class="neg-margin-small">
                                                <RadzenDropDown TValue="int?" TextProperty="Value" ValueProperty="Key"
                                                                Data=@BlockStatusDict @bind-Value=@SelectedGroup.Status
                                                                Change=@SaveRamsComponent AllowClear="true"
                                                                Class="form-control"/>
                                            </div>
                                        </div>

                                        <RadzenTabs @ref=@ContainerContentTab class="rams-tab">
                                            <Tabs>
                                                <RadzenTabsItem Text="Data">
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <strong>@Localizer["RamsTechnicalTxt"]:</strong>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <strong>@Localizer["RamsFunctionalTxt"]:</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double" Format="F2"
                                                                                 Label=@MtbfLabel
                                                                                 @bind-Value=@SelectedItem.Results.MtbfTechnical
                                                                                 Multiplier=@(DisplayMode.ShowYears ? 1 : CurrentDiagram?.AvailableTime ?? 1)
                                                                                 Disabled="true"/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double" Format="F2"
                                                                                 Label=@MtbfLabel
                                                                                 @bind-Value=@SelectedItem.Results.MtbfFunctional
                                                                                 Multiplier=@(DisplayMode.ShowYears ? 1 : CurrentDiagram?.AvailableTime ?? 1)
                                                                                 Disabled="true"/>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverTextBox Label=@Localizer["RamsAvailabilityTxt"]
                                                                             Value=@SelectedItem.Results.AvailTechnical.FormatRoundedValue(Language, 2)
                                                                             Disabled="true"/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverTextBox Label=@Localizer["RamsAvailabilityTxt"]
                                                                             Value=@SelectedItem.Results.AvailFunctional.FormatRoundedValue(Language, 2)
                                                                             Disabled="true"/>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            @if (DisplayMode.ShowFunctional)
                                                            {
                                                                <AMproverNumberInput TValue="double" Format="F2"
                                                                                     Label=@(SelectedItem.Results.AffectedBufferTime > 0 ? $"MTTR (FRT) (Affected by a buffer time of {SelectedItem.Results.AffectedBufferTime})" : "MTTR (FRT)")
                                                                                     @bind-Value=@SelectedItem.Results.MttrFunctional
                                                                                     Disabled="true"/>
                                                            }
                                                            else
                                                            {
                                                                <AMproverNumberInput TValue="double" Format="F2"
                                                                                     Label=@(SelectedItem.Results.AffectedBufferTime > 0 ? $"MTTR (FRT) (Affected by a buffer time of {SelectedItem.Results.AffectedBufferTime})" : "MTTR (FRT)")
                                                                                     @bind-Value=@SelectedItem.Results.MttrTechnical
                                                                                     Disabled="true"/>
                                                            }
                                                        </div>
                                                        <br/>
                                                        <div class="col-sm-6">
                                                            <Radzen.Blazor.RadzenCheckBox
                                                                @bind-Value=@SelectedGroup.Completed
                                                                TValue="bool?"
                                                                Change=@SaveRamsComponent/> @Localizer["RamsCompletedTxt"]
                                                            <br/>
                                                            <Radzen.Blazor.RadzenCheckBox
                                                                @bind-Value=@SelectedGroup.Identical
                                                                TValue="bool?"
                                                                Change=@(() => SaveRamsComponent(true))/> @Localizer["RamsIdenticalToFirstBlockTxt"]
                                                            <br/><br/>
                                                        </div>
                                                    </div>
                                                    <RadzenTabs>
                                                        <Tabs>
                                                            <RadzenTabsItem Text="General">
                                                                <AMproverTextArea Label=@Localizer["RamsRemarksTxt"]
                                                                                  Placeholder="Remarks"
                                                                                  @bind-Value=@SelectedGroup.Remark
                                                                                  Change=@SaveRamsComponent>
                                                                </AMproverTextArea>
                                                            </RadzenTabsItem>
                                                            <RadzenTabsItem Text="Capacity">
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <strong>@Localizer["RamsTechnicalTxt"]:</strong>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <strong>@Localizer["RamsFunctionalTxt"]:</strong>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="double?"
                                                                                             Format="F2"
                                                                                             Label="Capacity"
                                                                                             @bind-Value=@SelectedGroup.CapacityTechn
                                                                                             Disabled="true"/>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="double?"
                                                                                             Format="F2"
                                                                                             Label="Capacity"
                                                                                             @bind-Value=@SelectedGroup.CapacityFunct
                                                                                             Disabled="true"/>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <div class="neg-margin-small">
                                                                            <label>Capacity Unit</label>
                                                                        </div>
                                                                        <div class="neg-margin-small">
                                                                            <AMDropdown TKeyType="int?" 
                                                                                Data=@UnitTypeDict
                                                                                            @bind-Value=@SelectedGroup.CapacityUnit
                                                                                            Change=@SaveRamsComponent
                                                                                            AllowClear="true"
                                                                                            Disabled="true" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="double?"
                                                                                             Format="F2"
                                                                                             Label="Capacity Factor"
                                                                                             @bind-Value=@SelectedGroup.CapacityFactor
                                                                                             Disabled="true"/>
                                                                    </div>
                                                                </div>
                                                            </RadzenTabsItem>
                                                            <RadzenTabsItem Text="SIL/PFD">
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <div class="form-group">
                                                                            <div class="neg-margin-small">
                                                                                <label>Classification:</label>
                                                                            </div>
                                                                            <div class="neg-margin-small">
                                                                                <RadzenDropDown TValue="string"
                                                                                                Data=@(new List<string> {"DD", "DU", "SD", "SU"})
                                                                                                @bind-Value=@SelectedGroup.ClassDc
                                                                                                Change=@ProcessClassificationChangesAndSaveRamsComponent
                                                                                                AllowClear="true"
                                                                                                Class="form-control"
                                                                                                Disabled=@DisableBlockEditing/>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="double?"
                                                                                             Format="F2"
                                                                                             Label=@Localizer["RamsDCdTxt"]
                                                                                             @bind-Value=@SelectedGroup.Dcd
                                                                                             Placeholder="@Localizer["RamsDCdTxt"]"
                                                                                             Change=@(() => SaveRamsComponent(true))
                                                                                             Disabled=@DisableBlockEditing/>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="int?"
                                                                                             Label=@Localizer["RamsTestIntervalTxt"]
                                                                                             @bind-Value=@SelectedGroup.TestInterval
                                                                                             Placeholder="@Localizer["RamsTestIntervalTxt"]"
                                                                                             Change=@(() => SaveRamsComponent(true))
                                                                                             Disabled=@DisableBlockEditing />
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="double?"
                                                                                             Format="F2"
                                                                                             Label=@Localizer["RamsBetaTxt"]
                                                                                             @bind-Value=@SelectedGroup.Beta
                                                                                             Placeholder="@Localizer["RamsBetaTxt"]"
                                                                                             Change=@(() => SaveRamsComponent(true))
                                                                                             Disabled=@DisableBlockEditing />
                                                                    </div>
                                                                </div>
                                                            </RadzenTabsItem>
                                                        </Tabs>
                                                    </RadzenTabs>
                                                </RadzenTabsItem>
                                                <RadzenTabsItem Text="Cost">
                                                    <Radzen.Blazor.RadzenCheckBox @bind-Value=@SelectedGroup.WantLcc
                                                                                  TValue="bool?"
                                                                                  Change=@SaveRamsComponent/> @Localizer["RamsLccCalcTxt"]
                                                    <br/>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <Radzen.Blazor.RadzenCheckBox
                                                                @bind-Value=@SelectedGroup.CostOwner
                                                                TValue="bool?"
                                                                Change=@SaveRamsComponent/> @Localizer["RamsCostOwnerTxt"]
                                                            <br/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <Radzen.Blazor.RadzenCheckBox
                                                                @bind-Value=@SelectedGroup.CostLinked
                                                                TValue="bool?"
                                                                Change=@SaveRamsComponent/> @Localizer["RamsCalcInFmecaTxt"]
                                                            <br/><br/>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-4">
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label=@Localizer["RamsPrevCostTxt"]
                                                                                 @bind-Value=@SelectedGroup.PreventiveCost
                                                                                 Change=@SaveRamsComponent
                                                                                 Disabled=@(SelectedGroup.CostOwner != true)/>
                                                        </div>
                                                        <div class="col-sm-8" style="background-color:lightgrey">
                                                            @Localizer["RamsCorrectiveCostTxt"]
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label=@Localizer["RamsTechnicalTxt"]
                                                                                 @bind-Value=@SelectedGroup.TechnCorrCost
                                                                                 Change=@SaveRamsComponent
                                                                                 Disabled=@(SelectedGroup.CostOwner != true)/>
                                                            <br/>
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label=@Localizer["RamsNotEffByCircuitTxt"]
                                                                                 @bind-Value=@SelectedGroup.CircuitDepCorrCost
                                                                                 Change=@SaveRamsComponent
                                                                                 Disabled=@(SelectedGroup.CostOwner != true)/>
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label=@Localizer["RamsEffByCircuitTxt"]
                                                                                 @bind-Value=@SelectedGroup.CircuitDepCorrCost
                                                                                 Change=@SaveRamsComponent
                                                                                 Disabled=@(SelectedGroup.CostOwner != true)/>
                                                        </div>
                                                    </div>
                                                    <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                         Label=@Localizer["RamsTotalCostTxt"]
                                                                         @bind-Value=@SelectedGroup.TotalCost
                                                                         Change=@SaveRamsComponent
                                                                         Disabled=@(SelectedGroup.CostOwner != true)/>
                                                </RadzenTabsItem>
                                                <RadzenTabsItem Text=@Localizer["RamsABSTxt"]>
                                                    <div class="form-group">
                                                        <div class="neg-margin-small">
                                                            <label>Asset:</label>
                                                        </div>
                                                        <div class="neg-margin-small">
                                                            <RadzenDropDown TValue="int?" TextProperty="Value"
                                                                            ValueProperty="Key" Data=@AssetDict
                                                                            @bind-Value=@SelectedGroup.SiId
                                                                            Change=@SaveRamsComponent
                                                                            AllowClear="true" Class="form-control"/>
                                                        </div>
                                                    </div>
                                                    <AMproverNumberInput Label="Year" TValue="int?" Placeholder="Year"
                                                                         @bind-Value=@SelectedGroup.Year
                                                                         Change=@SaveRamsComponent/>
                                                </RadzenTabsItem>
                                                <RadzenTabsItem Text=@Localizer["RamsHierObjectTxt"]>
                                                    <div class="form-group">
                                                        <div class="neg-margin-small">
                                                            <label>@Localizer["RamsHierObjectTxt"]</label>
                                                        </div>
                                                        <div class="neg-margin-small">
                                                            @if (SelectedGroup.ObjectId.HasValue)
                                                            {
                                                                <TreeComponent TItem=RiskTreeObject
                                                                               Treeview=RiskTree
                                                                               NodeClickCallback=ClickRiskTreeNode/>
                                                                }
                                                        </div>
                                                    </div>
                                                </RadzenTabsItem>
                                            </Tabs>
                                        </RadzenTabs>
                                    </div>
                                }
                                else
                                {
                                    <p>@((MarkupString) Localizer["RamsNoGroupSelectedTxt"].Value)</p>
                                }
                            </RadzenTabsItem>
                            <RadzenTabsItem Text="Block" @ref=@EditorTabBlock
                                            Disabled=@DisableBlockTab>
                                @if (SelectedNode != null)
                                {
                                    <div class="col-sm-12">
                                        <div class="row">
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Label="Id" @bind-Value=@SelectedNode.Id
                                                                     Disabled="true"/>
                                            </div>
                                            <div class="col-sm-9">
                                                <AMproverTextBox Label=@Localizer["RamsNameTxt"]
                                                                 @bind-Value=@SelectedNode.Name Placeholder="Name"
                                                                 Change=@SaveRamsComponent MaxLength="40"
                                                                 Disabled=@(SelectedNode.DiagramRefId != null)/>
                                            </div>
                                        </div>
                                        <AMproverTextArea Label=@Localizer["RamsDescrTxt"] Placeholder="Description"
                                                          @bind-Value=@SelectedNode.Descr
                                                          Change=@SaveRamsComponent
                                                          Disabled=@(SelectedNode.DiagramRefId != null)/>

                                        <div class="form-group">
                                            <div class="neg-margin-small">
                                                <label>Status:</label>
                                            </div>
                                            <div class="neg-margin-small">
                                                <RadzenDropDown TValue="int?" TextProperty="Value" ValueProperty="Key"
                                                                Data=@BlockStatusDict @bind-Value=@SelectedNode.Status
                                                                Change=@(() => SaveRamsComponent(true))
                                                                AllowClear="true" Class="form-control"
                                                                Disabled=@(SelectedNode.DiagramRefId != null)/>
                                            </div>
                                        </div>

                                        <RadzenTabs @ref=@BlockContentTab>
                                            <Tabs>
                                                <RadzenTabsItem Text="Data">
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <strong>@Localizer["RamsTechnicalTxt"]:</strong>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <strong>@Localizer["RamsFunctionalTxt"]:</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double?" Format="F2"
                                                                                 Label=@MtbfLabel
                                                                                 @bind-Value=@SelectedNode.Mtbftechn
                                                                                 Multiplier=@(DisplayMode.ShowYears ? 1 : CurrentDiagram?.AvailableTime ?? 1)
                                                                                 Change=@(() => SaveRamsComponent(true))
                                                                                 Disabled=@(DisableBlockEditing || SelectedNode.DiagramRefId != null || SelectedNode.LinkType == 4)/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double?" Format="F2"
                                                                                 Label=@MtbfLabel
                                                                                 @bind-Value=@SelectedNode.Mtbffunct
                                                                                 Multiplier=@(DisplayMode.ShowYears ? 1 : CurrentDiagram?.AvailableTime ?? 1)
                                                                                 Change=@(() => SaveRamsComponent(true))
                                                                                 Disabled=@(DisableBlockEditing || SelectedNode.DiagramRefId != null || SelectedNode.LinkType == 4)/>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverTextBox Label=@Localizer["RamsAvailabilityTxt"]
                                                                             Value=@SelectedNode.AvailabilityOutput?.FormatRoundedValue(Language, 2)
                                                                             Disabled="true"/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <AMproverTextBox Label=@Localizer["RamsAvailabilityTxt"]
                                                                             Value=@SelectedNode.AvailabilityInput?.FormatRoundedValue(Language, 2)
                                                                             Disabled="true"/>
                                                        </div>

                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <AMproverNumberInput TValue="double?" Format="F2"
                                                                                 Label=@(SelectedItem.Results.AffectedBufferTime > 0 ? $"MTTR (FRT) (Affected by a buffer time of {SelectedItem.Results.AffectedBufferTime})" : "MTTR (FRT) - hrs ")
                                                                                 @bind-Value=@SelectedNode.Mttr
                                                                                 Change=@(() => SaveRamsComponent(true))
                                                                                 Disabled=@(DisableBlockEditing || SelectedNode.DiagramRefId != null)/>
                                                        </div>
                                                        <br/>
                                                        <div class="col-sm-6">
                                                            <Radzen.Blazor.RadzenCheckBox
                                                                @bind-Value=@SelectedNode.Completed
                                                                TValue="bool?"
                                                                Change=@SaveRamsComponent/> @Localizer["RamsCompletedTxt"]
                                                            <br/>
                                                        </div>
                                                    </div>
                                                    <RadzenTabs>
                                                        <Tabs>
                                                            <RadzenTabsItem Text="General">
                                                                @if (SelectedNode?.Status == (int?) RamsBlockStatus.Buffer)
                                                                {
                                                                    <div class="row">
                                                                        <div class="col-sm-6">
                                                                            <AMproverNumberInput TValue="double?"
                                                                                                 Format="F2"
                                                                                                 Label=@Localizer["RamsBufferTimeTxt"]
                                                                                                 @bind-Value=@SelectedNode.BufferTime
                                                                                                 Change=@(() => SaveRamsComponent(true))/>
                                                                        </div>
                                                                    </div>
                                                                }
                                                                <AMproverTextArea Label=@Localizer["RamsRemarksTxt"]
                                                                                  Placeholder="Remarks"
                                                                                  @bind-Value=@SelectedNode.Remark
                                                                                  Change=@SaveRamsComponent>
                                                                </AMproverTextArea>
                                                            </RadzenTabsItem>
                                                            <RadzenTabsItem Text="Capacity">
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <strong>@Localizer["RamsTechnicalTxt"]:</strong>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <strong>@Localizer["RamsFunctionalTxt"]:</strong>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="double?"
                                                                                             Format="F2"
                                                                                             Label="Capacity"
                                                                                             @bind-Value=@SelectedNode.CapacityTechn
                                                                                             Change=@(() => SaveRamsComponent(true))
                                                                                             Disabled=@(DisableBlockEditing || SelectedNode.DiagramRefId != null)/>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="double?"
                                                                                             Format="F2"
                                                                                             Label="Capacity"
                                                                                             @bind-Value=@SelectedNode.CapacityFunct
                                                                                             Change=@(() => SaveRamsComponent(true))
                                                                                             Disabled=@(DisableBlockEditing || SelectedNode.DiagramRefId != null)/>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <div class="neg-margin-small">
                                                                            <label>Capacity Unit</label>
                                                                        </div>
                                                                        <div class="neg-margin-small">
                                                                            <AMDropdown TKeyType="int?" 
                                                                                Data=@UnitTypeDict
                                                                                        @bind-Value=@SelectedNode.CapacityUnit
                                                                                        Change=@(() => SaveRamsComponent(true))
                                                                                        AllowClear="true"
                                                                                        Disabled=@(DisableBlockEditing || SelectedNode.DiagramRefId != null)
                                                                                        />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="double?"
                                                                                             Format="F2"
                                                                                             Label="Capacity Factor"
                                                                                             @bind-Value=@SelectedNode.CapacityFactor
                                                                                             Change=@(() => SaveRamsComponent(true))
                                                                                             Disabled=@(DisableBlockEditing || SelectedNode.DiagramRefId != null)/>
                                                                    </div>
                                                                </div>
                                                            </RadzenTabsItem>
                                                            <RadzenTabsItem Text="SIL/PFD">
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <div class="form-group">
                                                                            <div class="neg-margin-small">
                                                                                <label>Classification:</label>
                                                                            </div>
                                                                            <div class="neg-margin-small">
                                                                                <RadzenDropDown TValue="string"
                                                                                                Data=@(new List<string> {"DD", "DU", "SD", "SU"})
                                                                                                @bind-Value=@SelectedNode.ClassDc
                                                                                                Change=@ProcessClassificationChangesAndSaveRamsComponent
                                                                                                AllowClear="true"
                                                                                                Class="form-control"
                                                                                                Disabled=@DisableBlockEditing/>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="double?"
                                                                                             Format="F2"
                                                                                             Label=@Localizer["RamsDCdTxt"]
                                                                                             @bind-Value=@SelectedNode.Dcd
                                                                                             Placeholder="@Localizer["RamsDCdTxt"]"
                                                                                             Change=@(() => SaveRamsComponent(true))
                                                                                             Disabled=@DisableBlockEditing/>
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="int?"
                                                                                             Label=@Localizer["RamsTestIntervalTxt"]
                                                                                             @bind-Value=@SelectedNode.TestInterval
                                                                                             Placeholder="@Localizer["RamsTestIntervalTxt"]"
                                                                                             Change=@(() => SaveRamsComponent(true))
                                                                                             Disabled=@DisableBlockEditing/>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <AMproverNumberInput TValue="double?"
                                                                                             Format="F2"
                                                                                             Label=@Localizer["RamsBetaTxt"]
                                                                                             @bind-Value=@SelectedNode.Beta
                                                                                             Placeholder="@Localizer["RamsBetaTxt"]"
                                                                                             Change=@(() => SaveRamsComponent(true))
                                                                                             Disabled=@DisableBlockEditing/>
                                                                    </div>
                                                                </div>
                                                            </RadzenTabsItem>
                                                        </Tabs>
                                                    </RadzenTabs>
                                                </RadzenTabsItem>
                                                <RadzenTabsItem Text="Cost">
                                                    <Radzen.Blazor.RadzenCheckBox
                                                        @bind-Value=@SelectedNode.WantLcc TValue="bool?"
                                                        Change=@SaveRamsComponent/> @Localizer["RamsLccCalcTxt"]
                                                    <br/>
                                                    <div class="row">
                                                        <div class="col-sm-6">
                                                            <Radzen.Blazor.RadzenCheckBox
                                                                @bind-Value=@SelectedNode.CostOwner
                                                                TValue="bool?"
                                                                Change=@SaveRamsComponent/> @Localizer["RamsCostOwnerTxt"]
                                                            <br/>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <Radzen.Blazor.RadzenCheckBox
                                                                @bind-Value=@SelectedNode.CostLinked
                                                                TValue="bool?"
                                                                Change=@SaveRamsComponent/> @Localizer["RamsCalcInFmecaTxt"]
                                                            <br/><br/>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-sm-4">
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label=@Localizer["RamsPrevCostTxt"]
                                                                                 @bind-Value=@SelectedNode.PreventiveCost
                                                                                 Change=@SaveRamsComponent
                                                                                 Disabled=@(SelectedNode.CostOwner != true)/>
                                                        </div>
                                                        <div class="col-sm-8" style="background-color:lightgrey">
                                                            @Localizer["RamsCorrectiveCostTxt"]
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label=@Localizer["RamsTechnicalTxt"]
                                                                                 @bind-Value=@SelectedNode.TechnCorrCost
                                                                                 Change=@SaveRamsComponent
                                                                                 Disabled=@(SelectedNode.CostOwner != true)/>
                                                            <br/>
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label=@Localizer["RamsNotEffByCircuitTxt"]
                                                                                 @bind-Value=@SelectedNode.CircuitDepCorrCost
                                                                                 Change=@SaveRamsComponent
                                                                                 Disabled=@(SelectedNode.CostOwner != true)/>
                                                            <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                                 Label=@Localizer["RamsEffByCircuitTxt"]
                                                                                 @bind-Value=@SelectedNode.CircuitDepCorrCost
                                                                                 Change=@SaveRamsComponent
                                                                                 Disabled=@(SelectedNode.CostOwner != true)/>
                                                        </div>
                                                    </div>
                                                    <AMproverNumberInput TValue="decimal?" Format="F2"
                                                                         Label=@Localizer["RamsTotalCostTxt"]
                                                                         @bind-Value=@SelectedNode.TotalCost
                                                                         Change=@SaveRamsComponent
                                                                         Disabled=@(SelectedNode.CostOwner != true)/>
                                                </RadzenTabsItem>
                                                <RadzenTabsItem Text=@Localizer["RamsABSTxt"]>
                                                    <div class="form-group">
                                                        <div class="neg-margin-small">
                                                            <label>Asset:</label>
                                                        </div>
                                                        <div class="neg-margin-small">
                                                            <RadzenDropDown TValue="int?" TextProperty="Value"
                                                                            ValueProperty="Key" Data=@AssetDict
                                                                            @bind-Value=@SelectedNode.SiId
                                                                            Change=@SaveRamsComponent
                                                                            AllowClear="true" Class="form-control"/>
                                                        </div>
                                                    </div>
                                                    <AMproverNumberInput TValue="int?" Placeholder="Year"
                                                                         @bind-Value=@SelectedNode.Year
                                                                         Change=@SaveRamsComponent/>
                                                </RadzenTabsItem>
                                                <RadzenTabsItem Text=@Localizer["RamsHierObjectTxt"]>
                                                    <div class="form-group">
                                                        <div class="neg-margin-small">
                                                            <label>@Localizer["RamsHierObjectTxt"]</label>
                                                        </div>
                                                        <div class="neg-margin-small">
                                                            @if (SelectedNode.RiskId.HasValue)
                                                            {
                                                                <div class="row">
                                                                    <div class="col-sm-6">
                                                                        <RadzenDropDown TValue="int?"
                                                                                        Data=@LinkTypeDict
                                                                                        TextProperty="Value"
                                                                                        ValueProperty="Key"
                                                                                        @bind-Value=@SelectedNode.LinkType
                                                                                        Change=@SaveRamsComponent
                                                                                        AllowClear="true"
                                                                                        Class="form-control"
                                                                                        Disabled=@DisableBlockEditing/>
                                                                    </div>
                                                                    <div class="col-sm-6">
                                                                        <RadzenDropDown TValue="int?"
                                                                                        Data=@LinkMethodDict
                                                                                        TextProperty="Value"
                                                                                        ValueProperty="Key"
                                                                                        @bind-Value=@SelectedNode.LinkMethod
                                                                                        Change=@SaveRamsComponent
                                                                                        AllowClear="true"
                                                                                        Class="form-control"
                                                                                        Disabled=@DisableBlockEditing/>
                                                                    </div>
                                                                </div>
                                                                <TreeComponent TItem=RiskTreeObject
                                                                               Treeview=RiskTree
                                                                               NodeClickCallback=ClickRiskTreeNode/>
                                                                }
                                                        </div>
                                                    </div>
                                                </RadzenTabsItem>
                                            </Tabs>
                                        </RadzenTabs>
                                    </div>
                                }
                                else
                                {
                                    <p>@((MarkupString) Localizer["RamsNoBlockSelectedTxt"].Value)</p>
                                }
                            </RadzenTabsItem>
                        </Tabs>
                    </RadzenTabs>
                </div>
            </div>
        </div>
    }

    <!-- Right Panel - Rams diagram editor -->
    <div
        class="@(!ShowHidePanelMode ? "col-md-9" : "col-md-12") diagram-container-width@(!ShowHidePanelMode ? "" : "-full")">
        <!-- menu bar -->
        @if (CurrentDiagram != null)
        {
            <div class="row">
                <div class="col-6">
                    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Start" Gap="0.4rem"
                                 Wrap="FlexWrap.Wrap" class="btn-bar-left">

                        <RadzenSplitButton Text="Add block" Size=ButtonSize.Small ButtonStyle=ButtonStyle.Secondary
                                           Click=@(args => AddBlock(args))
                                           Disabled=@DisableAddButton>
                            <ChildContent>
                                <RadzenSplitButtonItem Text="Before" Value="0" Icon="navigate_before"/>
                                <RadzenSplitButtonItem Text="After" Value="1" Icon="navigate_next"/>
                            </ChildContent>
                        </RadzenSplitButton>

                        <RadzenButton Icon="delete" Text="Remove selected" Click=@RemoveComponent
                                      Size=ButtonSize.Small ButtonStyle=ButtonStyle.Danger
                                      Visible="SelectedItem != null"
                                      Disabled="SelectedItem == null || !AppState.CanEdit"/>

                        <RadzenButton Icon="content_copy" Text="Copy" Click=@CopyComponent
                                      Size=ButtonSize.Small ButtonStyle=ButtonStyle.Secondary
                                      Visible="SelectedItem != null"
                                      Disabled="SelectedItem == null || !AppState.CanEdit"/>

                        <RadzenButton Icon="content_paste" Text="Paste" Click=@PasteComponent
                                      Size=ButtonSize.Small ButtonStyle=ButtonStyle.Secondary
                                      Visible="CopiedItem != null"
                                      Disabled="CopiedItem == null || !AppState.CanEdit"/>

                        <RadzenSplitButton @ref=@DiagramButton Text="Nest diagram"
                                           Size=ButtonSize.Small ButtonStyle=ButtonStyle.Secondary
                                           Click=@(args => AddDiagram(args))
                                           Visible="(CurrentDiagram != null && Diagrams.Any(x => x.Id != CurrentDiagram.Id))"
                                           Disabled="DisableAddButton || CurrentDiagram == null || !Diagrams.Any(x => x.Id != CurrentDiagram.Id)">
                            <ChildContent>
                                @foreach (var diagram in Diagrams.Where(x => x.Id > 0 && x.Id != CurrentDiagram?.Id))
                                {
                                    <RadzenSplitButtonItem Text=@diagram.Name Value=@diagram.Id.ToString()/>
                                }
                            </ChildContent>
                        </RadzenSplitButton>

                        <RadzenSplitButton Text="Add page break" Size=ButtonSize.Small ButtonStyle=ButtonStyle.Info
                                           Click=@(args => AddPageBreakHandler(args))
                                           Disabled=@DisableAddButton>
                            <ChildContent>
                                <RadzenSplitButtonItem Text="Page Break" Icon="cut" Value="default"/>
                                <RadzenSplitButtonItem Text="Page Break Before Selected" Icon="cut"
                                                       Value="before" Disabled=@(SelectedItem == null)/>
                                <RadzenSplitButtonItem Text="Page Break After Selected" Icon="cut"
                                                       Value="after" Disabled=@(SelectedItem == null)/>
                            </ChildContent>
                        </RadzenSplitButton>

                        <RadzenButton Icon="undo" Text="Undo" Click=@UndoChanges Size=ButtonSize.Small
                                      ButtonStyle=ButtonStyle.Secondary
                                      Visible="UndoList.Count > 0"
                                      Disabled="UndoList.Count == 0 || !AppState.CanEdit "/>

                    </RadzenStack>
                </div>
                <div class="col-6">
                    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.End" Gap="0.4rem"
                                 Wrap="FlexWrap.Wrap" class="btn-bar-right mb-2">

                        <RadzenButton Icon="camera_enhance" Click=DownloadImage Size=ButtonSize.Small
                                      ButtonStyle=ButtonStyle.Secondary
                                      @ref=CameraButton
                                      @onmouseover="@(() => ShowTooltip(CameraButton.Element, Localizer["RamsDownloadDiagramBtnTxt"]))"
                                      @onmouseleave="@(() => HideTooltip(CameraButton.Element))"/>
                        <RadzenButton Icon="chrome_reader_mode" Click=HidePanelMode Size=ButtonSize.Small
                                      ButtonStyle=ButtonStyle.Secondary Text="Panel"
                                      @ref=PanelHideButton
                                      @onmouseover="@(() => ShowTooltip(PanelHideButton.Element, Localizer["RamsPanelHideBtnTxt"]))"
                                      @onmouseleave="@(() => HideTooltip(PanelHideButton.Element))"/>
                        <RadzenButton Icon="settings_applications" Click=SwitchDisplayMode Size=ButtonSize.Small
                                      ButtonStyle=ButtonStyle.Secondary
                                      Text="Display"
                                      @ref=DisplayModeButton
                                      @onmouseover="@(() => ShowTooltip(DisplayModeButton.Element, Localizer["RamsDisplayModeBtnTxt"]))"
                                      @onmouseleave="@(() => HideTooltip(DisplayModeButton.Element))"/>
                        <RadzenButton Icon="developer_mode" Click=SwitchMode Size=ButtonSize.Small
                                      ButtonStyle=ButtonStyle.Secondary Text="View"
                                      @ref=DatagridViewButton
                                      @onmouseover="@(() => ShowTooltip(DatagridViewButton.Element, ShowDiagram ? Localizer["RamsModeBtnTxt"] : Localizer["RamsModeBtnTxtDefault"]))"
                                      @onmouseleave="@(() => HideTooltip(DatagridViewButton.Element))"/>
                        <RadzenButton Icon="save" Click=SaveRamsDiagram Size=ButtonSize.Small
                                      ButtonStyle=ButtonStyle.Primary Text="Save"
                                      Disabled=!AppState.CanEdit
                                      @ref=SaveButton
                                      @onmouseover="@(() => ShowTooltip(SaveButton.Element, Localizer["RamsSaveBtnTxt"]))"
                                      @onmouseleave="@(() => HideTooltip(SaveButton.Element))"/>
                    </RadzenStack>
                </div>
            </div>
        }

        @if (ShowDisplayMode)
        {
            <div class="row" style="border:1px solid #e9ecef; padding: 10px; margin-bottom: 10px;margin-top: 10px">
                <div class="col-md-2">
                    <RadzenRadioButtonList Orientation="Orientation.Vertical" @bind-Value=@ShownValues
                                           TValue="int" Change=@UpdateDisplayMode>
                        <Items>
                            <RadzenRadioButtonListItem Text=@Localizer["RamsTechValTxt"] Value="1"/>
                            <RadzenRadioButtonListItem Text=@Localizer["RamsFunctValTxt"] Value="2"/>
                        </Items>
                    </RadzenRadioButtonList>
                </div>
                <div class="col-md-2">
                    <RadzenRadioButtonList Orientation="Orientation.Vertical" @bind-Value=@ShownNotation
                                           TValue="int" Change=@UpdateDisplayMode>
                        <Items>
                            <RadzenRadioButtonListItem Text="Decimal notation" Value="1"/>
                            <RadzenRadioButtonListItem Text="Scientific notation" Value="2"/>
                        </Items>
                    </RadzenRadioButtonList>
                </div>
                <div class="col-md-2">
                    <RadzenRadioButtonList Orientation="Orientation.Vertical" @bind-Value=@ShownFunctions
                                           TValue="int" Change=@UpdateDisplayMode>
                        <Items>
                            <RadzenRadioButtonListItem Text="Show MTBF" Value="1"/>
                            <RadzenRadioButtonListItem Text="Show Lambda" Value="2"/>
                        </Items>
                    </RadzenRadioButtonList>
                </div>
                <div class="col-md-2">
                    <RadzenRadioButtonList Orientation="Orientation.Vertical" @bind-Value=@ShownCalculation
                                           TValue="int" Change=@UpdateDisplayMode>
                        <Items>
                            <RadzenRadioButtonListItem Text="Show PFD" Value="1"/>
                            <RadzenRadioButtonListItem Text="Show SIL" Value="2"/>
                            <RadzenRadioButtonListItem Text="Show None" Value="4"/>
                        </Items>
                    </RadzenRadioButtonList>
                </div>

                <div class="col-md-2">
                    <RadzenRadioButtonList Orientation="Orientation.Vertical" @bind-Value=@ShownReliability
                                           TValue="int" Change=@UpdateDisplayMode>
                        <Items>
                            <RadzenRadioButtonListItem Text="Hours" Value="1"/>
                            <RadzenRadioButtonListItem Text="Years" Value="2"/>
                        </Items>
                    </RadzenRadioButtonList>
                </div>
            </div>
        }

        <!-- diagram -->
        <div class="row @(ShowDiagram ? "diagram-border" : "")">
            @if (ShowDiagram)
            {
                <div class="col-12">
                    <RamsDiagramComponent @ref=@DiagramComponent
                                          AvailableTime=@(CurrentDiagram?.AvailableTime ?? 0)
                                          Language=@Language
                                          Title=@CurrentDiagram?.Name
                                          Diagram=@DiagramContent
                                          DisplayMode=@DisplayMode
                                          ComponentSelectCallback=@(x => SelectComponent(x))
                                          ComponentReOrderCallback=@(response => SaveAndReorderRamsDiagram(response))
                                          ComponentCollapseCallback=@CollapseContainer
                                          ComponentParallelCallback=@SaveRamsDiagram
                                          AddItemsToNewContainerCallback=@AddItemsToNewContainer
                                          DrawLines=@DrawConnectionLines
                                          RemoveLines=@RemoveLines
                                          RecalculateDiagramSize=@RecalculateDiagramSize
                                          RamsAllowedDept=@RamsAllowedDeptSetting?.IntValue
                                          SelectedComponentId=@SelectedComponent>
                    </RamsDiagramComponent>
                </div>
            }
            else if (!ShowDiagram)
            {
                <div class="col-12">
                    <UtilityGrid TItem=RamsModel
                                 @ref=_ramsGrid
                                 Data=@RamsComponents.ToList()
                                 FileName=@GridNames.Rams.RamsComponents
                                 SaveCallback=@SaveRamsModel
                                 FileUploadCallBack=@ProcessUpload
                                 Interactive=true
                                 AllowXlsExport=true
                                 AllowFiltering=true />
                </div>
            }
            else
            {
                <div class="col-12">
                    <p class="selection-border text-center">@((MarkupString) Localizer["RamsSelectDiagramTxt"].Value)</p>
                </div>
            }
        </div>
    </div>
</div>
