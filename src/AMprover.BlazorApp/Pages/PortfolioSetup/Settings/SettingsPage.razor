@page "/settings"
@using AMprover.Data.Constants

<h2>@Localizer["SeHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Settings"/>
    </div>
</div>

<p>@((MarkupString) Localizer["SeSubHeaderTxt"].Value)</p>

@if (IsLoaded)
{
    <RadzenTabs>
        <Tabs>
            <!-- Localization -->
            <RadzenTabsItem Text=@Localizer["SeLocalizationTabTxt"]>
                <div class="row">
                    <div class="col-md-4 col-6">
                        <AMDropdown
                            @bind-Value=@LanguageSetting.TextValue
                            Data=@Languages
                            Change=@(args => UpdateSettings(LanguageSetting))
                            Label=@Localizer["SeLanguageTxt"]/>

                        <AMDropdown
                            @bind-Value=@CurrencySetting.TextValue
                            Data=@Currencies
                            Change=@(args => UpdateSettings(CurrencySetting))
                            Label=@Localizer["SeCurrencyTxt"]/>
                    </div>
                </div>
            </RadzenTabsItem>

            <!-- Calculation -->
            <RadzenTabsItem Text=@Localizer["SeCalculationTabTxt"]>
                @foreach (var setting in CalculationSettings)
                {
                    <div class="row">
                        <div class="col-md-4 col-6">
                            @switch (setting.Value.SettingType)
                            {
                                case SettingType.Decimal:
                                    <AMproverNumberInput @bind-Value=@setting.Value.DecimalValue
                                                         Change=@(args => UpdateSettings(setting.Value))
                                                         TValue="decimal?"
                                                         Format="P2"
                                                         Min="0"
                                                         Label=@setting.Key/>
                                    break;
                                case SettingType.Int:
                                    <AMproverNumberInput @bind-Value=@setting.Value.IntValue
                                                         Change=@(() => UpdateSettings(setting.Value))
                                                         TValue="int?"
                                                         Format="N2"
                                                         Min="0"
                                                         Label=@setting.Key/>
                                    break;
                            }
                        </div>
                    </div>
                }
            </RadzenTabsItem>

            <!-- SETTINGS -->
            <AuthorizeView Context="_" Roles="@RoleConstants.Administrators">
                <Authorized>
                    <RadzenTabsItem Text=@Localizer["SeConfigurationTabTxt"]>
                        <AMproverNumberInput @bind-Value=@RamsAllowedDeptSetting.IntValue Label=@Localizer["SeRamsAllowedDeptTxt"] Placeholder=@Localizer["SeRamsAllowedDeptTxt"]
                                             TValue="int?"
                                             Change=@(() => UpdateSettings(RamsAllowedDeptSetting))/>

                        <AMproverNumberInput @bind-Value=@RamsTotalAllowedBlocksSetting.IntValue Label=@Localizer["SeRamsTotalAllowedBlocksTxt"] Placeholder=@Localizer["SeRamsTotalAllowedBlocksTxt"]
                                             TValue="int?"
                                             Change=@(() => UpdateSettings(RamsTotalAllowedBlocksSetting))/>


                        <Radzen.Blazor.RadzenCheckBox
                            Value="@(EnableColumnsForTypes.IntValue == 1)"
                            TValue="bool?"
                            Change="@(args => OnCheckboxChange(args, EnableColumnsForTypes))"/>

                        &nbsp; @Localizer["SeEnableColumnForTypeTxt"]

                        <br/><br/>

                        <Radzen.Blazor.RadzenCheckBox
                            Value="@(ShowSubColumns.IntValue == 1)"
                            TValue="bool?"
                            Change="@(args => OnCheckboxChange(args, ShowSubColumns))"/>

                        &nbsp; @Localizer["SeRiskMatrixShowSubColumnsTxt"]

                        <br/><br/>

                        <Radzen.Blazor.RadzenCheckBox
                            Value="@(ShowRegenerateImages.IntValue == 1)"
                            TValue="bool?"
                            Change="@(args => OnCheckboxChange(args, ShowRegenerateImages))"/>

                        &nbsp; @Localizer["SeShowRegenerateImagesTxt"]

                    </RadzenTabsItem>
                </Authorized>
            </AuthorizeView>
        </Tabs>
    </RadzenTabs>
}