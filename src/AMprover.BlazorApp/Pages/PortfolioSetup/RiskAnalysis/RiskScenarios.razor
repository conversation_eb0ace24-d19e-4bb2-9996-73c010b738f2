@page "/scenarios"
@inject IStringLocalizer<RiskScenarios> Localizer
@inject IScenarioManager ScenarioManager
@inject IDropdownManager DropdownManager

<h2>@Localizer["ScHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Scenarios" />
    </div>
</div>

<p>@((MarkupString)Localizer["ScSubHeaderTxt"].Value)</p>

@if (ShowWarning)
{
    <div class="alert alert-warning" role="alert">
        @WarningText
    </div>
}

<UtilityGrid TItem=ScenarioModel
              @ref=ScenarioCrudGrid
              Data=@Scenarios
              FileName=@GridNames.Portfolio.Scenarios
              Interactive=true
              AddNewButton=true
              AllowFiltering=true
              SaveCallback=@SaveScenario
              CloneCallBack=@CloneScenario
              DeleteCallback=@DeleteRiskScenario
              DropDownOverrides=@GetDropdownOverrides() />
