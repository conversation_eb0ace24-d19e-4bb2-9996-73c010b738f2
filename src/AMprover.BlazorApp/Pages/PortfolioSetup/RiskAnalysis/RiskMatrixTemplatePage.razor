@page "/value-risk-matrix/{MatrixId:int}"

<h2>@Localizer["RmtHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col-6">
        <Breadcrumbs Category="Value Risk Matrix" Section=@MatrixId.ToString()/>
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@Localizer["RmtHeaderTxt"] DialogContent=@Localizer["RmtMenuTxt"]/>
        <RadzenButton Disabled=!AppState.CanEdit Text=@Localizer["RmtCopyBtn"] Icon="edit" ButtonStyle=ButtonStyle.Secondary class="my-2 mx-1" Click=@CopyTemplate />
        <RadzenButton Disabled=!AppState.CanEdit Text=@Localizer["RmtSaveBtn"] Icon="save" class="my-2" Click=@ValidFormSubmitted ButtonStyle=ButtonStyle.Secondary />
    </div>
</div>

<p>@((MarkupString)Localizer["RmtSubHeaderTxt"].Value)</p>

@if (RiskMatrixTemplate != null)
{
    <EditForm Model="@RiskMatrixTemplate">
        <DataAnnotationsValidator/>
        <div class="row">
            <div class="col-sm-2">
                <div class="form-group neg-margin-small">
                    <label>ID:</label>
                    <label class="form-control neg-margin-small">@RiskMatrixTemplate.Id</label>
                </div>
            </div>
            <div class="col-sm-3">
                <AMproverTextBox @bind-Value="RiskMatrixTemplate.ShortName" MaxLength="6" Label=@Localizer["RmtShortNameLbl"]/>
            </div>
            <div class="col-sm-7">
                <AMproverTextBox @bind-Value="RiskMatrixTemplate.Name" MaxLength="30" Label=@Localizer["RmtFmecaNameLbl"]/>
            </div>
        </div>
        <br/>
        <div class="row">
            <div class="col-sm-2">
                <Radzen.Blazor.RadzenCheckBox @bind-Value=@RiskMatrixTemplate.IsDefault/> @Localizer["RmtDefaultLbl"] <br/><br/>
            </div>
        </div>

        <AMproverTextBox @bind-Value="RiskMatrixTemplate.Description" MaxLength="50" Label=@Localizer["RmtDescriptionLbl"] />

        @if (ShowError)
        {
            <div class="alert alert-danger" role="alert">
                @ErrorText
            </div>
        }

    </EditForm>
    <br/>
    <RadzenTabs @ref=@TemplateTabs>
        <Tabs>
            @for (var tabIndex = 0; tabIndex < Tabs.Count; tabIndex++)
            {
                var tab = Tabs[tabIndex];
                <RadzenTabsItem Text="@tab">
                    <table class="table table-bordered icon-hover" style="table-layout: fixed">
                        <tbody>
                        @for (var index = 0; index < RiskMatrixTemplate.MainGrid.TableContent.Count; index++)
                        {
                            var tempIndex = index;
                            var row = RiskMatrixTemplate.MainGrid.TableContent[index];
                            <tr style="height:50px">
                                @for (var cellIndex = 0; cellIndex < row.Cells.Count; cellIndex++)
                                {
                                    var tempCellIndex = cellIndex;
                                    var cell = row.Cells[cellIndex];
                                    var column = RiskMatrixTemplate.MainGrid.TableColumns[cellIndex];
                                    <td id="@cell.UniqueId(tab)" style="@cell.BackGround(); position: relative; width: 50px;">
                                        @(index == 0 ? cell.GetHeader(tab) : column.HasSubColumns ?
                                            cell.GetConcatenatedSubGridValue(RiskMatrixTemplate, tempIndex, tempCellIndex, tab, Language) :
                                            cell.GetValue(RiskMatrixTemplate, tab, tempCellIndex))

                                        @if (index == 0 && CanEdit)
                                        {
                                            <a style="position:absolute;right: -6px;top: -12px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => AddColumn(tempCellIndex))">
                                                <i class="fas fa-plus-circle"></i>
                                            </a>

                                            <a style="position:absolute;left:50%;top: -12px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => RemoveColumn(tempCellIndex))">
                                                <i class="fas fa-trash-alt"></i>
                                            </a>
                                        }

                                        @if (tempCellIndex == 0 && tempIndex != 0 && CanEdit)
                                        {
                                            <a style="position:absolute;left: -6px;bottom: -12px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => AddRow(tempIndex))">
                                                <i class="fas fa-plus-circle"></i>
                                            </a>

                                            <a style="position:absolute;left: -6px;vertical-align: middle;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => RemoveRow(tempIndex))">
                                                <i class="fas fa-trash-alt"></i>
                                            </a>
                                        }

                                        @if (tempIndex == 0 && column.HasSubColumns)
                                        {
                                            if (ActivatedSubGrids.Contains(tempCellIndex))
                                            {
                                                <a style="position:absolute;left:10px;bottom: 1px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => RemoveActiveSubgrid(tempCellIndex))">
                                                    <i class="fa-solid fa-arrow-circle-left"></i>
                                                </a>
                                            }
                                            else
                                            {
                                                <a style="position:absolute;left:10px;bottom: 1px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => AddActiveSubgrid(tempCellIndex))">
                                                    <i class="fa-solid fa-arrow-circle-right"></i>
                                                </a>
                                            }

                                            <a style="position:absolute;left:30%;top: -12px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => AddSubColumn(tempCellIndex))">
                                                <i class="fas fa-plus-circle"></i>
                                            </a>
                                        }

                                        <a style="position:absolute;right: 10px;bottom: 1px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => DialogService.Open<RiskMatrixTemplateCellComponent>("", new Dictionary<string, object> {{nameof(RiskMatrixTemplateCellComponent.MatrixId), MatrixId}, {nameof(RiskMatrixTemplateCellComponent.Row), tempIndex}, {nameof(RiskMatrixTemplateCellComponent.Column), tempCellIndex}, {nameof(RiskMatrixTemplateCellComponent.CallBack), OnInitialized}}, new DialogOptions {Width = "700px", Height = "500px"}))">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>

                                    @if (column.HasSubColumns && ActivatedSubGrids.Contains(tempCellIndex))
                                    {
                                        var subgrid = RiskMatrixTemplate.SubGrids.FirstOrDefault(x => x.ColumnId == tempCellIndex);

                                        if (subgrid == null) continue;

                                        for (var subgridColumnIndex = 0; subgridColumnIndex < subgrid.TableColumns.Count; subgridColumnIndex++)
                                        {
                                            var tempSubgridColumIndex = subgridColumnIndex;
                                            if (tempIndex < 0 || tempIndex >= subgrid.TableContent.Count)
                                                continue;

                                            var subGridRow = subgrid.TableContent[tempIndex];
                                            if (subGridRow == null)
                                                continue;

                                            if (tempSubgridColumIndex < 0 || tempSubgridColumIndex >= subGridRow.Cells.Count)
                                                continue;

                                            var subGridCell = subGridRow.Cells[tempSubgridColumIndex];
                                            if (subGridCell == null)
                                                continue;

                                            if (tempIndex == 0)
                                            {
                                                <td class="risk-matrix-subgrid-cell" id="@subGridCell.UniqueId(tab, true)" style="@subGridCell.BackGround();position: relative; width: 50px;">
                                                    @subGridCell.GetHeader(tab)

                                                    @if (CanEdit)
                                                    {
                                                        <a style="position:absolute;left:50%;top: -12px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => RemoveSubColumn(tempCellIndex, tempSubgridColumIndex))">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </a>
                                                    }

                                                    <a style="position:absolute;right: 10px;bottom: 1px;cursor:pointer;z-index:100" class="icon"
                                                       @onclick="@(_ => DialogService.Open<RiskMatrixTemplateSubCellComponent>("", new Dictionary<string, object> {{nameof(RiskMatrixTemplateSubCellComponent.MatrixId), MatrixId}, {nameof(RiskMatrixTemplateSubCellComponent.Row), tempIndex}, {nameof(RiskMatrixTemplateSubCellComponent.Column), tempCellIndex}, {nameof(RiskMatrixTemplateSubCellComponent.SubRow), tempIndex}, {nameof(RiskMatrixTemplateSubCellComponent.SubColumn), tempSubgridColumIndex}, {nameof(RiskMatrixTemplateSubCellComponent.CallBack), OnInitialized}}, new DialogOptions {Width = "700px", Height = "490px"}))">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </td>
                                            }
                                            else
                                            {
                                                <td class="risk-matrix-subgrid-cell" id="@subGridCell.UniqueId(tab, true)" style="@subGridCell.BackGround();position: relative; width: 50px;">
                                                        @subGridCell.GetValue(RiskMatrixTemplate, tab, tempCellIndex)

                                                    <a style="position:absolute;right: 10px;bottom: 1px;cursor:pointer;z-index:100" class="icon"
                                                       @onclick="@(_ => DialogService.Open<RiskMatrixTemplateSubCellComponent>("", new Dictionary<string, object> {{nameof(RiskMatrixTemplateSubCellComponent.MatrixId), MatrixId}, {nameof(RiskMatrixTemplateSubCellComponent.Row), tempIndex}, {nameof(RiskMatrixTemplateSubCellComponent.Column), tempCellIndex}, {nameof(RiskMatrixTemplateSubCellComponent.SubRow), tempIndex}, {nameof(RiskMatrixTemplateSubCellComponent.SubColumn), tempSubgridColumIndex}, {nameof(RiskMatrixTemplateSubCellComponent.CallBack), OnInitialized}}, new DialogOptions {Width = "700px", Height = "490px"}))">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </td>
                                            }
                                        }
                                    }
                                }
                            </tr>
                        }
                        </tbody>
                    </table>
                </RadzenTabsItem>
            }
        </Tabs>
    </RadzenTabs>
}
