@page "/cluster-cost-settings"
@inject IStringLocalizer<ClusterCostPage> _localizer

<h2>@_localizer["CcpHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Cluster Cost Settings" />
    </div>
</div>

<p>@((MarkupString)_localizer["CcpSubHeaderTxt"].Value)</p>

<RadzenTabs>
    <Tabs>

    @foreach (var type in CostType)
    {
        <RadzenTabsItem Text=@GetClusterCostType(type.Key)>
            <UtilityGrid TItem=CommonCostModel
                    @ref=CommonCostsGrid
                    Data=@CommonCosts.Where(x => x.Type == type.Key.ToString()).ToList()
                    FileName=@GridNames.Portfolio.CommonCosts 
                    Interactive=true
                    AllowXlsExport=true
                    AllowFiltering=true
                    AddNewButton=true 
                    DeleteCallback=@DeleteCommonCost
                    SaveCallback=@UpdateCommonCost
                    PasteCallBack=@(args => PasteCommonCostCallback(args.Item1))
                    NewItemTemplate=@(new CommonCostModel { Type = type.Key.ToString(), CalculationType = CommonCostCalculationType.PxN.ToString() })/>
        </RadzenTabsItem>
    }

    </Tabs>
</RadzenTabs>
