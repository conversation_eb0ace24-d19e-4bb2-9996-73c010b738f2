@page "/common-actions"
@using AMprover.BusinessLogic.Models.Import

<h2>@Localizer["CaHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Common actions" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@Localizer["CaHeaderTxt"] DialogContent=@Localizer["CaMenuTxt"] />
    </div>
</div>

<p>@((MarkupString)Localizer["CaSubHeaderTxt"].Value)</p>

<RadzenTabs @ref=@Tabs>
    <Tabs>

        <RadzenTabsItem Text=@Localizer["CaHeaderTxt"]>
            <UtilityGrid TItem=CommonTaskModel
                         @ref=@CommonActionsGrid
                         Data=@CommonActions
                         FileName=@GridNames.Portfolio.CommonActions
                         Interactive=true
                         AllowFiltering=true
                         AddRowCallBack=@(args => OpenCommonActionPopUp(0))
                         OpenPopup=@(args => OpenCommonActionPopUp(args.Id))
                         DeleteCallback=@DeleteCommonAction
                         SaveCallback=@UpdateCommonAction
                         PasteCallBack=@(args => PasteCommonTaskCallback(args.Item1))
                         DropDownOverrides=@GetCommonActionDropdownOverrides()
                         OptionOverrides=@GetCommonActionOptionOverrides() />
        </RadzenTabsItem>

        <RadzenTabsItem Text=@Localizer["CaImportExportTabHeader"]>

            <RadzenButton Text="Import" Click=@OpenImportWidget />

            <UtilityGrid TItem=CommonActionImportModel
                         Data=@CommonActionsImport
                         FileName=@GridNames.Portfolio.CommonActionsImport
                         AllowXlsExport=true
                         Interactive=false />
        </RadzenTabsItem>
    </Tabs>
</RadzenTabs>
