@page "/administration"
@using AMprover.Data.Constants;

<h3>Administration</h3>
<AuthorizeView Roles="@RoleConstants.Administrators">
    <Authorized>

        <NavLink class="nav-link" href="/administration/users">
            <span class="oi oi-plus" aria-hidden="true"></span> User management
        </NavLink>

        <NavLink class="nav-link" href="/administration/databases">
            <span class="oi oi-plus" aria-hidden="true"></span> Database Management
        </NavLink>

    </Authorized>
    <NotAuthorized>
        <p>You're not loggged in or not member of the needed role.</p>
    </NotAuthorized>
</AuthorizeView>
