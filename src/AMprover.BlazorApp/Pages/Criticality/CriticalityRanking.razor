@page "/criticality-ranking"

<h2>@Localizer["CrHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col-6">
        <Breadcrumbs Category="Criticality Ranking" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right btn-centered my-2 mx-2" DialogTitle=@Localizer["CrMenuTitle"] DialogContent=@Localizer["CrMenuTxt"] />

        <RadzenButton Text=@Localizer["CrUpdateAssetsBtn"]
                      ButtonStyle="ButtonStyle.Primary"
                      Icon="update" class="float-right my-2"
                      Click=@ImportAssets
                      Disabled=@Loading />

        <RadzenButton Text=@Localizer["CrRecalcBtn"]
                      ButtonStyle="ButtonStyle.Secondary"
                      Icon="refresh" class="float-right my-2 mx-2"
                      Click=@RecalculateAllCriticalities
                      Disabled=@Loading />

        <RadzenButton Text=@Localizer["CrDeleteBtn"]
                      ButtonStyle="ButtonStyle.Danger"
                      Icon="delete" class="float-right my-2"
                      Click=@OpenDeleteWidget
                      Disabled=@Loading />
    </div>
</div>

<div class="row">
    <div class="col-7">
        <div class="criticality-pie-chart">
            <RadzenChart>
                <RadzenPieSeries Data="@CriticalityCategories" Title="Categories"
                                 CategoryProperty="Key" ValueProperty="Value" Fills=@PieChartColors />
            </RadzenChart>
        </div>
    </div>

    <div class="col-5">
        <div class="row">
            @if (GetCritLevel() <= 3)
            {
                <div class="col-4" />
                // Work Around to get the UpperLevel Colums to the right in case of CritLevel == 3
            }
            <div class="col-4" style="margin-top:10px;">
                @if (CritCategoryCalculation.TextValue == "ValueCalculation")
                {
                    <AMproverNumberInput Label=@GetUpperBtxt()
                                         @bind-Value=@UpperBlevel.IntValue
                                         Change=@(() => SaveCriticalitySettings(UpperBlevel))
                                         TValue="int?"
                                         Format="c0" />

                    <AMproverNumberInput Label=@GetUpperCtxt()
                                         @bind-Value=@UpperClevel.IntValue
                                         Change=@(() => SaveCriticalitySettings(UpperClevel))
                                         TValue="int?"
                                         Format="c0" />
                }
                else if (CritCategoryCalculation.TextValue is "CriticalityCalculation" or "CriticalityHighCalculation")
                {
                    <AMproverNumberInput Label=@GetUpperBtxt()
                                         @bind-Value=@UpperBlevel.IntValue
                                         Change=@(() => SaveCriticalitySettings(UpperBlevel))
                                         TValue="int?" />

                    <AMproverNumberInput Label=@GetUpperCtxt()
                                         @bind-Value=@UpperClevel.IntValue
                                         Change=@(() => SaveCriticalitySettings(UpperClevel))
                                         TValue="int?" />
                }
            </div>
            @if (GetCritLevel() >= 4)
            {
                <div class="col-4" style="margin-top:10px;">
                    @if (CritCategoryCalculation.TextValue == "ValueCalculation")
                    {
                        <AMproverNumberInput Label=@GetUpperDtxt()
                                             @bind-Value=@UpperDlevel.IntValue
                                             Change=@(() => SaveCriticalitySettings(UpperDlevel))
                                             TValue="int?"
                                             Format="c0" />
                        @if (GetCritLevel() >= 5)
                        {
                            <AMproverNumberInput Label=@GetUpperEtxt()
                                                 @bind-Value=@UpperElevel.IntValue
                                                 Change=@(() => SaveCriticalitySettings(UpperElevel))
                                                 TValue="int?"
                                                 Format="c0" />
                        }
                    }
                    else if (CritCategoryCalculation.TextValue is "CriticalityCalculation" or "CriticalityHighCalculation")
                    {
                        <AMproverNumberInput Label=@GetUpperDtxt()
                                             @bind-Value=@UpperDlevel.IntValue
                                             Change=@(() => SaveCriticalitySettings(UpperDlevel))
                                             TValue="int?" />
                        @if (GetCritLevel() >= 5)
                        {
                            <AMproverNumberInput Label=@GetUpperEtxt()
                                                 @bind-Value=@UpperElevel.IntValue
                                                 Change=@(() => SaveCriticalitySettings(UpperElevel))
                                                 TValue="int?" />
                        }
                    }
                </div>
            }

            <div class="col-4" style="margin-top:10px;">
                <AMDropdown AllowFiltering="true"
                            Data=@CategoryDropdownOptions
                            @bind-Value=@CategoryType
                            Change=@ChangeCategoryType
                            Label=@(Localizer["CrCategoryTypeTxt"]) />

                <AMDropdown AllowFiltering="true"
                            Data=@CategoryCalcDropdownOptions
                            @bind-Value=@CategoryCalculation
                            Change=@ChangeCategoryCalculation
                            Label=@(Localizer["CrCategoryCalculationTxt"]) />
            </div>
        </div>
    </div>
</div>

<RadzenTabs @ref="Tabs">
    <Tabs>
        <RadzenTabsItem>
            <UtilityGrid TItem=CriticalityRankingModelFlat
                         @ref=UtilityGrid
                         Data=CriticalityRankings
                         FileName=@GridNames.Criticality.Rankings
                         OptionOverrides=@CriticalityOptions
                         SaveCallback=@SaveCriticality
                         DropDownOverrides=@DropdownOverrides
                         OpenPopup=@(args => EditRow(args))
                         Interactive="true"
                         MaxRows="100"
                         AllowXlsExport=true
                         AllowFiltering=true
                         OnFilter=@OnFilter
                         DynamicWidth=true
                         OpenPageCallback=@(args => OpenRiskOnAbs(args.Asset.Id))
                         OpenPageTextOverride=@Localizer["CrOpenAssetInRiskOnAbs"] />
        </RadzenTabsItem>
    </Tabs>
</RadzenTabs>
