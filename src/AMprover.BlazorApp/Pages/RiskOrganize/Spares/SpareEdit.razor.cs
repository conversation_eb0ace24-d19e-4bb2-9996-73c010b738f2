using System.Threading.Tasks;
using AMprover.BlazorApp.Enums;
using AMprover.BlazorApp.Shared;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Spares;

public partial class SpareEdit
{
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private ISparePartManager SparePartManager { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private ILogger<SpareEdit> Logger { get; set; }
    [Inject] private IStringLocalizer<SpareEdit> Localizer { get; set; }
    [Inject] public AppState AppState { get; set; }

    [Parameter] public int SpareId { get; set; }
    [Parameter] public int RiskId { get; set; }
    [Parameter] public bool Pmo { get; set; }
    [Parameter] public EventCallback<SpareModel> Callback { get; set; }

    public SpareModel Spare { get; set; }
    public string ErrorText { get; set; }
    private LookupSettingModel DefaultDepreciationPct { get; set; }
    private LookupSettingModel DefaultSpareManagementPct { get; set; }
    private LookupSettingModel DefaultModificationPct { get; set; }

    private EntityEditorMode EditorMode
    {
        get
        {
            return SpareId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    protected override async Task OnInitializedAsync()
    {
        switch (EditorMode)
        {
            case EntityEditorMode.Create:
                Spare = new SpareModel {Id = SpareId, Pmo = Pmo};
                break;
            default:
                Spare = await SparePartManager.GetSpareByIdAsync(SpareId);
                break;
        }

        DefaultDepreciationPct = await LookupManager.GetLookupSettingByPropertyNameAsync("DepreciationPct");
        DefaultSpareManagementPct = await LookupManager.GetLookupSettingByPropertyNameAsync("SpareManPct");
        DefaultModificationPct = await LookupManager.GetLookupSettingByPropertyNameAsync("ModificationPct");
    }

    private async Task CalculateSpareParts()
    {
        var risk = await RiskAnalysisManager.GetRiskAsync(Spare.MrbId > 0 ? Spare.MrbId : RiskId);

        Spare.CalculateSparePartCount(risk);
        await CalculateSpareCosts();

        //Does task and spare parts calculation
        risk.CalculatePreventiveCosts(DefaultDepreciationPct.DecimalValue, DefaultSpareManagementPct.DecimalValue,
            DefaultModificationPct.DecimalValue, true);

        await RiskAnalysisManager.UpdateRiskAsync(risk);
    }

    private async Task CalculateSpareCosts()
    {
        Spare.CalculateSparePartCost();
        Spare = await SparePartManager.UpdateSpareAsync(Spare);
    }

    private bool CalculateSparePartsBtnDisabled =>
        Spare.ObjectCount == null || Spare.OrderLeadTime == null || Spare.Reliability == null;

    private async Task ValidTaskSubmitted(SpareModel spare)
    {
        await Callback.InvokeAsync(spare);
        DialogService.Close();
    }

    private void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        ErrorText = "Update NOT executed, invalid form submitted";
        Logger.LogWarning("Invalid {EditorMode} form submitted for {SpareName} with Id {SpareId}", EditorMode,
            nameof(Spare), SpareId);
        Logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }
}