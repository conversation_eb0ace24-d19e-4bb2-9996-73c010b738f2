@using AMprover.BusinessLogic.Models.Import

@inherits BaseDialogComponent
<div class="row">
    <div class="col-12">

        <div class="form-group">
            @Localizer["RoImportRiskTxt"]
        </div>

        @if(Status == ImportRiskStatus.SelectFile)
        {
            <div class="form-group">
                <AMDropdown @bind-Value=@RiskObjectId
                            Data=@RiskObjects
                            Required=true />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@GenerateObjects /> @Localizer["RoImportRiskGenerateObjLbl"] <br />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@GenerateFailureModes /> @Localizer["RoImportRiskGenerateFailModeLbl"] <br /><br />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@UseImportId /> Use Import Id
                <div class="alert alert-info mt-2" role="alert">@Localizer["RoImportRiskUseImportId"]</div>
            </div>

            <div class="form-group pb-2">
                <RadzenFileInput @ref=@UploadRef
                                 @bind-Value=@FileUpload
                                 Accept=".xls, .xlsx"
                                 TValue="string"
                                 ChooseText=@Localizer["RoImportRiskSelectFile"]
                                 Change=@ChangeFile />
            </div>

            <div class="form-group">
                <RadzenButton Click=@AnalyzeFile
                              Text=@Localizer["RoImportRiskAnalyzeFile"]
                              Disabled=@(string.IsNullOrWhiteSpace(FileUpload)) />
            </div>
        }
        else if (Status == ImportRiskStatus.ImportRisks)
        {
            <!-- Show status details -->
            <div class=@(AnalyzeSuccess() ? "alert alert-success" : "alert alert-danger")>
                Importing: '@ImportAnalyzeResult.FileName' <br />
                onto RiskObject '@ImportAnalyzeResult.RiskObjectName' <br />

                <br />
                @(FoundRiskInImport() ? "✔" : "❌") Risks in Import @ImportAnalyzeResult.RisksInFile <br />

                @if(!FoundRiskInImport())
                {
                    <div class="m-2">
                        There were no Risks found in your import file. Please check that you uploaded the correct file.
                    </div>
                }
                else
                {
                    <div>
                        ✔ Risks already on RiskObject: @ImportAnalyzeResult.RisksInDatabase  <br />
                        ✔ Risks that will be added: @ImportAnalyzeResult.RisksToAdd <br />
                        ✔ Risks that will be updated: @ImportAnalyzeResult.RisksToUpdate <br />

                        <!-- Objects Handling -->
                        @if (ObjectsSuccess() && ImportAnalyzeResult.MissingObjects > 0)
                        {
                            <span>✔ Objects that will be added to the database: @ImportAnalyzeResult.MissingObjects <br /></span>
                        }
                        else if (ImportAnalyzeResult.MissingObjects > 0)
                        {
                            <span>❌ Objects missing from the database: @ImportAnalyzeResult.MissingObjects <br /></span>
                        }

                        <!-- FailureMode Handling -->
                        @if (FailureModeSuccess() && ImportAnalyzeResult.MissingFailureModes > 0)
                        {
                            <span>✔ FailureModes that will be added to the database: @ImportAnalyzeResult.MissingFailureModes <br /></span>
                        }
                        else if (ImportAnalyzeResult.MissingFailureModes > 0)
                        {
                            <span>❌ FailureModes Missing from database: @ImportAnalyzeResult.MissingFailureModes <br /></span>
                        }

                        @if(!ObjectsSuccess())
                        {
                            <hr/>

                            <div class="m-2">
                                There are @ImportAnalyzeResult.MissingObjects Object(s) in the import file that do not exist in the Database.
                                Please go back and Check <b>@Localizer["RoImportRiskGenerateObjLbl"]</b> if you want to generate these objects.
                            </div>
                        }

                        @if(!FailureModeSuccess())
                        {
                            <hr/>

                            <div class="m-2">
                                There are @ImportAnalyzeResult.MissingFailureModes FailureMode(s) in the import file that do not exist in the Database.
                                Please go back and Check <b>@Localizer["RoImportRiskGenerateFailModeLbl"]</b> if you want to generate these FailureModes.
                            </div>
                        }
                    </div>
                }
            </div>

            <div class="row mt-5">
                <div class="col-6 center">
                    <RadzenButton Click=@PreviousStatus
                                    Text=@Localizer["RoImportPrevious"] />
                </div>
                <div class="col-6 center">
                    <RadzenButton Click=@UploadRisks
                                    Text=@Localizer["RoImportRiskStartImport"]
                                    Disabled=@(string.IsNullOrWhiteSpace(FileUpload)) />
                </div>
            </div>

        }
        else if(Status == ImportRiskStatus.Finished)
        {
            @if (ShowError())
            {
                <div class="form-group pt-3">
                    <div class="alert alert-danger" role="alert">
                        @ImportResult.ErrorMessage
                    </div>
                </div>
            }

            @if (ShowSuccess())
            {
                <div class="form-group pt-3">
                    <div class="alert alert-success" role="alert">
                        Successfully imported @(ImportResult.ItemsAdded) items
                    </div>
                </div>
            }
        }

    </div>
</div>
