using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Pages.PortfolioSetup;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Import;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Import;

public partial class ImportCommonActionWidget
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private ICommonActionImportManager ImportManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private ILogger<ImportCommonActionWidget> Logger { get; set; }
    [Inject] private IStringLocalizer<CommonActionEdit> Localizer { get; set; }
    [Inject] public AppState AppState { get; set; }

    [Parameter] public EventCallback RefreshCommonActions { get; set; }

    private RadzenFileInput<string> UploadRef { get; set; }


    public int RiskObjectId { get; set; }

    private Dictionary<int, string> RiskObjects { get; set; }

    private AnalyzeCommonActionImportResult ImportAnalyzeResult { get; set; } = new();

    private ImportResult ImportResult { get; set; } = new();

    private string FileUpload { get; set; }

    protected override async Task OnInitializedAsync()
    {
        RiskObjects = await DropdownManager.GetRiskObjectDictAsync();
        RiskObjectId = RiskObjects.Keys.FirstOrDefault();
        ImportResult = new ImportResult();
        ImportAnalyzeResult = new AnalyzeCommonActionImportResult();
        FileUpload = null;
    }

    private async Task AnalyzeFile()
    {
        await AnalyzeImport(FileUpload?.Split(',').LastOrDefault()?.Trim()).ConfigureAwait(false);
    }

    private async Task AnalyzeImport(string base64Input)
    {
        ShowLoadingDialog();

        ImportAnalyzeResult.Excel = base64Input;
        ImportAnalyzeResult.FileName = !string.IsNullOrWhiteSpace(UploadRef.FileName)
            ? UploadRef.FileName
            : ImportAnalyzeResult.FileName;

        ImportAnalyzeResult = await ImportManager.AnalyzeImportFileAsync(ImportAnalyzeResult);

        // this only closes the loading Dialog, not the current dialog
        DialogService.Close();
    }

    private async Task UploadRisks()
    {
        ShowLoadingDialog();
        ImportResult = await ImportManager.ImportCommonActionsAsync(ImportAnalyzeResult);

        // this only closes the loading Dialog, not the current dialog
        DialogService.Close();

        if (ImportResult.Success)
        {
            FileUpload = null;

            if (RefreshCommonActions.HasDelegate)
                await RefreshCommonActions.InvokeAsync();
        }

        ImportAnalyzeResult.Status = ImportCommonActionStatus.Finished;
    }

    private void ShowLoadingDialog()
    {
        DialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    private void ChangeFile()
    {
        ImportResult = new ImportResult();
    }

    private bool ShowError() => !string.IsNullOrWhiteSpace(ImportResult.ErrorMessage);

    private bool ShowSuccess() => ImportResult.Success;

    private void PreviousStatus()
    {
        ImportAnalyzeResult.Status = ImportCommonActionStatus.SelectFile;
    }

    // Helper properties for the view
    private ImportCommonActionStatus Status => ImportAnalyzeResult.Status;
    private AnalyzeCommonActionImportResult Result => ImportAnalyzeResult;

    // Helper methods for the view
    private bool FoundItemsInImport() => Result.ImportItems.Count > 0;
    private bool WorkPackageSuccess() => Result.MissingWorkpackages.Count == 0;
    private bool IntervalUnitsSuccess() => Result.MissingIntervalUnits.Count == 0;
    private bool StrategySuccess() => Result.GenerateStrategies || Result.MissingStrategies.Count == 0;
    private bool InitiatorsSuccess() => Result.GenerateInitiators || Result.MissingInitiators.Count == 0;
    private bool ExecutorsSuccess() => Result.GenerateExecutors || Result.MissingExecutors.Count == 0;
    private bool UnitTypesSuccess() => Result.GenerateUnitTypes || Result.MissingUnitTypes.Count == 0;
    public bool AnalyzeSuccess() => FoundItemsInImport()
                                    && WorkPackageSuccess()
                                    && IntervalUnitsSuccess()
                                    && StrategySuccess()
                                    && InitiatorsSuccess()
                                    && ExecutorsSuccess()
                                    && UnitTypesSuccess();
    private bool CanProcessFile() => FoundItemsInImport() && WorkPackageSuccess() && IntervalUnitsSuccess();
}