using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Risks;

public partial class RiskCopyWidget
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IObjectManager ObjectManager { get; set; }
    [Inject] private ILogger<RiskCopyWidget> Logger { get; set; }
    [Inject] private IStringLocalizer<RiskEdit> Localizer { get; set; }

    [Parameter] public int RiskObjectId { get; set; }
    [Parameter] public int RiskId { get; set; }
    [Parameter] public List<TreeNodeGeneric<RiskTreeObject>> RisksToCopy { get; set; }

    private List<ObjectModel> Objects { get; set; } = [];
    private Dictionary<ObjectLevel, string> ObjectLevels { get; set; }
    private RiskCopySettings RiskCopySettings { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        Objects = await RiskAnalysisManager.GetAllObjectsAsync();
        ObjectLevels = await ObjectManager.GetObjectLevelNames();

        RiskCopySettings.SystemId = RisksToCopy.FirstOrDefault()?.Source.SystemId;
        RiskCopySettings.ComponentId = RisksToCopy.FirstOrDefault()?.Source.ComponentId;
        RiskCopySettings.AssemblyId = RisksToCopy.FirstOrDefault()?.Source.AssemblyId;

        if (RisksToCopy?.Any() != true)
            Logger.LogError($"{nameof(RiskCopyWidget)} has been initialized without any {nameof(RisksToCopy)}");
    }

    private async Task ValidTaskSubmitted(RiskCopySettings copySettings)
    {
        var risks = await RiskAnalysisManager.CopyRisksToAsync(
            RisksToCopy.Where(x => x.Source.RiskId != null)
                .Select(x => x.Source.RiskId.Value),
            copySettings);

        NavigationManager.NavigateTo($"/value-risk-analysis/{RisksToCopy.FirstOrDefault()?.Source.RiskObjectId}/risks/{risks.FirstOrDefault()?.Id}", true);
    }

    private void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        Logger.LogError($"Invalid form submitted in {nameof(RiskCopyWidget)}");
        Logger.LogError(Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }

    private string GetObjectLevel(ObjectLevel level)
    {
        return ObjectLevels.TryGetValue(level, out var name) ? name : level.ToString();
    }
}