@page "/"
@using AMprover.BlazorApp
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    Layout = null;
    
    // Centralized version number for all assets
    var appVersion = "0.6";
    var versionParam = $"?v={appVersion}";
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>AMprover</title>
    <base href="~/"/>
    
    <!-- External CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css">
    
    <!-- Internal CSS -->
    <link rel="stylesheet" href="_content/Radzen.Blazor/css/material-base.css">
    <link href="css/site.css@(versionParam)" rel="stylesheet"/>
    <link href="css/custom.css@(versionParam)" rel="stylesheet"/>
    <link href="css/print.css@(versionParam)" rel="stylesheet" media="print"/>
</head>
<body>
<app>
    <component type="typeof(App)" render-mode="Server"/>
</app>

<div id="blazor-error-ui">
    <environment include="Staging,Production">
        An error has occurred. This application may no longer respond until reloaded.
    </environment>
    <environment include="Development">
        An unhandled exception has occurred. See browser dev tools for details.
    </environment>
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>


<script src="_content/Radzen.Blazor/Radzen.Blazor.js"></script>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js" integrity="sha384-OgVRvuATP1z7JjHLkuOU7Xw704+h835Lr+6QL9UvYjZE3Ipu6Tp75j7Bh/kR0JKI" crossorigin="anonymous"></script>

<script src="_framework/blazor.server.js" autostart="false"></script>
<script>
    Blazor.start({
        configureSignalR: function (builder){
         let c = builder.build();
             c.serverTimeoutInMilliseconds = 3000000;
             c.keepAliveIntervalInMilliseconds = 1500000;
         builder.build = () => {
            return c;
         };
        }
    });
</script>

<script src="/js/zoom.js@(versionParam)"></script>
<script src="/js/FileInputValueClearer.js@(versionParam)"></script>
<script src="/js/CookieHelper.js@(versionParam)"></script>
<script src="/js/ConvertToImage.js@(versionParam)"></script>
<script src="/js/convertToPageSizedImage.js@(versionParam)"></script>
<script src="/js/html2canvas.min.js@(versionParam)"></script>
<script src="/js/iframe-pdf.js@(versionParam)"></script>
<script src="/js/lineHelper.js@(versionParam)"></script>
<script src="/js/UtilityGridWidthHelper.js@(versionParam)"></script>
<script src="/js/AMDropdown.js@(versionParam)"></script>
<script src="/js/tooltip.js@(versionParam)"></script>

</body>
</html>
