@page "/sapa-overview"
@using AMprover.BusinessLogic.Models.Sapa

<h2>Sapa Overview</h2>

<div class="row header-navigation subheader-height">
    <div class="col-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
                <NavLink class="breadcrumb-item" href="/sapa-overview" Match="NavLinkMatch.All">
                    Sapa Overview
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-3 text-center">
    </div>
    <div class="col-5 text-right">
        <Information class="sapa-float-right float-right btn-centered my-2 ml-1 mr-2"
                     DialogTitle=@Localizer["SapaInfoTitle"]
                     DialogContent=@Localizer["SapaInfoTxt"] />

        <RadzenButton Icon="print" class="my-2 mx-1"
                      Click=PrintPage
                      Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary />

        @if (SelectedNode?.SapaTreeNodeType is SapaTreeNodeType.RiskObject)
        {
            <RadzenButton Icon="add_circle_outline" class="my-2 mx-1"
                          Text=@Localizer["SapaGenerateSapa"]
                          Click=@GenerateSapa
                          Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary
                          Disabled=@(!AppState.CanEdit || (Sapa?.Status != BusinessLogic.Enums.Status.Budgeting && Sapa != null)) />
        }
        @if (SelectedNode?.SapaTreeNodeType is SapaTreeNodeType.RiskObject or SapaTreeNodeType.SapaCollection)
        {
            <RadzenButton Icon="edit" class="my-2 mx-1"
                          Text=@Localizer["SapaUpdateSapa"]
                          Click=UpdateSapa
                          Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary
                          Disabled=@(!AppState.CanEdit || Sapa?.Status != BusinessLogic.Enums.Status.Budgeting) />

            <RadzenButton Icon="delete" class="my-2 mx-1"
                          Text=@Localizer["SapaDeleteSapa"]
                          Click=DeleteSapa
                          Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary
                          Disabled=@(!AppState.CanEdit || Sapa?.Status != BusinessLogic.Enums.Status.Budgeting) />
        }

    </div>
</div>

<div class="row">
    <div class="col-sm-3 neg-margin">
        <h5>@Localizer["SapaTreeTxt"]</h5>

        <TreeComponent TItem=SapaTreeObject
                       Treeview=@SapaTree
                       NodeClickCallback=@ClickTreeNode
                       ExpandNodeWhenSelected="true" />

        <div class="task-message">
            <Radzen.Blazor.RadzenCheckBox @bind-Value=ShowTaskInFuture/> @Localizer["SapaShowTasksInFuture"] 
        </div>
    </div>
    <div class="col-9">
        <RadzenTabs @bind-SelectedIndex=@QueryParams.TopTabs Change=OnChangeTopTabsComponent @ref=@SapaTabs>
            <Tabs>
                <RadzenTabsItem Text=@Localizer["SapaTabTxt"]>
                    @if (Sapa == null && SelectedNode != null)
                    {
                        <div class="row">
                            <div class="col-5 m-4">

                                @if (SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.RiskObject && SelectedSapaDict.Keys.Count > 0)
                                {
                                    <AMDropdown AllowFiltering="true"
                                                @bind-Value=@SelectedSapa
                                                Data=@SelectedSapaDict
                                                Label="Selected" />
                                }
                                else
                                {
                                    @Localizer["SapaNoSapaExists"]
                                }

                            </div>
                        </div>
                    }

                    @if (Sapa != null)
                    {
                        <div class="row">
                            <div class="col-8">
                                @if (GetNodeType() == SapaTreeNodeType.SapaCollection)
                                {
                                    <div class="row">
                                        <div class="col-2">
                                            <AMproverTextBox Label=@Localizer["SapaId"]
                                                             Value="@SapaCollection.Id.ToString()"
                                                             ReadOnly="true" />
                                        </div>
                                        <div class="col-10">
                                            <AMproverTextBox Label=@Localizer["SapaName"]
                                                             @bind-Value="@SapaCollection.Name"
                                                             Change=@SaveSapaCollection />
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div class="row">
                                        <div class="col-2">
                                            <AMproverTextBox Label=@Localizer["SapaId"]
                                                             Value="@Sapa.Id.ToString()"
                                                             ReadOnly="true" />
                                        </div>
                                        <div class="col-10">
                                            <AMproverTextBox Label=@Localizer["SapaName"]
                                                             @bind-Value="@Sapa.Name"
                                                             Change=@SaveSapa
                                                             ReadOnly=@GetDisabled() />
                                        </div>
                                    </div>
                                }

                                <div class="row">
                                    <div class="col-6">
                                        <div class="sub-frame">
                                            <RadzenTabs class="sapa-side-tab" @bind-SelectedIndex=@QueryParams.BottomTabs TabPosition=TabPosition.Left @ref=@DetailTabs>
                                                <Tabs>
                                                    @foreach (var year in Sapa?.Years ?? new List<SapaYearModel>())
                                                    {
                                                        <RadzenTabsItem Text="@year.Year.ToString()">

                                                            <div class="sapa-side-tab-content col-12 ml-1 pr-0">
                                                                <AMproverNumberInput Label=@(Localizer["SapaBudgetYear"] + " " + year.Year.ToString())
                                                                                     @bind-Value=@year.Budget
                                                                                     OnBlur=@SaveSapa
                                                                                     Min=0
                                                                                     Format="c0"
                                                                                     ReadOnly=@GetDisabledBudget() />
                                                            </div>

                                                            <div class="sapa-side-tab-content ml-4 pr-2 overview-frame">
                                                                <div class="col-12 px-1">
                                                                    <div>
                                                                        <text class=""> @Localizer["SapaApproved"]:</text>
                                                                        <text class="currency-right">@FormatAsSelectedCurrency(year.BudgetApproved)</text>
                                                                    </div>
                                                                    <div>
                                                                        <text class=""> @Localizer["SapaBudgetRemain"]:</text>
                                                                        <text class="currency-right">@FormatAsSelectedCurrency(year.Budget - year.BudgetApproved)</text>
                                                                    </div>
                                                                    <br>
                                                                    <div>
                                                                        <text class=""> @Localizer["SapaRequested"]:</text>
                                                                        <text class="currency-right">@FormatAsSelectedCurrency(year.BudgetRequest)</text>
                                                                    </div>
                                                                    <div>
                                                                        <text class=""> @Localizer["SapaNotApproved"]:</text>
                                                                        <text class="currency-right">@FormatAsSelectedCurrency(year.BudgetApproved - year.BudgetRequest)</text>
                                                                    </div>
                                                                    <br>
                                                                </div>
                                                            </div>
                                                        </RadzenTabsItem>
                                                    }
                                                </Tabs>

                                            </RadzenTabs>
                                        </div>
                                    </div>
                                    <div class="col-6">

                                        @if (GetNodeType() == SapaTreeNodeType.SapaCollection)
                                        {
                                            <AMproverTextArea Label=@Localizer["SapaDescription"]
                                                              @bind-Value="@SapaCollection.Description"
                                                              Rows="2"
                                                              Change=@SaveSapaCollection
                                                              ReadOnly=@GetCollectionOrWorkPackageDisabled() />
                                        }
                                        else
                                        {
                                            <AMproverTextArea Label=@Localizer["SapaDescription"]
                                                              @bind-Value="@Sapa.Description"
                                                              Rows="2"
                                                              Change=@SaveSapa
                                                              ReadOnly=@GetCollectionOrWorkPackageDisabled() />
                                        }

                                        <div class="row">
                                            <div class="col-6">
                                                <AMDropdown AllowFiltering="true"
                                                            Data=@ItemStatusDict @bind-Value=@Sapa.Status
                                                            Label=@Localizer["SapaStatus"]
                                                            Required=false
                                                            AllowClear=true
                                                            Change=@UpdateSapaStatus
                                                            Readonly=@(SelectedNode.SapaTreeNodeType != SapaTreeNodeType.RiskObject) />
                                            </div>

                                            <div class="col-6">
                                                <AMproverTextBox Label=@Localizer["SapaResponsible"]
                                                                 @bind-Value="@Sapa.Responsible"
                                                                 Change=@SaveSapa
                                                                 ReadOnly=@GetDisabled() />
                                            </div>
                                            <div class="col-12">

                                                @if (GetNodeType() == SapaTreeNodeType.SapaCollection)
                                                {
                                                    <AMproverTextArea Label=@Localizer["SapaRemark"]
                                                                      @bind-Value="@SapaCollection.Remark"
                                                                      Rows="2"
                                                                      Change=@SaveSapaCollection
                                                                      ReadOnly=@GetCollectionOrWorkPackageDisabled() />
                                                }
                                                else
                                                {
                                                    <AMproverTextArea Label=@Localizer["SapaRemark"]
                                                                      @bind-Value="@Sapa.Remark"
                                                                      Rows="2"
                                                                      Change=@SaveSapa
                                                                      ReadOnly=@GetCollectionOrWorkPackageDisabled() />
                                                }

                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-6">
                                                @if (SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.RiskObject)
                                                {
                                                    <AMDropdown AllowFiltering="true"
                                                                @bind-Value=@SelectedSapa
                                                                Data=@SelectedSapaDict
                                                                Label="Selected"
                                                                ReadOnly=@(!AppState.CanEdit || Sapa?.Status != BusinessLogic.Enums.Status.Budgeting) />
                                                }
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="col-4">

                                <div class="overview-frame my-3 mr-3 pt-4 px-4 pb-5">
                                    <div>
                                        <text class="bold">@Localizer["SapaSummaryTxt"] @Sapa.FirstYear.ToString() - @Sapa.LastYear.ToString():</text>
                                    </div>
                                    <br>
                                    <div>
                                        <text class="bold"> @Localizer["SapaBudget"]:</text>
                                        <text class="currency-bold">@FormatAsSelectedCurrency(Sapa.Budget)</text>
                                    </div>
                                    <br>
                                    <div>
                                        <text class="bold"> @Localizer["SapaTotalApproved"]:</text>
                                        <text class="currency-bold">@FormatAsSelectedCurrency(Sapa.BudgetApproved)</text>
                                    </div>
                                    <div>
                                        <text class="bold"> @Localizer["SapaBudgetRemain"]:</text>
                                        <text class="currency-bold">@FormatAsSelectedCurrency(Sapa.Budget - Sapa.BudgetApproved)</text>
                                    </div>
                                    <br>
                                    <div>
                                        <text class="bold"> @Localizer["SapaTotalRequested"]:</text>
                                        <text class="currency-bold">@FormatAsSelectedCurrency(Sapa.BudgetRequest)</text>
                                    </div>
                                    <div>
                                        <text class="bold"> @Localizer["SapaDeclined"]:</text>
                                        <text class="currency-bold">@FormatAsSelectedCurrency(Sapa.BudgetRequest - Sapa.BudgetApproved)</text>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                </RadzenTabsItem>
                <RadzenTabsItem Text=@Localizer["SapaGraphTxt"]>
                    <div class="row">
                        <div class="col-1">
                            <RadzenToggleButton class="sapa-setting-button"
                                                Text=@SheqToggleButton
                                                ButtonStyle=ButtonStyle.Base
                                                Size=ButtonSize.ExtraSmall
                                                Value=@SheqView
                                                Variant="Variant.Outlined"
                                                Shade="Shade.Default"
                                                ToggleShade="Shade.Darker"
                                                Click=@ToggleSheqView />

                            <RadzenToggleButton class="sapa-setting-button"
                                                Text=@YearToggleButton
                                                ButtonStyle=ButtonStyle.Base
                                                Size=ButtonSize.ExtraSmall
                                                Value=@YearView
                                                Variant="Variant.Outlined"
                                                Shade="Shade.Default"
                                                ToggleShade="Shade.Darker"
                                                Click=@ToggleYearView />

                            <RadzenToggleButton class="sapa-setting-button"
                                                Text=@WpToggleButton
                                                Visible=@WpButtonVisible
                                                ButtonStyle=ButtonStyle.Base
                                                Size=ButtonSize.ExtraSmall
                                                Value=@WpView
                                                Variant="Variant.Outlined"
                                                Shade="Shade.Default"
                                                ToggleShade="Shade.Darker"
                                                Click=@ToggleWpView />
                        </div>
                        <div class="col-11">
                            <RadzenRow>
                                <RadzenColumn Size="3">
                                    <RadzenStack>
                                        @if (ShowSheqView.TextValue == "true")
                                        {
                                            <RadzenChart @key=@ChartKey class="sapa-graph-layout">
                                                <RadzenLegend Visible="false" />
                                                <RadzenDonutSeries Data="@ChartDataPercOfBudget" Fills=@SheqColorScheme CategoryProperty="BudgetCriteria" ValueProperty="SapaCost"
                                                                   InnerRadius="90" Radius="120" Strokes=@(new[] {"#606060", "#909090"}) StrokeWidth="1" TotalAngle="360" StartAngle="180">
                                                    <ChildContent>
                                                        <RadzenSeriesDataLabels Visible="false" />
                                                    </ChildContent>
                                                    <TitleTemplate>
                                                        <div class="rz-donut-content">
                                                            <div>
                                                                <h2>@FormatAsSelectedCurrency(TotalBudgetNeed)</h2>
                                                                <strong>Budget Need @BudgetYear</strong>
                                                            </div>
                                                            <div>
                                                                <strong>@SheqDonutTitle</strong>
                                                            </div>
                                                        </div>
                                                    </TitleTemplate>
                                                    <TooltipTemplate Context="data">
                                                        <div>
                                                            <span>@data.BudgetCriteria: </span>@data.CostString
                                                        </div>
                                                    </TooltipTemplate>
                                                </RadzenDonutSeries>
                                            </RadzenChart>
                                        }
                                        else
                                        {
                                            <RadzenChart @key=@ChartKey class="sapa-graph-layout">
                                                <RadzenLegend Visible="false" />
                                                @if (BudgetSpent <= 1)
                                                {
                                                    <RadzenDonutSeries Data="@ChartDataPercOfBudget" Fills=@(new[] {"#62993E", "#A1C490"}) CategoryProperty="BudgetCriteria" ValueProperty="SapaCost"
                                                                       InnerRadius="90" Radius="120" Strokes=@(new[] {"#606060", "#909090"}) StrokeWidth="1" TotalAngle="360" StartAngle="180">
                                                        <ChildContent>
                                                            <RadzenSeriesDataLabels Visible="false" />
                                                        </ChildContent>
                                                        <TitleTemplate>
                                                            <div class="rz-donut-content">
                                                                <div>
                                                                    <h2>@FormatAsSelectedPercentage(BudgetSpent)</h2>
                                                                    <strong>Budget Utilization</strong>
                                                                </div>
                                                                <div>
                                                                    <strong>@BudgetYear</strong>
                                                                </div>
                                                            </div>
                                                        </TitleTemplate>
                                                        <TooltipTemplate Context="data">
                                                            <div>
                                                                <span>@data.BudgetCriteria: </span><strong>@data.CostString</strong>
                                                            </div>
                                                        </TooltipTemplate>
                                                    </RadzenDonutSeries>
                                                }
                                                else if (BudgetSpent != null)
                                                {
                                                    <RadzenDonutSeries Data="@ChartDataPercOfBudget" Fills=@(new[] {"#FF0000", "#62993E"}) CategoryProperty="BudgetCriteria" ValueProperty="SapaCost"
                                                                       InnerRadius="90" Radius="120" Strokes=@(new[] {"#606060", "#909090"}) StrokeWidth="1" TotalAngle="360" StartAngle="180">
                                                        <ChildContent>
                                                            <RadzenSeriesDataLabels Visible="false" />
                                                        </ChildContent>
                                                        <TitleTemplate>
                                                            <div class="rz-donut-content">
                                                                <div>
                                                                    <h2 style="color:red;">@FormatAsSelectedPercentage(BudgetSpent)</h2>
                                                                    <strong>Budget Utilization</strong>
                                                                </div>
                                                                <div>
                                                                    <strong>@BudgetYear</strong>
                                                                </div>
                                                            </div>
                                                        </TitleTemplate>
                                                        <TooltipTemplate Context="data">
                                                            <div>
                                                                <span>@data.BudgetCriteria: </span><strong>@data.CostString</strong>
                                                            </div>
                                                        </TooltipTemplate>
                                                    </RadzenDonutSeries>
                                                }
                                            </RadzenChart>
                                        }
                                    </RadzenStack>
                                </RadzenColumn>

                                <RadzenColumn Size="5">
                                    <div class="sapa-graph-text">
                                        @GraphTitle
                                    </div>
                                    @if (NoDep == 0)
                                    {
                                        <div class="sapa-no-graph-text">No budgetdata is available, is the department linked in the RiskOrganizer module?</div>
                                    }
                                    else
                                    {
                                        <RadzenChart @key=@ChartKey class="sapa-graph">
                                            <RadzenSeriesDataLabels Visible="false" />
                                            <RadzenColumnSeries Data="@ChartDataAssignedBudget" Fill="#EDDB00" Title="@AssignedBudgetYear" CategoryProperty="Department" ValueProperty="SapaCost">
                                                <TooltipTemplate Context="data">
                                                    <div>
                                                        <span>@Localizer["SapaBudgetAssigned"]</span> = <strong>@data.CostString</strong>
                                                    </div>
                                                </TooltipTemplate>
                                            </RadzenColumnSeries>
                                            <RadzenColumnSeries Data="@ChartDataApprovedCost" Fill="#F79831" Title="@TotalApprovedYear" CategoryProperty="Department" ValueProperty="SapaCost">
                                                <TooltipTemplate Context="data">
                                                    <div>
                                                        <span>@Localizer["SapaApproved"]</span> = <strong>@data.CostString</strong>
                                                    </div>
                                                </TooltipTemplate>
                                            </RadzenColumnSeries>
                                            <RadzenColumnSeries Data="@ChartDataDeclinedCost" Fill="#8F9296" Title="@TotalDeclinedYear" CategoryProperty="Department" ValueProperty="SapaCost">
                                                <TooltipTemplate Context="data">
                                                    <div>
                                                        <span>@Localizer["SapaDeclined"]</span> = <strong>@data.CostString</strong>
                                                    </div>
                                                </TooltipTemplate>
                                            </RadzenColumnSeries>
                                            <RadzenColumnOptions Radius="5" />
                                            <RadzenValueAxis Formatter="@FormatAsEur">
                                                <RadzenValueAxis Min="0" />
                                                <RadzenGridLines Visible="true" />
                                            </RadzenValueAxis>
                                            <RadzenLegend Visible="true" Position="LegendPosition.Bottom" />
                                        </RadzenChart>
                                    }
                                </RadzenColumn>

                                <RadzenColumn Size="3">
                                    <RadzenStack>
                                        <RadzenChart @key=@ChartKey class="sapa-graph-layout">
                                            <RadzenLegend Visible="false" />
                                            <RadzenDonutSeries Data="@ChartDataSapaIndex" Fills=@(new[] {"#e9ecef", "#A1C490", "#e9ecef", "#62993E", "#e9ecef", "#FF0000"}) CategoryProperty="BudgetCriteria" ValueProperty="SapaPercentage"
                                                               InnerRadius="90" Radius="120" Strokes=@(new[] {"#909090", "#909090", "#606060", "#909090", "#909090", "#909090"}) StrokeWidth="1" TotalAngle="360" StartAngle="180">
                                                <ChildContent>
                                                    <RadzenSeriesDataLabels Visible="false" />
                                                </ChildContent>
                                                <TitleTemplate>
                                                    <div class="rz-donut-content">
                                                        <div>
                                                            <h2>@FormatAsSelectedNumber(AverageSapaIndex)</h2>
                                                            <strong>Average Sapa Index</strong>
                                                        </div>
                                                        <div>
                                                            <strong>@BudgetYear</strong>
                                                        </div>
                                                    </div>
                                                </TitleTemplate>
                                                <TooltipTemplate Context="data">
                                                    <div>
                                                        <span>@data.BudgetCriteria: </span><strong>@data.CostString</strong>
                                                    </div>
                                                </TooltipTemplate>
                                            </RadzenDonutSeries>
                                        </RadzenChart>
                                    </RadzenStack>
                                </RadzenColumn>

                            </RadzenRow>
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@GetFilterTabText()>
                    <div class="row">
                        <div class="col-12 col-md-6 col-lg-4">

                            <RadzenListBox class="riskorganizer-scenario-listbox"
                                           Data=@SapaWorkPackages @bind-Value=TempSelectedWorkPackages
                                           FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                           AllowFiltering="true" Multiple="true" />

                            <RadzenButton Text="Apply"
                                          Disabled=@ApplySelectedWorkPackagesBtnEnabled()
                                          Click=@UpdateSelectedWorkPackages />

                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@Localizer["SapaSettingTabTxt"]>
                    @if (Sapa != null)
                    {
                        <div class="row">
                            <div class="col-4">
                                <AMproverNumberInput Label=@Localizer["SapaCbiScore"]
                                                     @bind-Value="@Sapa.CbiScore"
                                                     OnBlur=@SaveSapa
                                                     ReadOnly=@GetDisabled() />
                            </div>
                            <div class="col-4">
                                <AMproverTextBox Label=@Localizer["SapaCbiItem"]
                                                 @bind-Value="@Sapa.CbiItem"
                                                 Change=@SaveSapa
                                                 ReadOnly=@GetDisabled() />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-4">
                                <AMDropdown AllowFiltering="true"
                                            Data=@ExecutorDict @bind-Value="@Sapa.ExecutorId"
                                            Label=@Localizer["SapaExecutor"]
                                            Required=false
                                            Change=@SaveSapa
                                            Readonly=@GetDisabled() />
                            </div>
                            <div class="col-4">
                                <AMDropdown AllowFiltering="true"
                                            Data=@InitiatorDict @bind-Value="@Sapa.InitiatorId"
                                            Label="@Localizer["SapaInitiator"]"
                                            Required=false
                                            Change=@SaveSapa
                                            ReadOnly=@GetDisabled() />
                            </div>
                        </div>
                        <br>

                        <br>
                        <div>
                            <Radzen.Blazor.RadzenCheckBox @bind-Value=ApproveForAllYears
                                                          Disabled=@GetDisabled() /> &nbsp @Localizer["SapaApproveForAllYears"]
                        </div>
                    }
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
</div>
<div class="row">
    <div class="col-12">

        @if (Sapa != null)
        {
            <RadzenTabs @bind-SelectedIndex=@QueryParams.BottomTabs @ref=@BottomTabs Change=OnChangeBottomTabsComponent>
                <Tabs>
                    @foreach (var year in Sapa?.Years ?? new List<SapaYearModel>())
                    {
                        <RadzenTabsItem Text="@year.Year.ToString()">
                            <div class="row">
                                <div class="col-12">

                                    @if (GetDisabled())
                                    {
                                        <UtilityGrid TItem=SapaDetailModel
                                                        @ref=SapaGrid
                                                        Data=@(ShowTaskInFuture ? year.Details.ToList() : year.Details.Where(x => x.CostYear1 > 0).ToList())
                                                        FileName=@GridNames.Sapa.Details
                                                        Interactive=true
                                                        DisableEditButton=true
                                                        AllowXlsExport=true
                                                        AllowFiltering=true
                                                        OpenPageCallback=@(args => OpenRisksPage(args))
                                                        DefaultWidth=94 />
                                    }
                                    else if (SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.WorkPackage
                                        && Sapa.Status == BusinessLogic.Enums.Status.Budgeting)
                                    {
                                        <UtilityGrid TItem=SapaDetailModel
                                                        @ref=SapaGrid
                                                        Data=@(ShowTaskInFuture ? year.Details.ToList() : year.Details.Where(x => x.CostYear1 > 0).ToList())
                                                        FileName=@GridNames.Sapa.Details
                                                        Interactive=true
                                                        DisableEditButton=true
                                                        AllowXlsExport=true
                                                        AllowFiltering=true
                                                        AcceptCallback=@(AcceptSapaDetail)
                                                        DeclineCallback=@DeclineSapaDetail
                                                        OpenPageCallback=@(args => OpenSapaDetailsPopup(args))
                                                        OrderedListCallback=@AcceptAllCallBack
                                                        OrderedListCallbackBtnIcon="check"
                                                        OrderedListCallbackBtnTxt=@Localizer["SapaApproveAllBtn"]
                                                        OrderedListCallbackExplanation=@Localizer["SapaApproveAllText"]
                                                        DefaultWidth=94 />
                                    }
                                    else
                                    {
                                        <UtilityGrid TItem=SapaDetailModel
                                                        @ref=SapaGrid
                                                        Data=@(ShowTaskInFuture ? year.Details.ToList() : year.Details.Where(x => x.CostYear1 > 0).ToList())
                                                        FileName=@GridNames.Sapa.Details
                                                        Interactive=true
                                                        DisableEditButton=true
                                                        AllowXlsExport=true
                                                        AllowFiltering=true
                                                        OpenPageCallback=@(args => OpenSapaDetailsPopup(args))
                                                        DefaultWidth=94 />
                                    }

                                </div>
                            </div>
                        </RadzenTabsItem>
                    }
                </Tabs>
            </RadzenTabs>
        }
    </div>
</div>