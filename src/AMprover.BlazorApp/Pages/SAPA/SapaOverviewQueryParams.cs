using System.Collections.Generic;
using AMprover.BusinessLogic.Models.Sapa;
using AMprover.BusinessLogic.Navigation;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Pages.SAPA;

public class SapaOverviewQueryParams : QueryParamsBase
{
    public SapaOverviewQueryParams(
        ILoggerFactory loggerFactory,
        NavigationManager navManager)
        : base(loggerFactory, navManager)
    {
    }

    private SapaTreeNodeType? _nodeType;
    public SapaTreeNodeType? NodeType
    {
        get => _nodeType;
        set { _nodeType = value; UpdateUrl(); }
    }

    private int? _nodeId;
    public int? NodeId
    {
        get => _nodeId;
        set { _nodeId = value; UpdateUrl(); }
    }

    private int _topTabs;
    public int TopTabs
    {
        get => _topTabs;
        set { _topTabs = value; UpdateUrl(); }
    }

    private int _bottomTabs;
    public int BottomTabs
    {
        get => _bottomTabs;
        set { _bottomTabs = value; UpdateUrl(); }
    }

    private List<string> _workPackages = [];

    public List<string> WorkPackages
    {
        get => _workPackages;
        set
        {
            _workPackages = value;
            UpdateUrl();
        }
    }
}