@page "/portfolioinfo"

<h2>Authentication details</h2>

<hr />

<p>@_authMessage</p>
<ul>
    <li>Username = @_authUsername</li>
    <li>Name = @_authFullname</li>
</ul>
@if (_authClaims.Any())
{
    <strong>Claims</strong>
    <ul>
        @foreach (var claim in _authClaims)
        {
            <li>@claim.Type: @claim.Value</li>
        }
    </ul>
}
<hr />

<h3>Portfolios for logged-in user</h3>
<ul>
    @foreach (var portfolio in _userPortfolios)
    {
        <li>
            Name = @portfolio.Name ; Database = @portfolio.DatabaseName ; Selected = @(portfolio.Id == _userPorfolioSelectedId)
        </li>
    }
</ul>

<hr />

<h3>Portfolio database info</h3>
<i>Database: @_dbName</i>
<br />
<i>Connection Ok?: @_dbAvailable</i>

<h3>Fake Exception</h3>
<RadzenButton Icon="error" Text="Fake Error" Size="ButtonSize.Medium" Click="@(args => ThrowUp())" />
