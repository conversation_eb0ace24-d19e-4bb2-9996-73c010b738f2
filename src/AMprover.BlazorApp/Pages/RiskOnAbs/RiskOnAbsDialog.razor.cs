using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.RiskOnAbs;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using <PERSON><PERSON><PERSON>;

namespace AMprover.BlazorApp.Pages.RiskOnAbs;

public partial class RiskOnAbsDialog
{
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IStringLocalizer<RiskOnAbsPage> Localizer { get; set; }

    [Parameter] public MarkupString Body { get; set; }
    [Parameter] public MatrixSelectionAggregationCell MatrixAggregationCell { get; set; }

    private void ClickRisk(RiskModelFlat risk)
    {
        NavigationManager.NavigateTo($"/value-risk-analysis/{risk.RiskObjectId}/risks/{risk.Id}");
        DialogService.Close();
    }
}