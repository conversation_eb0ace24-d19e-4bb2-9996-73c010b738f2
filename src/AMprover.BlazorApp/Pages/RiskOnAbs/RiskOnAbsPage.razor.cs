using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Shared;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.ABS;
using AMprover.BusinessLogic.Models.Criticality;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.RiskOnAbs;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Pages.RiskOnAbs;

public partial class RiskOnAbsPage
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IAssetBreakdownManager AssetBreakdownManager { get; set; }
    [Inject] private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IPageNavigationManager PageNavigationManager { get; set; }
    [Inject] private ICriticalityRankingManager CriticalityManager { get; set; }
    [Inject] private ILogger<RiskOnAbsPage> Logger { get; set; }
    [Inject] private IStringLocalizer<RiskOnAbsPage> Localizer { get; set; }

    [CascadingParameter] public AppState AppState { get; set; }

    public TreeGeneric<RiskOnAbsTreeModel> RiskOnAbsTree { get; } = new();
    private new List<AssetModel> Assets { get; set; } = [];
    private RiskOnAbsTreeModel SelectedNode { get; set; }
    public RadzenTabs Tabs { get; set; }

    private List<MatrixTypes> MatrixTypeList { get; } = [MatrixTypes.Before, MatrixTypes.Pmo, MatrixTypes.After];
    private MatrixTypes MatrixType { get; set; } = MatrixTypes.Before;
    private string Currency => AppState.Currency;

    private bool showOnlyAssetsWithData { get; set; }
    
    private bool IsLoaded { get; set; }
    
    private string GetTabTitle(MatrixTypes matrixType) => @Localizer[$"RabsMatrix{matrixType}"];

    private bool ShowOnlyAssetsWithData
    {
        get => showOnlyAssetsWithData;
        set
        {
            showOnlyAssetsWithData = value;
            FilterTreeIfNoData(RiskOnAbsTree.Node);
        }
    }

    private List<RiskMatrixTemplateModel> RiskMatrixTemplates { get; set; }
    private RiskMatrixTemplateModel RiskMatrixTemplate { get; set; }
    private MatrixSelectionAggregation MatrixSelectionAggregation { get; set; }
    private bool IsDescending { get; set; }

    private RiskOnAbsQueryString Query { get; set; }

    private CriticalityRankingModelFlat Criticality { get; set; }

    private List<KeyValuePair<string, string>> CriticalityDetails { get; set; } = [];
    private List<GridColumnModel> Columns { get; set; }
    private PropertyInfo[] CriticalityProperties { get; set; }

    protected override async Task OnInitializedAsync()
    {
        RiskMatrixTemplates = await RiskAnalysisSetupManager.GetAllTemplatesAsync();
        RiskMatrixTemplate = RiskMatrixTemplates.FirstOrDefault();
        GetOrientation();

        Query = new RiskOnAbsQueryString(NavigationManager.ToAbsoluteUri(NavigationManager.Uri));

        Columns = (await LookupManager
            .GetColumnsAsync(GridNames.Criticality.Rankings, true))
            .Where(x => x.Visible)
            .ToList();

        CriticalityProperties = typeof(CriticalityRankingModelFlat).GetProperties();

        ProcessQueryString();
        await InitializeAssetTree();
        IsLoaded = true;
    }

    private void GetOrientation()
    {
        var firstEffectColumn = RiskMatrixTemplate.MainGrid.TableColumns.FindIndex(x => x.IsEffectColumn);
        var templateValues = RiskMatrixTemplate.MainGrid.TableContent.Select(x =>
            x.Cells[firstEffectColumn].CustomValue?.ToDecimal(Currency).IsNullOrZero() == true
                ? x.Cells[firstEffectColumn].Value.ToDecimal(Currency)
                : x.Cells[firstEffectColumn].CustomValue?.ToDecimal(Currency)).ToList();
        IsDescending = templateValues.IsDescending();
    }

    private void ProcessQueryString()
    {
        if (string.IsNullOrWhiteSpace(Query.Matrix))
            return;

        var matrix = RiskMatrixTemplates.FirstOrDefault(x => x.Name.Equals(Query.Matrix, StringComparison.OrdinalIgnoreCase));
        if (matrix == null)
            return;

        RiskMatrixTemplate = matrix;
        GetOrientation();
    }

    private void ChangeMatrix()
    {
        SetUrl(Query.SetMatrix(RiskMatrixTemplate.Name));
        InitializeAssetTree();
    }

    private void SetUrl(string url)
    {
        PageNavigationManager.SavePageQueryString(url.Split('?').FirstOrDefault(),
            url.Split('?').Skip(1).FirstOrDefault());
        NavigationManager.NavigateTo(url);
    }

    private async Task InitializeAssetTree()
    {
        Assets = await AssetBreakdownManager.GetAllAssetsWithRisksFromSpAsync(RiskMatrixTemplate.Id);
        RiskOnAbsTree.Initialize(AssetBreakdownManager.GetRiskOnAbsTree(Assets));

        if (ShowOnlyAssetsWithData)
            FilterTreeIfNoData(RiskOnAbsTree.Node);

        TreeNodeGeneric<RiskOnAbsTreeModel> firstNode = null;
        if (Query.AssetId != null)
            firstNode = RiskOnAbsTree.GetFlattenedNodes().FirstOrDefault(x => x.Source.Asset?.Id == Query.AssetId);
        firstNode ??= RiskOnAbsTree.Node.Nodes.FirstOrDefault();

        if (firstNode == null)
            return;

        RiskOnAbsTree.SelectNode(firstNode);
        await SelectNode(firstNode);
    }

    private void FilterTreeIfNoData(TreeNodeGeneric<RiskOnAbsTreeModel> node)
    {
        if (node.Parent != null && HasUnderLyingRisk(node) == false)
            node.Hidden = ShowOnlyAssetsWithData;

        foreach (var child in node.Nodes)
            FilterTreeIfNoData(child);
    }

    private static bool HasUnderLyingRisk(TreeNodeGeneric<RiskOnAbsTreeModel> node)
    {
        return node.Source.IsRiskNode || node.Nodes.Any(HasUnderLyingRisk);
    }

    private async Task ChangeTab(int index)
    {
        // Matrix Logic, the last tab does not contain a Matrix
        if (index <= 2)
        {
            MatrixType = MatrixTypeList[index];
            await SelectNode(RiskOnAbsTree.SelectedNode);
        }

        SetUrl(Query.SetTabId(index));
    }

    private async Task ClickNode(TreeNodeGeneric<RiskOnAbsTreeModel> node)
    {
        var assetId = node.Source.Asset?.Id ?? node.Parent.Source.Asset.Id;
        SetUrl(Query.SetAssetId(assetId));

        await SelectNode(node);
    }

    private async Task SelectNode(TreeNodeGeneric<RiskOnAbsTreeModel> node)
    {
        SelectedNode = node.Source;
        MatrixSelectionAggregation = new MatrixSelectionAggregation();

        var risks = SelectedNode.IsRiskNode
            ? [new ValueTuple<AssetModel, RiskModel>(node.Parent.Source.Asset, SelectedNode.Risk)]
            : GetRisksFromAsset(node);

        foreach (var risk in risks)
            AppendRiskSelectionsToMatrix(risk.Item1, risk.Item2);

        await LoadCriticalityDetails(node.Source.Asset?.Code);
    }

    private async Task LoadCriticalityDetails(string assetCode)
    {
        Criticality = await CriticalityManager.GetCriticalityAsync(assetCode);
        CriticalityDetails = [];

        if (Criticality == null)
            return;

        foreach (var col in Columns)
        {
            var prop = CriticalityProperties.FirstOrDefault(x => x.Name == col.FieldName);
            if (prop == null)
                continue;

            var value = prop.GetValue(Criticality);
            if (value != null)
            {
                CriticalityDetails.Add(new KeyValuePair<string, string>(col.ColumnHeader, value.ToString()));
            }
        }
    }

    private void OpenRiskFromNode(RiskOnAbsTreeModel node)
    {
        if (node.IsRiskNode)
            NavigationManager.NavigateTo($"/value-risk-analysis/{node.Risk.RiskObjectId}/risks/{node.Risk.Id}");
        else
            DialogService.Open<InformationDialog>("Not a Value Risk", new Dictionary<string, object>
            {
                {"DialogContent", "You can only open Value Risk Nodes from here, not Asset Nodes."}
            });
    }

    private static List<(AssetModel, RiskModel)> GetRisksFromAsset(TreeNodeGeneric<RiskOnAbsTreeModel> node)
    {
        var result = new List<(AssetModel, RiskModel)>();

        foreach (var child in node.Nodes)
            result.AddRange(GetRisksFromAsset(child));

        if (node.Source.IsRiskNode)
            result.Add(new ValueTuple<AssetModel, RiskModel>(node.Parent.Source.Asset, node.Source.Risk));

        return result;
    }

    private void AppendRiskSelectionsToMatrix(AssetModel asset, RiskModel risk)
    {
        var values = GetRiskMatrixValues(risk);
        var selectionX = GetSelectionX(risk);
        var selectionY = IsDescending ? values.Min() : values.Max();

        while (values.Count < selectionX)
            values.Add(null);

        values.Add(selectionY);

        for (var i = 0; i < values.Count; i++)
        {
            var column = MatrixSelectionAggregation.Columns.Skip(i).FirstOrDefault();

            if (column == null)
            {
                column = new MatrixSelectionAggregationColumn();
                MatrixSelectionAggregation.Columns.Add(column);
            }

            var selection = column.Cells.Find(x => x.Index == values[i]);

            if (selection == null)
            {
                selection = new MatrixSelectionAggregationCell { Index = values[i] };
                column.Cells.Add(selection);
            }

            if (!selection.Risks.Exists(x => x.Id == risk.Id))
                selection.Risks.Add(risk);

            if (asset != null)
                selection.Assets.Add(asset);

            selection.Count++;
        }
    }

    private int? GetSelectionX(RiskModel risk)
    {
        return MatrixType switch
        {
            MatrixTypes.Before => risk.FmecaSelections?.Before?.MtbfSelected,
            MatrixTypes.After => risk.FmecaSelections?.After?.MtbfSelected,
            MatrixTypes.Pmo => risk.FmecaSelections?.Pmo?.MtbfSelected,
            _ => throw new ArgumentException($"{MatrixType} is not supported")
        };
    }

    private List<int?> GetRiskMatrixValues(RiskModel risk)
    {
        return MatrixType switch
        {
            MatrixTypes.Before => [..risk.FmecaSelections?.Before?.Selected ?? []],
            MatrixTypes.After => [..risk.FmecaSelections?.After?.Selected ?? []],
            MatrixTypes.Pmo => [..risk.FmecaSelections?.Pmo?.Selected ?? []],
            _ => throw new ArgumentException($"{MatrixType} is not supported")
        };
    }

    private void NavLinkAbs()
    {
        NavigationManager.NavigateTo("/value-risks-on-abs/assign-assets");
    }
}