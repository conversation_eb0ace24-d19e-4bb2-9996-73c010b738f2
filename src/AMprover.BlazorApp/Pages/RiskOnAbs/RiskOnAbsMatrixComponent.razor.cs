using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.RiskOnAbs;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using <PERSON><PERSON>zen;

namespace AMprover.BlazorApp.Pages.RiskOnAbs;

public partial class RiskOnAbsMatrixComponent
{
    [Inject] private IStringLocalizer<RiskOnAbsPage> Localizer { get; set; }
    [Inject] private DialogService DialogService { get; set; }

    [CascadingParameter] public AppState AppState { get; set; }

    [Parameter] public RiskMatrixTemplateModel RiskMatrixTemplate { get; set; }
    [Parameter] public MatrixSelectionAggregation MatrixSelectionAggregation { get; set; }

    private string _tab = "Description";
    private List<int> ActivatedSubgrids { get; set; } = [];

    private void AddActiveSubgrid(int column)
    {
        if (!ActivatedSubgrids.Contains(column))
            ActivatedSubgrids.Add(column);
    }

    private void RemoveActiveSubgrid(int column)
    {
        if (ActivatedSubgrids.Contains(column))
            ActivatedSubgrids.Remove(column);
    }

    private MarkupString GetCellSelectedContent(MatrixSelectionAggregationCell cell)
    {
        if (cell == null)
            return (MarkupString)"";

        var size = System.Math.Min(25 + cell.Count, 60);
        var fontSize = cell.Count <= 999
            ? size / 2.0
            : size / 2.5;

        var styles = new List<string>
        {
            $"width:{size}px",
            $"height:{size}px",
            $"font-size:{fontSize}px",
        };

        return (MarkupString)$"<div class=\"rabs-count-circle\" style=\"{string.Join(';', styles)}\">{cell.Count}</div>";
    }

    private MarkupString GetChanceContent(int cellCount)
    {
        if (cellCount == 0)
            return (MarkupString)string.Empty;

        var size = System.Math.Min(25 + cellCount, 60);
        var padding = size * .1;
        var fontSize = cellCount <= 999
            ? size / 2.0
            : size / 2.5;

        var styles = new List<string>
        {
            $"width:{size}px",
            $"height:{size}px",
            $"font-size:{fontSize}px",
            $"padding:{padding}px 0px",
        };

        return (MarkupString)$"<div class=\"rabs-chance-count\" style=\"{string.Join(';', styles)}\">{cellCount}</div>";
    }

    private void ShowRisks(MatrixSelectionAggregationCell cell)
    {
        DialogService.Open<RiskOnAbsDialog>(
            Localizer["RabsAggregationDialogTitle"],
            new Dictionary<string, object>
            {
                {nameof(RiskOnAbsDialog.Body), (MarkupString)Localizer["RabsAggregationDialogBody"].Value },
                {nameof(RiskOnAbsDialog.MatrixAggregationCell), cell }
            }, new DialogOptions
            {
                Draggable = true
            });
    }

    private MatrixSelectionAggregationCell GetCell(int column, int row)
    {
        var col = MatrixSelectionAggregation?.Columns?.Skip(column).FirstOrDefault();
        var cell = col?.Cells.FirstOrDefault(x => x.Index == row);
        return cell;
    }

    private int? GetChanceCount(int column)
    {
        var gridColumn = RiskMatrixTemplate.MainGrid.TableColumns[column];
        if (gridColumn.IsEffectColumn)
            return null;

        var summaryColumn = MatrixSelectionAggregation?.Columns?.Skip(column).FirstOrDefault();
        var count = summaryColumn?.Cells.Where(x => x.Index != null).Sum(x => x.Count);
        return count > 0 ? count : null;
    }
}