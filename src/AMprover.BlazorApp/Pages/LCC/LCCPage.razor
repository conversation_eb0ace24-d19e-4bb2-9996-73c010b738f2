@page "/lcc/{LccId:int?}"
@using BusinessLogic.Models.LCC
@using ModelRisk = AMprover.BusinessLogic.Models.RiskAnalysis.RiskModel
@using ModelTask = AMprover.BusinessLogic.Models.RiskAnalysis.TaskModel
@inject IStringLocalizer<LCCPage> _localizer

<div class="row">
    <div class="col-6">
        <h2>@_localizer["LccHeaderTxt"]</h2>
    </div>
</div>

@if (IsLoaded)
{
    <div class="row header-navigation">
        <div class="col-4">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                        Home
                    </NavLink>
                    <NavLink class="breadcrumb-item" aria-current="page" Match="NavLinkMatch.All">
                        LCC
                    </NavLink>
                </ol>
            </nav>
        </div>
        <div class="col-4 text-center">
            <Paginator Initial=GetInitialPaginatorValue()
                       Count=GetScenarioCount()
                       CallBack=PaginatorCallback
                       @ref=Paginator/>
        </div>
        <div class="col-4 text-right">
            <Information class="float-right my-2 mr-2" DialogTitle=@_localizer["LccMenuTitle"] DialogContent=@_localizer["LccMenuTxt"]/>
            <RadzenButton Icon="save" Text=@_localizer["LccSaveBtn"] class="float-right my-2 mx-2" Click=@SaveAndReCalculateLcc ButtonStyle=ButtonStyle.Secondary Disabled=@(!AppState.CanEdit || SelectedLcc == null) />
            <RadzenButton Icon="add_circle_outline" Text=@_localizer["LccNewLccBtn"] class="float-right my-2 mx-0" Click=@OpenNewLccWidget ButtonStyle=ButtonStyle.Secondary Disabled=!AppState.CanEdit />
            </div>
    </div>

    <div class="row">
        <div class="col-sm-3">
            <AMDropdown AllowFiltering="true"
                        Data="@RiskScenarios.ToDictionary(x => x.Id, x => x.Name)"
                        @bind-value=SelectedScenario
                        Change=@(args => SelectScenario(args))
                        Label="Scenario"
                        ContainerClass="mb-2"/>

            <div>
                <RadzenTabs @ref=@TreeTab TabPosition=TabPosition.Top class="tree-tabs">
                    <Tabs>
                        <RadzenTabsItem>
                            <TreeComponent TItem=LccTreeObject
                                           Treeview=LccTree
                                           NodeClickCallback=ClickTreeNode
                                           DeleteCallback=@(args => DeleteLccFromTree(args))/>
                        </RadzenTabsItem>
                    </Tabs>
                </RadzenTabs>
            </div>
        </div>
        <div class="col-sm-9">
            <RadzenTabs @ref=@LccDetailsTab TabPosition=TabPosition.Top class="hide-tabs-nav">
                <Tabs>
                    <RadzenTabsItem>
                        @if (SelectedLcc != null)
                        {
                            <EditForm Model="@SelectedLcc" OnValidSubmit=@ValidLccSubmitted OnInvalidSubmit=@InvalidLccSubmitted>
                                <div class="row">
                                    <div class="col-sm-8">
                                        <div class="row neg-margin">
                                            <div class="col-sm-2">
                                                <div class="form-group">
                                                    <label>Id:</label>
                                                    <span class="form-control readonly neg-margin-small">@SelectedLcc.Id</span>
                                                </div>
                                            </div>
                                            <div class="col-sm-10" style="margin-top: 6px">
                                                <AMproverTextBox @bind-Value=@SelectedLcc.Name MaxLength="50" Label=@_localizer["LccNameLbl"]/>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Format="c0" Label=@_localizer["LccArvLbl"]   @bind-Value=@SelectedLcc.ReplacementValue/>
                                            </div>
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Label=@_localizer["LccNoOfYearsLbl"]   @bind-Value=@SelectedLcc.MaxYears/>
                                            </div>
                                            <div class="col-sm-6">
                                                @if (SelectedLcc.PartOfLcc != null)
                                                {
                                                    <AMproverTextBox Disabled=true @bind-Value=@(SelectedLcc.PartOfLcc.Name) Label=@_localizer["LccPartOfLbl"]/>
                                                }
                                                else
                                                {
                                                    <AMproverTextBox Disabled=true Label=@_localizer["LccPartOfLbl"]/>
                                                }
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Label=@_localizer["LccStartYearLbl"]   @bind-Value=@SelectedLcc.StartYear Min=0/>
                                            </div>
                                            <div class="col-sm-3">
                                                @*<AMproverNumberInput Label=@_localizer["LccAgeLbl"] @bind-Value=@SelectedLcc.Age Min=0/>*@
                                            </div>
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Format="P2" Label=@_localizer["LccDiscountRateLbl"]   @bind-Value=@SelectedLcc.DiscountRate/>
                                            </div>
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Format="c0" Label=@_localizer["LccProdCostLbl"]   @bind-Value=@SelectedLcc.ProductionCost/>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <AMproverTextArea @bind-Value=@SelectedLcc.Remark Cols="30" Rows="3" Label=@_localizer["LccRemarksLbl"]/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-4">
                                        <div class="overview-frame mr-3 pt-3 px-4 pb-3">
                                            <text class="large-bold"> @_localizer["LccTotTotal"]</text>
                                            <div>
                                                <text class=""> @_localizer["LccTotAverageCostLbl"]:</text>
                                                <text class="currency-bold"> @FormatDecimalAsSelectedCurrency(SelectedLcc.TotalAverageCost)</text>
                                            </div>
                                            <div>
                                                <text class=""> @_localizer["LccAverageOptCostLbl"]:</text>
                                                <text class="currency-bold"> @FormatDecimalAsSelectedCurrency(SelectedLcc.AverageOptimalCost)</text>
                                            </div>
                                            <div>
                                                <text class=""> @_localizer["LccOptimizationPotLbl"]:</text>
                                                <text class="currency-bold"> @FormatDecimalAsSelectedCurrency(SelectedLcc.Potential)</text>
                                            </div>
                                            <div>
                                                <text class=""> @_localizer["LccMcRavLbl"]:</text>
                                                <text class="currency-bold"> @FormatDecimalAsSelectedCurrency(SelectedLcc.McRav)</text>
                                            </div>
                                            <br/>
                                            <text class="large-bold"> @_localizer["LccLifeTime"]</text>
                                            @if (SelectedLcc.StartYear >= 1100) //In this case year allocation is used
                                            {
                                                <div>
                                                    <text class=""> @GetOptLifeTime() </text>
                                                    <text class="currency-bold"> @FormatAsNumber(SelectedLcc.Npvyear)</text>
                                                </div>
                                            }
                                            else
                                            {
                                                <div>
                                                    <text class=""> @_localizer["LccOptLifeTimeUnitTxt"]:</text>
                                                    <text class="currency-bold"> @FormatAsNumber(SelectedLcc.Npvyear)</text>
                                                </div>
                                            }
                                            <div>
                                                <text class=""> @_localizer["LccNpvLbl"]:</text>
                                                <text class="currency-bold"> @FormatDecimalAsSelectedCurrency(SelectedLcc.Npv)</text>
                                            </div>
                                            <div>
                                                <text class=""> @_localizer["LccAecLbl"]:</text>
                                                <text class="currency-bold"> @FormatDecimalAsSelectedCurrency(SelectedLcc.Aec)</text>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br/>
                            </EditForm>
                        }
                    </RadzenTabsItem>
                </Tabs>
            </RadzenTabs>
        </div>
    </div>
    <div class="row">
        <RadzenTabs @ref=@LccTabs style="width: 100%">
            <Tabs>
                <RadzenTabsItem Text=@_localizer["LccOptLifeTimeLbl"]>
                    @if (SelectedLcc?.OptimalLifeTimeGraph != null)
                    {
                        <h4>LCC - @_localizer["LccOptLifeTimeLbl"]</h4>
                        <RadzenChart @ref="@OptimalLifeTimeGraph">
                            <RadzenChartTooltipOptions/>
                            @foreach (var line in SelectedLcc.OptimalLifeTimeGraph.Items)
                            {
                                if (line.IsOptimalYear)
                                {
                                    <RadzenAreaSeries Fill="@line.Color" Stroke="@line.Color" Smooth="false" Data="@line.Data" CategoryProperty="XAxis"
                                                      Title=@_localizer[line.Legend] LineType="LineType.Solid" ValueProperty="Max"/>
                                }
                                else if (line.Stacked && LccExclude)
                                {
                                    <RadzenStackedColumnSeries Data="@line.Data" Fill="@line.Color" CategoryProperty="XAxis" Title=@_localizer[line.Legend] ValueProperty="YAxis"/>
                                }
                                else if ((!line.Stacked && LccExclude && line.IsPmo) || (!line.Stacked && !line.IsPmo))
                                {
                                    <RadzenLineSeries Stroke="@line.Color" Smooth="false" Data="@line.Data" CategoryProperty="XAxis"
                                                      Title=@_localizer[line.Legend] LineType="@(line.DashedLine ? LineType.Dashed : LineType.Solid)" ValueProperty="YAxis">
                                        <RadzenMarkers Fill="@line.Color"/>
                                    </RadzenLineSeries>
                                }
                            }
                            <RadzenColumnOptions Radius="1" Width="8" Margin="2"/>
                            <RadzenValueAxis Formatter=@FormatAsSelectedCurrency Max=@SelectedLcc.OptimalLifeTimeGraph.Max>
                                <RadzenGridLines Visible="true"/>
                                <RadzenAxisTitle Text=@_localizer["LccAmountLbl"]/>
                            </RadzenValueAxis>
                            <RadzenCategoryAxis Padding="5">
                                <RadzenGridLines Visible="true"/>
                                <RadzenAxisTitle Text=@_localizer["LccYearsLbl"]/>
                            </RadzenCategoryAxis>

                        </RadzenChart>
                        <Radzen.Blazor.RadzenCheckBox TValue="bool" @bind-Value=@LccExclude Visible=@ShowPmoCheckbox/>
                        if (ShowPmoCheckbox) @_localizer["LccShowPmoLbl"]
                    }

                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["LccRealExpTab"]>

                    @if (SelectedLcc?.RealExpenditureGraph != null)
                    {
                        <h4>LCC - @_localizer["LccRealExpTab"]</h4>
                        <RadzenChart @ref="@RealExpenditureGraph">
                            <RadzenChartTooltipOptions/>
                            @foreach (var line in SelectedLcc.RealExpenditureGraph.Items)
                            {
                                if (line.IsOptimalYear)
                                {
                                    <RadzenAreaSeries Fill="@line.Color" Stroke="@line.Color" Smooth="false" Data="@line.Data" CategoryProperty="XAxis"
                                                      Title=@_localizer[line.Legend] LineType="LineType.Solid" ValueProperty="Max"/>
                                }
                                else
                                {
                                    <RadzenLineSeries Stroke="@line.Color" Smooth="false" Data="@line.Data" CategoryProperty="XAxis"
                                                      Title=@_localizer[line.Legend] LineType="@(line.DashedLine ? LineType.Dashed : LineType.Solid)" ValueProperty="YAxis"/>
                                }
                            }
                            <RadzenValueAxis Formatter=@FormatAsSelectedCurrency Max=@SelectedLcc.RealExpenditureGraph.Max>
                                <RadzenGridLines Visible="true"/>
                                <RadzenAxisTitle Text=@_localizer["LccAmountLbl"]/>
                            </RadzenValueAxis>
                            <RadzenCategoryAxis>
                                <RadzenGridLines Visible="true"/>
                                <RadzenAxisTitle Text=@_localizer["LccYearsLbl"]/>
                            </RadzenCategoryAxis>
                        </RadzenChart>
                    }
                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["LccRealCostTab"]>
                    <div class="row">
                        <div class="col-sm-12">

                            <!-- Optimal Costs on LCC Grid -->
                            <UtilityGrid TItem=LccDetailModel
                                         @ref=RealCostsGrid
                                         Data=SelectedLcc.Details
                                         FileName=@GridNames.Lcc.RealCosts
                                         RowSelectCallBack=@(args => SetSelectedLcc((LccDetailModel) args))
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         MaxRows=10/>
                        </div>
                    </div>
                    <br/>
                    @if (SelectedLccDetail?.EffectDetails != null)
                    {
                        <div class="row">
                            <div class="col-sm-12">

                                <!-- Real Costs Effect on LCC Grid -->
                                <UtilityGrid TItem=LccEffectDetailModel
                                             @ref=RealCostEffectDetailsGrid
                                             Data=SelectedLccDetail.EffectDetails
                                             FileName=@GridNames.Lcc.RealCostEffect
                                             AllowXlsExport=true
                                             AllowFiltering=true
                                             MaxRows=10/>
                            </div>
                        </div>
                    }
                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["LccOptCostTab"]>
                    <div class="row">
                        <div class="col-sm-12">

                            <!-- Optimal Costs on LCC Grid -->
                            <UtilityGrid TItem=LccDetailModel
                                         @ref=OptimalCostsGrid
                                         Data=SelectedLcc.Details
                                         FileName=@GridNames.Lcc.OptimalCost
                                         RowSelectCallBack=@(args => SetSelectedLcc((LccDetailModel) args))
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         MaxRows=10/>
                        </div>
                    </div>
                    <br/>
                    @if (SelectedLccDetail?.EffectDetails != null)
                    {
                        <div class="row">
                            <div class="col-sm-12">

                                <!-- Optimal Costs Effect on LCC Grid -->
                                <UtilityGrid TItem=LccEffectDetailModel
                                             @ref=OptimalCostEffectDetailsGrid
                                             Data=SelectedLccDetail.EffectDetails
                                             FileName=@GridNames.Lcc.OptimalCostEffect
                                             AllowXlsExport=true
                                             AllowFiltering=true
                                             MaxRows=10/>
                            </div>
                        </div>
                    }
                </RadzenTabsItem>
                @if (@ShowPmoCheckbox)
                {
                    <RadzenTabsItem Text=@_localizer["LccOptCostPmoTab"]>
                        <div class="row">
                            <div class="col-sm-12">

                                <!-- Optimal Costs on LCC Grid -->
                                <UtilityGrid TItem=LccDetailModel
                                             @ref=OptimalCostsGridPmo
                                             Data=SelectedLcc.Details
                                             FileName=@GridNames.Lcc.OptimalCostPmo
                                             RowSelectCallBack=@(args => SetSelectedLcc((LccDetailModel) args))
                                             AllowXlsExport=true
                                             AllowFiltering=true
                                             MaxRows=10/>
                            </div>
                        </div>
                        <br/>
                        @if (SelectedLccDetail?.EffectDetails != null)
                        {
                            <div class="row">
                                <div class="col-sm-12">

                                    <!-- Optimal Costs Effect on LCC Grid -->
                                    <UtilityGrid TItem=LccEffectDetailModel
                                                 @ref=OptimalCostEffectDetailsGridPmo
                                                 Data=SelectedLccDetail.EffectDetails
                                                 FileName=@GridNames.Lcc.OptimalCostEffectPmo
                                                 AllowXlsExport=true
                                                 AllowFiltering=true
                                                 MaxRows=10/>
                                </div>
                            </div>
                        }
                    </RadzenTabsItem>
                }
                <RadzenTabsItem Text=@_localizer["LccActionsTab"]>
                    <div class="row">
                        <div class="col-sm-12">
                            <!-- Tasks on LCC Grid -->
                            <!-- .Where(x => x.Pmo == _Pmo) -->
                            <UtilityGrid TItem=ModelTask
                                         @ref=TasksGrid
                                         Data=@(SelectedLcc.Tasks.ToList())
                                         FileName=@GridNames.Lcc.PreventiveActions
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         Interactive=true
                                         MaxRows=50
                                         EditCallBackOverride=@(args => OpenRisksPage((ModelTask) args))
                                         DropDownOverrides=@TaskDropDownOverrides/>
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["LccRisksTab"]>
                    <div class="row">
                        <div class="col-sm-12">
                            <!-- Risks on LCC Grid -->
                            <UtilityGrid TItem=ModelRisk
                                         @ref=RisksGrid
                                         Data=SelectedLcc.Risks.ToList()
                                         FileName=@GridNames.Lcc.Risks
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         Interactive=true
                                         MaxRows=50
                                         EditCallBackOverride=@(args => OpenRisksPage((ModelRisk) args))
                                         DropDownOverrides=@RiskDropDownOverrides/>
                        </div>
                    </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
}