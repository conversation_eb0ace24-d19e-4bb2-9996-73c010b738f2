@inherits ComponentBase

<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
            Home
        </NavLink>
        <NavLink class="breadcrumb-item" href="/portfolio" Match="NavLinkMatch.All">
            Portfolio Setup
        </NavLink>
        @if (!string.IsNullOrWhiteSpace(SectionRelativeUrl))
        {
            <NavLink class="breadcrumb-item" href="@SectionRelativeUrl" Match="NavLinkMatch.All">
                @SectionName
            </NavLink>
            if (!string.IsNullOrWhiteSpace(PageName))
            {
                <NavLink class="breadcrumb-item" aria-current="page" href="@($"{@SectionRelativeUrl}{ @PageRelativeUrl}")" Match="NavLinkMatch.All">
                    @PageName
                </NavLink>

                if (!string.IsNullOrWhiteSpace(ActionName))
                {
                    <NavLink class="breadcrumb-item" aria-current="page" href="@($"{@SectionRelativeUrl}{@PageRelativeUrl}/{ItemId}")" Match="NavLinkMatch.All">
                        @ActionName
                    </NavLink>
                }
            }
        }
    </ol>
</nav>
