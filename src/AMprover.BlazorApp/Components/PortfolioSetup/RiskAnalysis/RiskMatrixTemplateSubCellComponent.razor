@page "/libraries/{MatrixId:int}/rows/{Row:int}/columns/{Column:int}/rows/{SubRow:int}/columns/{SubColumn:int}"

@inherits BaseDialogComponent
<EditForm Model="@RiskMatrixTemplate" OnValidSubmit=@ValidFormSubmitted OnInvalidSubmit=@InvalidFormSubmitted>
    @if (Row > 0)
    {
        <h2>@Localizer["RmtCcCellProperties"]</h2>
    }
    else
    {
        <h4>@Localizer["RmtCcColProperties"]</h4>
    }
    <br/>
    <div class="row">
        <div class="col-sm-12">
            <div class="form-group">
                <label>@Localizer["RmtCcCellColor"]</label>
                <RadzenColorPicker @bind-Value=@SelectedCell.Color ShowHSV="false" ShowRGBA="false" Disabled=!AppState.CanEdit>

                    <!-- Grays -->
                    <RadzenColorPickerItem Value="FFFFFF" />
                    <RadzenColorPickerItem Value="EEEEEE" />
                    <RadzenColorPickerItem Value="EAEAEA" />
                    <RadzenColorPickerItem Value="E6E6E6" />
                    <RadzenColorPickerItem Value="DDDDDD" />
                    <RadzenColorPickerItem Value="C0C0C0" />
                    <RadzenColorPickerItem Value="B2B2B2" />
                    <RadzenColorPickerItem Value="808080" />
                    <RadzenColorPickerItem Value="606060" />
                    <RadzenColorPickerItem Value="404040" />
                    <RadzenColorPickerItem Value="000000" />

                    <!-- Yellow/Orange/Red -->
                    <RadzenColorPickerItem Value="FFFFCC" />
                    <RadzenColorPickerItem Value="FFFF99" />
                    <RadzenColorPickerItem Value="FFFF00" />
                    <RadzenColorPickerItem Value="FFCC99" />
                    <RadzenColorPickerItem Value="FFCC00" />
                    <RadzenColorPickerItem Value="FFA500" />
                    <RadzenColorPickerItem Value="FF9900" />
                    <RadzenColorPickerItem Value="FF9966" />
                    <RadzenColorPickerItem Value="FFCCCC" />
                    <RadzenColorPickerItem Value="FF3300" />
                    <RadzenColorPickerItem Value="CC0000" />
                    <RadzenColorPickerItem Value="800000" />

                    <!-- Purple -->

                    <RadzenColorPickerItem Value="FFCCFF" />
                    <RadzenColorPickerItem Value="FF66FF" />
                    <RadzenColorPickerItem Value="FF00FF" />
                    <RadzenColorPickerItem Value="CC00CC" />
                    <RadzenColorPickerItem Value="800080" />

                    <!-- Blue -->
                    <RadzenColorPickerItem Value="CCFFFF" />
                    <RadzenColorPickerItem Value="CCECFF" />
                    <RadzenColorPickerItem Value="99CCFF" />
                    <RadzenColorPickerItem Value="00FFFF" />
                    <RadzenColorPickerItem Value="008080" />
                    <RadzenColorPickerItem Value="3366FF" />
                    <RadzenColorPickerItem Value="0066FF" />
                    <RadzenColorPickerItem Value="0000FF" />
                    <RadzenColorPickerItem Value="000080" />

                    <!-- Green -->
                    <RadzenColorPickerItem Value="CCFFCC" />
                    <RadzenColorPickerItem Value="00FF00" />
                    <RadzenColorPickerItem Value="33CC33" />
                    <RadzenColorPickerItem Value="00CC00" />
                    <RadzenColorPickerItem Value="339933" />
                    <RadzenColorPickerItem Value="008000" />
                    <RadzenColorPickerItem Value="808000" />

                </RadzenColorPicker>
            </div>
        </div>
    </div>

    @if (IsEffectColumn || Row == 0)
    {
        <div class="row">
            <div class="col-sm-12">
                <div class="form-group">
                    <label>@Localizer["RmtCcDescription"]</label>
                    <AMproverTextBox class="form-control" @bind-Value=@SelectedCell.Description />
                </div>
            </div>
        </div>
    }

    @if (IsEffectColumn && Row > 0)
    {
        var column = RiskMatrixTemplate.MainGrid.TableColumns[Column];
        var numbertype = column.IsPercentage ? "P0" : "C0";
        
        <div class="row">
            <div class="col-sm-12">
                <AMproverNumberInput Min="0" Label=@Localizer["RmtCcImpactValue"] Format=@numbertype @bind-Value=@SelectedCell.Value />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <AMproverNumberInput Min="0" Label=@Localizer["RmtCcCustomValue"] Format="N4" @bind-Value=@SelectedCell.CustomValue />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <AMproverNumberInput Min="0" Label=@Localizer["RmtCcPoints"] Format="N0" class="form-control" @bind-Value=@SelectedCell.Points />
            </div>
        </div>
    }

    @if (!IsEffectColumn && Row == 0)
    {
        <div class="row">
            <div class="col-sm-12">
                <AMproverNumberInput Min="0" Label=@Localizer["RmtCcMtbf"] Format="N4" @bind-Value=@SelectedCell.CustomValue />
            </div>
        </div>
    }

    @if (IsEffectColumn && Row == 0)
    {
        <div class="row">
            <div class="col-sm-12">
                <AMproverTextBox Label=@Localizer["RmtCcSecValueEntity"] @bind-Value=@SelectedColumn.Entity />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <AMDropdown Label=@Localizer["RmtCcCorCostCalc"]
                    @bind-Value=@SelectedColumn.Setting
                    Data=CorrectiveCostOptions
                    AllowClear=true />
            </div>
        </div>
    }
    <br />
    @if (SetTypesToColumn && Row == 0)
    {
        <div class="row mt-35">
            <div class="col-sm-6">
                <AMDropdown @bind-Value=@SelectedColumn.Type
                        Data=ColumnType
                        AllowClear=true />
            </div>
            <div class="col-sm-5">
                @Localizer["RmtCcSetSheToColumn"]
            </div>
        </div>
    }

    <br/>
    <RadzenButton type="submit" Disabled=!AppState.CanEdit class="btn btn-secondary" Text="Save" />
</EditForm>

