using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Components.PortfolioSetup.RiskAnalysis;

public partial class RiskMatrixTemplateCellComponent
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IStringLocalizer<RiskMatrixTemplateCellComponent> Localizer { get; set; }
    
    [Parameter] public int MatrixId { get; set; }
    [Parameter] public int Row { get; set; }
    [Parameter] public int Column { get; set; }
    [Parameter] public Action CallBack { get; set; }

    private List<LookupSettingModel> LookupSettings { get; set; }
    private LookupSettingModel EnableColumnsForTypes { get; set; }

    private bool IsEffectColumn { get; set; }
    private bool SetTypesToColumn { get; set; }

    private RiskMatrixTemplateModel RiskMatrixTemplate { get; set; }
    private RiskMatrixTemplateCell SelectedCell { get; set; }
    private RiskMatrixTemplateColumn SelectedColumn { get; set; }

    public Dictionary<int, string> CorrectiveCostOptions { get; set; }
        = new()
        {
            {1, "Technical cost"},
            {2, "Circuit affected cost"},
            {3, "Normal"}
        };

    public Dictionary<int, string> ColumnType { get; set; }
        = new()
        {
            {1, "Percentage - Sub Column Max."}, //Only Main Column
            {2, "Percentage - Sub Column Prod."}, //Only Main Column
            {3, "Labda (%) - Sub Column Max."}, //Only Main Column
            //{4, "SHE Column"}, // NB. Only available for sub columns
            {5, "CBI (%) - Sub Column Prod."} //Only Main Column
        };

    protected override async Task OnInitializedAsync()
    {
        try
        {
            RiskMatrixTemplate = await RiskAnalysisSetupManager.GetTemplateAsync(MatrixId);

            if (RiskMatrixTemplate == null)
            {
                DialogService.Close();
                return;
            }

            if (RiskMatrixTemplate.MainGrid?.TableContent == null || 
                Row >= RiskMatrixTemplate.MainGrid.TableContent.Count || 
                Column >= RiskMatrixTemplate.MainGrid.TableContent[Row].Cells.Count)
            {
                DialogService.Close();
                return;
            }

            SelectedCell = RiskMatrixTemplate.MainGrid.TableContent[Row].Cells[Column];

            // Decimals are not allowed in the matrices. 1 Exception is the MTBF
            // allow setting to decimals only for header rows to enable this
            if (Row == 0)
                SelectedCell.AllowDecimals = true;

            if (RiskMatrixTemplate.MainGrid?.TableColumns == null || Column >= RiskMatrixTemplate.MainGrid.TableColumns.Count)
            {
                DialogService.Close();
                return;
            }

            var column = RiskMatrixTemplate.MainGrid.TableColumns[Column];
            IsEffectColumn = column.IsEffectColumn;

            //To Enable the SAPA module, it's necessary to use percentages in the Matrix. Herfore the boolean is used
            LookupSettings = await LookupManager.GetLookupSettingsAsync();
            EnableColumnsForTypes =
                LookupSettings.FirstOrDefault(x =>
                    x.Property.Equals(PropertyNames.EnableColumnsForTypes, StringComparison.OrdinalIgnoreCase)) ??
                new LookupSettingModel {Property = PropertyNames.EnableColumnsForTypes};
            SetTypesToColumn = EnableColumnsForTypes.IntValue == 1;
            SelectedColumn = column;
        }
        catch (Exception ex)
        {
            // Log the exception
            var logger = LoggerFactory.CreateLogger<RiskMatrixTemplateCellComponent>();
            logger.LogError(ex, "Error initializing RiskMatrixTemplateCellComponent");

            // Close the dialog
            DialogService.Close();
        }
    }

    private async Task ValidFormSubmitted(EditContext editContext)
    {
        var model = (RiskMatrixTemplateModel) editContext.Model;

        //Remove subgrids
        if (!SelectedColumn.HasSubColumns)
        {
            model.SubGrids.RemoveAll(x => x.ColumnId == Column);
        }

        await RiskAnalysisSetupManager.UpdateTemplateAsync(model);
        CallBack?.Invoke();
        DialogService.Close();
    }

    private static void InvalidFormSubmitted(EditContext editContext)
    {
        //Throw error message.
    }
}
