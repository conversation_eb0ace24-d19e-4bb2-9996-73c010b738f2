using Microsoft.AspNetCore.Components;
using AMprover.BusinessLogic;

namespace AMprover.BlazorApp.Components;

public abstract class BaseLayout : LayoutComponentBase
{
    [Inject] public AppState AppState { get; set; }

    protected string SideMenuClass { get; set; }

    protected void ActivateMenu()
    {
        AppState.SiteMenuOpen = !AppState.SiteMenuOpen;
        SideMenuClass = AppState.SiteMenuOpen ? "active" : string.Empty;
    }
}