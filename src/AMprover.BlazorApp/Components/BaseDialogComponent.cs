using System;
using System.ComponentModel;
using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using <PERSON><PERSON>zen;

namespace AMprover.BlazorApp.Components;

public abstract class BaseDialogComponent : ComponentBase, IDisposable
{
    [Inject] protected AppState AppState { get; set; } 
    [Inject] protected DialogService DialogService { get; set; } 

    protected override void OnInitialized()
    {
        if (AppState != null)
        {
            AppState.PropertyChanged += OnAppStateChanged;
        }
        base.OnInitialized();
    }

    protected virtual async void OnAppStateChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName is nameof(AppState.CanEdit) or nameof(AppState.UserRole) or nameof(AppState.Initialized))
        {
            await InvokeAsync(StateHasChanged);
        }
    }

    protected void CloseDialog(object result = null)
    {
        DialogService.Close(result);
    }

    public virtual void Dispose()
    {
        if (AppState != null)
        {
            AppState.PropertyChanged -= OnAppStateChanged;
        }
    }
}