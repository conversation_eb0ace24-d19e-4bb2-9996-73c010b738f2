<div class="paginator-container">
    <div class="row">
        <div class="col-3">
        </div>

        <div class="col-6">
            <div class="row">
                <ul class="pagination">
                    <li class="page-item">
                        <a class="page-link" @onclick=@First @onclick:preventDefault>
                            <i class="fas fa-angle-double-left"/>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" @onclick=@Previous @onclick:preventDefault>
                            <i class="fas fa-angle-left"/>
                        </a>
                    </li>
                    <li class="pagination-input">
                        <RadzenNumeric TValue="int" Value=@GetCurrentDisplayIndex() 
                                       ValueChanged=@(x => SetFromInput(x))
                                       Min=1
                                       Max=@Count
                                       ShowUpDown="false"
                                       class="text-box-width" />
                    </li>
                    <li class="page-item">
                        <a class="page-link">/</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link">@Count.ToString()</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" @onclick=@Next @onclick:preventDefault>
                            <i class="fas fa-angle-right"/>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" @onclick=@Last @onclick:preventDefault>
                            <i class="fas fa-angle-double-right"/>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
