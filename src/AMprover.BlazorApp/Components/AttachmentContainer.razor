<div class="attachment-container">
    <div class="attachment-new-container">

        <h3 style="display:inline-block">Attachments</h3>
        @if (_editAttachment == null)
        {
            <RadzenButton Disabled="ReadOnly" Text="Add" Icon="add_circle_outline" Click="AddItem"
                          Style="float:right;margin-right:35px;"/>
        }

    </div>

    @if (Data?.Count > 0)
    {
        <ul class="attachment-list">
            @foreach (var attachment in Data)
            {
                <li>
                    @if (attachment == _editAttachment)
                    {
                        <Radzen.Blazor.RadzenTemplateForm TItem="AttachmentModel" Data=@attachment Submit=@Save>

                            <div class="row">
                                <div class="col-12">
                                    <RadzenButton class="btn-centered mt-2 mr-2" Text="Save" Disabled="ReadOnly"
                                                  Icon="Save" type="submit" ButtonStyle="ButtonStyle.Secondary"/>
                                    <RadzenButton class="btn-centered mt-2 ml-1 mr-2" Text="Cancel" Icon="Cancel"
                                                  ButtonStyle=ButtonStyle.Warning
                                                  Click=@(() => CancelEditItem(attachment))/>
                                    <RadzenButton class="btn-centered mt-2 mb-4 ml-1 mr-2" Text="Delete"
                                                  Disabled="ReadOnly" Icon="Delete" ButtonStyle=ButtonStyle.Danger
                                                  Click=@(() => DeleteItem(attachment))/>
                                </div>
                                <div class="col-6">
                                    <AMproverTextBox Label="Title" Required=true @bind-Value=@attachment.Title/>
                                </div>
                                <div class="col-6">
                                    <AMproverTextBox Label="Url" Required=true @bind-Value=@attachment.Uri/>
                                </div>
                                <div class="col-6">
                                    <AMproverDictionaryDropdown Label="Category" Data=@Categories
                                                                @bind-Value=@attachment.CatgoryId/>
                                </div>
                                <div class="col-6">
                                    <AMproverTextBox Label="Description" @bind-Value=@attachment.Description/>
                                </div>
                            </div>

                        </Radzen.Blazor.RadzenTemplateForm>
                    }
                    else
                    {
                        <div class="row">
                            <div class="col-4">
                                <RadzenButton Icon="Edit" class="mr-3" Click=@(() => EditItem(attachment))/>
                                <a title="@attachment.Description" href="@attachment.Uri"
                                   target="_blank">@attachment.Title</a>
                            </div>
                            <div class="col-3 pt-2">
                                <span><b>@GetCategory(attachment)</b></span>
                            </div>
                            <div class="col-5 pt-2">
                                <span>@attachment.Description</span>
                            </div>
                        </div>
                    }
                </li>
            }
        </ul>
    }
</div>

