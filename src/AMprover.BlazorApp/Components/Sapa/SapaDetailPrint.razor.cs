using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Sapa;
using AutoMapper;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;

namespace AMprover.BlazorApp.Components.Sapa;

public partial class SapaDetailPrint
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private ISapaOverviewManager SapaOverviewManager { get; set; }
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private IObjectManager ObjectManager { get; set; }
    [Inject] private IMapper Mapper { get; set; }
    [Inject] private LocalizationHelper LocalizationHelper { get; set; }
    [Inject] private IStringLocalizer<SapaDetailPrint> Localizer { get; set; }
    [Inject] private ILogger<SapaDetailPrint> Logger { get; set; }
    [Inject] public AppState AppState { get; set; }

    [Parameter] public int SapaDetailId { get; set; }
    [Parameter] public EventCallback<SapaDetailModel> Callback { get; set; }

    private SapaDetailModel SapaDetail { get; set; }
    private RiskModel Risk { get; set; }
    public UtilityGrid<TaskModel> PreventiveActionsGrid { get; set; }
    public UtilityGrid<SpareModel> SparesGrid { get; set; }
    private Dictionary<string, Dictionary<int, string>> TaskModelDropDownOverrides { get; set; }

    private string Currency { get; set; }

    protected override async Task OnInitializedAsync()
    {
        SapaDetail = await SapaOverviewManager.GetSapaDetailAsync(SapaDetailId);
        Risk = await RiskAnalysisManager.GetRiskAsync(SapaDetail.RiskId);
        TaskModelDropDownOverrides = await DropdownManager.GetTaskModelDropDownOverridesAsync();
        Currency = await LookupManager.GetCurrencyAsync();
        await base.OnInitializedAsync();
    }

    private string FormatAsSelectedCurrency(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
    }

    private string FormatAsNumber(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("N0", CultureInfo.CreateSpecificCulture(Currency));
    }

    private static void UpdateCurrentRisk()
    {
        //read only, no updates
    }
}
