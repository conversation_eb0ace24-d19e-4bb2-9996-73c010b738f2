using System.Collections.Generic;
using Microsoft.AspNetCore.Components;
using <PERSON><PERSON><PERSON>;

namespace AMprover.BlazorApp.Components;

public partial class NotificationDialog
{
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }

    [Parameter] public bool ReloadPageOnClose { get; set; }
    [Parameter] public List<string> Texts { get; set; }

    public string Ok => "Ok";

    private void CloseNotification()
    {
        DialogService.Close();

        if (ReloadPageOnClose)
            NavigationManager.NavigateTo(NavigationManager.Uri, true);
    }
}