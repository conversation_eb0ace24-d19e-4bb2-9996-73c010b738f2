<AuthorizeView>
    <Authorized>
        @if (PortfoliosForUser is {Count: > 1 })
        {
            <Radzen.Blazor.RadzenDropDown TValue="int" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" @bind-Value="SelectedPortfolioId" Data="@PortfoliosForUser"
                                          TextProperty="@nameof(SwitchItem.Name)" ValueProperty="@nameof(SwitchItem.Id)"
                                          Change="@(args => Switch())" />
        }
    </Authorized>
    <NotAuthorized>
    </NotAuthorized>
</AuthorizeView>
