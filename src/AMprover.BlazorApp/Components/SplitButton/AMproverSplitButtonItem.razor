@inherits RadzenComponent
@if (Visible)
{
<li class="rz-menuitem" role="menuitem" @onclick="@OnClick" @attributes="Attributes" style="@Style">
    <a class="rz-menuitem-link">
        @if (!string.IsNullOrEmpty(Icon))
        {
            <span class="rz-menuitem-icon">@((MarkupString)Icon)</span>
        }
         @if (!string.IsNullOrEmpty(Image))
        {
            <img class="rz-navigation-item-icon" src="@Image" />
        }
        @if (!string.IsNullOrEmpty(Text))
        {
            <span class="rz-menuitem-text">@Text</span>
        }
    </a>
</li>
}

