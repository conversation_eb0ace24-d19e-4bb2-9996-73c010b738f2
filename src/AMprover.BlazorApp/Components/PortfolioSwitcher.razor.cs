using System.Collections.Generic;
using System.Threading.Tasks;
using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Components;

public partial class PortfolioSwitcher
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IPortfolioManager PortfolioManager { get; set; }
    [Inject] private ILogger<PortfolioSwitcher> Logger { get; set; }

    private List<SwitchItem> PortfoliosForUser { get; set; }

    public int SelectedPortfolioId { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var userPortfolios = await PortfolioManager.GetPortfoliosForLoggedInUserAsync();

        // Avoid null references
        PortfoliosForUser ??= [];

        if (PortfoliosForUser.Count > 0) 
            return;
            
        foreach (var port in userPortfolios)
        {
            PortfoliosForUser.Add(new SwitchItem { Id = port.Id, Name = port.Name });
        }
        var currentPortfolio = await PortfolioManager.GetCurrentPortfolioAsync();
        SelectedPortfolioId = currentPortfolio.Id;
    }

    private async Task Switch()
    {
        await PortfolioManager.SwitchPortfolioForLoggedInUserAsync(SelectedPortfolioId);
        // Database contexts are being connected via the SignalR circuit, and scoped
        // to such circuit. Only change the underlying data in the database will not
        // be reflecting while navigating to other pages. force a reload will get
        // a fresh circuit and thus new database contexts. 
        NavigationManager.NavigateTo("/", true);
    }

    public class SwitchItem
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }
}