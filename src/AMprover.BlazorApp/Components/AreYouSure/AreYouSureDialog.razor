@typeparam TItem
@inherits ComponentBase

<div>
    <p class="mb-4">@(GetText())</p>

    @if(Items != null && Items.Any())
    {
        <div class="row">
            <div class="col">
                <ul>
                @foreach(var item in Items)
                {
                    <li>@item</li>
                }
                </ul>
            </div>
        </div>
    }

    <div class="row">
        <div class="col">
            <RadzenButton Text="Yes" Click=YesButton class="mr-3"/>
            <RadzenButton Text="No" Click=NoButton ButtonStyle="ButtonStyle.Secondary" class="ml-3"/>
        </div>
    </div>
</div>
