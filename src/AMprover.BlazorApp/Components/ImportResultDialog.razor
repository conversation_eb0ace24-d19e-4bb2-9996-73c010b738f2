<div>
    <p>
        @GetText()
    </p>

    @if (ImportResult.IdsItemsFailed.Count > 0)
    {
        <p>The following lines failed: @string.Join(",", ImportResult.IdsItemsFailed).</p>
    }

    @if (!string.IsNullOrWhiteSpace(ImportResult.ErrorMessage))
    {
        <p>
            <small>@ImportResult.ErrorMessage</small>
        </p>

        <ul>
        @foreach(var detail in ImportResult.ErrorDetails)
        {
             <li>@detail</li>
        }
        </ul>
    }

    @if (ImportResult.Status == ImportStatus.Loading)
    {
        <RadzenProgressBar Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" Style="margin-bottom: 20px"/>
    }

    <div class="row">
        <div class="col-md-12">
            @if (ImportResult.Status != ImportStatus.Loading)
            {
                <RadzenButton Text="Ok" Click="() => DialogService.Close()" Style="margin-bottom: 10px; width: 150px" Disabled="false"/>
            }
        </div>
    </div>
</div>
