@using AMprover.BlazorApp.Helpers
@using AMprover.BusinessLogic.Enums.Rams

@if (Item.Results != null || Item.Type == RamsComponentType.PageBreak)
{
    if (Item.Type == RamsComponentType.Container)
    {
        <div @onclick:stopPropagation="true" @onclick:preventDefault="true">
            <div class="@(Item.Collapsed ? ((DisplayMode.ShowPfd || DisplayMode.ShowSil) ? "rams-container collapsed" : "rams-container collapsedsmall") : "rams-container") d-flex flex-column
            @(SelectedComponentId == Item.Id ? "selected" : "") order-@(Item.Order)"
                 @onclick=@(() => SelectItem(Item))>
                <h4 title="@Item.Title">
                    <span class=@(Item.Collapsed ? "fas fa-arrow-circle-up" : "fas fa-arrow-circle-down") @onclick=@(() => CollapseItem(Item)) @onclick:stopPropagation="true"
                          @onclick:preventDefault>
                    </span>
                    <b>@Item.Title</b>
                </h4>

                @if (!Item.Collapsed && Item.ParallelTracks > 1)
                {
                    <span class="xoon">@Item.XooN oo @Item.ParallelTracks</span>
                }

                @if (Item.Collapsed)
                {
                    <div class="left-sidebar">
                        <div class="rams-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    @if (DisplayMode.ShowLambda)
                                    {
                                        @if (DisplayMode.ScientificNotation)
                                        {
                                            <label>λ(t) = @(DisplayMode.ShowFunctional ? Item.Results.LabdaFunctional.ToString("0.0#e+0") : Item.Results.LabdaTechnical.ToString("0.0#e+0"))/y</label>
                                        }
                                        else
                                        {
                                            <label>λ(t) = @(DisplayMode.ShowFunctional ? Math.Round(Item.Results.LabdaFunctional, 3) : Math.Round(Item.Results.LabdaTechnical, 3))/y</label>
                                        }
                                    }
                                    else if (DisplayMode.ShowMtbf)
                                    {
                                        <label>
                                            MTBF = @(DisplayMode.ShowFunctional
                                                       ? RamsDisplayHelper.FormatMtbf(Item.Results.MtbfFunctional, DisplayMode.ShowYears, AvailableTime, Language)
                                                       : RamsDisplayHelper.FormatMtbf(Item.Results.MtbfTechnical, DisplayMode.ShowYears, AvailableTime, Language))
                                        </label>
                                    }
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12 txt-margin">
                                    @if (DisplayMode.ScientificNotation)
                                    {
                                        <label>R(t) = @(DisplayMode.ShowFunctional ? Item.Results.ReliabilityFunctional.ToString("0.0#e+0") : Item.Results.ReliabilityTechnical.ToString("0.0#e+0"))%</label>
                                    }
                                    else
                                    {
                                        <label>R(t) = @($"{(DisplayMode.ShowFunctional ? Math.Round(Item.Results.ReliabilityFunctional * 100, 2) : Math.Round(Item.Results.ReliabilityTechnical * 100, 2)):0.00}%")</label>
                                    }
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12 txt-margin">
                                    @if (Item.Results.AffectedBufferTime > 0)
                                    {
                                        <em>
                                            @if (DisplayMode.ScientificNotation)
                                            {
                                                <label>A = @(DisplayMode.ShowFunctional ? Item.Results.AvailFunctional.ToString("0.0#e+0") : Item.Results.AvailTechnical.ToString("0.0#e+0"))%</label>
                                            }
                                            else
                                            {
                                                <label>A = @($"{(DisplayMode.ShowFunctional ? Item.Results.AvailFunctional * 100 : Item.Results.AvailTechnical * 100):0.00}%")</label>
                                            }
                                        </em>
                                    }
                                    else
                                    {
                                        @if (DisplayMode.ScientificNotation)
                                        {
                                            <label>A = @(DisplayMode.ShowFunctional ? Item.Results.AvailFunctional.ToString("0.0#e+0") : Item.Results.AvailTechnical.ToString("0.0#e+0"))%</label>
                                        }
                                        else
                                        {
                                            <label>A = @($"{(DisplayMode.ShowFunctional ? Item.Results.AvailFunctional * 100 : Item.Results.AvailTechnical * 100):0.00}%")</label>
                                        }
                                    }
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12 txt-margin">
                                    @if (DisplayMode.ShowPfd)
                                    {
                                        <label>PFD = @Math.Round(Item.Results.PFD, 3)</label>
                                    }
                                    else if (DisplayMode.ShowSil)
                                    {
                                        <label>SIL = @(Item.Results.RamsSil ?? "N/A")</label>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                }

                @if (!Item.Collapsed)
                {
                    <table @ref="RecursiveTable" id=@Item.Id>
                        <tbody>
                        <RamsComponent Class=@(Item.Collapsed ? "flex-container collapsed" : "flex-container")
                                       Diagram=@Diagram
                                       Items=@Item.Parts
                                       ItemWrapperClass=@(_ => "flex-item")
                                       ComponentsReOrderCallback=@ComponentReOrderCallback
                                       AddItemsToNewContainerCallback=@AddItemsToNewContainerCallback
                                       RecalculateDiagramSize=@RecalculateDiagramSize
                                       RemoveLines=@RemoveLines
                                       DrawLines=@DrawLines
                                       RamsAllowedDept=@RamsAllowedDept>
                            <ChildContent Context="childItem">
                                <RamsDiagramRecursiveComponent AvailableTime=@AvailableTime
                                                               Language=@Language
                                                               Diagram=@Diagram
                                                               Item=@childItem
                                                               DisplayMode=@DisplayMode
                                                               NodeType=@Item.NodeType
                                                               ComponentCollapseCallback=@ComponentCollapseCallback
                                                               ComponentParallelCallback=@ComponentParallelCallback
                                                               ComponentSelectCallback=@ComponentSelectCallback
                                                               ComponentReOrderCallback=@ComponentReOrderCallback
                                                               AddItemsToNewContainerCallback=@AddItemsToNewContainerCallback
                                                               RecalculateDiagramSize=@RecalculateDiagramSize
                                                               RemoveLines=@RemoveLines
                                                               DrawLines=@DrawLines
                                                               RamsAllowedDept=@RamsAllowedDept
                                                               SelectedComponentId=@SelectedComponentId>
                                </RamsDiagramRecursiveComponent>
                            </ChildContent>
                        </RamsComponent>
                        </tbody>
                    </table>

                    <div class="rams-footer d-flex mt-auto justify-content-between">
                        <div @ref="ToolTip" class="rams-footer-content" @onmouseover="@(_ => ShowTooltipWithHtml())">
                            @GetCalculationsBasedOnDisplay()(null)
                        </div>
                    </div>
                }
            </div>
        </div>
    }
    else if (Item.Type == RamsComponentType.PageBreak)
    {
        <div @onclick:stopPropagation="true" @onclick:preventDefault="true">
            <div class="rams-page-break d-flex flex-column @(SelectedComponentId == Item.Id ? "selected" : "") order-@(Item.Order) page-break"
                 @onclick=@(() => SelectItem(Item))>
                <div class="page-break-content">
                    <h4 title="@Item.Title">
                        <i class="fas fa-cut"></i>
                        @Item.Title
                    </h4>
                    <div class="page-break-line"></div>
                    <small class="text-muted">Page Break</small>
                </div>
            </div>
        </div>
    }
    else if (Item.Type == RamsComponentType.Block)
    {
        <div @onclick:stopPropagation="true" @onclick:preventDefault="true">
            <div class="rams-block d-flex flex-column @(SelectedComponentId == Item.Id ? "selected" : "") order-@(Item.Order) @(Item.Classification) @(Item.RiskId != null ? "riskblock" : "")"
                 @onclick=@(() => SelectItem(Item))>
                <div class="left-link-item @(Item.RiskId != null ? "linkedtorisk" : "") @(Item.DiagramId != null ? "linkedtodiagram" : "")">
                    <h4 title="@Item.Title">@Item.Title</h4>
                </div>
                <div class="left-sidebar @(Item.Status.ToString().ToLowerInvariant())">
                    <div class="rams-body">
                        <div class="row">
                            <div class="col-sm-12 neg-margin-small">
                                @if (DisplayMode.ShowLambda)
                                {
                                    @if (DisplayMode.ScientificNotation)
                                    {
                                        <label>λ(t) = @(DisplayMode.ShowFunctional ? Item.Results.LabdaFunctional.ToString("0.0#e+0") : Item.Results.LabdaTechnical.ToString("0.0#e+0"))/y</label>
                                    }
                                    else
                                    {
                                        <label>λ(t) = @(DisplayMode.ShowFunctional ? Item.Results.LabdaFunctional.FormatRoundedValue(Language, 3) : Item.Results.LabdaTechnical.FormatRoundedValue(Language, 3))/y</label>
                                    }
                                }
                                else if (DisplayMode.ShowMtbf)
                                {
                                    <label>
                                        MTBF = @(DisplayMode.ShowFunctional
                                                   ? RamsDisplayHelper.FormatMtbf(Item.Results.MtbfFunctional, DisplayMode.ShowYears, AvailableTime, Language)
                                                   : RamsDisplayHelper.FormatMtbf(Item.Results.MtbfTechnical, DisplayMode.ShowYears, AvailableTime, Language))
                                    </label>
                                }
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 txt-margin">
                                @if (DisplayMode.ScientificNotation)
                                {
                                    <label>R(t) = @(DisplayMode.ShowFunctional ? Item.Results.ReliabilityFunctional.ToString("0.0#e+0") : Item.Results.ReliabilityTechnical.ToString("0.0#e+0"))%</label>
                                }
                                else
                                {
                                    <label>R(t) = @($"{(DisplayMode.ShowFunctional ? (Item.Results.ReliabilityFunctional * 100).FormatRoundedValue(Language, 2) : (Item.Results.ReliabilityTechnical * 100).FormatRoundedValue(Language, 2))}%")</label>
                                }
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 txt-margin">
                                @if (DisplayMode.ScientificNotation)
                                {
                                    <label>A = @(DisplayMode.ShowFunctional ? Item.Results.AvailFunctional.ToString("0.0#e+0") : Item.Results.AvailTechnical.ToString("0.0#e+0"))%</label>
                                }
                                else
                                {
                                    <label>A = @($"{(DisplayMode.ShowFunctional ? (Item.Results.AvailFunctional * 100).FormatRoundedValue(Language, 2) : (Item.Results.AvailTechnical * 100).FormatRoundedValue(Language, 2))}%")</label>
                                }
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 txt-margin">
                                @if (DisplayMode.ShowPfd)
                                {
                                    <label>PFD = @Math.Round(Item.Results.PFD, 3)</label>
                                }
                                else if (DisplayMode.ShowSil)
                                {
                                    <label>SIL = @(Item.Results.RamsSil ?? "N/A")</label>
                                }
                            </div>
                        </div>
                        <div class="rams-footer">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
}
