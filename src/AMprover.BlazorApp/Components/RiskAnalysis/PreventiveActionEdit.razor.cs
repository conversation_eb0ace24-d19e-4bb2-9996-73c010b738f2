using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Enums;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AutoMapper;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Radzen;

namespace AMprover.BlazorApp.Components.RiskAnalysis;

public partial class PreventiveActionEdit : BaseDialogComponent
{
    #region Injected Services

    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; } 
    [Inject] private IPortfolioSetupManager PortfolioSetupManager { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IMapper Mapper { get; set; } 
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private IObjectManager ObjectManager { get; set; } 
    [Inject] private ILogger<PreventiveActionEdit> Logger { get; set; } 
    [Inject] private IStringLocalizer<PreventiveActionEdit> Localizer { get; set; } 

    #endregion

    #region Parameters

    [Parameter] public int PreventiveActionId { get; set; }
    [Parameter] public int? RiskId { get; set; }
    [Parameter] public bool IsClusterTask { get; set; }
    [Parameter] public bool Pmo { get; set; }
    [Parameter] public bool IsSapaTask { get; set; }
    [Parameter] public EventCallback<TaskModel> Callback { get; set; }

    #endregion

    #region Properties

    public TaskModel PreventiveAction { get; set; }
    private RiskModel Risk { get; set; }
    private ObjectModel ObjectModel { get; set; }
    private bool ShowFailedValidations { get; set; }
    public bool IsSapaView { get; set; }

    #endregion

    #region Dropdown Data

    private Dictionary<int, string> ExecutorsDict { get; set; } = new();
    private Dictionary<int, string> InitiatorsDict { get; set; } = new();
    private Dictionary<int, string> WorkPackageDict { get; set; } = new();
    private Dictionary<int, string> SapaWorkPackageDict { get; set; } = new();
    private Dictionary<int, string> IntervalUnitsDict { get; set; } = new();
    public Dictionary<int, string> ClusterDict { get; set; } = new();
    private Dictionary<int, string> PolicyDict { get; set; } = new();
    private Dictionary<int, string> UnitTypeDict { get; set; } = new();
    private Dictionary<string, string> TaskDict { get; set; } = new();
    public Dictionary<int, string> TaskOnRiskDict { get; set; } = new();
    private Dictionary<int, string> RiskDict { get; set; } = new();
    private Dictionary<int, string> CommonActionDict { get; set; } = new();
    private Dictionary<int?, string> PartOfDict { get; set; } = new();
    private Dictionary<int, string> StatusDict { get; set; } = new();

    #endregion

    #region Disable Flags

    private bool DisableReferenceId { get; set; }
    private bool DisableExecutor { get; set; }
    private bool DisableInitiator { get; set; }
    private bool DisableWorkPackage { get; set; }
    private bool DisableIntervalUnit { get; set; }
    private bool DisableCosts { get; set; }
    private bool DisablePolicy { get; set; }
    private bool DisableActionType { get; set; }
    private bool DisableName { get; set; }
    private bool IsCsirView { get; set; }

    #endregion

    #region Computed Properties

    private EntityEditorMode EditorMode => PreventiveActionId switch
    {
        0 => EntityEditorMode.Create,
        _ => EntityEditorMode.Update,
    };

    #endregion

    #region Lifecycle Methods

    protected override async Task OnInitializedAsync()
    {
        // Call base initialization (handles AppState subscription)
        base.OnInitialized();

        try
        {
            await LoadDropdownData();
            await LoadRiskAndTaskData();
            await SetupCommonActions();
            await ProcessCommonActionContent();

            // Force UI update after initialization
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error initializing PreventiveActionEdit component");
            // Handle error appropriately - maybe show error message or close dialog
        }
    }

    protected override async void OnAppStateChanged(object? sender, PropertyChangedEventArgs e)
    {
        Logger.LogDebug($"PreventiveActionEdit: AppState changed - {e.PropertyName}, CanEdit: {AppState?.CanEdit}");

        if (e.PropertyName == nameof(AppState.CanEdit) ||
            e.PropertyName == nameof(AppState.UserRole) ||
            e.PropertyName == nameof(AppState.Initialized))
        {
            await InvokeAsync(StateHasChanged);
        }
    }

    public override void Dispose()
    {
        Logger.LogDebug("PreventiveActionEdit: Disposing component");
        base.Dispose();
    }

    #endregion

    #region Initialization Helpers

    private async Task LoadDropdownData()
    {
        var tasks = new
        {
            Executors = DropdownManager.GetExecutorDictAsync(),
            Initiators = DropdownManager.GetInitiatorDictAsync(),
            WorkPackages = DropdownManager.GetWorkpackageDictAsync(),
            SapaWorkPackages = DropdownManager.GetSapaWorkpackageDictAsync(),
            IntervalUnits = DropdownManager.GetIntervalUnitDictAsync(),
            Clusters = DropdownManager.GetClusterDictAsync(),
            Policies = DropdownManager.GetPolicyDictAsync(),
            Risks = DropdownManager.GetRisksDictAsync(),
            Statuses = DropdownManager.GetStatusDictAsync(),
            UnitTypes = DropdownManager.GetLookupUserDefinedByFilterDictAsync("UnitTypes"),
            MeasureTypes = LookupManager.GetLookupByFilterDictAsync("MeasureType", true),
            Tasks = DropdownManager.GetTasksDictAsync((int) RiskId, Pmo)
        };

        await Task.WhenAll(
            tasks.Executors, tasks.Initiators, tasks.WorkPackages, tasks.SapaWorkPackages,
            tasks.IntervalUnits, tasks.Clusters, tasks.Policies, tasks.Risks,
            tasks.Statuses, tasks.UnitTypes, tasks.MeasureTypes, tasks.Tasks
        );
        
        ExecutorsDict = await tasks.Executors;
        InitiatorsDict = await tasks.Initiators;
        WorkPackageDict = await tasks.WorkPackages;
        SapaWorkPackageDict = await tasks.SapaWorkPackages;
        IntervalUnitsDict = await tasks.IntervalUnits;
        ClusterDict = await tasks.Clusters;
        PolicyDict = await tasks.Policies;
        RiskDict = await tasks.Risks;
        StatusDict = await tasks.Statuses;
        UnitTypeDict = await tasks.UnitTypes;
        TaskDict = await tasks.MeasureTypes;
        TaskOnRiskDict = await tasks.Tasks;
    }

    private async Task LoadRiskAndTaskData()
    {
        Risk = await RiskAnalysisManager.GetRiskAsync((int) RiskId!);
        var task = await RiskAnalysisManager.GetTaskByIdAsync(PreventiveActionId);

        if (Risk != null && task != null)
        {
            task.CalculateCosts(Risk);
        }

        PreventiveAction = EditorMode switch
        {
            EntityEditorMode.Create => new TaskModel
            {
                Id = PreventiveActionId,
                MrbId = RiskId,
                Type = "tsk",
                Pmo = Pmo
            },
            _ => task
        };

        if (Risk?.Tasks != null && PreventiveAction != null)
        {
            PartOfDict = Risk.Tasks
                .Where(x => x.PartOf == null && x.Id != PreventiveAction.Id)
                .ToDictionary(x => (int?) x.Id, y => y.Name);
        }
    }

    private async Task SetupCommonActions()
    {
        if (Risk == null) return;

        var analysisType = Risk.RiskObject?.AnalysisType?.ToLower() ?? string.Empty;
        IsCsirView = analysisType.Contains("csir");
        IsSapaView = analysisType.Contains("sapa");

        if (!IsCsirView)
        {
            CommonActionDict = await DropdownManager.GetCommonTasksDictAsync();
        }
        else if (Risk.SystemId == null)
        {
            CommonActionDict = await DropdownManager.GetCommonTasksPrioDictAsync();
        }
        else if (Risk.ComponentId == null)
        {
            ObjectModel = await ObjectManager.GetObject((int) Risk.SystemId);
            CommonActionDict = await DropdownManager.GetCommonTasksPrioDefinedByFilterDictAsync(ObjectModel.ShortKey);
        }
        else
        {
            ObjectModel = await ObjectManager.GetObject((int) Risk.ComponentId);
            CommonActionDict = await DropdownManager.GetCommonTasksPrioDefinedByFilterDictAsync(ObjectModel.ShortKey);
        }
    }

    #endregion

    #region Event Handlers

    private async Task ProcessCommonActionContent(bool setFields = false)
    {
        if (PreventiveAction?.CommonActionId == null)
        {
            ResetDisableStates();
            StateHasChanged();
            return;
        }

        try
        {
            var commonAction = await PortfolioSetupManager.GetCommonActionAsync(PreventiveAction.CommonActionId.Value);

            if (setFields)
            {
                ApplyCommonActionFields(commonAction);
            }

            SetDisableStatesFromCommonAction(commonAction);

            // Fix UI bug where all required fields give an error message
            PreventiveAction = JsonConvert.DeserializeObject<TaskModel>(JsonConvert.SerializeObject(PreventiveAction))!;

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing common action content");
        }
    }

    private async Task ValidTaskSubmitted(TaskModel task)
    {
        try
        {
            RemoveYearCostsFromNonExistentYears(task);
            ShowFailedValidations = false;
            CloseDialog();
            await Callback.InvokeAsync(task);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error submitting task");
            ShowFailedValidations = true;
        }
    }

    private void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        ShowFailedValidations = true;
        Logger.LogWarning($"Invalid {EditorMode} form submitted for {nameof(TaskModel)} with Id {PreventiveActionId}");
        Logger.LogWarning(JsonConvert.SerializeObject(args));
    }

    private void ReCalculateTaskCosts()
    {
        PreventiveAction?.CalculateCosts();
        StateHasChanged();
    }

    private void ReCalculateEstimatedCosts()
    {
        PreventiveAction?.RecalculateEstimatedCosts();
        PreventiveAction?.CalculateCosts();
        StateHasChanged();
    }

    private async Task CalculateDowntimeCosts()
    {
        if (RiskId == null || PreventiveAction == null) return;

        try
        {
            Risk = await RiskAnalysisManager.GetRiskAsync((int) RiskId);
            var dtCost = Risk?.RiskObject?.DownTimeCost;
            if (dtCost.HasValue)
            {
                PreventiveAction.CalculateDownTimeCost(dtCost.Value);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error calculating downtime costs");
        }
    }

    private void UpdateYearCosts()
    {
        if (PreventiveAction == null) return;

        PreventiveAction.EstCosts =
            (PreventiveAction.CostY1 ?? 0) +
            (PreventiveAction.CostY2 ?? 0) +
            (PreventiveAction.CostY3 ?? 0) +
            (PreventiveAction.CostY4 ?? 0) +
            (PreventiveAction.CostY5 ?? 0);

        StateHasChanged();
    }

    #endregion

    #region Helper Methods

    private void ResetDisableStates()
    {
        DisableInitiator = false;
        DisableExecutor = false;
        DisableWorkPackage = false;
        DisableIntervalUnit = false;
        DisableCosts = false;
        DisablePolicy = false;
        DisableName = false;
        DisableActionType = false;
        DisableReferenceId = false;
    }

    private void ApplyCommonActionFields(dynamic commonAction)
    {
        if (PreventiveAction == null) return;

        if (!string.IsNullOrEmpty(commonAction.Name))
            PreventiveAction.Name = commonAction.Name;

        if (!string.IsNullOrEmpty(commonAction.ReferenceId))
            PreventiveAction.ReferenceId = commonAction.ReferenceId;

        if (!string.IsNullOrEmpty(commonAction.Description))
            PreventiveAction.Description = commonAction.Description;

        if (!string.IsNullOrEmpty(commonAction.GeneralDescription))
            PreventiveAction.GeneralDescription = commonAction.GeneralDescription;

        if (!string.IsNullOrEmpty(commonAction.Remark))
            PreventiveAction.Remark = commonAction.Remark;

        if (!string.IsNullOrEmpty(commonAction.Permit))
            PreventiveAction.Permit = commonAction.Permit;

        if (!string.IsNullOrEmpty(commonAction.Responsible))
            PreventiveAction.Responsible = commonAction.Responsible;

        if (!string.IsNullOrEmpty(commonAction.Type))
            PreventiveAction.Type = commonAction.Type;

        if (commonAction.MxPolicyId?.HasValue == true)
            PreventiveAction.MxPolicy = commonAction.MxPolicyId;

        if (commonAction.InitiatorId?.HasValue == true)
            PreventiveAction.InitiatorId = commonAction.InitiatorId.Value;

        if (commonAction.ExecutorId?.HasValue == true)
            PreventiveAction.ExecutorId = commonAction.ExecutorId.Value;

        if (commonAction.WorkPackageId?.HasValue == true)
            PreventiveAction.WorkpackageId = commonAction.WorkPackageId.Value;

        if (commonAction.IntervalUnitId?.HasValue == true)
            PreventiveAction.IntervalUnitId = commonAction.IntervalUnitId.Value;

        if (commonAction.Interval?.HasValue == true)
            PreventiveAction.Interval = commonAction.Interval.Value;

        if (commonAction.Costs?.HasValue == true)
            PreventiveAction.EstCosts = commonAction.Costs.Value;

        if (commonAction.DownTime?.HasValue == true)
            PreventiveAction.DownTime = commonAction.DownTime.Value;

        if (commonAction.Duration?.HasValue == true)
            PreventiveAction.Duration = commonAction.Duration.Value;

        if (commonAction.PriorityCode?.HasValue == true)
            PreventiveAction.PriorityCode = commonAction.PriorityCode.Value;

        if (commonAction.SortOrder?.HasValue == true)
            PreventiveAction.SortOrder = commonAction.SortOrder.Value;

        PreventiveAction.UnitType = commonAction.UnitType;
    }

    private void SetDisableStatesFromCommonAction(dynamic commonAction)
    {
        DisableInitiator = commonAction.InitiatorModifiable == false;
        DisableExecutor = commonAction.ExecutorModifiable == false;
        DisableWorkPackage = commonAction.WorkPackageModifiable == false;
        DisableIntervalUnit = commonAction.IntervalModifiable == false;
        DisableCosts = commonAction.CostModifiable == false;

        // Always readonly when common action is selected
        DisableReferenceId = !string.IsNullOrWhiteSpace(commonAction.ReferenceId);
        DisablePolicy = true;
        DisableName = true;
        DisableActionType = true;
    }

    private void RemoveYearCostsFromNonExistentYears(TaskModel task)
    {
        if (task.ValidFromYear <= 0) return;

        var years = (task.ValidUntilYear ?? task.ValidFromYear) - task.ValidFromYear + 1;

        if (years <= 1) task.CostY2 = 0;
        if (years <= 2) task.CostY3 = 0;
        if (years <= 3) task.CostY4 = 0;
        if (years <= 4) task.CostY5 = 0;

        UpdateYearCosts();
    }

    private int GetYears()
    {
        if (PreventiveAction?.ValidFromYear == null || PreventiveAction?.ValidUntilYear == null)
            return 0;

        return PreventiveAction.ValidUntilYear.Value - PreventiveAction.ValidFromYear.Value;
    }

    #endregion
}