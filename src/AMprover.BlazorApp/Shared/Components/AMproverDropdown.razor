@inherits AMproverDropdownBase<TValue>
@typeparam TValue

<div class="form-group">

    @if (!string.IsNullOrEmpty(Label))
    {
        <div class="neg-margin-small">
            <label>@Label:</label>
        </div>
    }
    <div class="neg-margin-small">
        <Radzen.Blazor.RadzenDropDown @bind-Value=@Value
                                      Disabled=@IsDisabled()
                                      Data=Data
                                      TextProperty=@TextProperty
                                      Change=@Change
                                      AllowClear=@AllowClear
                                      AllowFiltering=@AllowFiltering
                                      FilterCaseSensitivity=FilterCaseSensitivity.CaseInsensitive
                                      Name=@Name
                                      class=@("form-control " + GetClass()) />

        @if (Required)
        {
            <RadzenRequiredValidator Component=@Name DefaultValue=@DefaultValue />
        }
    </div>
</div>

