@inherits AMproverDropdownBase<TKey>
@typeparam TKey
@typeparam TValue

<div class="form-group">

    @if (!string.IsNullOrEmpty(Label))
    {
        <div class="neg-margin-small">
            <label>@Label:</label>
        </div>
    }
    <div class="neg-margin-small">
        <Radzen.Blazor.RadzenDropDown @bind-Value=@Value
                                      Disabled=@IsDisabled()
                                      Data=Data
                                      ValueProperty="Key"
                                      TextProperty="Value"
                                      Change=@Change
                                      AllowClear=@AllowClear
                                      AllowFiltering=@AllowFiltering
                                      FilterCaseSensitivity=FilterCaseSensitivity.CaseInsensitive
                                      Name=@Name
                                      class=@("form-control " + GetClass())/>

        @if (Required)
        {
            <RadzenRequiredValidator Component=@Name DefaultValue=@DefaultValue/>

            @if (Min != null)
            {
                /* Intellisense error here is known, no easy fix. But code compiles fine */
                <RadzenNumericRangeValidator Component=@Name Min=Min Text="Required"/>
            }
        }
    </div>
</div>


