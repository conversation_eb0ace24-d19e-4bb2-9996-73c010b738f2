@if (Open)
{
    <div class="am-dropdown-option-container" style=@GetStyle() @onmouseover=@(() => Hovered = true) @onmouseout=@(() => Hovered = false) @onblur=Blur>

        @* When the dropdown opens we focus on the searchbar. When Filtering is disabled, we focus on this invisble element to enable onBlur *@
        <input type="button" class="am-dropdown-focus" @onblur=Blur @ref=FocusElement />

        @if (AllowFiltering)
        {
            <div class="am-dropdown-filter-container">
                <input class="am-dropdown-filter-input" placeholder="search..." type="text" @oninput=UpdateSearch @onblur=Blur @ref=SearchElement />
            </div>
        }

        <div class="am-dropdown-scroll-container">
            @foreach (var opt in FilteredData ?? Data)
            {
                <div class=@GetItemClass(opt) @onclick=@(x => ClickOption(opt))>
                    @opt
                </div>
            }
        </div>
    </div>
}
