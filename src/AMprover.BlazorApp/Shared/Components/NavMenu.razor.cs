using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Constants;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Shared.Components;

public partial class NavMenu
{
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IPageNavigationManager PageNavigationManager { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private IStringLocalizer<NavMenu> Localizer { get; set; }

    private List<LookupSettingModel> LookupSettings { get; set; }
    private LookupSettingModel EnableColumnsForTypes { get; set; }
    private bool HasSelectedRiskAssessment { get; set; }
    private bool SapaEnabled { get; set; }
    private bool ExpandPortfolioSetup { get; set; }
    private string RiskAssessmentUrl { get; set; }
    private static string AdminRoles => $"{RoleConstants.Administrators},{RoleConstants.PortfolioAdministrators}";

    protected override async Task OnInitializedAsync()
    {
        LookupSettings = await LookupManager.GetLookupSettingsAsync();

        var urls = (await PageNavigationManager.GetPageQueryStrings()).Where(x =>
            x.Path.Contains("risks")
            && x.Path.Contains("value-risk-analysis")
            && !x.Path.Contains("/0")).ToList();

        RiskAssessmentUrl = urls.MaxBy(x => x.Id)?.Path;
        HasSelectedRiskAssessment = urls.Count > 0;
            
        EnableColumnsForTypes = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.EnableColumnsForTypes, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.EnableColumnsForTypes };
        SapaEnabled = EnableColumnsForTypes.IntValue == 1;
    }

    private async Task Navigate(string page)
    {
        var navigationItem = await PageNavigationManager.GetPageUrl(page);
        //If contains invalid risk analysis link
        if (navigationItem.Contains("/value-risk-analysis/0/risks/0"))
        {
            NavigationManager.NavigateTo("/value-risk-organizer");
        }
            
        //If contains a removed risk analysis link. Doing this here instead of on initialized because we don't want to
        //try and resolve risk item every page load
        if (navigationItem.Contains("risks") && navigationItem.Contains("value-risk-analysis"))
        {
            //split string to get risk id
            var urlParts = navigationItem.Split("/");
            var riskId = urlParts.LastOrDefault();
            
            //If risk id is available in string and is not 0 check if exists
            if (riskId != null)
            {
                if (int.TryParse(riskId, out var riskItemId))
                {
                    if (await RiskAnalysisManager.FindRiskExistsAsync(riskItemId))
                    {
                        NavigationManager.NavigateTo(navigationItem);
                    }
                }
            }
        }
            
        //If above validations are false continue with navigation
        NavigationManager.NavigateTo(navigationItem);
    }
}