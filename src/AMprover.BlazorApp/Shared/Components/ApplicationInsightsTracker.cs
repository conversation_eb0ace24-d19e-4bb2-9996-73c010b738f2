// using System;
// using System.Threading.Tasks;
// using AMprover.Data.Infrastructure;
// using Microsoft.ApplicationInsights;
// using Microsoft.ApplicationInsights.DataContracts;
// using Microsoft.AspNetCore.Components;
// using Microsoft.AspNetCore.Components.Authorization;
// using Microsoft.AspNetCore.Components.Routing;
//
// namespace AMprover.BlazorApp.Shared.Components;
//
// public class ApplicationInsightsTracker : ComponentBase, IDisposable
// {
//     [Inject]
//     private TelemetryClient _telemetryClient { get; init; }
//
//     [Inject]
//     private NavigationManager _navigationManager { get; init; }
//
//     [Inject]
//     private AuthenticationStateProvider _authStateProvider { get; init; }
//
//     [Inject]
//     private IAssetManagementPortfolioResolver _assetManagementPortfolioResolver { get; init; }
//
//     protected override void OnInitialized()
//     {
//         try
//         {
//             Task<AuthenticationState> _currentAuthenticationStateTask;
//
//             // Act on changes of Authentication state
//             _authStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;
//
//             _currentAuthenticationStateTask = _authStateProvider.GetAuthenticationStateAsync();
//
//             OnAuthenticationStateChanged(_currentAuthenticationStateTask);
//         }
//         catch (Exception ex)
//         {
//             Console.WriteLine($"Error in OnInitialized: {ex.Message}");
//             // Don't rethrow to prevent application crash
//         }
//     }
//
//
//     private void OnAuthenticationStateChanged(Task<AuthenticationState> authenticationStateTask)
//     {
//         // taken from: https://github.com/dotnet/aspnetcore/issues/29235 as own attempt event didn't fire
//         InvokeAsync(async () =>
//         {
//             try
//             {
//                 var authState = await authenticationStateTask;
//
//                 var user = authState.User;
//
//                 var authenticated = user.Identity?.IsAuthenticated ?? false;
//
//                 _telemetryClient.Context.User.AuthenticatedUserId = authenticated ? user.Identity.Name?.ToLower() : null;
//             }
//             catch (Exception ex)
//             {
//                 Console.WriteLine($@"Error in OnAuthenticationStateChanged: {ex.Message}");
//             }
//         });
//     }
//
//     protected override async Task OnAfterRenderAsync(bool firstRender)
//     {
//         try
//         {
//             if (firstRender)
//             {
//                 _navigationManager.LocationChanged += NavigationManagerOnLocationChanged;
//             }
//
//             await EnrichCurrentPortfolio();
//             await base.OnAfterRenderAsync(firstRender);
//         }
//         catch (Exception ex)
//         {
//             Console.WriteLine($@"Error in OnAfterRender: {ex.Message}");
//             // Don't rethrow to prevent application crash
//         }
//     }
//
//     private async Task EnrichCurrentPortfolio()
//     {
//         if (_telemetryClient.IsEnabled())
//         {
//             try
//             {
//                 var currentPortfolio = await _assetManagementPortfolioResolver.GetCurrentPortfolioAsync();
//                 if (currentPortfolio != null)
//                 {
//                     var customDimensionKey = "AMproverPortfolio";
//                     _telemetryClient.Context.GlobalProperties[customDimensionKey] = currentPortfolio.Name;
//                 }
//             }
//             catch (ObjectDisposedException)
//             {
//                 // Ignore disposed context exceptions during navigation
//             }
//             catch (Exception ex)
//             {
//                 Console.WriteLine($@"Error in EnrichCurrentPortfolio: {ex.Message}");
//                 // Don't rethrow to prevent application crash
//             }
//         }
//     }
//
//     private Task NavigationManagerOnLocationChanged(object sender, LocationChangedEventArgs e)
//     {
//         try
//         {
//             var newLocationUri = new Uri(e.Location.ToLower(), UriKind.Absolute);
//             _telemetryClient.TrackPageView( // can do a TrackPageView here, but that doesn't show up in default Application Insights UI :(
//                 new PageViewTelemetry
//                 {
//                     Name = newLocationUri.PathAndQuery,
//                     Url = newLocationUri
//                 });
//             
//             _ = EnrichCurrentPortfolio();
//         }
//         catch (Exception ex)
//         {
//             Console.WriteLine($@"Error in NavigationManagerOnLocationChanged: {ex.Message}");
//             // Don't rethrow to prevent application crash
//         }
//     }
//
//
//     public void Dispose()
//     {
//         _authStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
//         _navigationManager.LocationChanged -= NavigationManagerOnLocationChanged;
//
//     }
// }
