using System;
using System.Security.Claims;
using AMprover.BlazorApp.Areas.Identity;
using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BlazorApp.Components.Tree;
using AMprover.BlazorApp.Configuration;
using AMprover.BlazorApp.Helpers;
using AMprover.BlazorApp.Pages.Report;
using AMprover.BlazorApp.Pages.RiskOrganize;
using AMprover.BlazorApp.Pages.RiskOrganize.Risks;
using AMprover.BlazorApp.Pages.SAPA;
using AMprover.BlazorApp.Services;
using AMprover.BlazorApp.Shared;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.BackgroundServices;
using AMprover.BusinessLogic.Configuration;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.ABS;
using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.Models.Criticality;
using AMprover.BusinessLogic.Models.Failures;
using AMprover.BusinessLogic.Models.Import;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Models.Reports;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.RiskOnAbs;
using AMprover.BusinessLogic.Models.Sapa;
using AMprover.Data;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using AMprover.Data.Infrastructure;
using AMprover.Data.Repositories;
using BlazorDownloadFile;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Radzen;

namespace AMprover.BlazorApp.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAMproverServices(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
    {
            // Database configuration
            services.AddDatabaseServices(configuration, environment);

            // Core services
            services.AddCoreServices(configuration);

            // Component services
            services.AddComponentServices();

            // Register models for components
            services.RegisterComponentModels();

            return services;
        }

    private static void AddDatabaseServices(this IServiceCollection services, IConfiguration configuration,
        IWebHostEnvironment environment)
    {
        // Main database context
        services.AddDbContext<MainDbContext>(options =>
        {
            if (environment.IsDevelopment())
            {
                options.EnableSensitiveDataLogging();
            }

            options.UseSqlServer(
                configuration.GetConnectionString(ConnectionstringConstants.DefaultConnectionStringName),
                sqlBuilder => { sqlBuilder.EnableRetryOnFailure(5, TimeSpan.FromSeconds(10), null); });
        });

        // Register DbContextFactory for MainDbContext
        services.AddDbContextFactory<MainDbContext>(options =>
        {
            if (environment.IsDevelopment())
            {
                options.EnableSensitiveDataLogging();
            }

            options.UseSqlServer(
                configuration.GetConnectionString(ConnectionstringConstants.DefaultConnectionStringName),
                sqlBuilder => { sqlBuilder.EnableRetryOnFailure(5, TimeSpan.FromSeconds(10), null); });
        }, ServiceLifetime.Scoped);

        // Ensure migrations are executed
        services.AddTransient<IStartupFilter, AMproverDataContextAutomaticMigrationStartupFilter<MainDbContext>>();

        // Identity configuration
        services.AddDefaultIdentity<UserAccount>(options =>
            {
                options.SignIn.RequireConfirmedAccount = true;
                options.User.RequireUniqueEmail = true;
            })
            .AddRoles<IdentityRole>()
            .AddEntityFrameworkStores<MainDbContext>();

        // Only allow a useraccount to be used in 1 session.
        services.SetupAmproverSecurityStamp();

        // Portfolio database context
        services.AddScoped<IAssetManagementPortfolioResolver, AssetManagementPortfolioResolver>();

        // Register custom DbContextFactory
        services.AddScoped<IAssetManagementDbContextFactory, AssetManagementDbContextFactory>();

        // Register adapter for IDbContextFactory<AssetManagementDbContext>
        services.AddScoped<IDbContextFactory<AssetManagementDbContext>, AssetManagementDbContextFactoryAdapter>();

        // Register custom repository
        services.AddScoped(typeof(IAssetManagementBaseRepository<>), typeof(AssetManagementBaseRepository<>));

        // Repositories
        services.AddScoped<IPortfolioRepository, PortfolioRepository>();
        services.Scan(scan => scan.FromAssemblyOf<IRiskObjectRepository>()
            .AddClasses(classes =>
                classes.Where(type => type.Name.EndsWith("Repository") && !type.Name.EndsWith("BaseRepository")))
            .AsImplementedInterfaces()
            .WithScopedLifetime());
    }

    private static void AddCoreServices(this IServiceCollection services, IConfiguration configuration)
    {
            // SignalR
            services.AddSignalR(hubOptions =>
            {
                hubOptions.MaximumReceiveMessageSize = 4 * 1024 * 1024; // 4 MB
            });

            // MVVM functionality
            services.AddSingleton<LocalizationHelper>();
            services.AddScoped<JavaScriptHelper>();

            // Email configuration
            services.Configure<OutboundEmailSettings>(configuration.GetSection("OutboundEmail"));
            services.AddSingleton<IEmailSender, IdentityEmailSender>();

            // Force logout without required page refresh if userAccount is used in multiple sessions
            services.AddScoped<AuthenticationStateProvider, RevalidatingIdentityAuthenticationStateProvider<UserAccount>>();

            // Application services
            services.AddScoped<AppState>();
            services.AddScoped<IPortfolioManager, PortfolioManager>();
            services.AddScoped<ICachingService, CachingService>();
            services.AddScoped<IGridColumnService, GridColumnService>();
            services.AddScoped<CookieService>();
            services.AddScoped<DropdownHelper>();
            services.AddScoped<TooltipHelper>();

            // Logging
            services.AddScoped(provider => new AMproverLogger("AMprover",
                provider.GetRequiredService<IConfiguration>(),
                provider.GetRequiredService<AuthenticationStateProvider>(),
                provider.GetRequiredService<IPortfolioManager>()
            ));

            // Background services
            services.Configure<FmecaImageSettings>(configuration.GetSection("FmecaImageSettings"));
            services.AddScoped<IFmecaImageService, FmecaImageService>();
            services.AddScoped<HtmlImageConverter>();
            services.AddHostedService<FmecaImageServiceScheduler>();

            // Managers
            services.Scan(scan => scan.FromAssemblyOf<RiskAnalysisManager>()
                .AddClasses(classes => classes.Where(type => type.Name.EndsWith("Manager")))
                .AsImplementedInterfaces()
                .WithScopedLifetime());

            // Radzen services
            services.Scan(scan => scan.FromAssemblyOf<DialogService>()
                .AddClasses(classes => classes.Where(type => type.Name.EndsWith("Service")))
                .AsSelf()
                .WithScopedLifetime());

            // Auto Mapper
            services.RegisterMappings();

            // Download functionality
            services.AddBlazorDownloadFile();

            // RAMS service
            services.AddScoped(typeof(DragAndDropService<>));

            // Utility components and query params
            services.AddTransient<RiskOrganizerQueryParams>();
            services.AddTransient<RiskEditQueryParams>();
            services.AddTransient<ReportViewQueryParams>();
            services.AddTransient<SapaOverviewQueryParams>();
    }

    private static void AddComponentServices(this IServiceCollection services)
    {
            // Add Razor pages and Blazor server
            services.AddRazorPages();
            services.AddServerSideBlazor();
    }

    private static void RegisterComponentModels(this IServiceCollection services)
    {
            // Register Models for Tree Component
            RegisterTreeComponent<RiskTreeObject>(services);
            RegisterTreeComponent<LccTreeObject>(services);
            RegisterTreeComponent<ClusterTreeObject>(services);
            RegisterTreeComponent<RamsTreeObject>(services);
            RegisterTreeComponent<AssetModel>(services);
            RegisterTreeComponent<RiskOnAbsTreeModel>(services);
            RegisterTreeComponent<SapaTreeObject>(services);

            // Register Models for UtilityGrid
            RegisterUtilityGridComponent<CriticalityRankingModelFlat>(services);
            RegisterUtilityGridComponent<SpareModel>(services);
            RegisterUtilityGridComponent<TaskModel>(services);
            RegisterUtilityGridComponent<RiskModel>(services);
            RegisterUtilityGridComponent<RiskWithPlainObjectsModel>(services);
            RegisterUtilityGridComponent<TaskWithPlainObjectsModel>(services);
            RegisterUtilityGridComponent<RiskImportModel>(services);
            RegisterUtilityGridComponent<TaskImportModel>(services);
            RegisterUtilityGridComponent<SpareImportModel>(services);
            RegisterUtilityGridComponent<PmoRiskReportItemModel>(services);
            RegisterUtilityGridComponent<CommonTaskModel>(services);
            RegisterUtilityGridComponent<CommonCostModel>(services);
            RegisterUtilityGridComponent<ClusterTaskPlanModel>(services);
            RegisterUtilityGridComponent<ClusterTaskPlanModelWithExtraTaskProperties>(services);
            RegisterUtilityGridComponent<ClusterCostModel>(services);
            RegisterUtilityGridComponent<ClusterModel>(services);
            RegisterUtilityGridComponent<LccEffectDetailModel>(services);
            RegisterUtilityGridComponent<LccDetailModel>(services);
            RegisterUtilityGridComponent<RiskMatrixTemplateModel>(services);
            RegisterUtilityGridComponent<ScenarioModel>(services);
            RegisterUtilityGridComponent<FailureModeModel>(services);
            RegisterUtilityGridComponent<FailureCategory>(services);
            RegisterUtilityGridComponent<GenericObjectLevel>(services);
            RegisterUtilityGridComponent<ObjectModel>(services);
            RegisterUtilityGridComponent<IntervalUnitModel>(services);
            RegisterUtilityGridComponent<InitiatorModel>(services);
            RegisterUtilityGridComponent<ExecutorModel>(services);
            RegisterUtilityGridComponent<WorkPackageModel>(services);
            RegisterUtilityGridComponent<PolicyModel>(services);
            RegisterUtilityGridComponent<AssetModel>(services);
            RegisterUtilityGridComponent<LookupUserDefinedModel>(services);
            RegisterUtilityGridComponent<RiskObjectModel>(services);
            RegisterUtilityGridComponent<RamsModel>(services);
            RegisterUtilityGridComponent<TaskPlanReportItemModel>(services);
            RegisterUtilityGridComponent<UserAccount>(services);
            RegisterUtilityGridComponent<CommonActionImportModel>(services);
            RegisterUtilityGridComponent<SapaDetailModel>(services);
            RegisterUtilityGridComponent<SapaWorkpackageModel>(services);
            RegisterUtilityGridComponent<AttachmentCategoryModel>(services);
            RegisterUtilityGridComponent<AttachmentModel>(services);
            RegisterUtilityGridComponent<LookupSettingModel>(services);
    }

    private static void RegisterTreeComponent<T>(IServiceCollection services)
    {
        services.AddTransient<TreeComponent<T>>();
        services.AddTransient<TreeNodeComponent<T>>();
    }

    private static void RegisterUtilityGridComponent<T>(IServiceCollection services)
    {
        services.AddTransient<UtilityGrid<T>>();
    }

    private static void SetupAmproverSecurityStamp(this IServiceCollection services)
    {
        services.ConfigureApplicationCookie(opts =>
        {
            // Trigger on Login
            opts.Events.OnSigningIn = async context =>
            {
                var userMgr = context.HttpContext.RequestServices.GetRequiredService<UserManager<UserAccount>>();
                var signInMgr = context.HttpContext.RequestServices.GetRequiredService<SignInManager<UserAccount>>();

                var userId = context.Principal?.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userId is null) return;

                var user = await userMgr.FindByIdAsync(userId);
                if (user is null) return;

                // Set new security timestamp cookie in DB
                await userMgr.UpdateSecurityStampAsync(user);

                // Recreate cookie so it contains the new Secutiry timestamp as well
                var newPrincipal = await signInMgr.CreateUserPrincipalAsync(user);
                context.Principal = newPrincipal;
            };

            // Trigger on HTTP Request (page refresh)
            opts.Events.OnValidatePrincipal = async context =>
            {
                var userMgr = context.HttpContext.RequestServices.GetRequiredService<UserManager<UserAccount>>();
                var stampClaimType = userMgr.Options.ClaimsIdentity.SecurityStampClaimType;

                var stampInCookie = context.Principal?.FindFirstValue(stampClaimType);
               
                if (context.Principal is null || stampInCookie is null)
                    return;
                
                var user = await userMgr.GetUserAsync(context.Principal);
                var stampInDb = user == null
                    ? null
                    : await userMgr.GetSecurityStampAsync(user);

                if (stampInCookie != stampInDb)
                {
                    context.RejectPrincipal();
                    await context.HttpContext.SignOutAsync(IdentityConstants.ApplicationScheme);
                }
            };
        });
    }
}
