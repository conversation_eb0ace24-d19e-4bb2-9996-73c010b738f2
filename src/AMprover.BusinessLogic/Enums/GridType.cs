using System.ComponentModel;

namespace AMprover.BusinessLogic.Enums;

public enum GridType
{
    Description,
    [Description("Impact Value")] ImpactValue,
    [Description("Custom Value")] CustomValue,
    [Description("Points")] Points,
    [Description("Risk without actions")] RiskWithoutActions,
    [Description("Spare parts")] SpareParts,
    [Description("Preventive actions")] PreventiveActions,
    [Description("Risk with taken actions")] RiskWithTakenActions,
    [Description("Risk with current actions")] RiskWithCurrentActions
}
