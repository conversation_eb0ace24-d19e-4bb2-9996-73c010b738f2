using System;
using System.Text;

namespace AMprover.BusinessLogic.Enums;

public enum DatabaseSelectionCriterium
{
    Equals,
    DoesNotEqual,
    Contains,
    StartsWith,
    EndsWith
}

public static class DatabaseSelectionCriteriumExtensions
{
    public static string ToDisplayString(this DatabaseSelectionCriterium criterium)
    {
        return criterium.ToString().ToHasSpaces();
    }

    private static string ToHasSpaces(this string input)
    {
        StringBuilder sb = new StringBuilder();

        foreach(var character in input)
        {
            if (Char.IsUpper(character))
                sb.Append($" {character}");
            else
                sb.Append(character);
        }

        return sb.ToString();
    }

    public static string ToDatabaseString(this DatabaseSelectionCriterium criterium)
    {
        return criterium switch
        {
            DatabaseSelectionCriterium.Contains => "Contains",
            DatabaseSelectionCriterium.DoesNotEqual => "!=",
            DatabaseSelectionCriterium.EndsWith => "Ends with",
            DatabaseSelectionCriterium.Equals => "=",
            DatabaseSelectionCriterium.StartsWith => "Begins with",
            _ => "="
        };
    }

    public static DatabaseSelectionCriterium FromDBSelectionCriterium(this string criterium)
    {
        return criterium?.ToLower() switch
        {
            "contains" => DatabaseSelectionCriterium.Contains,
            "!=" => DatabaseSelectionCriterium.DoesNotEqual,
            "ends with" => DatabaseSelectionCriterium.EndsWith,
            "=" => DatabaseSelectionCriterium.Equals,
            "begins with" => DatabaseSelectionCriterium.StartsWith,
            _ => DatabaseSelectionCriterium.Equals
        };
    }
}