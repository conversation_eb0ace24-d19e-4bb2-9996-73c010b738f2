using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.LCC.Calculation;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using FailureRateHelper = AMprover.BusinessLogic.Helpers.FailureRateHelper;
using Lcc = AMprover.Data.Entities.AM.Lcc;
using LccModel = AMprover.BusinessLogic.Models.LCC.LccModel;
using Task = System.Threading.Tasks.Task;
using TaskModel = AMprover.BusinessLogic.Models.RiskAnalysis.TaskModel;

namespace AMprover.BusinessLogic;

public interface ILccManager
{
    Task<TreeNodeGeneric<LccTreeObject>> GetTreeViewForScenarioAsync(int id);

    Task<List<LccModel>> GetLccsBaseModelsForScenarioAsync(int id);

    Task DeleteLccAsync(int id);

    Task<int?> CreateLccAsync(NewLccModel lccModel, bool setReplacementValue);

    Task CreateLccForRamsDiagramAsync(RamsDiagramModel ramsDiagram, string language);

    Task DeleteLccByRiskObjectAsync(int riskObjectId);

    Task DeleteLccByRamsDiagramAsync(int ramsDiagramId);

    Task<LccModel> GetLccByRamsIdAsync(int id);

    Task<LccModel> GetLccDetailedAsync(int id);

    Task<LccModel> CalculateLccAsync(LccModel lcc);

    Task RecalculateLccByRiskAsync(int? riskId);

    Task RecalculateLccByRiskAsync(RiskModel risk);

    TreeNodeGeneric<LccTreeObject> RemoveLccNodeFromTree(TreeNodeGeneric<LccTreeObject> tree, List<int> path);
}

public class LccManager(
    IAssetManagementDbContextFactory dbContextFactory,
    IMapper mapper,
    ILookupManager lookupManager,
    AuthenticationStateProvider authenticationStateProvider)
    : BaseManager(dbContextFactory, authenticationStateProvider), ILccManager
{
    private readonly List<LccRamsItem> _ramsItems = [];

    public async Task<int?> CreateLccAsync(NewLccModel lccModel, bool setReplacementValue)
    {
        if (lccModel.SelectedNode == null) return null;

        var source = lccModel.SelectedNode.Source;
        source.Name = source.Name.Trim();

        var existingLccs = await GetLccsForScenarioAsync(lccModel.ScenarioId);
        var currentExistingLcc = existingLccs.FirstOrDefault(x =>
            x.RiskObjectId == source.RiskObjectId &&
            x.ChildObjectId == source.CollectionId &&
            x.ChildObject1Id == source.InstallationId &&
            x.ChildObject2Id == source.SystemId &&
            x.ChildObject3Id == source.ComponentId &&
            x.ChildObject4Id == source.AssemblyId &&
            x.Name == source.Name);

        if (currentExistingLcc != null) return currentExistingLcc.Id;

        var lcc = new LccModel
        {
            RiskObjectId = source.RiskObjectId,
            ChildObjectId = source.CollectionId,
            ChildObject1Id = source.InstallationId,
            ChildObject2Id = source.SystemId,
            ChildObject3Id = source.ComponentId,
            ChildObject4Id = source.AssemblyId,
            Name = source.Name
        };

        if (lccModel.SelectedNode?.Parent?.Source?.LccId != null)
        {
            var lccId = lccModel.SelectedNode.Parent.Source.LccId;
            lcc.PartOf = lccId;
        }
        else
        {
            lcc.ScenarioId = lccModel.ScenarioId;
        }

        if (setReplacementValue && source.InstallationId != null)
        {
            await ExecuteWithContextAsync(async context =>
            {
                var lccInstallation = await context.Object.FirstOrDefaultAsync(x => x.ObjId == source.InstallationId);
                lcc.ReplacementValue = lccInstallation?.ObjNewValue;
            });
        }

        // Load the RiskObject with its risks and tasks
        if (lcc.RiskObjectId.HasValue)
        {
            var riskObject = await ExecuteWithContextAsync(async context =>
            {
                return await context.RiskObject.AsNoTracking()
                    .Include(x => x.RiskObjFmeca)
                    .Include(x => x.Risks).ThenInclude(x => x.FailureMode)
                    .Include(x => x.Risks).ThenInclude(x => x.Spares)
                    .Include(x => x.Risks).ThenInclude(x => x.Tasks)
                    .ThenInclude(x => x.TskIntervalUnitNavigation)
                    .AsSplitQuery()
                    .FirstOrDefaultAsync(x => x.RiskObjId == lcc.RiskObjectId);
            });

            if (riskObject != null)
            {
                lcc.RiskObject = mapper.Map<RiskObjectModel>(riskObject);
                lcc.RiskObject.Risks = mapper.Map<List<RiskModel>>(riskObject.Risks);

                // Filter risks based on LCC data
                lcc.FilterLccRisks();
            }
        }

        return await ExecuteWithSaveAsync(async context =>
        {
            var createdLcc = await context.Lcc.AddAsync(mapper.Map<Lcc>(lcc));
            return createdLcc.Entity;
        }, async (_, result) => (await GetLccDetailedAsync(result.LccId)).Id);
    }

    public async Task CreateLccForRamsDiagramAsync(RamsDiagramModel ramsDiagram, string language)
    {
        RamsDiagramContentModel diagramContent;

        using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(ramsDiagram.Serialized)))
        using (var reader = new StreamReader(stream))
        await using (var jsonReader = new JsonTextReader(reader))
        {
            var serializer = JsonSerializer.Create(new JsonSerializerSettings
            {
                Culture = new CultureInfo(language)
            });
            diagramContent = serializer.Deserialize<RamsDiagramContentModel>(jsonReader);
        }

        await CreateLccForRamsItemAsync(ramsDiagram, null, diagramContent.Parts, ramsDiagram.Rams);
    }

    private async Task CreateLccForRamsItemAsync(RamsDiagramModel diagram, Lcc parent, List<RamsComponentModel> parts,
        List<RamsModel> rams)
    {
        foreach (var part in parts)
        {
            var lcc = new LccModel
            {
                PartOf = parent?.LccId,
                Name = parent == null ? diagram.Name : part.Title,
                RamsDiagramId = diagram.Id,
                RamsId = rams.FirstOrDefault(x => x.NodeId == part.Id)?.Id,
                ScenarioId = parent == null ? diagram.ScenId : null,
                RiskObjectId = diagram.RiskObject
            };

            var createdLcc = await ExecuteWithSaveAsync(async context =>
            {
                var entity = await context.Lcc.AddAsync(mapper.Map<Lcc>(lcc));
                return entity.Entity;
            });

            if (part.Type == RamsComponentType.Container)
                await CreateLccForRamsItemAsync(diagram, createdLcc, part.Parts, rams);
        }
    }

    public async Task DeleteLccByRiskObjectAsync(int riskObjectId)
    {
        await ExecuteWithSaveAsync(async context =>
        {
            var lccs = await context.Set<Lcc>()
                .Where(x => x.LccRiskObject == riskObjectId).ToListAsync();

            context.RemoveRange(lccs);
        });
    }

    public async Task DeleteLccByRamsDiagramAsync(int ramsDiagramId)
    {
        await ExecuteWithSaveAsync(async context =>
        {
            var lccs = await context.Set<Lcc>()
                .Where(x => x.LccRamsDiagramId == ramsDiagramId).ToListAsync();

            context.RemoveRange(lccs);
        });
    }

    public async Task<LccModel> GetLccByRamsIdAsync(int id)
    {
        return await ExecuteWithContextAsync(async context =>
        {
            var result = await context.Lcc.FirstOrDefaultAsync(x => x.LccRamsDiagramId == id);
            return result != null ? mapper.Map<LccModel>(result) : null;
        });
    }

    public async Task<LccModel> GetLccDetailedAsync(int id)
    {
        return await ExecuteWithContextAsync(async context =>
        {
            var lcc = await context.Set<Lcc>()
                .Include(x => x.LccPartOfLcc)
                .Include(x => x.ChildObject4)
                .Include(x => x.ChildObject3)
                .Include(x => x.ChildObject2)
                .Include(x => x.ChildObject)
                .Include(x => x.RiskObject).ThenInclude(x => x.RiskObjFmeca)
                .Include(x => x.RiskObject).ThenInclude(x => x.Risks).ThenInclude(x => x.FailureMode)
                .Include(x => x.RiskObject).ThenInclude(x => x.Risks).ThenInclude(x => x.Spares)
                .Include(x => x.RiskObject).ThenInclude(x => x.Risks).ThenInclude(x => x.Tasks)
                .ThenInclude(x => x.TskIntervalUnitNavigation)
                .Include(x => x.Details).ThenInclude(x => x.EffectDetails)
                .Include(x => x.VdmxlItem)
                .Include(x => x.LccSi).ThenInclude(x => x.PickSi).ThenInclude(x => x.PckSiMrb)
                .AsSplitQuery()
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.LccId == id);

            var lccDto = lcc != null ? mapper.Map<LccModel>(lcc) : new LccModel();

            var riskObject = await ExecuteWithContextAsync(async context =>
            {
                return await context.RiskObject.AsNoTracking()
                    .Include(x => x.RiskObjFmeca)
                    .Include(x => x.Risks).ThenInclude(x => x.FailureMode)
                    .Include(x => x.Risks).ThenInclude(x => x.Spares)
                    .Include(x => x.Risks).ThenInclude(x => x.Tasks)
                    .ThenInclude(x => x.TskIntervalUnitNavigation)
                    .AsSplitQuery()
                    .FirstOrDefaultAsync(x => x.RiskObjId == lcc.LccRiskObject);
            });

            if (riskObject != null)
            {
                lccDto.RiskObject.Risks = mapper.Map<List<RiskModel>>(riskObject.Risks);
                //filter lcc risks based on lcc data
                lccDto.FilterLccRisks();
            }

            return lccDto;
        });
    }

    private async Task<IEnumerable<LccModel>> GetLccsForScenarioAsync(int id)
    {
        return await ExecuteWithContextAsync(async context =>
        {
            var lccsForScenario = await context.Set<Lcc>()
                .AsSplitQuery().AsNoTracking().ToListAsync();

            var lccs = new List<LccModel>();
            foreach (var lcc in lccsForScenario.Where(x => x.LccScenarioId == id))
            {
                AddLccRecursive(lccs, lcc, lccsForScenario);
            }

            return lccs;
        });
    }

    public async Task<List<LccModel>> GetLccsBaseModelsForScenarioAsync(int id)
    {
        return await ExecuteWithContextAsync(async context =>
        {
            var lcc = await context.Lcc.Where(x => x.LccScenarioId == id).AsNoTracking().ToListAsync();

            // For the RiskOrganizer we need to append Lcc to RiskObjects, but we want very minimal data.
            // since you can't place a Base model inside a derivedModel property we just map twice
            // First from DB to baseModel, and then from baseModel to "Full" Model
            return lcc.Select(x => mapper.Map<LccModel>(mapper.Map<LccBaseModel>(x))).ToList();
        });
    }

    public async Task<TreeNodeGeneric<LccTreeObject>> GetTreeViewForScenarioAsync(int id)
    {
        return await ExecuteWithContextAsync(async context =>
        {
            var tree = new TreeNodeGeneric<LccTreeObject>(null, null);

            //root item(s) for scenario
            var lccsForScenario = await context.Set<Lcc>()
                .Where(x => (x.LccScenarioId == id && x.LccRamsDiagramId == null) ||
                            (x.LccScenarioId == id && x.LccRamsDiagramId != null && x.LccPartOf == null))
                .Include(x => x.RiskObject)
                .Include(x => x.LccChildren).ThenInclude(x => x.RiskObject)
                .Include(x => x.LccChildren).ThenInclude(x => x.LccChildren).ThenInclude(x => x.RiskObject)
                .Include(x => x.LccChildren).ThenInclude(x => x.LccChildren).ThenInclude(x => x.LccChildren)
                .ThenInclude(x => x.LccChildren).ThenInclude(x => x.LccChildren).ThenInclude(x => x.LccChildren)
                .ThenInclude(x => x.RiskObject)
                .AsNoTracking()
                .AsSplitQuery().ToListAsync();

            foreach (var lcc in lccsForScenario.OrderBy(x => x.LccChildren?.Any() == false).ThenBy(x => x.LccName))
            {
                AddNode(tree, lcc, null);
            }

            return tree;
        });
    }

    public async Task<LccModel> CalculateLccAsync(LccModel lcc)
    {
        return lcc.RamsDiagramId == null
            ? await CalculateLccByRiskAsync(lcc)
            : await CalculateLccByRamsDiagramAsync(lcc);
    }

    private async Task<LccModel> CalculateLccByRiskAsync(LccModel lcc)
    {
        RiskMatrixTemplateModel template = null;
        var riskObject = lcc.RiskObject;

        //top level
        if (!lcc.PartOf.HasValue)
        {
            var firstChild = await ExecuteWithContextAsync(async context =>
            {
                return await context.Lcc.AsNoTracking()
                    .Include(x => x.LccPartOfLcc)
                    .Include(x => x.ChildObject4)
                    .Include(x => x.ChildObject3)
                    .Include(x => x.ChildObject2)
                    .Include(x => x.ChildObject)
                    .Include(x => x.RiskObject).ThenInclude(x => x.RiskObjFmeca)
                    .Include(x => x.RiskObject).ThenInclude(x => x.Risks).ThenInclude(x => x.FailureMode)
                    .Include(x => x.RiskObject).ThenInclude(x => x.Risks).ThenInclude(x => x.Spares)
                    .Include(x => x.RiskObject).ThenInclude(x => x.Risks).ThenInclude(x => x.Tasks)
                    .ThenInclude(x => x.TskIntervalUnitNavigation)
                    .Include(x => x.Details).ThenInclude(x => x.EffectDetails)
                    .AsSplitQuery()
                    .FirstOrDefaultAsync(x => x.LccPartOf == lcc.Id);
            });

            if (firstChild != null)
            {
                var firstChildLcc = mapper.Map<LccModel>(firstChild);
                riskObject = firstChildLcc.RiskObject;
                lcc.Risks = riskObject.Risks;
                template = firstChildLcc.RiskObject.Fmeca;
            }
        }

        //risk object needed for risk lcc calculation
        if (riskObject != null)
        {
            //Check if Fmeca template is available on lcc level (linked risk object)
            template = lcc.RiskObject?.Fmeca;
        }
        //si id for SI lcc calculation.
        else if (lcc.SiId.HasValue)
        {
            //get risks from LCC --> SI --> PickSI --> MRB
            lcc.Risks = lcc.Si.PickAssets.Select(x => x.Risk).ToList();
            //Get template from first risk from SI
            template = lcc.Risks.FirstOrDefault(x => x.RiskObject.Fmeca != null)?.Template;
        }

        //Get Tasks
        var tasks = new List<TaskModel>();
        foreach (var risk in lcc.Risks)
        {
            tasks.AddRange(risk.Tasks);
        }

        lcc.Tasks = tasks;

        return template == null ? lcc : await GenericLccCalculationAsync(lcc, template);
    }

    private async Task<LccModel> CalculateLccByRamsDiagramAsync(LccModel lcc)
    {
        // Delete all details and effect details
        await ExecuteWithContextAsync(async context =>
        {
            await context.Set<Lccdetail>()
                .Where(x => x.LccDetLccId == lcc.Id)
                .ExecuteDeleteAsync();
        });

        //Prepare calculation: discount rate
        var discountRate = (lcc.DiscountRate is > 0
            ? lcc.DiscountRate
            : (await lookupManager.GetLookupSettingByPropertyNameAsync("DiscountRate")).DecimalValue) / 100m;

        //Prepare calculation: get number of years
        var numberOfYears = lcc.MaxYears;

        //create year collection
        var yearCollection = new LccYearCollection(lcc, numberOfYears, discountRate ?? 0);

        //Calculate and store years collection
        return await CreateLccDetailsForRamsForYearsAsync(lcc, yearCollection, numberOfYears);
    }

    public async Task RecalculateLccByRiskAsync(int? riskId)
    {
        if (riskId == null) return;

        var lccId = await ExecuteWithContextAsync(async context =>
        {
            var mrb = await context.Mrb.AsNoTracking().FirstOrDefaultAsync(x => x.Mrbid == riskId);
            var risk = mapper.Map<RiskModel>(mrb);

            return (await context.Lcc.AsNoTracking().FirstOrDefaultAsync(x =>
                x.LccRiskObject == risk.RiskObjectId &&
                x.LccChildObject == risk.CollectionId &&
                x.LccChildObject1 == risk.InstallationId &&
                x.LccChildObject2 == risk.SystemId &&
                x.LccChildObject3 == risk.ComponentId &&
                x.LccChildObject4 == risk.AssemblyId))?.LccId;
        });

        if (!lccId.HasValue) return;
        await RecursiveLccRecalculationAsync(lccId.Value);
    }

    public async Task RecalculateLccByRiskAsync(RiskModel risk)
    {
        if (risk == null) return;

        var lccId = await ExecuteWithContextAsync(async context =>
        {
            return (await context.Lcc.AsNoTracking().FirstOrDefaultAsync(x =>
                x.LccRiskObject == risk.RiskObjectId &&
                x.LccChildObject == risk.CollectionId &&
                x.LccChildObject1 == risk.InstallationId &&
                x.LccChildObject2 == risk.SystemId &&
                x.LccChildObject3 == risk.ComponentId &&
                x.LccChildObject4 == risk.AssemblyId))?.LccId;
        });

        if (!lccId.HasValue) return;
        await RecursiveLccRecalculationAsync(lccId.Value);
    }

    public async Task DeleteLccAsync(int id)
    {
        await ExecuteWithSaveAsync(async context =>
        {
            var lcc = await context.Lcc.FirstOrDefaultAsync(x => x.LccId == id);
            if (lcc != null)
            {
                context.Lcc.Remove(lcc);
            }
        });
    }

    #region LCC calculation helpers

    private async Task RecursiveLccRecalculationAsync(int lccId)
    {
        while (true)
        {
            var lcc = await GetLccDetailedAsync(lccId);
            await CalculateLccAsync(lcc);

            if (lcc.PartOf is > 0)
            {
                lccId = lcc.PartOf.Value;
                continue;
            }

            break;
        }
    }

    private async Task<LccModel> GenericLccCalculationAsync(LccModel lcc, RiskMatrixTemplateModel template)
    {
        // Delete all details and effect details
        await ExecuteWithContextAsync(async context =>
        {
            await context.Set<Lccdetail>()
                .Where(x => x.LccDetLccId == lcc.Id)
                .ExecuteDeleteAsync();
        });

        lcc.Details = [];

        //Prepare calculation: discount rate
        var discountRate = (lcc.DiscountRate is > 0
            ? lcc.DiscountRate
            : (await lookupManager.GetLookupSettingByPropertyNameAsync("DiscountRate")).DecimalValue) / 100m;

        //Prepare calculation: get number of years
        var numberOfYears = lcc.MaxYears;

        //create year collection
        var yearCollection = new LccYearCollection(lcc, numberOfYears, discountRate ?? 0);

        //create list of risks
        var riskList = new LccRiskList(lcc);

        //calculate risk list
        riskList.Calculate(yearCollection, numberOfYears,
            (await lookupManager.GetLookupSettingByPropertyNameAsync("WeibullFactor"))?.DecimalValue ?? 0,
            (await lookupManager.GetLookupSettingByPropertyNameAsync("DepreciationPct"))?.DecimalValue ?? 0,
            (await lookupManager.GetLookupSettingByPropertyNameAsync("SpareManPct"))?.DecimalValue ?? 0,
            lcc);

        // Get lcc from DB, update it, and save changes
        lcc = await ExecuteWithSaveAsync(
            async context =>
            {
                var dbLcc = await context.Set<Lcc>()
                    .AsSplitQuery()
                    .FirstOrDefaultAsync(x => x.LccId == lcc.Id);

                //First map lcc to model without relationships, then map the updated properties to the db object
                mapper.Map(lcc, dbLcc);

                //create lcc details and lcc effect details
                await CreateLccDetailsForYearsAsync(lcc, lcc, yearCollection, template, numberOfYears);

                //calculate new values Lcc
                CalculateAndUpdateLcc(dbLcc, lcc, yearCollection);
                var result = context.Update(dbLcc);
                return result.Entity;
            },
            async (_, entity) => await GetLccDetailedAsync(entity.LccId));

        //then calculate VdmXl (with adjusted Details and Effect Details available - by using GetLccDetailed)
        await CalculateVdmXlAsync(lcc);

        //Return LccModel, not via lcc because then the data is not updated with the VdmXl data...
        return await GetLccDetailedAsync(lcc.Id);
    }

    private async Task<LccModel> CreateLccDetailsForRamsForYearsAsync(LccModel lcc, LccYearCollection yearCollection,
        int numberOfYears)
    {
        var ramsDiagram = await ExecuteWithContextAsync(async context =>
        {
            return await context.RamsDiagram.Include(x => x.Rams)
                .FirstOrDefaultAsync(x => x.RamsDgId == lcc.RamsDiagramId);
        });

        if (ramsDiagram == null)
            return null;

        for (var year = 1; year <= numberOfYears; year++)
        {
            var yearItem = yearCollection[year - 1];
            var ramsContainerDetailItem =
                await CalculateRamsContainerAsync(mapper.Map<RamsDiagramModel>(ramsDiagram), lcc.RamsId.Value, yearItem,
                    lcc);
            yearItem.YearSumItem.AddValues(ramsContainerDetailItem);
            yearItem.YearSumItem.AddColumnItems(ramsContainerDetailItem, lcc);
            yearItem.AddFailureRate(ramsContainerDetailItem.FailureRate);
        }

        await CreateLccDetailsForYearsAsync(lcc, lcc, yearCollection, null, numberOfYears, true);

        // Get lcc from DB, update it, and save changes
        lcc = await ExecuteWithSaveAsync(
            async context =>
            {
                var dbLcc = await context.Set<Lcc>()
                    .AsSplitQuery()
                    .AsNoTracking().FirstOrDefaultAsync(x => x.LccId == lcc.Id);

                //calculate new values Lcc
                var updatedLcc = CalculateAndUpdateLcc(dbLcc, lcc, yearCollection);

                context.Update(updatedLcc);
                return updatedLcc;
            },
            async (context, entity) => await GetLccDetailedAsync(entity.LccId));

        //then calculate VdmXl (with adjusted Details and Effect Details available - by using GetLccDetailed)
        await CalculateVdmXlAsync(lcc);

        //Return LccModel, not via lcc because then the data is not updated with the VdmXl data...
        return await GetLccDetailedAsync(lcc.Id);
    }

    private async Task<LccDetailItem> CalculateRamsContainerAsync(RamsDiagramModel diagram, int ramsId,
        LccYearItem yearItem, LccModel lcc)
    {
        var ramsContainerItem = new LccDetailItem();
        var failureRateContainer = 0.0m;
        var parallelCount = 0;
        var weiBullFactor = (await lookupManager.GetLookupSettingByPropertyNameAsync("WeibullFactor"))?.DecimalValue ??
                            0;
        var defaultDepreciationPct =
            (await lookupManager.GetLookupSettingByPropertyNameAsync("DepreciationPct"))?.DecimalValue ?? 0;
        var defaultSpareManagementPct =
            (await lookupManager.GetLookupSettingByPropertyNameAsync("SpareManPct"))?.DecimalValue ?? 0;

        foreach (var rams in diagram.Rams.OrderBy(x => x.Id))
        {
            //If outer container
            if (rams.Id == ramsId)
            {
                ramsContainerItem.RamsDiagramId = rams.DiagramId;
                ramsContainerItem.RamsId = rams.Id;

                failureRateContainer = new FailureRateHelper((decimal) rams.Mtbftechn, weiBullFactor).Constant();
                parallelCount = rams.ParallelBlocks ?? 1;

                if (rams.CostOwner == true)
                {
                    CalculateRamsBlockAsync(rams, ramsContainerItem, yearItem, weiBullFactor, defaultDepreciationPct,
                        defaultSpareManagementPct, lcc);
                    break;
                }

                continue;
            }

            if (rams.Container)
            {
                if (rams.CostOwner == true)
                {
                    CalculateRamsBlockAsync(rams, ramsContainerItem, yearItem, weiBullFactor, defaultDepreciationPct,
                        defaultSpareManagementPct, lcc);
                }
                else
                {
                    var ramsBlockItem = await CalculateRamsContainerAsync(diagram, rams.Id, yearItem, lcc);
                    ramsBlockItem.Name = rams.Name;
                    ramsContainerItem.DetailItems.Add(ramsBlockItem);
                    yearItem.RamsBlockDetails
                        .Add(ramsBlockItem);
                }

                continue;
            }

            if (rams.DiagramRefId == null && rams.DiagramRefId != 0)
            {
                var ramsDiagram = await ExecuteWithContextAsync(async context =>
                {
                    return await context.RamsDiagram.Include(x => x.Rams)
                        .FirstOrDefaultAsync(x => x.RamsDgId == rams.DiagramRefId);
                });

                if (ramsDiagram == null)
                    continue;

                var ramsBlockItem =
                    await CalculateRamsContainerAsync(mapper.Map<RamsDiagramModel>(ramsDiagram), 0, yearItem, lcc);
                ramsBlockItem.Name = rams.Name;
                ramsContainerItem.DetailItems.Add(ramsBlockItem);
                yearItem.RamsBlockDetails.Add(ramsBlockItem);
                continue;
            }

            CalculateRamsBlockAsync(rams, ramsContainerItem, yearItem, weiBullFactor, defaultDepreciationPct,
                defaultSpareManagementPct, lcc);
        }

        ramsContainerItem.CalcSumFromDetails(failureRateContainer, parallelCount);
        return ramsContainerItem;
    }

    private void CalculateRamsBlockAsync(RamsModel rams, LccDetailItem ramsContainerItem,
        LccYearItem yearItem, decimal weibullFactor, decimal depreciationPct, decimal spareManagementPct, LccModel lcc)
    {
        LccDetailItem ramsBlockItem = null;
        if (rams.RiskObjectId == null || rams.RiskObjectId == 0 || rams.LinkType == null ||
            rams.LinkType == (int) RamsLinkType.NotSet)
        {
            ramsBlockItem = SimpleCostPerRamsBlock(rams, yearItem.Year, weibullFactor);
        }
        else
        {
            LccRamsItem ramsItem;
            if (yearItem.Year == 1)
            {
                ramsItem = new LccRamsItem(rams);
                _ramsItems.Add(ramsItem);
                LccYearCollection.ReplacementValue += ramsItem.NewValue;
            }
            else
                ramsItem = _ramsItems.FirstOrDefault(x => x.RamsId == rams.Id);

            if (ramsItem == null)
            {
                return;
            }

            if (ramsItem.RiskList != null)
                ramsBlockItem =
                    ramsItem.RiskList.CalcYear(yearItem.Year, weibullFactor, depreciationPct, spareManagementPct, lcc);
        }

        if (ramsBlockItem != null)
        {
            ramsContainerItem.DetailItems.Add(ramsBlockItem);
            ramsBlockItem.Name = rams.Name;

            yearItem.RamsBlockDetails.Add(ramsBlockItem);
        }
    }

    private static LccDetailItem SimpleCostPerRamsBlock(RamsModel rams, int year, decimal weibullFactor)
    {
        var ramsBlockDetailItem = new LccDetailItem
        {
            TypedTaskCost =
            {
                [(int) TypedTaskCost.Task] = rams.PreventiveCost ?? 0
            }
        };

        var lccCalcAfter = new FailureRateHelper((decimal) rams.Mtbftechn, weibullFactor);
        var distAfter = (double) lccCalcAfter.Constant();

        // Corrective cost dependent on failure rate
        double correctiveCost = 0;
        if (rams.CircuitDepCorrCost != null)
        {
            correctiveCost = (double) rams.CircuitDepCorrCost.Value * (rams.LccusePfd ? rams.Pfd.Value : distAfter);
            ramsBlockDetailItem.CircuitDepCorrCost = (decimal) rams.CircuitDepCorrCost;
        }

        if (rams.FailCorrCost != null)
        {
            correctiveCost += (double) rams.FailCorrCost * distAfter;
            ramsBlockDetailItem.RiskAfterIndependent =
                (decimal) (rams.FailCorrCost * (decimal) distAfter); //27-10-12 use the risk not the effect
        }

        if (rams.TechnCorrCost != null)
        {
            correctiveCost += (double) rams.TechnCorrCost * distAfter;
            ramsBlockDetailItem.DirectCost = (decimal) rams.TechnCorrCost;
        }

        ramsBlockDetailItem.FailureRate = (decimal) distAfter;
        ramsBlockDetailItem.RiskAfter = (decimal) correctiveCost;
        ramsBlockDetailItem.CorrectiveCost = (decimal) correctiveCost;
        ramsBlockDetailItem.RamsDiagramId = rams.DiagramId;
        ramsBlockDetailItem.RamsId = rams.Id;
        return ramsBlockDetailItem;
    }

    private async Task CreateLccDetailsForYearsAsync(LccBaseModel lcc, LccModel lccModel,
        LccYearCollection yearCollection,
        RiskMatrixTemplateModel template, int numberOfYears, bool isRamsCalculation = false)
    {
        await ExecuteWithSaveAsync(async context =>
        {
            var newLccDetails = new List<Lccdetail>();
            var lccStartYear = lccModel.StartYear - 1;

            //18-05-12 Correction for the number of years
            for (var year = 1; year <= numberOfYears; year++)
            {
                var detail = new Lccdetail
                {
                    LccDetYear = year,
                    LccDetLccId = lcc.Id,
                    EffectDetails = new List<LcceffectDetail>()
                };
                newLccDetails.Add(detail);

                //Merge the taskTotals with the MrbSum
                var yearItem = yearCollection[year - 1];
                //New Optimal preventive cost calculation (weighing)

                yearItem.YearSumItem.AddValues(yearItem.MrbTaskSumItem);
                yearItem.YearSumItem.AddColumnItems(yearItem.MrbTaskSumItem, lccModel);

                // need to use the collection for calculating the value of a certain year (AEC,NPV)
                yearCollection.CalcYearItem(year);

                //update Lcc detail with core figures (ToDetailRow)
                if (yearCollection.Count > year - 1)
                {
                    detail.LccDetDepreciation = LccYearCollection.ReplacementValue / year;
                    yearItem.UpdateLccDetail(detail, lccStartYear);
                    detail.LccDetAec = yearCollection.Aec(year);

                    if (detail.LccDetAec < yearCollection.MinAec)
                    {
                        yearCollection.MinAec = detail.LccDetAec.Value;
                        yearCollection.OptimalYear = year;
                    }
                }

                if (isRamsCalculation)
                {
                    foreach (var ramsBlock in yearItem.RamsBlockDetails)
                    {
                        if (ramsBlock == null)
                            continue;
                        var effectDetail = new LcceffectDetail
                        {
                            LccEfctLccId = lcc.Id,
                            LccEfctLccDetailId = detail.LccDetId,
                            LccEfctRamsId = ramsBlock.RamsId,
                            LccEfctRamsDiagramId = ramsBlock.RamsDiagramId,
                            LccEfctYear = year,
                            LccEfctEffectColumn = ramsBlock.EffectColumn,
                            LccEfctEffectName = template != null
                                ? FmecaHelper.EffectName(ramsBlock.EffectColumn, template)
                                    .TruncateMax(50, 0)
                                : ramsBlock.Name.TruncateMax(50, 0),
                            LccEfctType = (int) EffectTypes.RamsDetail
                        };

                        ramsBlock.ToEffectDetail(effectDetail);
                        detail.EffectDetails.Add(effectDetail);
                    }
                }
                else
                {
                    foreach (var fmecaEffectItem in yearItem.YearSumItem.DetailItems)
                    {
                        if (fmecaEffectItem.IsEmpty())
                            continue;
                        var effectDetail = new LcceffectDetail
                        {
                            LccEfctLccId = lcc.Id,
                            LccEfctLccDetailId = detail.LccDetId,
                            LccEfctYear = year + lccStartYear,
                            LccEfctEffectColumn = fmecaEffectItem.EffectColumn,
                            LccEfctEffectName = template != null
                                ? FmecaHelper.EffectName(fmecaEffectItem.EffectColumn, template)
                                    .TruncateMax(50, 0)
                                : fmecaEffectItem.Name.TruncateMax(50, 0),
                            LccEfctType = (int) EffectTypes.EffectDetail
                        };

                        fmecaEffectItem.ToEffectDetail(effectDetail);
                        detail.EffectDetails.Add(effectDetail);
                    }
                }
            }

            context.Lccdetail.UpdateRange(newLccDetails);
            context.Set<Lccdetail>().AddRange(newLccDetails);
        });
    }

    private static Lcc CalculateAndUpdateLcc(Lcc dbLcc, LccModel lcc, LccYearCollection yearCollection)
    {
        if (dbLcc == null) return null;

        var optimalYear = yearCollection.OptimalYear;
        var startYear = lcc.StartYear - 1;

        dbLcc.LccReplacementValue = lcc.ReplacementValue;
        dbLcc.LccDiscountRate = lcc.DiscountRate;
        dbLcc.LccProductionCost = lcc.ProductionCost;
        dbLcc.LccExclude = lcc.Exclude;

        if (yearCollection.OptimalYear > 0)
        {
            dbLcc.LccNpvyear = optimalYear + startYear;
            var optimalYearItem = yearCollection[optimalYear - 1];
            dbLcc.LccNpv = 0;

            for (var year = 1; year <= optimalYear; year++)
            {
                dbLcc.LccNpv += yearCollection[year - 1].Npv;
            }

            if (dbLcc.LccNpv != null)
                dbLcc.LccNpv = Math.Round(dbLcc.LccNpv.Value, 2);

            dbLcc.LccTotalAverageCost = Math.Round(optimalYearItem.SumItem.TotalCost / optimalYear, 2);
            dbLcc.LccOptimalAverageCorrectiveCost = Math.Round(optimalYearItem.YearSumItem.AllCorrectiveCost, 2);
            dbLcc.LccOptimalAveragePreventiveCost = Math.Round(optimalYearItem.YearSumItem.AllPreventiveCost, 2);
            dbLcc.LccAverageOptimalCost = 2 * Math.Round(optimalYearItem.YearSumItem.AllOptimalCost, 2);
            dbLcc.LccAec = Math.Round(yearCollection.MinAec, 2);

            //Don't show Optimization when the optimal cost are zero
            if (dbLcc.LccTotalAverageCost != 0 && dbLcc.LccAverageOptimalCost > 0)
            {
                //The saving potential is two times the optimal cost based on the assumption that corrective cost = preventive cost on the optimum
                dbLcc.LccPotential =
                    Math.Round(
                        (decimal) (100.0m * (dbLcc.LccTotalAverageCost - dbLcc.LccAverageOptimalCost) /
                                   dbLcc.LccTotalAverageCost), 2);
                if (dbLcc.LccPotential <= 0) dbLcc.LccPotential = null;
            }
            else
            {
                dbLcc.LccPotential = null;
            }

            //included the direct cost
            if (LccYearCollection.ReplacementValue > 0)
                dbLcc.LccMcRav =
                    Math.Round(
                        100.0m * (optimalYearItem.SumItem.TotalCost / optimalYear) / LccYearCollection.ReplacementValue,
                        2);
            else
                dbLcc.LccMcRav = null;
        }
        else
        {
            dbLcc.LccNpvyear = 0;
            dbLcc.LccNpv = 0;
            dbLcc.LccTotalAverageCost = 0;
            dbLcc.LccOptimalAverageCorrectiveCost = 0;
            dbLcc.LccOptimalAveragePreventiveCost = 0;
            dbLcc.LccAverageOptimalCost = 0;
            dbLcc.LccAec = 0;
            dbLcc.LccPotential = null;

            if (LccYearCollection.ReplacementValue > 0)
                dbLcc.LccMcRav = 0;
            else
                dbLcc.LccMcRav = null;
        }

        return dbLcc;
    }

    private async Task CalculateVdmXlAsync(LccModel lcc)
    {
        var discountRate = (lcc.DiscountRate is > 0
            ? lcc.DiscountRate
            : (await lookupManager.GetLookupSettingByPropertyNameAsync("DiscountRate")).DecimalValue) / 100m;

        if (lcc.RiskObject == null) return;
        //to do: ophalen voor template tbv SI analyses

        // Delete all VdmXl Calculations
        await ExecuteWithContextAsync(async context =>
        {
            await context.Set<Vdmxl>()
                .Where(x => x.VdmLccId == lcc.Id)
                .ExecuteDeleteAsync();
        });

        if (discountRate != null)
        {
            var calculationResult = LccVdmXlCalc.Calculate(lcc, (double) discountRate);

            await ExecuteWithSaveAsync(async context =>
            {
                var newMyVdmxl = mapper.Map<Vdmxl>(calculationResult);
                await context.AddAsync(newMyVdmxl);
            });
        }
    }

    #endregion

    #region Treeview helpers

    public TreeNodeGeneric<LccTreeObject> RemoveLccNodeFromTree(TreeNodeGeneric<LccTreeObject> tree, List<int> path)
    {
        var node = path.Aggregate(tree, (current, t) => current.Nodes[t]);

        do
        {
            node.Parent.Nodes.Remove(node);
            node = node.Parent;
        } while (node.Nodes.Count == 0 && node?.Parent != null);

        return tree;
    }

    private static void AddNode(TreeNodeGeneric<LccTreeObject> tree, Lcc item,
        TreeNodeGeneric<LccTreeObject> parentNode)
    {
        var nodeType = GetNodeType(item);

        var node = new TreeNodeGeneric<LccTreeObject>(
                new LccTreeObject
                    {Id = item.LccId, Name = item.LccName, NodeType = nodeType}, parentNode)
            {Id = item.LccId, Name = item.LccName, Icon = nodeType.ToIcon()};

        if (parentNode == null)
            tree.Nodes.Add(node);
        else
            parentNode.Nodes.Add(node);

        if (item.LccChildren == null || item.LccChildren.Count == 0) return;
        foreach (var child in item.LccChildren.OrderBy(x => x.LccChildren?.Any() == false).ThenBy(x => x.LccName))
        {
            AddNode(tree, child, node);
        }
    }

    private static RiskTreeNodeType GetNodeType(Lcc lcc)
    {
        if (lcc.LccChildObject4 != null)
            return RiskTreeNodeType.Assembly;

        if (lcc.LccChildObject3 != null)
            return RiskTreeNodeType.Component;

        if (lcc.LccChildObject2 != null)
            return RiskTreeNodeType.System;

        return lcc.LccChildObject4 != null ? RiskTreeNodeType.Installation : RiskTreeNodeType.RiskObject;
    }

    private void AddLccRecursive(ICollection<LccModel> lccs, Lcc lcc, List<Lcc> dbLccs)
    {
        lccs.Add(mapper.Map<LccModel>(lcc));
        var children = dbLccs.Where(x => x.LccPartOf == lcc.LccId).ToList();

        if (children.Count == 0) return;
        foreach (var child in children)
        {
            AddLccRecursive(lccs, child, dbLccs);
        }
    }

    #endregion
}