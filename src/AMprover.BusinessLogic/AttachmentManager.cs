using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Infrastructure;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;

namespace AMprover.BusinessLogic;

public interface IAttachmentManager
{
    Task<List<AttachmentModel>> GetAttachmentsAsync();
    Task<List<AttachmentCategoryModel>> GetAttachmentCategoriesAsync();

    Task<AttachmentModel> SaveAttachmentAsync(AttachmentModel model);
    Task<AttachmentCategoryModel> SaveAttachmentCategoryAsync(AttachmentCategoryModel model);

    Task<(bool success, string error)> DeleteAttachmentCategoryAsync(int attachmentCategoryId);
    Task<(bool success, string error)> DeleteAttachmentAsync(int attachmentId);
}

public class AttachmentManager(
    IAssetManagementDbContextFactory dbContextFactory,
    AuthenticationStateProvider authenticationStateProvider,
    IMapper mapper)
    : BaseManager(dbContextFactory, authenticationStateProvider), IAttachmentManager
{

    public async Task<List<AttachmentModel>> GetAttachmentsAsync()
    {
        return await ExecuteWithContextAsync(async context =>
        {
            return await context.Attachment
                .Select(x => mapper.Map<AttachmentModel>(x))
                .ToListAsync();
        });
    }

    public async Task<List<AttachmentCategoryModel>> GetAttachmentCategoriesAsync()
    {
        return await ExecuteWithContextAsync(async context =>
        {
            return await context.AttachmentCategory
                .Select(x => mapper.Map<AttachmentCategoryModel>(x))
                .ToListAsync();
        });
    }

    public async Task<(bool success, string error)> DeleteAttachmentCategoryAsync(int attachmentCategoryId)
    {
        // First check if the category can be deleted
        var checkResult = await ExecuteWithContextAsync(async context =>
        {
            var count = await context.Attachment.CountAsync(x => x.AtchCatgoryId == attachmentCategoryId);
            var error = count > 0
                ? $"Unable to delete, category is used by {count} Attachments"
                : null;

            return (count == 0, error);
        });

        if (!checkResult.Item1)
        {
            return (false, checkResult.Item2);
        }

        // If it can be deleted, perform the delete operation
        return await ExecuteWithSaveAsync(async context =>
        {
            var model = await context.AttachmentCategory.FirstOrDefaultAsync(x => x.AtchCatId == attachmentCategoryId);
            if (model == null) return (false, checkResult.Item2);
            context.AttachmentCategory.Remove(model);
            return (true, checkResult.Item2);

        });
    }

    public async Task<(bool success, string error)> DeleteAttachmentAsync(int attachmentId)
    {
        return await ExecuteWithSaveAsync(async context =>
        {
            var attachment = await context.Attachment
                .FirstOrDefaultAsync(x => x.AtchId == attachmentId);

            if (attachment == null)
            {
                return (true, string.Empty);
            }

            context.Attachment.Remove(attachment);
            return (true, string.Empty);
        });
    }

    public async Task<AttachmentCategoryModel> SaveAttachmentCategoryAsync(AttachmentCategoryModel model)
    {
        return await ExecuteWithSaveAsync(async context =>
        {
            var dbModel = mapper.Map<Data.Entities.AM.AttachmentCategory>(model);
            context.Update(dbModel);

            return mapper.Map<AttachmentCategoryModel>(dbModel);
        });
    }

    public async Task<AttachmentModel> SaveAttachmentAsync(AttachmentModel model)
    {
        return await ExecuteWithSaveAsync(async context =>
        {
            var dbModel = mapper.Map<Data.Entities.AM.Attachment>(model);
            context.Update(dbModel);

            return mapper.Map<AttachmentModel>(dbModel);
        });
    }
}
