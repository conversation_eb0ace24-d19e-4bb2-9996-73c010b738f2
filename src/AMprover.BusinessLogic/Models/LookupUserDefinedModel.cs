using System;
using System.ComponentModel.DataAnnotations;
using AMprover.BusinessLogic.Attributes;

namespace AMprover.BusinessLogic.Models;

public class LookupUserDefinedModel
{
    [GridReadOnly]
    [GridWidthPixels(60)]
    public int Id { get; set; }

    [GridReadOnly]
    public string Filter { get; set; }

    [Required]
    public string ShortDescription { get; set; }
    public string LongDescription { get; set; }

    [Required]
    public int? Value { get; set; }

    [GridReadOnly]
    public string ModifiedBy { get; set; }

    [GridReadOnly]
    [GridWidthPixels(90)]
    public DateTime? DateModified { get; set; }

    public override string ToString()
    {
        return $"{ShortDescription} - {Value}";
    }
}