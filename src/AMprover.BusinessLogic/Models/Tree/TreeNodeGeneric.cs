using System.Collections.Generic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Models.RiskAnalysis;

namespace AMprover.BusinessLogic.Models.Tree;

public class TreeNodeGeneric<T>
{
    public TreeNodeGeneric(T item, TreeNodeGeneric<T> parent)
    {
        Source = item;
        Parent = parent;

        if (item is RiskTreeObject r)
        {
            Name = r.Name;
            SortOrder = r.SortOrder;
        }    

        if (item is LccTreeObject l)
            Name = l.Name;
        
        if (item is RamsTreeObject ra)
            Name = ra.Name;
    }

    public string Name { get; set; }
    public int Id { get; set; }
    public string Url { get; set; }
    public bool IsSelected { get; set; }
    public bool IsDisabled { get; set; }
    public string Icon { get; set; }
    public List<TreeNodeGeneric<T>> Nodes { get; set; } = [];
    public T Source { get; set; }
    public bool Open { get; set; }
    public TreeNodeGeneric<T> Parent { get; set; }
    public FilteredState FilteredState { get; set; }
    public int SortOrder { get; set; }
    public bool Hidden { get; set; }

    /// <summary>
    /// Get Ancestors Top to bottom (Great grandparent, grandparent, parent)
    /// </summary>
    /// <returns></returns>
    public List<TreeNodeGeneric<T>> GetAncestors()
    {
        var result = new List<TreeNodeGeneric<T>>();

        var parentNode = Parent;
        while (parentNode != null)
        {
            result.Add(parentNode);
            parentNode = parentNode.Parent;
        }

        result.Reverse();
        return result;
    }

    public string GetIconClass()
    {
        return $"{Icon ?? "fas fa-cog"} treenode-icon";
    }

    public string GetDisabledClass()
    {
        return IsDisabled ? "disabled" : string.Empty;
    }

    public override string ToString()
    {
        return $"{Name} - {Source.GetType()} - Children: {Nodes.Count}";
    }
}