using System.ComponentModel.DataAnnotations;
using AMprover.BusinessLogic.Attributes;

namespace AMprover.BusinessLogic.Models;

/// <summary>
/// An interval unit is a generic way for specifying input for calculations related to a given timespan. To be able to calculate with various types of units, we take a (non leap) year as baseline. So essentially you can configure any type of units be specifying the amount of that type in a single year of time.
/// </summary>
public class IntervalUnitModel
{
    /// <summary>
    /// Internal AMprover ID
    /// </summary>
    [Required]
    [GridReadOnly]
    [GridWidthPixels(60)]
    public int? Id { get; set; }

    [Required]
    [GridWidthPixels(70)]
    [MaxLength(10)]
    public string ShortKey { get; set; }

    [Required]
    [MaxLength(50)]
    public string Name { get; set; }

    /// <summary>
    /// The times in a year time the type of interval occurs. Example: 365 for units in days, or 0,0111 to specify once in a human lifetime based on average of 90 years.
    /// </summary>
    [Range(0, int.MaxValue)]
    public decimal? UnitsPerYear { get; set; }

    public EntityMetadata Metadata { get; set; }

    public override string ToString()
    {
        return Name;
    }
}