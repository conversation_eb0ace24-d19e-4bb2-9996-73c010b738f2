using System;
using System.Collections.Generic;
using AMprover.BusinessLogic.Attributes;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.PortfolioSetup;

namespace AMprover.BusinessLogic.Models.RiskAnalysis;

public class RiskObjectModelFlat : BaseModel
{
    public int ScenarioId { get; set; }
    public int ObjectId { get; set; }
    public int? ParentObjectId { get; set; }
    public int? DepartmentId { get; set; }
    public string AnalysisType { get; set; }
    public int FmecaId { get; set; }
    public int? SerialNumber { get; set; }
    public string MrbstartPoint { get; set; }
    public byte[] FuncDecomp { get; set; }
    public decimal? ProdCostHour { get; set; }
    public int? NoOfInstallation { get; set; }
    public string Responsible { get; set; }
    public decimal? RforSpares { get; set; }
    public string AmrbAttended { get; set; }
    public string AbmState { get; set; }
    public int? Lccyear { get; set; }
    public int? CopiedFrom { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime? DateModified { get; set; }
    public int? Status { get; set; }
    public decimal DownTimeCost { get; set; }
    public decimal? DirectCorrectiveCostBefore { get; set; }
    public decimal? CorrectiveCostBefore { get; set; }
    public decimal? PreventiveCostAfter { get; set; }
    public decimal? DirectCorrectiveCostAfter { get; set; }
    public decimal? CorrectiveCostAfter { get; set; }
    public decimal? PreventiveCostPmo { get; set; }
    public decimal? DirectCorrectiveCostPmo { get; set; }
    public decimal? CorrectiveCostPmo { get; set; }
    public decimal? CorrectiveCostLcc { get; set; }
    public decimal? PreventiveCostLcc { get; set; }
    public decimal? DirectCorrectiveCostLcc { get; set; }
    public decimal? Npv { get; set; }
    public decimal? Aec { get; set; }
    public decimal? LccPotential { get; set; }
}

public class RiskObjectModel : RiskObjectModelFlat
{
    public ScenarioModel Scenario { get; set; }
    public DepartmentModel Department { get; set; }
    public ObjectModel Object { get; set; }
    public ObjectModel ParentObject { get; set; }
    public RiskMatrixTemplateModel Fmeca { get; set; }
    public LookupModel StatusNavigation { get; set; }

    [GridIgnore] public IEnumerable<RiskModel> Risks { get; set; } = new List<RiskModel>();
    [GridIgnore] public IEnumerable<AttachmentModel> Attachments { get; set; } = new List<AttachmentModel>();

    public LccModel Lcc { get; set; }

    public bool IsPmoType()
    {
        return !string.IsNullOrWhiteSpace(AnalysisType) &&
               AnalysisType.Contains("pmo", StringComparison.InvariantCultureIgnoreCase);
    }
}