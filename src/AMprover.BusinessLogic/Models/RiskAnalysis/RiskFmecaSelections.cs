using System.Collections.Generic;

namespace AMprover.BusinessLogic.Models.RiskAnalysis;

public class RiskFmecaSelections
{
    public FmecaSelection Pmo { get; set; }

    public FmecaSelection Before { get; set; }

    public FmecaSelection After { get; set; }
}

public class FmecaSelection
{
    public List<int?> Selected { get; set; } = [];

    public int? MtbfSelected { get; set; }

    public void SetSelected(int index, int value)
    {
            if (index < Selected.Count)
            {
                Selected[index] = value;
            }
            else
            {
                while (index > Selected.Count)
                    Selected.Add(null);

                Selected.Add(value);
            } 
        }
}