namespace AMprover.BusinessLogic.Models.RiskAnalysis;

public class ClusterTaskWithPlainObjectsModel: TaskWithPlainObjectsModel
{
    public string Installation { get; set; }
    public string System { get; set; }
    public string Component { get; set; }
    public string Assembly { get; set; }
    public int SortOrder { get; set; }
    public string <PERSON><PERSON>rio { get; set; }
    //Headername:verzameling (object0)
    public string Collection { get; set; }
    public string RiskObject { get; set; }
    public string Risk { get; set; }
    public string Type { get; set; }
    public string Description { get; set; }
    //Headername:Instructions 
    public string GeneralDescription { get; set; }
    public string Cluster { get; set; }
    public bool ClusterCostMember { get; set; }
    public decimal ClusterCosts { get; set; }
    public decimal Units { get; set; }
    public string UnitType { get; set; }
    public decimal EstimatedCosts { get; set; }
    public decimal DownTime { get; set; }
    public decimal Duration { get; set; }
}