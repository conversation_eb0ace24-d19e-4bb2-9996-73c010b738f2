using System.ComponentModel.DataAnnotations;
using AMprover.BusinessLogic.Attributes;
using AMprover.Data.Entities.AM;

namespace AMprover.BusinessLogic.Models.RiskAnalysis;

public class WorkPackageModel
{
    public WorkPackageModel() { }

    public WorkPackageModel(Workpackage workPackage)
    {
        Id = workPackage.WpId;
        Name = workPackage.WpName;
        Description = workPackage.WpDescription;
    }

    [GridReadOnly]
    [GridWidthPixels(60)]
    public int? Id { get; set; }

    [Required]
    [MaxLength(50)]
    public string Name { get; set; }

    [Required]
    [MaxLength(20)]
    public string ShortDescription { get; set; }

    public string Description { get; set; }

    [Range(0, int.MaxValue)]
    [GridWidthPixels(80)]
    public decimal Interval { get; set; }

    [Required]
    [GridFilterProperty("IntervalUnitNavigation.Name")]
    public int IntervalUnit { get; set; }
    public IntervalUnitModel IntervalUnitNavigation { get; set; } = new IntervalUnitModel();

    [GridFilterProperty("ExecutorNavigation.Name")]
    public int? Executor { get; set; }
    public ExecutorModel ExecutorNavigation { get; set; }

    public override string ToString()
    {
        return Name;
    }
}