using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using AMprover.BusinessLogic.Attributes;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.Failures;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.XMLEntities;

namespace AMprover.BusinessLogic.Models.RiskAnalysis;

public class RiskModelFlat : RiskBaseModel
{
    // ChildObject
    public int? CollectionId { get; set; }

    // ChildObject1 
    public int? InstallationId { get; set; }

    // RiskObject
    [GridFilterProperty("RiskObject.Name")]
    public int RiskObjectId { get; set; }

    // ChildObject2 
    public int? SystemId { get; set; }

    // ChildObject3
    public int? ComponentId { get; set; }

    // ChildObject4
    public int? AssemblyId { get; set; }

    // Used for mathics Tasks & Spares when importing
    public int? ImportId { get; set; }

    public string Description { get; set; }

    [Description("Failure mode")]
    [GridHeader("Failure mode")]
    public int? FailureModeId { get; set; }

    [Description("Cause")]
    [GridHeader("Cause")]
    public string FailureCause { get; set; }

    [Description("Consequences")]
    [GridHeader("Consequences")]
    public string FailureConsequences { get; set; }

    [Description("Failure category 1")]
    [GridHeader("Failure category 1")]
    public string FailureCategory1 { get; set; }

    [Description("Failure category 2")]
    [GridHeader("Failure category 2")]
    public string FailureCategory2 { get; set; }

    public decimal? DownTimeAfter { get; set; }
    public decimal? DownTimeBefore { get; set; }
    public decimal? DownTimePmo { get; set; }
    public string Remarks { get; set; }
    public string Remarks1 { get; set; }
    public string Function { get; set; }
    public string OpsProcedure { get; set; }
    public decimal? SpareCosts { get; set; }
    public decimal? SpareCostsPmo { get; set; }
    public decimal? CapCosts { get; set; }
    public decimal? ActionCosts { get; set; } = 0;
    public decimal? CurrentActionCosts { get; set; } = 0;
    public decimal? ActionCostsPmo { get; set; } = 0;
    public decimal? OptimalCosts { get; set; }
    public decimal? Aec { get; set; }
    public decimal? SapaIndex { get; set; }
    public string InitiatedBy { get; set; }
    public DateTime? DateInitiated { get; set; }
    public int? Status { get; set; }
    public Enums.Status? ItemStatus { get; set; }
    public int? MasterId { get; set; }
    [MaxLength(150)] public string Responsible { get; set; }
    [MaxLength(150)] public string Norm { get; set; }
    [MaxLength(10)] public string State { get; set; }
    public string ExclOptCb { get; set; }
    public string FmecaSelect { get; set; }
    public int FmecaVersion { get; set; }
    public decimal? EffectBefore { get; set; }
    public decimal? CustomBefore { get; set; }
    public decimal? MtbfBefore { get; set; }
    public decimal? RiskBefore { get; set; }
    public decimal? PointsBefore { get; set; }
    public decimal? EffectAfter { get; set; }
    public decimal? MtbfAfter { get; set; }
    public decimal? RiskAfter { get; set; } = 0;
    public decimal? PointsAfter { get; set; }
    public decimal? CustomAfter { get; set; }
    public decimal? CustomPmo { get; set; }
    public decimal? EffectPmo { get; set; }
    public decimal? MtbfPmo { get; set; }
    public decimal? RiskPmo { get; set; } = 0;
    public decimal? PointsPmo { get; set; }
    public decimal SpareManagementCost { get; set; }
    public decimal SpareManagementCostPmo { get; set; }
    public decimal? DirectCostBefore { get; set; }
    public decimal? DirectCostAfter { get; set; }
    public decimal? DirectCostPmo { get; set; }
    public decimal? CustomEffectBefore { get; set; }
    public decimal? CustomEffectAfter { get; set; }
    public decimal? CustomEffectPmo { get; set; }
    public decimal? PointsEffectBefore { get; set; }
    public decimal? PointsEffectAfter { get; set; }
    public decimal? PointsEffectPmo { get; set; }
    public decimal? CircuitAffectedCostBefore { get; set; }
    public decimal? CircuitAffectedCostAfter { get; set; }
    public decimal? CircuitAffectedCostPmo { get; set; }
    public string SafetyBefore { get; set; }
    public string SafetyAfter { get; set; }
    public string SafetyPmo { get; set; }
    public string MarketBefore { get; set; }
    public string MarketAfter { get; set; }
    public string MarketPmo { get; set; }
    public int? ChildType { get; set; }
    public int SortOrder { get; set; }
}

public class RiskModel : RiskModelFlat
{
    // ChildObject
    public ObjectModel Collection { get; set; }

    // ChildObject1 
    public ObjectModel Installation { get; set; }

    // RiskObject
    public RiskObjectModel RiskObject { get; set; }

    // ChildObject2 
    public ObjectModel System { get; set; }

    // ChildObject3
    public ObjectModel Component { get; set; }

    // ChildObject4
    public ObjectModel Assembly { get; set; }
    
    // Additional Data
    public int? AdditionalDataId { get; set; }
    public AdditionalDataModel AdditionalData { get; set; } = new();

    public FailureModeModel FailureMode { get; set; }

    public RiskMatrixTemplateModel Template { get; set; }
    public RiskMatrixDataGrid MainDataGrid { get; set; } = new RiskMatrixDataGrid();
    public List<RiskMatrixDataGrid> SubDataGrids { get; set; } = [];

    public RiskFmecaSelections FmecaSelections { get; set; }

    public List<SpareModel> Spares { get; set; } = [];
    public List<TaskModel> Tasks { get; set; } = [];
    public List<AttachmentModel> Attachments { get; set; } = [];

    public decimal? PreventiveCosts => (ActionCosts ?? 0) + (SpareCosts ?? 0) + SpareManagementCost;
    public decimal? SapaIndex => (PreventiveCosts ?? 0) == 0 
        ? null 
        : (RiskBefore - RiskAfter)/(PreventiveCosts ?? 1000000000); //NB. Make sure PreventiveCosts can't be zero

    public decimal? PreventiveCostsPmo => (ActionCostsPmo ?? 0) + (SpareCostsPmo ?? 0) + SpareManagementCostPmo;

    public decimal OptimalCostsCalculated => RiskAfter > (PreventiveCosts ?? 0)
        ? CalcOptimalCost(PreventiveCosts ?? 0, RiskAfter ?? 0, 3)
        : CalcOptimalCost(RiskAfter ?? 0, PreventiveCosts ?? 0, 4);

    /// Optimal image is determined by comparing action costs with costs after
    /// action costs > costs after --> left of optimum
    /// costs after > action costs --> right of optimum
    public string OptimalImage
    {
        get
        {
            if (IsSubOptimalLeft)
            {
                return "/images/suboptimalleft.png";
            }

            return IsSubOptimalRight
                ? "/images/suboptimalright.png"
                : "/images/optimal.png";
        }
    }

    public int OptimalPercentage
    {
        get
        {
            if (IsSubOptimalLeft)
            {
                return 15;
            }

            return IsSubOptimalRight ? 85 : 50;
        }
    }

    public bool IsSubOptimalLeft => OptimalCostsCalculated > (decimal) 1.2 * (PreventiveCosts ?? 0);

    public bool IsSubOptimalRight => OptimalCostsCalculated < (decimal) .8 * (PreventiveCosts ?? 0);

    #region template properties
    
    public string Currency { get; set; }
    
    public bool IsRiskAnalysis { get; set; }
    
    public bool IsSapaView { get; set; }
    
    public string Language { get; set; }
    
    public bool ShowSubColumns { get; set; }

    #endregion

    public FmecaSelData ToRiskData()
    {
        var data = new FmecaSelData
        {
            MrbId = Id.ToString(), FmecaMainSelGrid = new FmecaMainSelGrid
            {
                EffectColumns = MainDataGrid.TableColumns.Count.ToString(),
                CustomMtbfAfter = MainDataGrid.CustomMtbfAfter,
                CustomMtbfBefore = MainDataGrid.CustomMtbfBefore,
                CustomMtbfPmo = MainDataGrid.CustomMtbfPmo,
                MtbfSelectedAfter = MainDataGrid.MtbfSelectedAfter,
                MtbfSelectedBefore = MainDataGrid.MtbfSelectedBefore,
                MtbfSelectedPmo = MainDataGrid.MtbfSelectedPmo,
                FmecaSelColumns = new FmecaSelColumns
                {
                    FmecaSelColumn = MainDataGrid.TableColumns.Select(x => new FmecaSelColumn
                    {
                        ColIndex = x.ColumnIndex.ToString(),
                        CustomAfter = x.CustomAfter,
                        CustomBefore = x.CustomBefore,
                        CustomPmo = x.CustomPmo,
                        SelectBefore = x.SelectBefore,
                        SelectAfter = x.SelectAfter,
                        SelectPmo = x.SelectPmo,
                        ValueAfter = x.ValueAfter,
                        ValueBefore = x.ValueBefore,
                        ValuePmo = x.ValuePmo,
                        PointsBefore = x.PointsBefore,
                        PointsAfter = x.PointsAfter,
                        PointsPmo = x.PointsPmo
                    }).ToList()
                }
            }
        };

        data.FmecaSubGrids ??= new FmecaDataSubGrids();
        data.FmecaSubGrids.SubGrids ??= [];

        foreach (var subgrid in SubDataGrids)
        {
            data.FmecaSubGrids.SubGrids.Add(new DataSubGrid
            {
                Key = subgrid.ColumnId.ToString(),
                Value = new DataValue
                {
                    CustomMtbfAfter = subgrid.CustomMtbfAfter,
                    CustomMtbfBefore = subgrid.CustomMtbfBefore,
                    CustomMtbfPmo = subgrid.CustomMtbfPmo,
                    MtbfSelectedAfter = subgrid.MtbfSelectedAfter,
                    MtbfSelectedBefore = subgrid.MtbfSelectedBefore,
                    MtbfSelectedPmo = subgrid.MtbfSelectedPmo,
                    EffectColumns = subgrid.TableColumns.Count.ToString(),
                    FmecaSelColumns = new FmecaSelColumns
                    {
                        FmecaSelColumn = subgrid.TableColumns.Select(x => new FmecaSelColumn
                        {
                            ColIndex = x.ColumnIndex.ToString(),
                            CustomAfter = x.CustomAfter,
                            CustomBefore = x.CustomBefore,
                            CustomPmo = x.CustomPmo,
                            SelectBefore = x.SelectBefore,
                            SelectAfter = x.SelectAfter,
                            SelectPmo = x.SelectPmo,
                            ValueAfter = x.ValueAfter,
                            ValueBefore = x.ValueBefore,
                            ValuePmo = x.ValuePmo,
                            PointsBefore = x.PointsBefore,
                            PointsAfter = x.PointsAfter,
                            PointsPmo = x.PointsPmo
                        }).ToList()
                    }
                }
            });
        }

        return data;
    }

    #region helpers

    /// <summary>
    /// Calculate spare costs when changes are made to the spares. These calculations are stored on risk (MRB) level.
    /// </summary>
    /// <param name="defaultDepreciationPct"></param>
    /// <param name="defaultSpareManagementPct"></param>
    /// <param name="newSpare"></param>
    public void CalculateSpareCost(decimal? defaultDepreciationPct, decimal? defaultSpareManagementPct,
        SpareModel newSpare = null)
    {
        decimal totalSpareCost = 0;
        decimal spareManagementCost = 0;

        decimal totalSpareCostPmo = 0;
        decimal spareManagementCostPmo = 0;

        var spares = new List<SpareModel>();
        spares.AddRange(Spares);
        if (newSpare != null) spares.Add(newSpare);

        foreach (var spare in spares)
        {
            var spareCost = spare.Costs ?? 0;

            if (spare.ObjectCount > 0)
                spareCost /= spare.ObjectCount.Value;

            var deprecPct = spare.DepreciationPct ?? defaultDepreciationPct ?? 0;

            if (spare.Pmo)
                totalSpareCostPmo += spareCost * (deprecPct / 100);
            else
                totalSpareCost += spareCost * (deprecPct / 100);

            if (spare.YearlyCost.HasValue)
            {
                if (spare.Pmo)
                    spareManagementCostPmo += spare.YearlyCost.Value;
                else
                    spareManagementCost += spare.YearlyCost.Value;
            }
            else if (defaultSpareManagementPct.HasValue)
            {
                // now use the yearly cost of the spares
                // if not present then use default percentages
                if (spare.Pmo)
                    spareManagementCostPmo += spareCost * (deprecPct / 100) * defaultSpareManagementPct.Value / 100;
                else
                    spareManagementCost += spareCost * (deprecPct / 100) * defaultSpareManagementPct.Value / 100;
            }
        }

        SpareCostsPmo = decimal.Round(totalSpareCostPmo, 2);
        SpareCosts = decimal.Round(totalSpareCost, 2);
        
        SpareManagementCostPmo = decimal.Round(spareManagementCostPmo, 2);
        SpareManagementCost = decimal.Round(spareManagementCost, 2);
    }

    /// <summary>
    /// Calculate costs related to tasks. In AMprover 4 the terms action costs and task costs are used both
    /// </summary>
    /// <param name="defaultModificationPct"></param>
    /// <returns></returns>
    public void CalculateTasksCost(decimal? defaultModificationPct)
    {
        decimal valueOpted = 0;
        decimal valueCurrent = 0;

        var tasks = Tasks.Where(x => x.Removed != true);

        foreach (var task in tasks)
        {
            task.CalculateCosts();

            var interval = task is {Interval: > 0} ? task.Interval.Value : 1;
            var intervalCalcUnit = task.IntervalUnit?.UnitsPerYear ?? 1;
            var taskCost = task.Costs ?? 0;
            taskCost += task.DtCost ?? 0;

            task.Type ??= "tsk"; //To do: Make sure item is filled in import

            switch (task.Type.ToLower())
            {
                case "capex":
                    if (intervalCalcUnit != 0)
                        taskCost *= intervalCalcUnit / interval;
                    if (task.Pmo)
                        valueCurrent += taskCost / MtbfPmo ?? 0;
                    else
                        valueOpted += taskCost / MtbfAfter ?? 0;
                    break;
                case "mod":
                    if (intervalCalcUnit != 0)
                        taskCost *= intervalCalcUnit / interval;
                    if (task.Pmo)
                        valueCurrent += taskCost * defaultModificationPct / 100m ?? 0;
                    else
                        valueOpted += taskCost * defaultModificationPct / 100m ?? 0;
                    break;
                default:
                    if (task.Pmo)
                        if (intervalCalcUnit != 0)
                            valueCurrent += taskCost * intervalCalcUnit / interval;
                        else
                            valueCurrent += taskCost * defaultModificationPct / 100m ?? 0;
                    else if (intervalCalcUnit != 0)
                        valueOpted += taskCost * intervalCalcUnit / interval;
                    else
                        valueOpted += taskCost * defaultModificationPct / 100m ?? 0;
                    break;
            }
        }

        ActionCosts = decimal.Round(valueOpted, 2);
        ActionCostsPmo = decimal.Round(valueCurrent, 2);
        CurrentActionCosts = ActionCostsPmo; //Nog netter maken, nu dubbel opgenomen (ivm naam consistency) - beiden gebruikt - ActionCostPmo niet opgeslagen?
    }

    private static decimal CalcOptimalCost(decimal mainCosts, decimal costs, decimal weight) =>
        mainCosts + (costs - mainCosts) / weight;

    public bool CalculatePreventiveCosts(decimal? defaultDepreciationPct, decimal? defaultSpareManagementPct,
        decimal? defaultModificationPct, bool calculateSpareCost)
    {
        var actionCost = ActionCosts;
        var actionCostPmo = ActionCostsPmo;
        var optimalCost = OptimalCosts;
        var totalSpareCost = SpareCosts;
        totalSpareCost += SpareManagementCost;

        CalculateTasksCost(defaultModificationPct);
        var preventive = ActionCosts;
        if (calculateSpareCost)
        {
            CalculateSpareCost(defaultDepreciationPct, defaultSpareManagementPct);
            preventive += SpareCosts + SpareManagementCost;
        }
        else
        {
            preventive += totalSpareCost;
        }

        RiskAfter ??= 0;
        OptimalCosts = decimal.Round(CalcOptimalCost(RiskAfter ?? 0, preventive ?? 0, 3), 2);
        return actionCost != ActionCosts || optimalCost != OptimalCosts;
    }

    public void BuildRiskMatrix()
    {
        if (RiskObject?.Fmeca != null)
        {
            Template = RiskObject.Fmeca;
            Template.BuildMatrix();
        }

        if (string.IsNullOrEmpty(FmecaSelect) || Template == null) return;

        var data = XMLSerializer.Deserialize<FmecaSelData>(FmecaSelect);
        MainDataGrid = new RiskMatrixDataGrid(Template, data.FmecaMainSelGrid);

        //If no subgrids in template then stop processing
        if (Template.SubGrids.Count == 0) return;

        //If subgrids do not exist yet in the data, create it.
        data.FmecaSubGrids ??= new FmecaDataSubGrids();

        //If the data doesnt have the same amount of subgrids add them
        if (data.FmecaSubGrids?.SubGrids.Count < Template.SubGrids.Count)
        {
            data.FmecaSubGrids.SubGrids.AddRange(Template.SubGrids
                .Where(y => !data.FmecaSubGrids.SubGrids
                    .Select(z => z.Key)
                    .ToList()
                    .Contains(y.ColumnId.ToString())).Select(n => new DataSubGrid
                {
                    Key = n.ColumnId.ToString(),
                    Value = new DataValue()
                }));
        }

        if (data.FmecaSubGrids?.SubGrids.Count != 0 == true)
            SubDataGrids =
            [
                ..data.FmecaSubGrids.SubGrids.Select(x => new RiskMatrixDataGrid(Template, x))
            ];
    }
    
    public RiskMatrixTemplateColumn GetColumn(int column)
    {
        return Template.MainGrid.TableColumns[column];
    }

    #endregion
}