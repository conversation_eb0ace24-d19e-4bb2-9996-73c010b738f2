using System;
using AMprover.BusinessLogic.Enums;
using AMprover.Data.Entities.AM;

namespace AMprover.BusinessLogic.Models.LCC.Calculation;

public class LccYearItem(int year, decimal discountRate, decimal productionCost)
{
    public LccDetailItem YearSumItem { set; get; }
    public LccDetailItem MrbTaskSumItem { set; get; }
    private LccItemList _ramsBlockDetails = null;
    private LccDetailItem WeightedSumItem { get; set; } // total of this and all previous years weighted per year 
    public LccDetailItem SumItem = new(); // total of this and all previous years 

    public decimal ExtraCostWeighted = 0;
    private decimal _failureRate; //added because the failure
    private decimal _failureRatePmo;

    public decimal ExtraCost => ReplacementValue;

    // all costs Weighted
    public decimal _productionCost = productionCost;
    public decimal? TotalCost => WeightedSumItem?.TotalCost ?? 0 + ExtraCostWeighted + _productionCost;
    public decimal? TotalCostPmo => WeightedSumItem?.TotalCostPmo ?? 0 + ExtraCostWeighted + _productionCost;
    public decimal RealTotalCost => YearSumItem.TotalCost + ReplacementValue + _productionCost;
    public decimal RealTotalCostPmo => YearSumItem.TotalCostPmo + ReplacementValue + _productionCost;

    public decimal Npv
    {
        get
        {
            try
            {
                if (discountRate == 0)
                    return RealTotalCost;
                var npv = (double) RealTotalCost / Math.Pow(1 + (double) discountRate, Year-1);
                return Convert.ToDecimal(npv);
            }
            catch (Exception)
            {
                //TODO: log error?
            }

            return 0;
        }
    }
    
    public LccItemList RamsBlockDetails
    {
        get
        {
            if (_ramsBlockDetails == null)
                _ramsBlockDetails = [];
            return _ramsBlockDetails;
        }
    }

    public int Year { get; } = year;

    //calculate the reliability based on the  _failureRate (= lambda)
    public decimal Reliability => (decimal)Math.Exp(-(double) _failureRate);
    public decimal ReliabilityPmo => (decimal)Math.Exp(-(double)_failureRatePmo);

    public decimal ReplacementValue { get; set; }

    public void AddWeighted(LccYearItem item, int weight)
    {
        WeightedSumItem.AddWeighted(item.YearSumItem, weight); // add the original values - weighted per Year
    }

    //Control the way that the failureRate is calculated by keeping it separated from the default
    public void AddFailureRate(decimal failureRate)
    {
        _failureRate += failureRate;
    }
    public void AddFailureRatePmo(decimal failureRate)
    {
        _failureRatePmo += failureRate;
    }

    // for the year you always start with the calculated values for the year itself This is _mrbSumItem
    public void InitWeightedSumItem()
    {
        WeightedSumItem = new LccDetailItem();
        WeightedSumItem.AddValues(YearSumItem);
    }

    // call this method after everything has been calculated
    public void UpdateLccDetail(Lccdetail lccDetail, int startYear)
    {
        lccDetail.LccDetYear = Year + startYear;
        //In the database I've altered the TypeCost savings to real cost instead of Weighted Cost (WeightedSumItem)
        lccDetail.LccDetActionCost = YearSumItem.TypedTaskCost[(int) TypedTaskCost.Action];
        lccDetail.LccDetModificationCost = YearSumItem.TypedTaskCost[(int) TypedTaskCost.Modification];
        lccDetail.LccDetCapexCost = YearSumItem.TypedTaskCost[(int)TypedTaskCost.Capex];
        lccDetail.LccDetProcedureCost = YearSumItem.TypedTaskCost[(int) TypedTaskCost.Procedure];
        lccDetail.LccDetTaskCost = YearSumItem.TypedTaskCost[(int) TypedTaskCost.Task];
        lccDetail.LccDetOpexCost = YearSumItem.TypedTaskCost[(int)TypedTaskCost.Opex] + _productionCost;
        lccDetail.LccDetActionCostPmo = YearSumItem.TypedTaskCostPmo[(int)TypedTaskCost.Action];
        lccDetail.LccDetModificationCostPmo = YearSumItem.TypedTaskCostPmo[(int)TypedTaskCost.Modification];
        lccDetail.LccDetCapexCostPmo = YearSumItem.TypedTaskCostPmo[(int)TypedTaskCost.Capex];
        lccDetail.LccDetProcedureCostPmo = YearSumItem.TypedTaskCostPmo[(int)TypedTaskCost.Procedure];
        lccDetail.LccDetTaskCostPmo = YearSumItem.TypedTaskCostPmo[(int)TypedTaskCost.Task];
        lccDetail.LccDetOpexCostPmo = YearSumItem.TypedTaskCostPmo[(int)TypedTaskCost.Opex] + _productionCost;
        lccDetail.LccDetDtCostTasks = YearSumItem.DtCostTasks;
        //end of change
        lccDetail.LccDetFmecaBefore = YearSumItem.FmecaBefore;
        lccDetail.LccDetRiskBefore = WeightedSumItem.RiskBefore;
        lccDetail.LccDetAverageBefore = SumItem.RiskBefore / Year;
        lccDetail.LccDetFmecaCustomBefore = YearSumItem.CustomBefore;
        lccDetail.LccDetFmecaCustomRiskBefore = WeightedSumItem.CustomRiskBefore;
        lccDetail.LccDetFmecaAfter = YearSumItem.FmecaAfter;
        lccDetail.LccDetRiskAfter = WeightedSumItem.RiskAfter;
        lccDetail.LccDetAverageAfter = SumItem.RiskAfter / Year;
        lccDetail.LccDetFmecaCustomAfter = YearSumItem.CustomAfter;
        lccDetail.LccDetFmecaCustomRiskAfter = WeightedSumItem.CustomRiskAfter;
        lccDetail.LccDetFmecaPmo = YearSumItem.FmecaPmo;
        lccDetail.LccDetRiskPmo = WeightedSumItem.RiskPmo;
        lccDetail.LccDetAveragePmo = SumItem.RiskPmo / Year;
        lccDetail.LccDetFmecaCustomPmo = YearSumItem.CustomPmo;
        lccDetail.LccDetFmecaCustomRiskPmo = WeightedSumItem.CustomRiskPmo;
        lccDetail.LccDetTotalNpv = Npv;
        lccDetail.LccDetAec = 
        lccDetail.LccDetFailureRate = _failureRate;
        lccDetail.LccDetFailureRatePmo = _failureRatePmo;
        lccDetail.LccDetReliability = Reliability;
        lccDetail.LccDetReliabilityPmo = ReliabilityPmo;
        lccDetail.LccDetMaintenanceCost = WeightedSumItem.MaintenanceCost;
        lccDetail.LccDetMaintenanceCostPmo = WeightedSumItem.MaintenanceCostPmo;
        lccDetail.LccDetRealTotalCost = RealTotalCost;
        lccDetail.LccDetRealTotalCostPmo = RealTotalCostPmo;
        lccDetail.LccDetRealPreventiveCost = YearSumItem.AllPreventiveCost; //Correction For the preventive cost
        lccDetail.LccDetRealPreventiveCostPmo = YearSumItem.AllPreventiveCostPmo; //Correction For the preventive cost
        lccDetail.LccDetAverageRealPreventiveCost = SumItem.AllPreventiveCost / Year;
        lccDetail.LccDetAverageRealPreventiveCostPmo = SumItem.AllPreventiveCostPmo / Year;
        lccDetail.LccDetPreventiveCost = WeightedSumItem.AllPreventiveCost;
        lccDetail.LccDetPreventiveCostPmo = WeightedSumItem.AllPreventiveCostPmo;
        lccDetail.LccDetRealCorrectiveCost = YearSumItem.AllCorrectiveCost;
        lccDetail.LccDetRealCorrectiveCostPmo = YearSumItem.AllCorrectiveCostPmo;
        lccDetail.LccDetAverageRealCorrectiveCost = SumItem.AllCorrectiveCost / Year;
        lccDetail.LccDetAverageRealCorrectiveCostPmo = SumItem.AllCorrectiveCostPmo / Year;
        lccDetail.LccDetCorrectiveCost = WeightedSumItem.AllCorrectiveCost;
        lccDetail.LccDetCorrectiveCostPmo = WeightedSumItem.AllCorrectiveCostPmo;
        lccDetail.LccDetTotalCost = TotalCost;
        lccDetail.LccDetTotalCostPmo = TotalCostPmo;
        lccDetail.LccDetOptimalPreventiveCost = YearSumItem.AllOptimalCost;
        lccDetail.LccDetOptimalPreventiveCostPmo = YearSumItem.AllOptimalCostPmo;
        
        //Include the management cost in the spare cost
        //In the database I've altered the SpareCost savings to real cost instead of Weighted Cost (WeightedSumItem)
        var totalSpareCost = YearSumItem.SparepartCost + YearSumItem.SpareManagementCost;
        lccDetail.LccDetSpareCost = totalSpareCost;
        var totalSpareCostPmo = YearSumItem.SparepartCostPmo + YearSumItem.SpareManagementCostPmo;
        lccDetail.LccDetSpareCostPmo = totalSpareCostPmo;
        lccDetail.LccDetDirectCost = YearSumItem.DirectCost;
        lccDetail.LccDetDirectCostPmo = YearSumItem.DirectCostPmo;
    }
}