namespace AMprover.BusinessLogic.Models.Reports;

public class FunctionalTreeReportItemModel
{
	public string ScenarioId { get; set; }
	public string ScenarioDescription { get; set; }
	public string ScenarioName { get; set; }
	public string RiskObjectId { get; set; }
	public string RiskObjName { get; set; }
	public string RiskObjStartPoint { get; set; }
	public string RFiskObjResponsible { get; set; }
	public string RiskObjObjName { get; set; }
	public string RiskObjObjFunction { get; set; }
	public string RiskObjObjLevel { get; set; }
	public string RiskObjObjNewValue { get; set; }
	public string RiskObjObjProductionTime { get; set; }
	public string ObjName { get; set; }
	public string ObjDescription { get; set; }
	public string ObjFunction { get; set; }
	public string ObjLevel { get; set; }
	public string ObjNewValue { get; set; }
	public string ObjProductionTime { get; set; }
	public string Obj1Name { get; set; }
	public string Obj1Description { get; set; }
	public string Obj1Function { get; set; }
	public string Obj1Level { get; set; }
	public string Obj1NewValue { get; set; }
	public string Obj1ProductionTime { get; set; }
	public string Obj2Name { get; set; }
	public string Obj2Description { get; set; }
	public string Obj2Function { get; set; }
	public string Obj2Level { get; set; }
	public string Obj2NewValue { get; set; }
	public string Obj2ProductionTime { get; set; }
	public string Obj3Name { get; set; }
	public string Obj3Description { get; set; }
	public string Obj3Function { get; set; }
	public string Obj3Level { get; set; }
	public string Obj3NewValue { get; set; }
	public string Obj3ProductionTime { get; set; }
	public string Obj4Id { get; set; }
	public string Obj4Name { get; set; }
	public string Obj4Description { get; set; }
	public string Obj4Function { get; set; }
	public string Obj4Level { get; set; }
	public string Obj4NewValue { get; set; }
	public string Obj4ProductionTime { get; set; }
}