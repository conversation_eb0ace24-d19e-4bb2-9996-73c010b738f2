using System.Collections.Generic;

namespace AMprover.BusinessLogic.Models.Sapa;

public class SapaYearModel
{
    public int Id { get; set; }

    public int SapaId { get; set; }

    public int Year { get; set; }

    public decimal Budget { get; set; }

    public decimal BudgetRequest { get; set; }

    public decimal BudgetApproved { get; set; }

    public IEnumerable<SapaDetailModel> Details { get; set; }
}