namespace AMprover.BusinessLogic.Models.Sapa;

public class SapaDetailModel
{
    public int Id { get; init; }
    public int YearId { get; init; }
    public decimal TotalCapexNeeded { get; init; }
    public int RiskId { get; init; }
    public int TaskId { get; init; }
    public int SapaWorkpackageId { get; init; }
    public bool Approved { get; set; }
    public string Motivation { get; init; }
    public decimal CostYear1 { get; set; }
    public decimal CostYear2 { get; set; }
    public decimal CostYear3 { get; set; }
    public decimal CostYear4 { get; set; }
    public decimal CostYear5 { get; set; }

    // Mapped from Risk.RiskObject
    public string RiskObjectName { get; init; }
    public int RiskObjId { get; init; }

    // Mapped from Risk.RiskObject.Department
    public string DepDescription { get; init; }

    // Mapped from Risk
    public string RiskName { get; init; }
    public decimal? RiskSapaIndex { get; init; }
    public decimal? Score { get; init; }
    public string RiskFailureConsequence { get; init; }
    public string RiskDescription { get; init; }
    public string RiskRemark { get; init; }
    public string RiskRemark1 { get; init; }
    public string RiskStatus { get; init; }
    public decimal? RiskBefore { get; init; }
    public decimal? RiskAfter { get; init; }
    public decimal? PreventiveCosts { get; init; }
    public string RiskMtbfAfter { get; init; }
    public string RiskMtbfBefore { get; init; }
    public string RiskMtbfPmo { get; init; }
    public string RiskFailureCause { get; init; }
    public string RiskSafetyBefore { get; init; }
    public string RiskSafetyAfter { get; init; }
    public string RiskMarketBefore { get; init; }
    public string RiskMarketAfter { get; init; }
    public int RiskObject { get; init; }
    public int RiskObject1 { get; init; }
    public string Installation { get; init; }
    public int RiskObject2 { get; init; }
    public string System { get; init; }
    public int RiskObject3 { get; init; }
    public string Component { get; init; }
    public int RiskObject4 { get; init; }
    public string Assembly { get; init; }
    public byte[] RiskMatrixBefore { get; init; }
    public byte[] RiskMatrixAfter { get; init; }

    // Mapped from Task
    public string TaskName { get; init; }
    public string TaskDescription { get; init; }
    public string TaskRemark { get; init; }
    public string TaskGeneralDescription { get; init; }
    public string TaskPolicy { get; init; }
    public string TaskType { get; init; }
    public string TaskInterval { get; init; }
    public string TaskIntervalUnit { get; init; }
    public string TaskPartOf { get; init; }
    public string TaskFmecaEffectPct { get; init; }
    public string TaskInitiator { get; init; }
    public string TaskExecutor { get; init; }

    // Mapped from WorkPAckage
    public string WorkPackageName { get; init; }

    public SapaDetailModel CopyAsNew()
    {
        var copiedItem = (SapaDetailModel)MemberwiseClone();
        return copiedItem;
    }
}