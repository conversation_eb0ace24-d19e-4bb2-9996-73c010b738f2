using System;
using System.ComponentModel.DataAnnotations;
using AMprover.BusinessLogic.Attributes;
using AMprover.BusinessLogic.Models.ABS;

namespace AMprover.BusinessLogic.Models.Criticality;

public class CriticalityRankingModel : BaseModel
{
    [GridReadOnly] public string AssetCode { get; set; }
    [DownloadIgnore] [GridIgnore] public AssetModel Asset { get; set; }
    public string Description { get; set; }
    public int? FailureMode { get; set; }
    [MaxLength(50)] public string FailureMechanism { get; set; }
    public string FailureCause { get; set; }
    public string FailureConsequences { get; set; }
    public decimal? DownTimeAfter { get; set; }
    public decimal? MTTR { get; set; }
    public string Remarks { get; set; }
    [MaxLength(30)] public string InitiatedBy { get; set; }
    public DateTime DateInitiated { get; set; }
    [MaxLength(30)] public string ModifiedBy { get; set; }
    public DateTime DateModified { get; set; }
    public int? Status { get; set; }
    [MaxLength(150)] public string Responsible { get; set; }
    public string FmecaSelect { get; set; }
    public int? FmecaVersion { get; set; }
    [MaxLength(15)] public string Redundant { get; set; }
    public int? KooN { get; set; }
    [MaxLength(15)] public string Category { get; set; }

    public int? Fmeca { get; set; }
    public decimal? Fmeca1Value { get; set; }
    public int? Fmeca2 { get; set; }
    public decimal? Fmeca2Value { get; set; }
    public int? Fmeca3 { get; set; }
    public decimal? Fmeca3Value { get; set; }
    public int? Fmeca4 { get; set; }
    public decimal? Fmeca4Value { get; set; }
    public int? Fmeca5 { get; set; }
    public decimal? Fmeca5Value { get; set; }
    public int? Fmeca6 { get; set; }
    public decimal? Fmeca6Value { get; set; }
    public int? Fmeca7 { get; set; }
    public decimal? Fmeca7Value { get; set; }
    public decimal? TotalValue { get; set; }
    public int? Total { get; set; }
    public decimal? Probability { get; set; }
    public string ReStrategy { get; set; }
    public int? Mtbf { get; set; }
    public decimal? MtbfValue { get; set; }

    public void Calculate(int upperlevelB, int upperlevelC, int upperlevelD, int upperlevelE, string CalcValue,
        string CalcType)
    {
        if ((Probability != null && Probability != 0) || CalcValue == "CriticalityHighCalculation")
        {
            if (CalcValue == "CriticalityHighCalculation")
            {
                Total = Math.Max(Fmeca ?? 0, Fmeca2 ?? 0);
                Total = Math.Max(Total ?? 0, Fmeca3 ?? 0);
                Total = Math.Max(Total ?? 0, Fmeca4 ?? 0);
                Total = Math.Max(Total ?? 0, Fmeca5 ?? 0);
                Total = Math.Max(Total ?? 0, Fmeca6 ?? 0);
                Total = Math.Max(Total ?? 0, Fmeca7 ?? 0);
            }
            else if (Probability != null)
                Total = (int) Math.Round(((Fmeca ?? 0)
                                          + (Fmeca2 ?? 0)
                                          + (Fmeca3 ?? 0)
                                          + (Fmeca4 ?? 0)
                                          + (Fmeca5 ?? 0)
                                          + (Fmeca6 ?? 0)
                                          + (Fmeca7 ?? 0)
                    ) / (double) Probability + 0.01, 0); //+0.01 for right calculation

            if (CalcValue is "CriticalityCalculation" or "CriticalityHighCalculation")
            {
                if (CalcType == "FiveToOne")
                {
                    if (Total <= upperlevelE) Category = "5";
                    else if (Total <= upperlevelD) Category = "4";
                    else if (Total <= upperlevelC) Category = "3";
                    else if (Total <= upperlevelB) Category = "2";
                    else Category = "1";
                }
                else if (CalcType == "FourToOne")
                {
                    if (Total <= upperlevelD) Category = "4";
                    else if (Total <= upperlevelC) Category = "3";
                    else if (Total <= upperlevelB) Category = "2";
                    else Category = "1";
                }
                else if (CalcType == "ThreeToOne")
                {
                    if (Total <= upperlevelC) Category = "3";
                    else if (Total <= upperlevelB) Category = "2";
                    else Category = "1";
                }
                else if (CalcType == "HML")
                {
                    if (Total <= upperlevelC) Category = "L";
                    else if (Total <= upperlevelB) Category = "M";
                    else Category = "H";
                }
                else if (CalcType == "ABCDE")
                {
                    if (Total <= upperlevelE) Category = "E";
                    else if (Total <= upperlevelD) Category = "D";
                    else if (Total <= upperlevelC) Category = "C";
                    else if (Total <= upperlevelB) Category = "B";
                    else Category = "A";
                }
                else if (CalcType == "ABCD")
                {
                    if (Total <= upperlevelD) Category = "D";
                    else if (Total <= upperlevelC) Category = "C";
                    else if (Total <= upperlevelB) Category = "B";
                    else Category = "A";
                }
                else
                {
                    if (Total <= upperlevelC) Category = "C";
                    else if (Total <= upperlevelB) Category = "B";
                    else Category = "A";
                }
            }

            if (CalcValue == "ValueCalculation")
            {
                TotalValue = ((Fmeca1Value ?? 0)
                              + (Fmeca2Value ?? 0)
                              + (Fmeca3Value ?? 0)
                              + (Fmeca4Value ?? 0)
                              + (Fmeca5Value ?? 0)
                              + (Fmeca6Value ?? 0)
                              + (Fmeca7Value ?? 0)
                    ) / (decimal) Probability;

                if (CalcType == "FiveToOne")
                {
                    if (TotalValue <= upperlevelE) Category = "5";
                    else if (TotalValue <= upperlevelD) Category = "4";
                    else if (TotalValue <= upperlevelC) Category = "3";
                    else if (TotalValue <= upperlevelB) Category = "2";
                    else Category = "1";
                }
                else if (CalcType == "FourToOne")
                {
                    if (TotalValue <= upperlevelD) Category = "4";
                    else if (TotalValue <= upperlevelC) Category = "3";
                    else if (TotalValue <= upperlevelB) Category = "2";
                    else Category = "1";
                }
                else if (CalcType == "ThreeToOne")
                {
                    if (TotalValue <= upperlevelC) Category = "3";
                    else if (TotalValue <= upperlevelB) Category = "2";
                    else Category = "1";
                }
                else if (CalcType == "HML")
                {
                    if (TotalValue <= upperlevelC) Category = "L";
                    else if (TotalValue <= upperlevelB) Category = "M";
                    else Category = "H";
                }
                else if (CalcType == "ABCDE")
                {
                    if (TotalValue <= upperlevelE) Category = "E";
                    else if (TotalValue <= upperlevelD) Category = "D";
                    else if (TotalValue <= upperlevelC) Category = "C";
                    else if (TotalValue <= upperlevelB) Category = "B";
                    else Category = "A";
                }
                else if (CalcType == "ABCD")
                {
                    if (TotalValue <= upperlevelD) Category = "D";
                    else if (TotalValue <= upperlevelC) Category = "C";
                    else if (TotalValue <= upperlevelB) Category = "B";
                    else Category = "A";
                }
                else
                {
                    if (TotalValue <= upperlevelC) Category = "C";
                    else if (TotalValue <= upperlevelB) Category = "B";
                    else Category = "A";
                }
            }
        }
    }
}