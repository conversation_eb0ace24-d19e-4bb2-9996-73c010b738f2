using AMprover.BusinessLogic.Attributes;

namespace AMprover.BusinessLogic.Models.Criticality;

public class CriticalityRankingModelFlat : CriticalityRankingModel
{
    [GridReadOnly]
    public string AssetDescription { get; set; }
    
    [GridReadOnly]
    public string AssetCategory { get; set; }

    [GridReadOnly]
    public int? AssetCategoryId { get; set; }

    [GridReadOnly]
    public int? AssetMtbf { get; set; }
}