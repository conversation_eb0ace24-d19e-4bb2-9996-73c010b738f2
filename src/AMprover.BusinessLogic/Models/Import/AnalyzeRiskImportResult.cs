using System.Collections.Generic;
using AMprover.BusinessLogic.Models.Reports;

namespace AMprover.BusinessLogic.Models.Import;

public class AnalyzeRiskImportResult
{
    public string FileName { get; set; }
    public List<RiskImportModel> Risks { get; set; }
    public string RiskObjectName { get; set; }

    public int RisksInFile { get; set; }
    public int RisksInDatabase { get; set; }

    public int RisksToAdd { get; set; }
    public int RisksToUpdate { get; set; }

    public int MissingObjects { get; set; }
    public int MissingFailureModes { get; set; }

    public ImportRiskStatus Status { get; set; }
}

public enum ImportRiskStatus
{
    SelectFile,
    ImportRisks,
    Finished
}