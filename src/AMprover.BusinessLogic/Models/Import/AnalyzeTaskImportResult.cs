using System;
using System.Collections.Generic;

namespace AMprover.BusinessLogic.Models.Import;

public class AnalyzeTaskImportResult
{
    public string FileName { get; set; }
    public string RiskObjectName { get; set; }

    public List<string> MissingInitiators { get; set; } = [];
    public List<string> MissingExecutors { get; set; } = [];
    public List<string> MissingIntervalUnits { get; set; } = [];
    public List<string> MissingWorkPackages { get; set; } = [];
    public List<string> MissingPolicies { get; set; } = [];

    public int TasksInImport { get; set; }
    public int RisksInImport { get; set; }
    public int RisksFoundInDB { get; set; }

    public int TasksToAdd { get; set; }

    public int TasksToUpdate { get; set; }

    public List<string> MissingItems(ImportTaskStatus status) =>
        status switch
        {
            ImportTaskStatus.UpdateInitiator => MissingInitiators,
            ImportTaskStatus.UpdateExecutor => MissingExecutors,
            ImportTaskStatus.UpdateIntervalUnit => MissingIntervalUnits,
            ImportTaskStatus.UpdateWorkPackage => MissingWorkPackages,
            ImportTaskStatus.UpdatePolicy => MissingPolicies,
            _ => throw new NotImplementedException($"{status} was not implemented in {nameof(AnalyzeTaskImportResult)}.{nameof(MissingItems)}")
        };

    public bool MissingAnyItem() =>
        MissingInitiators.Count != 0
        || MissingExecutors.Count != 0
        || MissingIntervalUnits.Count != 0
        || MissingWorkPackages.Count != 0
        || MissingPolicies.Count != 0;

    public bool ImportFileAllowed() => TasksInImport > 0 && RisksInImport == RisksFoundInDB;

    public string GetAnalyzeStatus()
    {
        if (TasksInImport == 0 || RisksFoundInDB == 0)
            return "alert alert-danger";

        if (RisksInImport != RisksFoundInDB)
            return "alert alert-warning";

        return "alert alert-success";
    }
}

public class ReplaceTextValue(string original)
{
    public string Original { get; set; } = original;
    public string Replaced { get; set; }
}

public enum ImportTaskStatus
{
    SelectFile,
    UpdateInitiator,
    UpdateExecutor,
    UpdateIntervalUnit,
    UpdateWorkPackage,
    UpdatePolicy,
    ImportTasks,
    Finished
}

public class ImportEditStatus
{
    public List<ReplaceTextValue> ReplaceValues { get; set; }

    public Dictionary<string, string> Options { get; set; }
}