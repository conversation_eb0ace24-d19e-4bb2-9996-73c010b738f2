using AMprover.BusinessLogic.Attributes;

namespace AMprover.BusinessLogic.Models.Import;

public class SpareImportModel
{
    public int RiskId { get; set; }
    public int Id { get; set; }
    public string Name { get; set; }
    public string Remarks { get; set; }
    public string Category { get; set; }

    public int ObjectCount { get; set; }
    public int OrderLeadTime { get; set; }

    [GridNumericFormat("p5")]
    public decimal Reliability { get; set; }

    public decimal PurchasePrice { get; set; }
    public int NoOfSpares { get; set; }
    public decimal SpareCosts { get; set; }
    public int StockNumber { get; set; }

    public int PurchaseYear { get; set; }
    public decimal? YearlyCost { get; set; }
    public string SupplierId { get; set; }
    public string VendorId { get; set; }
    public string ReferenceId { get; set; }

    public bool Pmo { get; set; }
}