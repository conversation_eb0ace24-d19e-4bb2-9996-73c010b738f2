namespace AMprover.BusinessLogic.Models.Import;

public class TaskImportModel
{
    public int RiskId { get; set; }

    public int Id { get; set; }

    public string Name { get; set; }

    public string CommonAction { get; set; }

    // Instructions
    public string GeneralDescription { get; set; }

    public string Description { get; set; }

    public string Type { get; set; }

    public string Policy { get; set; }

    public string Initiator { get; set; }

    public string Executor { get; set; }

    public int Duration { get; set; }

    public int DownTime { get; set; }

    public string WorkPackage { get; set; }

    public int Interval { get; set; }

    public string IntervalUnit { get; set; }

    public int Units { get; set; }

    public int UnitType { get; set; }

    public decimal? EstCostPerUnit { get; set; }

    public decimal Costs { get; set; }

    public int? ValidFromYear { get; set; }

    public int? ValidUntilYear { get; set; }

    public string Norm { get; set; }

    public string Permit { get; set; }

    public string Remarks { get; set; }

    public bool Pmo { get; set; }
}