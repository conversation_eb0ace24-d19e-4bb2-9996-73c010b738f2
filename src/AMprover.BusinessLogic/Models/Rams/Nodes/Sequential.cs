using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Calculators;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Models.Rams.Calculations;
using JM.LinqFaster;

namespace AMprover.BusinessLogic.Models.Rams.Nodes;

public class Sequential : INodeBase
{
    public void Configure(RamsComponentModel container, RamsModel item, List<RamsModel> ramsItems,
        CalculationParameters calculationParameters, RamsDisplayModel display, double bufferTime)
    {
            var nodes = container.Parts.OrderBy(x => x.Order)
                .Where(x => x.Status != RamsBlockStatus.NotInstalled).ToList();

            if (nodes.Count > 0)
            {
                container.ReliabilityFunctionalFunction = nodes
                    .Select(node => node.ReliabilityFunctionalFunction)
                    .Where(x => x != null)
                    .DefaultIfEmpty()
                    .Aggregate((a, b) => period => a(period) * b(period));

                container.ReliabilityTechnicalFunction = nodes
                    .Select(node => node.ReliabilityTechnicalFunction)
                    .Where(x => x != null)
                    .DefaultIfEmpty()
                    .Aggregate((a, b) => period => a(period) * b(period));

                if (display.ShowPfd || display.ShowSil || display.ShowSilAc)
                {
                    container.LabdaDuFunction = Pfd.GetLabdaDu(nodes.Select(n => n.LabdaDuFunction));
                    container.LabdaDdFunction = Pfd.GetLabdaDd(nodes.Select(n => n.LabdaDdFunction));
                    container.LabdaSdFunction = Pfd.GetLabdaSd(nodes.Select(n => n.LabdaSdFunction));
                    container.LabdaSuFunction = Pfd.GetLabdaSu(nodes.Select(n => n.LabdaSuFunction));
                    container.PfdFunction = Pfd.GetPfd(nodes.Select(n => n.PfdFunction));

                    //Sff for containers should not be calculated but they are directly composed with SIL-AC
                    if (nodes.FirstOrDefault()?.Type != RamsComponentType.Container)
                    {
                        container.SffFunction = Pfd.GetSffLabdaBased(container.LabdaDuFunction,
                            container.LabdaDdFunction, container.LabdaSuFunction, container.LabdaSdFunction);
                        container.SilAcFunction = Pfd.GetSilAc(container.SffFunction, item.Hft ?? 0,
                            item.ACType);
                    }
                    else
                    {
                        container.SffFunction = Pfd.GetSff(nodes.SelectF(n => n.SffFunction));
                        container.SilAcFunction = Pfd.GetSilAc(nodes.SelectF(n => n.SilAcFunction));
                    }
                }

                container.AvailabilityFunctionalFunction = nodes.Select(node => node.AvailabilityFunctionalFunction)
                    .Where(x => x != null)
                    .DefaultIfEmpty()
                    .Aggregate((a, b) => () => a() * b());

                container.AvailabilityTechnicalFunction = nodes.Select(node => node.AvailabilityTechnicalFunction)
                    .Where(x => x != null)
                    .DefaultIfEmpty()
                    .Aggregate((a, b) => () => a() * b());

                // Capacity calculations for serial chain
                if (nodes.Count > 0)
                {
                    // For serial capacity, find the minimum capacity in the chain
                    container.CapacityFunctionalFunction = () =>
                    {
                        var minCapacity = nodes.Min(node => node.CapacityFunctionalFunction?.Invoke() ?? 0);
                        var availability = container.AvailabilityFunctionalFunction?.Invoke() ?? 0;
                        var capacityFactor = nodes.FirstOrDefault()?.CapacityFactorFunction?.Invoke() ?? 1.0;
                        return minCapacity * availability * capacityFactor;
                    };

                    container.CapacityTechnicalFunction = () =>
                    {
                        var minCapacity = nodes.Min(node => node.CapacityTechnicalFunction?.Invoke() ?? 0);
                        var availability = container.AvailabilityTechnicalFunction?.Invoke() ?? 0;
                        var capacityFactor = nodes.FirstOrDefault()?.CapacityFactorFunction?.Invoke() ?? 1.0;
                        return minCapacity * availability * capacityFactor;
                    };

                    // Use the capacity factor from the first node
                    container.CapacityFactorFunction = nodes.FirstOrDefault()?.CapacityFactorFunction ?? (() => 1.0);
                }

                if (!calculationParameters.UseCompatibilityMode)
                {
                    container.MttrFunctionalFunction = Mttr.MttrForCollection(container.AvailabilityFunctionalFunction,
                        () => container.MtbfInPeriodFunctionalFunction(calculationParameters.Period));
                    container.MttrTechnicalFunction = Mttr.MttrForCollection(container.AvailabilityTechnicalFunction,
                        () => container.MtbfInPeriodTechnicalFunction(calculationParameters.Period));
                }

                if (bufferTime > 0)
                {
                    var calculatedMttr = Math.Max(0, container.MttrFunctionalFunction?.Invoke() - bufferTime ?? 0);
                    container.AvailabilityFunctionalFunction =
                        BuildAvailabilityFunction(container, item, calculatedMttr, calculationParameters, true);
                    container.AvailabilityTechnicalFunction =
                        BuildAvailabilityFunction(container, item, calculatedMttr, calculationParameters, false);
                }
            }
            else
            {
                container.ReliabilityFunctionalFunction = _ => 1;
                container.ReliabilityTechnicalFunction = _ => 1;
                container.AvailabilityFunctionalFunction = () => 1;
                container.AvailabilityTechnicalFunction = () => 1;
                container.MttrFunctionalFunction = () => 8;
                container.MttrTechnicalFunction = () => 8;

                if (display.ShowPfd || display.ShowSil || display.ShowSilAc)
                {
                    container.LabdaDuFunction = () => 0;
                    container.LabdaDdFunction = () => 0;
                    container.LabdaSuFunction = () => 0;
                    container.LabdaSdFunction = () => 0;
                    container.PfdFunction = () => 0;
                    container.SffFunction = () => 1;
                    container.SilAcFunction = () => 4;
                }
            }
        }

    private static Func<ChanceModel> BuildAvailabilityFunction(RamsComponentModel component, RamsModel item,
        double? mttr, CalculationParameters parameters, bool isFunctional)
    {
            if (!parameters.CalculateAvailabityAutomatic)
            {
                return () =>
                    isFunctional ? parameters.AvailabilityFunctional(item) : parameters.AvailabilityTechnical(item);
            }

            if (mttr is null or 0)
            {
                return () => 1;
            }

            return parameters.UseCompatibilityMode
                ? Availability.Get(
                    () => isFunctional ? component.MtbfFunctionalFunction() : component.MtbfTechnicalFunction(),
                    () => (HoursModel) mttr)
                : () => Availability.Get(
                    period => isFunctional
                        ? component.MtbfInPeriodFunctionalFunction(period)
                        : component.MtbfInPeriodTechnicalFunction(period), () => (HoursModel) mttr,
                    () => parameters.AvailableTime)(parameters.Period);
        }
}
