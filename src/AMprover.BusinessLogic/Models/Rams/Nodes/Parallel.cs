using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Calculators;
using AMprover.BusinessLogic.Calculators.Sets;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Models.Rams.Nodes;

public class Parallel : INodeBase
{
    public void Configure(RamsComponentModel container, RamsModel item, List<RamsModel> items,
        CalculationParameters calculationParameters, RamsDisplayModel display, double bufferTime)
    {
            var startNodes = container.Parts
                .Where(x => x.Status != RamsBlockStatus.NotInstalled)
                .OrderBy(x => x.Order).ToList();

            item.N = () => container.N;

            item.N = () => startNodes.Count;

            var reliabilityFunctionalNodes = startNodes
                .Select(node =>
                    container.Parts.Where(next => next.ParallelTrack == node.ParallelTrack)
                        .Where(x => x.Status != RamsBlockStatus.NotInstalled)
                        .OrderBy(x => x.Order)
                        .Select(n => n.ReliabilityFunctionalFunction)
                        .Where(x => x != null)
                        .DefaultIfEmpty()
                        .Aggregate((a, b) => period => a(period) * b(period))
                );

            var reliabilityTechnicalNodes = startNodes
                .Select(node =>
                    container.Parts.Where(next => next.ParallelTrack == node.ParallelTrack)
                        .Where(x => x.Status != RamsBlockStatus.NotInstalled)
                        .OrderBy(x => x.Order)
                        .Select(n => n.ReliabilityTechnicalFunction)
                        .Where(x => x != null)
                        .DefaultIfEmpty()
                        .Aggregate((a, b) => period => a(period) * b(period))
                );

            var availabilityFunctionalNodes = startNodes
                .Select(node =>
                    container.Parts.Where(next => next.ParallelTrack == node.ParallelTrack)
                        .Where(x => x.Status != RamsBlockStatus.NotInstalled)
                        .OrderBy(x => x.Order)
                        .Select(n => n.AvailabilityFunctionalFunction)
                        .Where(x => x != null)
                        .DefaultIfEmpty()
                        .Aggregate((a, b) => () => a() * b()));

            var availabilityTechnicalNodes = startNodes
                .Select(node =>
                    container.Parts.Where(next => next.ParallelTrack == node.ParallelTrack)
                        .Where(x => x.Status != RamsBlockStatus.NotInstalled)
                        .OrderBy(x => x.Order)
                        .Select(n => n.AvailabilityTechnicalFunction)
                        .Where(x => x != null)
                        .DefaultIfEmpty()
                        .Aggregate((a, b) => () => a() * b())
                );

            container.ReliabilityFunctionalFunction = reliabilityFunctionalNodes.SymmetricState(item.K());
            container.ReliabilityTechnicalFunction = reliabilityTechnicalNodes.SymmetricState(item.K());

            container.AvailabilityFunctionalFunction = availabilityFunctionalNodes.SymmetricState(item.K());
            container.AvailabilityTechnicalFunction = availabilityTechnicalNodes.SymmetricState(item.K());
            container.MttrFunctionalFunction = () => item.Mttr ?? 0;
            container.MttrTechnicalFunction = () => item.Mttr ?? 0;

            // Capacity calculations for parallel chain
            if (startNodes.Count > 0)
            {
                // For parallel capacity, sum the capacities of all nodes
                container.CapacityFunctionalFunction = () =>
                {
                    double totalCapacity = 0;
                    foreach (var node in startNodes)
                    {
                        var nodeCapacity = node.CapacityFunctionalFunction?.Invoke() ?? 0;
                        var nodeAvailability = node.AvailabilityFunctionalFunction?.Invoke() ?? 0;
                        var nodeCapacityFactor = node.CapacityFactorFunction?.Invoke() ?? 1.0;
                        totalCapacity += nodeCapacity * nodeAvailability * nodeCapacityFactor;
                    }
                    return totalCapacity;
                };

                container.CapacityTechnicalFunction = () =>
                {
                    double totalCapacity = 0;
                    foreach (var node in startNodes)
                    {
                        var nodeCapacity = node.CapacityTechnicalFunction?.Invoke() ?? 0;
                        var nodeAvailability = node.AvailabilityTechnicalFunction?.Invoke() ?? 0;
                        var nodeCapacityFactor = node.CapacityFactorFunction?.Invoke() ?? 1.0;
                        totalCapacity += nodeCapacity * nodeAvailability * nodeCapacityFactor;
                    }
                    return totalCapacity;
                };

                // Use the capacity factor from the first node
                container.CapacityFactorFunction = startNodes.FirstOrDefault()?.CapacityFactorFunction ?? (() => 1.0);
            }

            var firstNode = startNodes.GetWorstNodeForLabda(calculationParameters.Period.To, true);

            if (firstNode == null) return;

            container.MtbfFunctionalFunction =
                () => firstNode.MtbfInPeriodFunctionalFunction(calculationParameters.Period);
            container.MtbfTechnicalFunction =
                () => firstNode.MtbfInPeriodTechnicalFunction(calculationParameters.Period);

            if (container.LabdaDuFunction != null && firstNode.LabdaDuFunction != null)
                firstNode = startNodes.GetWorstNodeForSil();

            container.PfdFunction = Pfd.GetPfdParallel(() => item.TestInterval ?? int.MaxValue,
                item.K, item.N, firstNode.LabdaDu, firstNode.LabdaDd,
                () => items.FirstOrDefault(x => x.NodeId == firstNode.Id)?.Dcd ?? 0,
                () => items.FirstOrDefault(x => x.NodeId == firstNode.Id)?.Mttr ?? 0,
                () => items.FirstOrDefault(x => x.NodeId == firstNode.Id)?.Beta ?? 0);
            container.SffFunction = Pfd.GetSffLabdaBased(firstNode.LabdaDuFunction, firstNode.LabdaDdFunction,
                firstNode.LabdaSuFunction, firstNode.LabdaSdFunction);
            container.SilAcFunction = Pfd.GetSilAc(container.SffFunction, item.Hft ?? 0, item.ACType);

            if (!calculationParameters.UseCompatibilityMode)
            {
                container.MttrFunctionalFunction = Mttr.MttrForCollection(container.AvailabilityFunctionalFunction,
                    () => container.MtbfInPeriodFunctionalFunction(calculationParameters.Period));
                container.MttrTechnicalFunction = Mttr.MttrForCollection(container.AvailabilityTechnicalFunction,
                    () => container.MtbfInPeriodTechnicalFunction(calculationParameters.Period));
            }

            if (bufferTime > 0)
            {
                var calculatedMttr = Math.Max(0, container.MttrFunctionalFunction?.Invoke() - bufferTime ?? 0);
                container.AvailabilityFunctionalFunction =
                    BuildAvailabilityFunction(container, item, calculatedMttr, calculationParameters, true);
                container.AvailabilityTechnicalFunction =
                    BuildAvailabilityFunction(container, item, calculatedMttr, calculationParameters, false);
            }
        }

    private static Func<ChanceModel> BuildAvailabilityFunction(RamsComponentModel component, RamsModel item,
        double? mttr, CalculationParameters parameters, bool isFunctional)
    {
            if (!parameters.CalculateAvailabityAutomatic)
            {
                return () =>
                    isFunctional ? parameters.AvailabilityFunctional(item) : parameters.AvailabilityTechnical(item);
            }

            if (mttr is null or 0)
            {
                return () => 1;
            }

            return parameters.UseCompatibilityMode
                ? Availability.Get(
                    () => isFunctional ? component.MtbfFunctionalFunction() : component.MtbfTechnicalFunction(),
                    () => (HoursModel) mttr)
                : () => Availability.Get(
                    period => isFunctional
                        ? component.MtbfInPeriodFunctionalFunction(period)
                        : component.MtbfInPeriodTechnicalFunction(period), () => (HoursModel) mttr,
                    () => parameters.AvailableTime)(parameters.Period);
        }
}
