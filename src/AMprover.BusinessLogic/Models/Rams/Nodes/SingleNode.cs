using System;
using System.Collections.Generic;
using AMprover.BusinessLogic.Calculators;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.Rams.Calculations;
using Rt =
    System.Func<AMprover.BusinessLogic.Models.Rams.Calculations.HoursModel,
        AMprover.BusinessLogic.Models.Rams.Calculations.ChanceModel>;

namespace AMprover.BusinessLogic.Models.Rams.Nodes;

public interface ISingleNode
{
    void Configure(RamsComponentModel container, RamsModel item, List<RamsModel> ramsItems,
        CalculationParameters calculationParameters, RamsDisplayModel display, double bufferTime);
}

public class SingleNode : ISingleNode
{
    public void Configure(RamsComponentModel node, RamsModel item, List<RamsModel> ramsItems,
        CalculationParameters calculationParameters, RamsDisplayModel display, double bufferTime)
    {
        //Switch between functional and technical for calculation
        HoursModel mtbfFunctional() => node.Status == RamsBlockStatus.NotInstalled
            ? 10000000
            : item.Mtbffunct * 8760D ?? 0;

        HoursModel mtbfTechnical() => node.Status == RamsBlockStatus.NotInstalled
            ? 10000000
            : item.Mtbftechn * 8760D ?? 0;

        Func<double> shape = item.Shape(item);

        //Make sure that the result is always >= 0
        var calculatedMttr = Math.Max(0, item.Mttr - bufferTime ?? 0);

        node.ReliabilityFunctionalFunction = BuildReliabilityFunction(
            Reliability.Rweibull(shape, item.CharLife(mtbfFunctional)), node, calculatedMttr,
            calculationParameters.UseCompatibilityMode, true);
        node.ReliabilityTechnicalFunction = BuildReliabilityFunction(
            Reliability.Rweibull(shape, item.CharLife(mtbfTechnical)), node, calculatedMttr,
            calculationParameters.UseCompatibilityMode, false);

        node.AvailabilityFunctionalFunction =
            BuildAvailabilityFunction(node, item, calculatedMttr, calculationParameters, true);
        node.AvailabilityTechnicalFunction =
            BuildAvailabilityFunction(node, item, calculatedMttr, calculationParameters, false);
        node.LabdaFunctionalFunction = BuildLabdaFunction(node, calculationParameters, true);
        node.LabdaTechnicalFunction = BuildLabdaFunction(node, calculationParameters, false);
        node.MtbfFunctionalFunction = mtbfFunctional;
        node.MtbfTechnicalFunction = mtbfTechnical;
        node.MttrFunctionalFunction = () => item.Mttr ?? 0;
        node.MttrTechnicalFunction = () => item.Mttr ?? 0;

        // Capacity calculations
        node.CapacityFunctionalFunction =
            () => node.Status == RamsBlockStatus.NotInstalled ? 0 : item.CapacityFunct ?? 0;
        node.CapacityTechnicalFunction =
            () => node.Status == RamsBlockStatus.NotInstalled ? 0 : item.CapacityTechn ?? 0;
        node.CapacityFactorFunction = () => item.CapacityFactor ?? 1.0;

        //To Do: Set period time for PFD, LabdaDd and LabdaDu calculation 
        const int h = 8760;

        if (item.Dcd != null)
        {
            node.Dcd = item.Dcd.Value;
        }

        if (calculationParameters.UseCompatibilityMode)
        {
            node.LabdaInPeriodFunctionalFunction = p => node.LabdaFunctionalFunction(p.To);
            node.LabdaInPeriodTechnicalFunction = p => node.LabdaTechnicalFunction(p.To);
            node.MtbfInPeriodFunctionalFunction = _ => node.MtbfFunctionalFunction();
            node.MtbfInPeriodTechnicalFunction = _ => node.MtbfTechnicalFunction();
        }

        node.LabdaDu = _ => 0;
        node.LabdaDd = _ => 0;
        node.LabdaSu = _ => 0;
        node.LabdaSd = _ => 0;

        switch (item.ClassDc?.ToUpperInvariant())
        {
            case "DU":
                node.LabdaDu = node.LabdaFunctionalFunction;
                node.Rdu = node.ReliabilityFunctionalFunction;
                break;

            case "DD":
                node.LabdaDd = node.LabdaFunctionalFunction;
                node.Rdd = node.ReliabilityFunctionalFunction;
                break;

            case "SU":
                node.LabdaSu = node.LabdaFunctionalFunction;
                node.Rsu = node.ReliabilityFunctionalFunction;
                break;

            case "SD":
                node.LabdaSd = node.LabdaFunctionalFunction;
                node.Rsd = node.ReliabilityFunctionalFunction;
                break;
        }

        node.LabdaDuFunction = Pfd.GetLDu(node.LabdaDu, h);
        node.LabdaDdFunction = Pfd.GetLDd(node.LabdaDd, h);
        node.LabdaSuFunction = Pfd.GetLSu(node.LabdaSu, h);
        node.LabdaSdFunction = Pfd.GetLSd(node.LabdaSd, h);

        node.LabdaDuFunction();

        node.PfdFunction = Pfd.GetPfd(() => item.TestInterval ?? int.MaxValue,
            () => item.Dcd ?? 0, node.LabdaDu, node.LabdaDd, h);
        node.SffFunction = Pfd.GetSff(node.LabdaDu, node.LabdaDd, node.LabdaSu, node.LabdaSd, h);
        node.SilAcFunction = () => 1;
    }

    private static Func<ChanceModel> BuildAvailabilityFunction(RamsComponentModel component, RamsModel item,
        double? mttr, CalculationParameters parameters, bool isFunctional)
    {
        if (!parameters.CalculateAvailabityAutomatic)
        {
            return () =>
                isFunctional ? parameters.AvailabilityFunctional(item) : parameters.AvailabilityTechnical(item);
        }

        if (mttr is null or 0)
        {
            return () => 1;
        }

        return parameters.UseCompatibilityMode
            ? Availability.Get(
                () => isFunctional ? component.MtbfFunctionalFunction() : component.MtbfTechnicalFunction(),
                () => (HoursModel) mttr)
            : () => Availability.Get(
                period => isFunctional
                    ? component.MtbfInPeriodFunctionalFunction(period)
                    : component.MtbfInPeriodTechnicalFunction(period), () => (HoursModel) mttr,
                () => parameters.AvailableTime)(parameters.Period);
    }

    private static Rt BuildReliabilityFunction(Rt r, RamsComponentModel item, double? mttr,
        bool useCompatibilityMode, bool isFunctional)
    {
        var coldStandby = RamsNodeHelper.SplitColdStandby(item);
        var result = coldStandby == null ? r :
            isFunctional ? coldStandby.Item1.ReliabilityFunctionalFunction :
            coldStandby.Item1.ReliabilityTechnicalFunction;

        if (useCompatibilityMode)
        {
            result = result.IncludeRepairTimeAssumingExponential(() => mttr ?? 0);
        }

        return result;
    }

    private static Func<HoursModel, RateModel> BuildLabdaFunction(RamsComponentModel component,
        CalculationParameters parameters, bool isFunctional)
    {
        if (component.Type == RamsComponentType.Container && parameters.UseCompatibilityMode)
        {
            return Labda.LabdaSystemAssumingExponential(isFunctional
                ? component.ReliabilityFunctionalFunction
                : component.ReliabilityTechnicalFunction);
        }

        return Labda.LabdaSystem2(isFunctional
            ? component.ReliabilityFunctionalFunction
            : component.ReliabilityTechnicalFunction);
    }
}