using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using AMprover.BusinessLogic.Attributes;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.Data.Entities.AM;

namespace AMprover.BusinessLogic.Models.Cluster;

public class ClusterModel : BaseModel
{
    [GridReadOnly] public decimal? Budget { get; set; }
    [GridReadOnly] public string Description { get; set; }
    [GridReadOnly] public int Level { get; set; }

    [GridReadOnly] public int? PartOf { get; set; }
    [GridReadOnly] public ClusterModel PartOfCluster { get; set; }

    [GridReadOnly] public decimal? Interval { get; set; }
    [GridReadOnly] public int? IntervalUnit { get; set; }
    [GridReadOnly] public decimal? EstTaskCosts { get; set; }
    [GridReadOnly] public decimal? TaskCosts { get; set; }
    [GridReadOnly] public decimal? SharedCosts { get; set; }
    [GridReadOnly] public decimal DisciplineCosts { get; set; }
    [GridReadOnly] public decimal MaterialCosts { get; set; }
    [GridReadOnly] public decimal EnergyCosts { get; set; }
    [GridReadOnly] public decimal ToolCosts { get; set; }
    [GridReadOnly] public decimal TotalCmnCost { get; set; }
    [GridReadOnly] public decimal? DownTime { get; set; }
    [GridReadOnly] public decimal? Duration { get; set; }
    [GridReadOnly] public int? Executor { get; set; }
    [GridReadOnly] public int? Initiator { get; set; }
    [GridReadOnly] public int? Status { get; set; }

    [MaxLength(50)]
    [GridReadOnly] public string ShortKey { get; set; }
    [GridReadOnly] public string InitiatedBy { get; set; }
    [GridReadOnly] public DateTime? DateInitiated { get; set; }
    [GridReadOnly] public string ModifiedBy { get; set; }
    [GridReadOnly] public DateTime? DateModified { get; set; }
    [GridReadOnly] public string Remark { get; set; }
    [GridReadOnly] public string Responsible { get; set; }
    [GridReadOnly] public string SecondValues { get; set; }

    [GridReadOnly] public int? ScenarioId { get; set; }
    [GridReadOnly] public Scenario Scenario { get; set; }

    [GridReadOnly] public int? RiskObjectId { get; set; }
    [GridReadOnly] public int? WorkpackageId { get; set; }
    [GridReadOnly] public bool? DivideDuration { get; set; }
    [GridReadOnly] public bool? DivideDownTime { get; set; }

    [MaxLength(12)]
    [GridReadOnly] public string SiteId { get; set; }
    [MaxLength(50)]
    [GridReadOnly] public string Location { get; set; }
    [GridReadOnly] public bool? Interruptable { get; set; }
    [GridReadOnly] public int? ShiftStartDate { get; set; }
    [GridReadOnly] public int? ShiftEndDate { get; set; }
    [MaxLength(30)]
    [GridReadOnly] public string TemplateType { get; set; }
    [MaxLength(12)]
    [GridReadOnly] public string OrgId { get; set; }
    [GridReadOnly] public int? Priority { get; set; }
    [GridReadOnly] public int? Sequence { get; set; }
    [GridReadOnly] public string ReferenceId { get; set; }

    [GridReadOnly] public List<ClusterCostModel> Costs { get; set; } = [];
    [GridReadOnly] public List<ClusterTaskPlanModel> TaskPlans { get; set; } = [];
    [GridReadOnly] public List<TaskModel> Tasks { get; set; } = [];
    [GridReadOnly] public IEnumerable<ClusterModel> Children { get; set; } = new List<ClusterModel>();
    [GridReadOnly] public RiskObjectModel RiskObject { get; set; }

    public void CalculateAllCosts()
    {
        //reset
        DisciplineCosts = EnergyCosts = MaterialCosts = ToolCosts = TotalCmnCost = 0;
        SharedCosts = 0;

        foreach (var cost in Costs)
        {
            var commonCost = cost.CommonCost;
            if (commonCost != null)
            {
                switch (Enum.Parse<CommonCostType>(commonCost.Type, true))
                {
                    case CommonCostType.Discipline:
                        DisciplineCosts += cost.Cost;
                        break;
                    case CommonCostType.Energy:
                        EnergyCosts += cost.Cost;
                        break;
                    case CommonCostType.Material:
                        MaterialCosts += cost.Cost;
                        break;
                    case CommonCostType.Tool:
                        ToolCosts += cost.Cost;
                        break;
                }
            }

            if (cost.IsCommonTaskCost == true)
                SharedCosts += cost.Cost;

            TotalCmnCost += cost.Cost;
        }
    }
}