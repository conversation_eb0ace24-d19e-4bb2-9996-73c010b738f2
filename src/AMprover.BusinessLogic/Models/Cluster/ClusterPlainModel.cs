using System;

namespace AMprover.BusinessLogic.Models.Cluster;

public class ClusterPlainModel : BaseModel
{
    public string Description { get; set; }
    public int Level { get; set; }

    public int? PartOf { get; set; }

    public decimal? Interval { get; set; }
    public int? IntervalUnit { get; set; }
    public decimal? EstTaskCosts { get; set; }
    public decimal? TaskCosts { get; set; }
    public decimal? SharedCosts { get; set; }
    public decimal DisciplineCosts { get; set; } 
    public decimal MaterialCosts { get; set; } 
    public decimal EnergyCosts { get; set; } 
    public decimal ToolCosts { get; set; }
    public decimal TotalCmnCost { get; set; }
    public decimal? DownTime { get; set; }
    public decimal? Duration { get; set; }
    public int? Executor { get; set; }
    public int? Initiator { get; set; }
    public int? Status { get; set; }
    public string ShortKey { get; set; }
    public string InitiatedBy { get; set; }
    public DateTime? DateInitiated { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime? DateModified { get; set; }
    public string Remark { get; set; }
    public string Responsible { get; set; }
    public string SecondValues { get; set; }

    public int? ScenarioId { get; set; }

    public int? RiskObjectId { get; set; }
    public int? WorkpackageId { get; set; }
    public bool? DivideDuration { get; set; }
    public bool? DivideDownTime { get; set; }
    public string SiteId { get; set; }
    public string Location { get; set; }
    public bool? Interruptable { get; set; }
    public int? ShiftStartDate { get; set; }
    public int? ShiftEndDate { get; set; }
    public string TemplateType { get; set; }
    public string OrgId { get; set; }
    public int? Priority { get; set; }
    public int? Sequence { get; set; }
    public string ReferenceId { get; set; }
}