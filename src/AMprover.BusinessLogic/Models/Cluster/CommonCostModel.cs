using System;
using System.ComponentModel.DataAnnotations;
using AMprover.BusinessLogic.Attributes;
using AMprover.BusinessLogic.Enums;

namespace AMprover.BusinessLogic.Models.Cluster;

public class CommonCostModel
{
    [GridReadOnly]
    [GridWidthPixels(60)]
    public int Id { get; set; }

    [Required]
    [GridWidthPixels(100)]
    [GridStringEnum(typeof(CommonCostType))]
    [MaxLength(20)]
    public string Type { get; set; }

    [Required]
    [MaxLength(40)]
    public string Description { get; set; }

    [Required]
    [GridStringEnum(typeof(CommonCostCalculationType))]
    [GridWidthPixels(100)]
    [GridHeader("Calculation")]
    [MaxLength(10)]
    public string CalculationType { get; set; }

    [GridWidthPixels(80)]
    [GridFieldType(FieldType.Integer)]
    public decimal? Units { get; set; }

    [GridWidthPixels(80)]
    [GridFieldType(FieldType.Integer)]
    public decimal? Number { get; set; }

    [GridFieldType(FieldType.Currency)]
    [GridWidthPixels(100)]
    public decimal? Price { get; set; }

    [GridFieldType(FieldType.Currency)]
    [GridWidthPixels(100)]
    public decimal? ExtraCost { get; set; }

    [GridWidthPixels(80)]
    [Required]
    [MaxLength(20)]
    public string ShortKey { get; set; }
    public string Remarks { get; set; }

    [GridWidthPixels(80)]
    [GridHeader("Price year")]
    public int? PriceIndexYear { get; set; }
    public int? PriceGroup { get; set; }

    [MaxLength(10)]
    public string UnitType { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime? DateModified { get; set; }

    [MaxLength(50)]
    public string ReferenceCode { get; set; }

    [MaxLength(50)]
    public string VendorCode { get; set; }
    public int? Status { get; set; }
    public DateTime? StatusDate { get; set; }
    public bool? Spare { get; set; }
    public bool? Rotating { get; set; }


    [MaxLength(12)]
    public string OrgId { get; set; }


    [MaxLength(20)]
    public string SubType { get; set; }


    [MaxLength(20)]
    public string SubSubType { get; set; }

    public InflationGroupModel InflationGroup { get; set; }
    //public virtual ICollection<ClusterCost> ClusterCost { get; set; } = new HashSet<ClusterCost>();
    //public virtual ICollection<CommonTaskCost> CommonTaskCost { get; set; } = new HashSet<CommonTaskCost>();

    /// <summary>
    /// Display Item properties from Generic
    /// </summary>
    /// <returns></returns>
    public override string ToString()
    {
        return $"{ShortKey} - {Type} ({Id})";
    }

    public bool HideUnits()
    {
        return CalculationType.Length < 4;
    }

    /// <summary>
    /// Copy an item and Set it's ID to 0. Used for inserting copied Items into Database
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public T CopyAsNew<T>() where T : CommonCostModel
    {
        var copiedItem = (T)MemberwiseClone();
        copiedItem.Id = 0;
        return copiedItem;
    }
}