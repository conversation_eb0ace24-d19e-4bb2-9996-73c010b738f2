using System;
using System.Linq;

namespace AMprover.BusinessLogic.Models;

/// <summary>
/// Generic class for metadata that is available many model / data entities
/// </summary>
public class EntityMetadata
{
    /// <summary>
    /// Username that created the entity originally
    /// </summary>
    public string CreatedByUsername { get; set; }

    /// <summary>
    /// Date (and often timestamp) when the idenity was created originally
    /// </summary>
    public DateTime? CreatedOn { get; set; }

    /// <summary>
    /// Username that last updated the entitiy
    /// </summary>
    public string LastUpdatedByUsername { get; set; }

    /// <summary>
    /// Date (and often timestamp) when the idenity was last updated
    /// </summary>
    public DateTime? LastUpdatedOn { get; set; }

    public virtual bool HasCreatedInfo => !string.IsNullOrWhiteSpace(CreatedByUsername) || CreatedOn.HasValue;

    public virtual bool HasUpdatedInfo => !string.IsNullOrWhiteSpace(LastUpdatedByUsername) || LastUpdatedOn.HasValue;

    private string TimestampToDisplayText(DateTime? timestamp)
    {
        string dateTimeIndicator = "<unavailable>";
        if (timestamp.HasValue)
        {
            dateTimeIndicator = timestamp.Value.AddDays(30) < DateTime.Today ? timestamp.Value.ToShortDateString() : timestamp.Value.ToString();
        }
        return dateTimeIndicator;
    }

    private string UsernameToDisplayText(string username)
    {
        return string.IsNullOrWhiteSpace(username) ? "<unknown>": username.ToLower();
    }

    public override string ToString()
    {
        string displayText = null;

        if(!HasCreatedInfo && !HasUpdatedInfo) { return displayText; }

        if (HasCreatedInfo) {
            displayText += $"Created on {TimestampToDisplayText(CreatedOn)} by {UsernameToDisplayText(CreatedByUsername)}, ";
        }
        if(HasUpdatedInfo) {
            displayText += $"last updated {TimestampToDisplayText(LastUpdatedOn)} by {UsernameToDisplayText(LastUpdatedByUsername)}.";
            if(!HasCreatedInfo) {
                displayText = displayText.First().ToString().ToUpper() + displayText.Substring(1);
            }
        }
        return displayText;
    }
}