using AMprover.BusinessLogic.Models.ABS;
using AMprover.BusinessLogic.Models.RiskAnalysis;

namespace AMprover.BusinessLogic.Models.RiskOnAbs;

/// <summary>
/// The Risk on ABS tree contains 2 separate kind of nodes: Assets & Risks
/// Since the Tree is build to support a single T, we need a wrapper model which we can use to function as both
/// </summary>
public class RiskOnAbsTreeModel
{
    public AssetModel Asset { get; set; }

    public RiskModel Risk { get; set; }

    public bool IsRiskNode => Risk != null;
}