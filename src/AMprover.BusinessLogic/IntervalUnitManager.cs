using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BusinessLogic;

public interface IIntervalUnitManager
{
    Task<List<IntervalUnitModel>> GetIntervalUnits();

    Task<IntervalUnitModel> GetIntervalUnit(int id);

    Task<(bool, IntervalUnitModel)> SaveIntervalUnit(IntervalUnitModel model);

    Task<bool> DeleteIntervalUnit(int id);
}

public class IntervalUnitManager(
    AuthenticationStateProvider authenticationStateProvider,
    IMapper mapper,
    IAssetManagementDbContextFactory dbContextFactory)
    : BaseManager(dbContextFactory, authenticationStateProvider), IIntervalUnitManager
{
    public async Task<List<IntervalUnitModel>> GetIntervalUnits()
    {
        return await ExecuteWithContextAsync(async context =>
        {
            var intervalUnits = await context.LookupIntervalUnit
                .OrderBy(x => x.IntUnitName)
                .ToListAsync();
            return intervalUnits.ConvertAll(mapper.Map<IntervalUnitModel>);
        });
    }

    public async Task<IntervalUnitModel> GetIntervalUnit(int id)
    {
        return await ExecuteWithContextAsync(async context =>
        {
            var intervalUnit = await context.LookupIntervalUnit
                .FirstOrDefaultAsync(x => x.IntUnitId == id);
            return mapper.Map<IntervalUnitModel>(intervalUnit);
        });
    }

    public async Task<(bool, IntervalUnitModel)> SaveIntervalUnit(IntervalUnitModel model)
    {
        return await ExecuteWithSaveAsync(async context =>
        {
            var dbModel = await context.LookupIntervalUnit.FirstOrDefaultAsync(x => x.IntUnitId == model.Id);
            dbModel = dbModel != null ? mapper.Map(model, dbModel) : mapper.Map<LookupIntervalUnit>(model);

            await SetMetaDataAsync(dbModel);

            // Update the entity in the context
            if (dbModel.IntUnitId == 0)
            {
                await context.LookupIntervalUnit.AddAsync(dbModel);
            }
            else
            {
                context.LookupIntervalUnit.Update(dbModel);
            }

            return (true, mapper.Map<IntervalUnitModel>(dbModel));
        });
    }

    private async Task SetMetaDataAsync(LookupIntervalUnit model)
    {
        model.IntUnitModifiedBy = await GetUserNameAsync();
        model.IntUnitDateModified = DateTime.Now;
    }

    public async Task<bool> DeleteIntervalUnit(int id)
    {
        return await ExecuteWithSaveAsync(async context =>
        {
            var itemToRemove = await context.LookupIntervalUnit.FirstOrDefaultAsync(x => x.IntUnitId == id);

            if (itemToRemove == null)
            {
                return false;
            }

            context.LookupIntervalUnit.Remove(itemToRemove);

            // The context will be saved automatically by ExecuteWithSaveAsync

            return true;
        });
    }
}