using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.Criticality;
using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using AMprover.Data.Models;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BusinessLogic;

public interface ICriticalityRankingManager
{
    Task<List<CriticalityRankingModelFlat>> GetAllCriticalityRankingsAsync();

    Task<CriticalityRankingModelFlat> GetCriticalityAsync(string assetCode);

    Task<List<CriticalityRankingModelFlat>> ImportFromAssetsAsync();

    Task<(bool, DbOperationResult<List<CriticalityRankingModelFlat>>)> ImportFileUploadAsync(
        List<CriticalityRankingModelFlat> items);

    Task<CriticalityRankingModelFlat> SaveCriticalityRankingAsync(CriticalityRankingModelFlat model);

    Task UpdateCriticalitiesAsync(List<CriticalityRankingModelFlat> crits);

    Task DeleteAllCriticalitiesAsync();
}

public class CriticalityRankingManager(
    AuthenticationStateProvider authenticationStateProvider,
    IAssetManagementDbContextFactory dbContextFactory,
    IMapper mapper)
    : BaseManager(dbContextFactory, authenticationStateProvider), ICriticalityRankingManager
{
    private static async Task<List<CriticalityRanking>> GetCritsWithSiAsync(AssetManagementDbContext dbContext)
    {
        // The Relationship between criticalities and Sis is not enforced in the database.
        // This allows reimporting the Sis without losing the from the criticalities to the Sis
        // However, this does mean that we have to manually link the objects together we cannot use EFCore's Include
        var dbCrits = await dbContext.CriticalityRanking.ToListAsync();
        var dbSi = await dbContext.Si.OrderBy(x => x.SiName).ToListAsync();

        foreach (var dbCrit in dbCrits)
            dbCrit.CritSi = dbSi.FirstOrDefaultBinarySearch(x => x.SiName, dbCrit.CritSiName);

        return dbCrits;
    }

    private static async Task<CriticalityRanking> GetCritWithSiAsync(string siName, AssetManagementDbContext dbContext)
    {
        // The Relationship between criticalities and Sis is not enforced in the database.
        var dbCrit = await dbContext.CriticalityRanking.FirstOrDefaultAsync(x => x.CritSiName == siName);
        var dbSi = await dbContext.Si.FirstOrDefaultAsync(x => x.SiName == siName);

        if (dbCrit != null)
            dbCrit.CritSi = dbSi;

        return dbCrit;
    }

    public async Task<List<CriticalityRankingModelFlat>> GetAllCriticalityRankingsAsync()
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var dbCrits = await GetCritsWithSiAsync(dbContext);
            var mapped = dbCrits.Select(mapper.Map<CriticalityRankingModel>).ToList();
            return mapped.Select(mapper.Map<CriticalityRankingModelFlat>).ToList();
        });
    }

    public async Task<CriticalityRankingModelFlat> GetCriticalityAsync(string assetCode)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var asset = await dbContext.Si.FirstOrDefaultAsync(x => x.SiName == assetCode);
            var crit = await dbContext.CriticalityRanking.FirstOrDefaultAsync(x => x.CritSiName == assetCode);

            if (crit == null || asset == null)
                return null;

            crit.CritSi = asset;
            var mapped = mapper.Map<CriticalityRankingModel>(crit);
            return mapper.Map<CriticalityRankingModelFlat>(mapped);
        });
    }

    /// <summary>
    /// Returns all newly created CriticalityRankingModels
    /// </summary>
    /// <returns></returns>
    public async Task<List<CriticalityRankingModelFlat>> ImportFromAssetsAsync()
    {
        return await ExecuteWithSaveAsync(async dbContext =>
        {
            var assets = await dbContext.Si.ToListAsync();
            var crits = await dbContext.CriticalityRanking.ToListAsync();
            var newAssets = assets.Where(a => crits.All(x => x.CritSiName != a.SiName)).ToList();
            var date = DateTime.Now;
            var userName = await GetUserNameAsync();

            var newCrits = newAssets.Select(x =>
                new CriticalityRanking
                {
                    CritSiName = x.SiName,
                    CritDateInitiated = date,
                    CritDateModified = date,
                    CritInitiatedBy = userName,
                    CritModifiedBy = userName
                }).ToList();

            dbContext.CriticalityRanking.AddRange(newCrits);

            var newModels = newCrits.Select(mapper.Map<CriticalityRankingModel>).ToList();
            return newModels.Select(mapper.Map<CriticalityRankingModelFlat>).ToList();
        });
    }

    public async Task<(bool, DbOperationResult<List<CriticalityRankingModelFlat>>)> ImportFileUploadAsync(
        List<CriticalityRankingModelFlat> items)
    {
        try
        {
            return await ExecuteWithSaveAsync(async dbContext =>
            {
                var dbCrits = await GetCritsWithSiAsync(dbContext);
                int notFoundCount = 0;

                foreach (var crit in items)
                {
                    var dbCrit = dbCrits.FirstOrDefault(x => x.CritSi.SiName == crit.AssetCode);

                    if (dbCrit != null)
                    {
                        mapper.Map(crit, dbCrit);

                        // no empty strings to prevent UI issue with pie chart
                        if (string.IsNullOrWhiteSpace(dbCrit.CritCategory))
                            dbCrit.CritCategory = null;

                        dbContext.Update(dbCrit);
                    }
                    else
                    {
                        notFoundCount++;
                    }
                }

                var models = await dbContext.CriticalityRanking.Select(x => mapper.Map<CriticalityRankingModel>(x))
                    .ToListAsync();
                var result =
                    new DbOperationResult<List<CriticalityRankingModelFlat>>(models
                        .Select(mapper.Map<CriticalityRankingModelFlat>).ToList())
                    {
                        StatusMessage = notFoundCount > 0
                            ? $"Successfully imported {items.Count - notFoundCount} items. Failed to match {notFoundCount} items."
                            : $"Successfully imported {items.Count} items."
                    };
                return (true, result);
            });
        }
        catch (Exception ex)
        {
            var result = DbOperationResult<List<CriticalityRankingModelFlat>>.Failed(null, ex.Message);
            return (false, result);
        }
    }

    /// <summary>
    /// Update a single Criticality Ranking Row
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<CriticalityRankingModelFlat> SaveCriticalityRankingAsync(CriticalityRankingModelFlat model)
    {
        return await ExecuteWithSaveAsync(async dbContext =>
        {
            var dbModel = await dbContext.CriticalityRanking.FirstOrDefaultAsync(x => x.CritId == model.Id);

            if (dbModel == null)
                return model;

            dbModel = mapper.Map(model, dbModel);

            if (dbModel == null)
                return model;

            dbModel.CritModifiedBy = await GetUserNameAsync();
            dbModel.CritDateModified = DateTime.Now;

            dbContext.Update(dbModel);

            var dbCrit = await GetCritWithSiAsync(model.AssetCode, dbContext);
            return mapper.Map<CriticalityRankingModelFlat>(mapper.Map<CriticalityRankingModel>(dbCrit));
        });
    }

    public async Task UpdateCriticalitiesAsync(List<CriticalityRankingModelFlat> crits)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            var dbModels = await dbContext.CriticalityRanking.ToListAsync();
            var dbModelsToUpdate = new List<CriticalityRanking>();
            var userName = await GetUserNameAsync();
            var now = DateTime.Now;

            foreach (var crit in crits)
            {
                var modelToUpdate = dbModels.FirstOrDefault(x => x.CritId == crit.Id);
                if (modelToUpdate != null)
                {
                    mapper.Map(crit, modelToUpdate);
                    modelToUpdate.CritModifiedBy = userName;
                    modelToUpdate.CritDateModified = now;
                    dbModelsToUpdate.Add(modelToUpdate);
                }
            }

            dbContext.UpdateRange(dbModelsToUpdate);
        });
    }

    public async Task DeleteAllCriticalitiesAsync()
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            dbContext.CriticalityRanking.RemoveRange(dbContext.CriticalityRanking);
        });
    }
}