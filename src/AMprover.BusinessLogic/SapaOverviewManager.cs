using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.Sapa;
using AMprover.BusinessLogic.Models.Tree;
using AMprover.Data.Entities.AM;
using AMprover.Data.Extensions;
using AMprover.Data.Infrastructure;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BusinessLogic;

public interface ISapaOverviewManager
{
    Task<List<string>> GetSapaWorkPackagesAsync();
    Task<TreeNodeGeneric<SapaTreeObject>> GetSapaTreeAsync(List<string> workPackages);

    Task<SapaCollectionModel> GetSapaCollectionAsync(int sapaCollectionId);
    Task<SapaModel> GetCombinedSapaModelByCollectionAsync(int collectionId, List<string> workPackages);
    Task<SapaModel> GetCombinedSapaModelByRiskObjectAsync(int riskObjectId, List<string> workPackages);
    Task<SapaModel> GetCombinedSapaModelByScenarioAsync(int scenarioId, List<string> workPackages);
    Task<SapaModel> GetSapaModelBySapaIdAsync(int sapaId);
    Task<SapaModel> GetSapaModelByRiskObjectAsync(int riskObjectId, int? workpackage);
    Task<SapaDetailModel> GetSapaDetailAsync(int taskId);

    Task DeleteBySapaCollectionAsync(int collectionId);
    Task DeleteSapaByRiskObjectAsync(int riskObjectId);

    Task<SapaCollectionModel> SaveSapaCollectionAsync(SapaCollectionModel sapaCollection);

    Task<SapaModel> SaveSapaAsync(SapaModel sapaModel);
    Task SetSapaStatusAsync(int riskObjectId, Status? status);
    Task NeedReviewSapaAsync(int riskObjectId);

    Task<SapaValidateResult> ValidateGenerateSapaAsync(int riskObjectId);
    Task<SapaModel> GenerateSapaByRiskObjectAsync(int riskObjectId, string noWorkPackageText);
    Task<List<SapaModel>> GetSapasAsync(int riskObjectId);
    Task<List<SapaModel>> GetSapasOnScenarioAsync(int scenarioId);
    Task<int?> GetRiskObjIdAsync(int scenarioId);

    Task UpdateSapaCollectionAsync(int sapaCollectionId, string noWorkPackageName);
    Task UpdateSapaByRiskObjectAsync(int riskObjectId, string noWorkPackageName);

    Task<Dictionary<int?, string>> GetSelectedSapaDictAsync(int riskObjectId);
    Task<List<SapaDetail>> GetSheqDataAsync(int riskObjectId);

    Task<decimal> GetAssignedBudgetAsync(int sapaId, int year);

    Task<int?> GetSelectedSapaCollectionAsync(int riskObjectId);
    Task SetSelectedSapaCollectionAsync(int riskObjectId, int? sapaCollectionId);
}

public class SapaOverviewManager(
    IAssetManagementDbContextFactory dbContextFactory,
    IMapper mapper,
    AuthenticationStateProvider authenticationStateProvider)
    : BaseManager(dbContextFactory, authenticationStateProvider), ISapaOverviewManager
{
    public async Task<List<string>> GetSapaWorkPackagesAsync()
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            return await dbContext.Sapa.Select(x => x.SapaName).Distinct().ToListAsync();
        });
    }

    public async Task<TreeNodeGeneric<SapaTreeObject>> GetSapaTreeAsync(List<string> workPackages)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var rootNode = new TreeNodeGeneric<SapaTreeObject>(null, null);

            var riskObjects = await dbContext.RiskObject
                .Include(x => x.RiskObjScenario)
                .Where(x => x.RiskObjAnalyseType == "SAPA")
                .ToListAsync();

            var riskObjectsGroupByScenario = riskObjects.GroupBy(x => x.RiskObjScenarioId);
            var sapaCollections = await dbContext.SapaCollection
                .Include(x => x.Sapas)
                .ThenInclude(x => x.SapaWorkpackage)
                .ToListAsync();

            if (workPackages?.Count > 0)
            {
                foreach (var sapaCollection in sapaCollections)
                {
                    sapaCollection.Sapas = sapaCollection.Sapas.Where(x => workPackages.Contains(x.SapaName)).ToList();
                }

                sapaCollections = sapaCollections.Where(x => x.Sapas?.Count > 0).ToList();
            }

            foreach (var riskObjectGroup in riskObjectsGroupByScenario)
            {
                var scenario = riskObjectGroup.First().RiskObjScenario;
                var scenarioNode = new TreeNodeGeneric<SapaTreeObject>(
                    new SapaTreeObject
                    {
                        Name = scenario.ScenName,
                        ScenarioId = scenario.ScenId,
                        SapaTreeNodeType = SapaTreeNodeType.Scenario
                    }, rootNode)
                {
                    Icon = SapaTreeNodeType.Scenario.ToIcon(),
                    Name = scenario.ScenName
                };
                rootNode.Nodes.Add(scenarioNode);

                foreach (var riskObject in riskObjectGroup)
                {
                    var riksObjectNode = new TreeNodeGeneric<SapaTreeObject>(
                        new SapaTreeObject
                        {
                            Name = riskObject.RiskObjName,
                            RiskObjectId = riskObject.RiskObjId,
                            SapaTreeNodeType = SapaTreeNodeType.RiskObject
                        }, scenarioNode)
                    {
                        Icon = SapaTreeNodeType.RiskObject.ToIcon(),
                        Name = riskObject.RiskObjName,
                    };

                    scenarioNode.Nodes.Add(riksObjectNode);

                    foreach (var sapaCollection in sapaCollections
                                 .Where(x => x.SapaCollRiskObjId == riskObject.RiskObjId).OrderBy(x => x.SapaCollName))
                    {
                        var sapaCollectionNode = new TreeNodeGeneric<SapaTreeObject>(
                            new SapaTreeObject
                            {
                                Name = sapaCollection.SapaCollName,
                                RiskObjectId = riskObject.RiskObjId,
                                SapaTreeNodeType = SapaTreeNodeType.SapaCollection,
                                SapaCollectionId = sapaCollection.SapaCollId,
                            }, riksObjectNode)
                        {
                            Icon = SapaTreeNodeType.SapaCollection.ToIcon(),
                            Name = sapaCollection.SapaCollName,
                            Id = sapaCollection.SapaCollId,
                            FilteredState = sapaCollection.SapaCollSelected ? FilteredState.Self : FilteredState.None
                        };
                        riksObjectNode.Nodes.Add(sapaCollectionNode);

                        foreach (var sapa in sapaCollection.Sapas)
                        {
                            var workPackageNode = new TreeNodeGeneric<SapaTreeObject>(
                                new SapaTreeObject
                                {
                                    Name = sapa.SapaName,
                                    SapaId = sapa.SapaId,
                                    RiskObjectId = riskObject.RiskObjId,
                                    SapaTreeNodeType = SapaTreeNodeType.WorkPackage,
                                    WorkPackageId = sapa.SapaWorkpackageId
                                }, sapaCollectionNode)
                            {
                                Icon = SapaTreeNodeType.WorkPackage.ToIcon(),
                                Name = sapa.SapaName,
                                Id = sapa.SapaId,
                            };

                            sapaCollectionNode.Nodes.Add(workPackageNode);
                        }
                    }
                }
            }

            return rootNode;
        });
    }

    public async Task<SapaCollectionModel> GetSapaCollectionAsync(int sapaCollectionId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var dbModel = await dbContext.SapaCollection.FirstOrDefaultAsync(x => x.SapaCollId == sapaCollectionId);

            return dbModel != null
                ? mapper.Map<SapaCollectionModel>(dbModel)
                : null;
        });
    }

    public async Task<SapaModel> GetCombinedSapaModelByCollectionAsync(int collectionId, List<string> workPackages)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            // ReSharper disable once EntityFramework.NPlusOne.IncompleteDataQuery
            var dbModels = await dbContext.Sapa
                .IncludeAllSapaData()
                .Where(x => x.SapaCollectionId == collectionId)
                .ToListAsync();

            if (workPackages?.Count > 0)
                dbModels = dbModels.Where(x => workPackages.Contains(x.SapaName)).ToList();

            if (dbModels.Count == 0)
                return null;

            var sapas = mapper.Map<List<SapaModel>>(dbModels);
            // ReSharper disable once EntityFramework.NPlusOne.IncompleteDataUsage
            return CombineSapaModels(sapas, dbModels.FirstOrDefault()?.SapaCollection?.SapaCollName);
        });
    }

    public async Task<List<SapaModel>> GetSapasAsync(int riskObjectId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var dbModels = await dbContext.Sapa
                .Include(x => x.SapaWorkpackage)
                .Where(x => x.SapaRiskObjId == riskObjectId && x.SapaCollection.SapaCollSelected)
                .ToListAsync();

            return mapper.Map<List<SapaModel>>(dbModels);
        });
    }

    public async Task<int?> GetRiskObjIdAsync(int scenarioId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            return await dbContext.RiskObject
                .Where(x => x.RiskObjAnalyseType == "SAPA")
                .Where(x => x.RiskObjScenarioId == scenarioId)
                .Select(x => (int?) x.RiskObjId)
                .FirstOrDefaultAsync();
        });
    }

    public async Task<List<SapaModel>> GetSapasOnScenarioAsync(int scenarioId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var riskObjectIds = await dbContext.RiskObject
                .Where(x => x.RiskObjScenarioId == scenarioId)
                .Select(x => x.RiskObjId)
                .ToListAsync();

            var dbModels = await dbContext.Sapa
                .Where(x => riskObjectIds.Contains(x.SapaRiskObjId) && x.SapaCollection.SapaCollSelected)
                .ToListAsync();

            return mapper.Map<List<SapaModel>>(dbModels);
        });
    }

    public async Task<SapaModel> GetCombinedSapaModelByRiskObjectAsync(int riskObjectId, List<string> workPackages)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var dbModels = await dbContext.Sapa
                .IncludeAllSapaData()
                .Where(x => x.SapaRiskObjId == riskObjectId && x.SapaCollection.SapaCollSelected)
                .ToListAsync();

            if (workPackages?.Count > 0)
                dbModels = dbModels.Where(x => workPackages.Contains(x.SapaName)).ToList();

            var sapas = mapper.Map<List<SapaModel>>(dbModels);
            var riskObjectName = await dbContext.RiskObject
                .Where(x => x.RiskObjId == riskObjectId)
                .Select(x => x.RiskObjName)
                .FirstOrDefaultAsync();

            var result = CombineSapaModels(sapas, riskObjectName);

            result.RiskObjectId = riskObjectId;
            return result;
        });
    }

    public async Task<SapaModel> GetCombinedSapaModelByScenarioAsync(int scenarioId, List<string> workPackages)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var riskObjectIds = await dbContext.RiskObject
                .Where(x => x.RiskObjScenarioId == scenarioId)
                .Select(x => x.RiskObjId)
                .ToListAsync();

            var dbModels = await dbContext.Sapa
                .IncludeAllSapaData()
                .Where(x => riskObjectIds.Contains(x.SapaRiskObjId) && x.SapaCollection.SapaCollSelected)
                .ToListAsync();

            if (workPackages?.Count > 0)
                dbModels = dbModels.Where(x => workPackages.Contains(x.SapaName)).ToList();

            var sapas = mapper.Map<List<SapaModel>>(dbModels);
            var scenarioName = await dbContext.Scenario
                .Where(x => x.ScenId == scenarioId)
                .Select(x => x.ScenName)
                .FirstOrDefaultAsync();

            return CombineSapaModels(sapas, scenarioName);
        });
    }

    private static SapaModel CombineSapaModels(List<SapaModel> sapas, string name)
    {
        var yearNumbers = sapas.SelectMany(x => x.Years).Select(x => x.Year).Distinct().OrderBy(x => x).ToList();

        Dictionary<int, List<SapaDetailModel>> missingDetails = new();
        foreach (var yearNumber in yearNumbers)
        {
            missingDetails.Add(yearNumber, []);
            var sapasWithThisYearMissing = sapas.Where(x => x.FirstYear > yearNumber).ToList();

            if (sapasWithThisYearMissing.Count == 0)
                break;

            foreach (var sapa in sapasWithThisYearMissing)
            {
                var yearDifference = sapa.FirstYear - yearNumber;
                var closestYear = sapa.Years.FirstOrDefault();

                if (closestYear == null)
                    continue;

                foreach (var sapaDetail in closestYear.Details)
                {
                    var detailCopy = sapaDetail.CopyAsNew();

                    for (var i = 0; i < yearDifference; i++)
                    {
                        detailCopy = ShiftSapaDetail1Year(detailCopy);
                    }

                    missingDetails[yearNumber].Add(detailCopy);
                }
            }
        }

        var allYearsGrouped = sapas.SelectMany(x => x.Years).GroupBy(x => x.Year).ToList();
        var years = new List<SapaYearModel>();
        foreach (var yearGroup in allYearsGrouped)
        {
            var realDetails = yearGroup.SelectMany(x => x.Details).ToList();
            var artificialDetails = missingDetails.TryGetValue(yearGroup.Key, out var detail)
                ? detail
                : [];

            var year = new SapaYearModel
            {
                Year = yearGroup.Key,
                Budget = yearGroup.Sum(x => x.Budget),
                BudgetApproved = yearGroup.Sum(x => x.BudgetApproved),
                BudgetRequest = yearGroup.Sum(x => x.BudgetRequest),
                Details = realDetails.Concat(artificialDetails)
            };

            years.Add(year);
        }

        return new SapaModel
        {
            Name = name,
            Status = sapas.FirstOrDefault()?.Status,
            FirstYear = sapas.Where(x => x.FirstYear > 0).MinBy(x => x.FirstYear)?.FirstYear,
            LastYear = sapas.Where(x => x.LastYear > 0).MaxBy(x => x.LastYear)?.LastYear,
            Budget = sapas.Sum(x => x.Budget),
            BudgetApproved = sapas.Sum(x => x.BudgetApproved),
            BudgetRequest = sapas.Sum(x => x.BudgetRequest),
            Years = years.OrderBy(x => x.Year)
        };
    }

    private static SapaDetailModel ShiftSapaDetail1Year(SapaDetailModel input)
    {
        input.CostYear5 = input.CostYear4;
        input.CostYear4 = input.CostYear3;
        input.CostYear3 = input.CostYear2;
        input.CostYear2 = input.CostYear1;
        input.CostYear1 = 0;

        return input;
    }

    public async Task<SapaModel> GetSapaModelBySapaIdAsync(int sapaId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var dbModel = await dbContext.Sapa
                .IncludeAllSapaData()
                .FirstOrDefaultAsync(x => x.SapaId == sapaId);

            return mapper.Map<SapaModel>(dbModel);
        });
    }

    public async Task<SapaModel> GetSapaModelByRiskObjectAsync(int riskObjectId, int? workpackage)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var dbModel = await dbContext.Sapa
                .IncludeAllSapaData()
                .FirstOrDefaultAsync(x => x.SapaRiskObjId == riskObjectId && x.SapaWorkpackageId == workpackage);

            return mapper.Map<SapaModel>(dbModel);
        });
    }

    public async Task DeleteBySapaCollectionAsync(int collectionId)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            var sapaCollection = await dbContext.SapaCollection
                .Include(x => x.Sapas)
                .ThenInclude(x => x.Years)
                .ThenInclude(x => x.Details)
                .FirstOrDefaultAsync(x => x.SapaCollId == collectionId);

            if (sapaCollection != null)
            {
                dbContext.SapaCollection.Remove(sapaCollection);
            }
        });
    }

    public async Task DeleteSapaByRiskObjectAsync(int riskObjectId)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            var sapaCollections = await dbContext.SapaCollection
                .Include(x => x.Sapas)
                .ThenInclude(x => x.Years)
                .ThenInclude(x => x.Details)
                .Where(x => x.SapaCollRiskObjId == riskObjectId)
                .ToListAsync();

            if (sapaCollections.Count > 0)
            {
                dbContext.SapaCollection.RemoveRange(sapaCollections);
            }
        });
    }

    public async Task<SapaModel> SaveSapaAsync(SapaModel sapaModel)
    {
        if (sapaModel.Id <= 0)
        {
            throw new ArgumentException($"Unable to Save Sapa with Id 0");
        }

        return await ExecuteWithSaveAsync<Sapa, SapaModel>(
            async dbContext =>
            {
                var dbModel = await dbContext.Sapa
                    .IncludeAllSapaData()
                    .FirstOrDefaultAsync(x => x.SapaId == sapaModel.Id);

                dbModel = mapper.Map(sapaModel, dbModel);

                dbContext.Update(dbModel);
                return dbModel;
            },
            async (context, entity) => await GetSapaModelBySapaIdAsync(entity.SapaId));
    }

    public async Task<SapaCollectionModel> SaveSapaCollectionAsync(SapaCollectionModel sapaCollection)
    {
        return await ExecuteWithSaveAsync(async dbContext =>
        {
            var dbModel = await dbContext.SapaCollection.FirstOrDefaultAsync(x => x.SapaCollId == sapaCollection.Id);

            if (dbModel != null)
            {
                mapper.Map(sapaCollection, dbModel);
                dbContext.Update(dbModel);
                return mapper.Map<SapaCollectionModel>(dbModel);
            }

            return null;
        });
    }

    public async Task SetSapaStatusAsync(int riskObjectId, Status? status)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            // Set Status on all related Sapas
            var sapas = await dbContext.Sapa.Where(x => x.SapaRiskObjId == riskObjectId).ToListAsync();
            sapas.ForEach(x => x.SapaStatusId = (int?) status);
            dbContext.UpdateRange(sapas);

            // Set Status on RiskObject
            var riskObject = await dbContext.RiskObject.FirstOrDefaultAsync(x => x.RiskObjId == riskObjectId);
            riskObject.RiskObjStatus = (int?) status;
            dbContext.Update(riskObject);

            // Set Status on all related Risks
            var mrbs = await dbContext.Mrb.Where(x => x.MrbRiskObject == riskObjectId).ToListAsync();
            mrbs.ForEach(x => x.MrbStatus = (int?) status);
            dbContext.UpdateRange(mrbs);

            // Set status on Tasks
            var mrbIds = mrbs.Select(x => x.Mrbid).ToList();
            var tasks = await dbContext.Task.Where(x => x.TskMrbId != null && mrbIds.Contains(x.TskMrbId.Value))
                .ToListAsync();
            tasks.ForEach(x => x.TskStatus = (int?) status);
            dbContext.UpdateRange(tasks);
        });
    }

    public async Task NeedReviewSapaAsync(int riskObjectId)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            // Set all Tasks on Complete Or Need_Review based on if the tasks were approved.
            var allTasks = await dbContext.Sapa
                .Include(x => x.SapaCollection)
                .Include(x => x.Years).ThenInclude(x => x.Details).ThenInclude(x => x.Task)
                .Where(x => x.SapaRiskObjId == riskObjectId && x.SapaCollection.SapaCollSelected)
                .SelectMany(x => x.Years).SelectMany(x => x.Details)
                .ToListAsync();

            var distinctTasks = allTasks.DistinctBy(x => x.SapaDetTskId).ToList();

            var approvedTasks = distinctTasks.Where(x => x.SapaDetApproved).Select(x => x.Task).ToList();
            var declindeTasks = distinctTasks.Where(x => !x.SapaDetApproved).Select(x => x.Task).ToList();

            approvedTasks.ForEach(x => x.TskStatus = (int?) Status.Complete);
            declindeTasks.ForEach(x => x.TskStatus = (int?) Status.Need_review);
            dbContext.UpdateRange(allTasks);

            // Do the same for Risks
            var allRiskIds = distinctTasks.Select(x => x.SapaDetMrbId).Distinct().ToList();
            var declinedRiskIds = declindeTasks.Where(x => x.TskMrbId != null).Select(x => x.TskMrbId.Value).Distinct()
                .ToList();
            var approvedRiskIds = allRiskIds.Except(declinedRiskIds).ToList();

            var allRisks = await dbContext.Mrb.Where(x => allRiskIds.Contains(x.Mrbid)).ToListAsync();
            var declinedRisks = allRisks.Where(x => declinedRiskIds.Contains(x.Mrbid)).ToList();
            var approvedRisks = allRisks.Where(x => approvedRiskIds.Contains(x.Mrbid)).ToList();

            declinedRisks.ForEach(x => x.MrbStatus = (int?) Status.Need_review);
            approvedRisks.ForEach(x => x.MrbStatus = (int?) Status.Complete);
            dbContext.UpdateRange(allRisks);

            // Set Status on all related Sapas
            var sapas = await dbContext.Sapa.Where(x => x.SapaRiskObjId == riskObjectId).ToListAsync();
            sapas.ForEach(x => x.SapaStatusId = (int?) Status.Need_review);
            dbContext.UpdateRange(sapas);

            // Set Status on RiskObject
            var riskObject = await dbContext.RiskObject.FirstOrDefaultAsync(x => x.RiskObjId == riskObjectId);
            riskObject.RiskObjStatus = (int?) (declinedRiskIds.Count > 0 ? Status.Need_review : Status.Complete);
            dbContext.Update(riskObject);
        });
    }

    public async Task<SapaValidateResult> ValidateGenerateSapaAsync(int riskObjectId)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync();
        var riskObject = await dbContext.RiskObject
            .Include(x => x.Risks)
            .ThenInclude(x => x.Tasks)
            .FirstOrDefaultAsync(x => x.RiskObjId == riskObjectId);

        var tasks = riskObject?.Risks.SelectMany(x => x.Tasks).ToList() ?? [];
        var firstYear = tasks.Where(x => x.TskValidFromYear != null).Select(x => (int) x.TskValidFromYear)
            .OrderBy(x => x).FirstOrDefault();
        var lastYear = tasks.Where(x => x.TskValidUntilYear != null).Select(x => (int) x.TskValidUntilYear)
            .OrderBy(x => x).LastOrDefault();

        return new SapaValidateResult
        {
            FirstYear = firstYear,
            LastYear = lastYear
        };
    }

    public async Task<SapaModel> GenerateSapaByRiskObjectAsync(int riskObjectId, string noWorkPackageText)
    {
        return await ExecuteWithSaveAsync<RiskObject, SapaModel>(
            async dbContext =>
            {
                var riskObject = await dbContext.RiskObject
                    .Include(x => x.Risks)
                    .ThenInclude(x => x.Tasks)
                    .ThenInclude(x => x.TskSapaWorkpackageNavigation)
                    .FirstOrDefaultAsync(x => x.RiskObjId == riskObjectId && x.RiskObjAnalyseType == "SAPA");

                await GenerateSapaforRiskObjectsAsync(riskObject, noWorkPackageText, dbContext);
                return riskObject;
            },
            async (context, entity) =>
            {
                // Get the combined model after saving changes
                return await GetCombinedSapaModelByRiskObjectAsync(entity.RiskObjId, null);
            });
    }

    public async Task UpdateSapaByRiskObjectAsync(int riskObjectId, string noWorkPackageName)
    {
        await ExecuteWithContextAsync(async dbContext =>
        {
            var sapaCollectionIds = await dbContext.SapaCollection
                .Where(x => x.SapaCollRiskObjId == riskObjectId)
                .Select(x => x.SapaCollId)
                .ToListAsync();

            foreach (var id in sapaCollectionIds)
            {
                await UpdateSapaCollectionAsync(id, noWorkPackageName);
            }
        });
    }

    public async Task UpdateSapaCollectionAsync(int sapaCollectionId, string noWorkPackageName)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            var dbCollection = await dbContext.SapaCollection
                .Include(x => x.Sapas)
                .ThenInclude(x => x.Years)
                .ThenInclude(x => x.Details)
                .FirstOrDefaultAsync(x => x.SapaCollId == sapaCollectionId);

            var riskObject = await dbContext.RiskObject
                .Include(x => x.Risks)
                .ThenInclude(x => x.Tasks)
                .ThenInclude(x => x.TskSapaWorkpackageNavigation)
                .FirstOrDefaultAsync(x => x.RiskObjId == dbCollection.SapaCollRiskObjId);

            var alltasks = riskObject.Risks.SelectMany(x => x.Tasks).Where(x => x.TskValidFromYear != null).ToList();
            var wpGroups = alltasks.GroupBy(x => x.TskSapaWorkpackage).OrderBy(x => x.Key);
            var wpIds = new List<int?>();

            foreach (var workPackage in wpGroups)
            {
                wpIds.Add(workPackage.Key);
                var firstYear = workPackage.MinBy(x => x.TskValidFromYear)?.TskValidFromYear ?? 0;
                var lastYear = workPackage.MaxBy(x => x.TskValidUntilYear)?.TskValidUntilYear ?? firstYear;
                var sapa = dbCollection.Sapas.FirstOrDefault(x => x.SapaWorkpackageId == workPackage.Key);

                var sapaName = workPackage.FirstOrDefault()?.TskSapaWorkpackageNavigation?.SapaWpName;
                if (string.IsNullOrWhiteSpace(sapaName))
                    sapaName = noWorkPackageName;

                if (sapa == null)
                {
                    sapa = new Sapa
                    {
                        Years = new List<SapaYear>(),
                        SapaApproveForAllYears = true,
                        SapaStatusId = (int?) Status.Budgeting,
                        SapaRiskObjId = riskObject.RiskObjId
                    };
                    dbCollection.Sapas.Add(sapa);
                }

                sapa.SapaFirstYear = firstYear;
                sapa.SapaLastYear = lastYear;
                sapa.SapaName = sapaName;
                sapa.SapaWorkpackageId = workPackage.Key;
                sapa.SapaCollectionId = dbCollection.SapaCollId;

                for (int year = firstYear; year <= lastYear; year++)
                {
                    var sapaYear = sapa.Years.FirstOrDefault(x => x.SapaYearYear == year);
                    if (sapaYear == null)
                    {
                        sapaYear = new SapaYear
                        {
                            SapaYearYear = year,
                            Details = new List<SapaDetail>()
                        };
                        sapa.Years.Add(sapaYear);
                    }

                    var validTaskIds = workPackage.Select(t => t.TskId).ToHashSet();

                    var toRemove = sapaYear.Details
                        .Where(d => !validTaskIds.Contains(d.SapaDetTskId))
                        .ToList();

                    dbContext.SapaDetail.RemoveRange(toRemove);

                    sapaYear.Details = sapaYear.Details
                        .Where(d => validTaskIds.Contains(d.SapaDetTskId))
                        .ToList();

                    foreach (var task in workPackage)
                    {
                        var sapaDetail = sapaYear.Details.FirstOrDefault(x => x.SapaDetTskId == task.TskId);
                        sapaDetail = GetSapaDetail(task, year, sapaDetail);

                        if (sapaDetail != null && sapaYear.Details.All(x => x.SapaDetTskId != task.TskId))
                        {
                            sapaYear.Details.Add(sapaDetail);
                        }
                    }

                    sapaYear.SapaYearBudgetRequest = sapaYear.Details.Sum(x => x.SapaDetCostYear1);
                }

                var yearsToRemove = sapa.Years.Where(x => x.SapaYearYear < firstYear || x.SapaYearYear > lastYear)
                    .ToList();
                sapa.Years = sapa.Years.Except(yearsToRemove).ToList();

                if (yearsToRemove.Count != 0)
                    dbContext.RemoveRange(yearsToRemove);
            }

            var workPackagesToRemove =
                dbCollection?.Sapas.Where(x => !wpIds.Contains(x.SapaWorkpackageId)).ToList() ?? [];

            if (dbCollection != null)
                dbCollection.Sapas = dbCollection.Sapas.Except(workPackagesToRemove).ToList();

            if (workPackagesToRemove.Count != 0)
                dbContext.RemoveRange(workPackagesToRemove);

            if (dbCollection != null)
                dbContext.Update(dbCollection);
        });
    }

    public async Task<Dictionary<int?, string>> GetSelectedSapaDictAsync(int riskObjectId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            return await dbContext.SapaCollection
                .Where(x => x.SapaCollRiskObjId == riskObjectId)
                .ToDictionaryAsync(x => (int?) x.SapaCollId, x => x.SapaCollName);
        });
    }

    public async Task<List<SapaDetail>> GetSheqDataAsync(int riskObjectId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            return await dbContext.SapaDetail
                .Where(x => x.SapaDetMrbId == riskObjectId)
                .Include(sapaDetail => sapaDetail.Risk)
                .ToListAsync();
        });
    }

    public async Task<decimal> GetAssignedBudgetAsync(int sapaId, int year)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var sapaYear = await dbContext.SapaYear
                .Where(x => x.SapaYearSapaId == sapaId)
                .FirstOrDefaultAsync(x => x.SapaYearYear == year);

            return sapaYear?.SapaYearBudget ?? 0;
        });
    }

    private async Task GenerateSapaforRiskObjectsAsync(RiskObject riskObject, string noWorkPackageName,
        AssetManagementDbContext dbContext)
    {
        var allTasks = riskObject.Risks.SelectMany(x => x.Tasks);
        var wpGroups = allTasks.GroupBy(x => x.TskSapaWorkpackage).OrderBy(x => x.Key).ToList();
        var collectionNames = await dbContext.SapaCollection
            .Where(x => x.SapaCollRiskObjId == riskObject.RiskObjId)
            .Select(x => x.SapaCollName)
            .ToListAsync();

        var sapaCollName = "Sapa Collection";
        var index = 1;
        while (true)
        {
            if (!collectionNames.Contains(sapaCollName))
                break;

            sapaCollName = $"Sapa Collection {index++}";
        }

        var sapaCollection = new SapaCollection
        {
            SapaCollName = sapaCollName,
            SapaCollRiskObjId = riskObject.RiskObjId,
        };
        dbContext.SapaCollection.Add(sapaCollection);
        await dbContext.SaveChangesAndClearAsync(await GetUserNameAsync());

        await SetSelectedSapaCollectionAsync(riskObject.RiskObjId, sapaCollection.SapaCollId);

        foreach (var workPackage in wpGroups)
        {
            var firstYear = workPackage.MinBy(x => x.TskValidFromYear)?.TskValidFromYear ?? 0;
            var lastYear = workPackage.MaxBy(x => x.TskValidUntilYear)?.TskValidUntilYear ?? firstYear;
            var sapaName = workPackage.FirstOrDefault()?.TskSapaWorkpackageNavigation?.SapaWpName;
            if (string.IsNullOrWhiteSpace(sapaName))
                sapaName = noWorkPackageName;

            var sapa = new Sapa
            {
                SapaFirstYear = firstYear,
                SapaLastYear = lastYear,
                SapaCollectionId = sapaCollection.SapaCollId,
                Years = new List<SapaYear>(),
                SapaWorkpackageId = workPackage.Key,
                SapaName = sapaName,
                SapaApproveForAllYears = true,
                SapaStatusId = (int?) Status.Budgeting,
                SapaRiskObjId = riskObject.RiskObjId
            };

            sapa = ProcessYears(sapa, firstYear, lastYear, workPackage.ToList());

            dbContext.Sapa.Add(sapa);
            await dbContext.SaveChangesAndClearAsync(await GetUserNameAsync());
        }

        await GetSapaModelByRiskObjectAsync(riskObject.RiskObjId, wpGroups.FirstOrDefault()?.Key);
    }

    private static Sapa ProcessYears(Sapa sapa, int firstYear, int lastYear, List<Data.Entities.AM.Task> tasks)
    {
        for (var year = firstYear; year <= lastYear; year++)
        {
            var sapaYear = new SapaYear
            {
                SapaYearYear = year,
                Details = new List<SapaDetail>()
            };

            foreach (var detail in tasks.Select(task => GetSapaDetail(task, year)).Where(detail => detail != null))
            {
                sapaYear.Details.Add(detail);
            }

            sapaYear.SapaYearBudgetRequest = sapaYear.Details.Sum(x => x.SapaDetCostYear1);
            sapa.Years.Add(sapaYear);
        }

        sapa.SapaBudgetRequest = sapa.Years.Sum(x => x.SapaYearBudgetRequest);
        return sapa;
    }

    public async Task<SapaDetailModel> GetSapaDetailAsync(int sapaDetailId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var sapaDetail = await dbContext.SapaDetail
                .Where(x => x.SapaDetId == sapaDetailId)
                .Include(x => x.Risk).ThenInclude(x => x.Installation)
                .Include(x => x.Risk).ThenInclude(x => x.System)
                .Include(x => x.Risk).ThenInclude(x => x.Component)
                .Include(x => x.Risk).ThenInclude(x => x.MrbImage)
                .Include(x => x.Task).ThenInclude(x => x.TskSapaWorkpackageNavigation)
                .FirstOrDefaultAsync();

            return mapper.Map<SapaDetailModel>(sapaDetail);
        });
    }

    private static SapaDetail GetSapaDetail(Data.Entities.AM.Task task, int year, SapaDetail detail = null)
    {
        if (task.TskValidFromYear == null || year > task.TskValidUntilYear)
            return null;

        detail ??= new SapaDetail
        {
            SapaDetTskId = task.TskId,
            SapaDetMrbId = task.TskMrbId ?? 0,
            SapaDetScore = task.TskMrb.MrbRiskBefore, //TODO: Multiply with task weight percent
        };

        detail.SapaDetCostYear1 = 0;
        detail.SapaDetCostYear2 = 0;
        detail.SapaDetCostYear3 = 0;
        detail.SapaDetCostYear4 = 0;
        detail.SapaDetCostYear5 = 0;

        var offset = year - task.TskValidFromYear.Value;
        decimal[] costArr =
        [
            task.TskCostY1 ?? 0, task.TskCostY2 ?? 0, task.TskCostY3 ?? 0, task.TskCostY4 ?? 0, task.TskCostY5 ?? 0
        ];

        if (offset >= 0)
            detail.SapaDetCostYear1 += costArr.Skip(offset).FirstOrDefault();

        if (offset >= -1)
            detail.SapaDetCostYear2 += costArr.Skip(offset + 1).FirstOrDefault();

        if (offset >= -2)
            detail.SapaDetCostYear3 += costArr.Skip(offset + 2).FirstOrDefault();

        if (offset >= -3)
            detail.SapaDetCostYear4 += costArr.Skip(offset + 3).FirstOrDefault();

        if (offset >= -4)
            detail.SapaDetCostYear5 += costArr.Skip(offset + 4).FirstOrDefault();

        detail.SapaDetTotalCapexNeeded = detail.SapaDetCostYear1 + detail.SapaDetCostYear2 + detail.SapaDetCostYear3 +
                                         detail.SapaDetCostYear4 + detail.SapaDetCostYear5;
        return detail;
    }

    public async Task<int?> GetSelectedSapaCollectionAsync(int riskObjectId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var selected = await dbContext.SapaCollection
                .FirstOrDefaultAsync(x => x.SapaCollRiskObjId == riskObjectId && x.SapaCollSelected);
            return selected?.SapaCollId;
        });
    }

    public async Task SetSelectedSapaCollectionAsync(int riskObjectId, int? sapaCollectionId)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            var sapaCollections = await dbContext.SapaCollection
                .Where(x => x.SapaCollRiskObjId == riskObjectId)
                .ToListAsync();

            sapaCollections.ForEach(x => x.SapaCollSelected = false);

            var collection = sapaCollections.FirstOrDefault(x => x.SapaCollId == sapaCollectionId);
            if (collection != null)
                collection.SapaCollSelected = true;

            dbContext.UpdateRange(sapaCollections);
        });
    }
}

public static class SapaExtensions
{
    public static IQueryable<Sapa> IncludeAllSapaData(this IQueryable<Sapa> query)
    {
        return query
            .Include(x => x.SapaCollection)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Risk)
            .ThenInclude(x => x.RiskObject)
            .ThenInclude(x => x.RiskObjDepartment)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Risk)
            .ThenInclude(x => x.Installation)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Risk)
            .ThenInclude(x => x.System)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Risk)
            .ThenInclude(x => x.Component)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Risk)
            .ThenInclude(x => x.Assembly)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Risk)
            .ThenInclude(x => x.MrbStatusNavigation)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Task)
            .ThenInclude(x => x.TskExecutorNavigation)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Task)
            .ThenInclude(x => x.TskInitiatorNavigation)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Task)
            .ThenInclude(x => x.TskSapaWorkpackageNavigation)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Task)
            .ThenInclude(x => x.TskIntervalUnitNavigation)
            .Include(x => x.Years)
            .ThenInclude(x => x.Details)
            .ThenInclude(x => x.Task)
            .ThenInclude(x => x.TskMxPolicyNavigation);
    }
}
