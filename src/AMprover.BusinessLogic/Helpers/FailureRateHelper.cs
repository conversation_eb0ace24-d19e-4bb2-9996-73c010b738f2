using System;
using AMprover.BusinessLogic.Enums;

namespace AMprover.BusinessLogic.Helpers;

public class FailureRateHelper(decimal mtbf, decimal weibullBeta) : DistributionHelper(mtbf, weibullBeta)
{
    private FailureRateTypes _failureRateType = FailureRateTypes.Constant;

    /// <summary>
    /// The failure rate type for the current calculation
    /// </summary>
    public FailureRateTypes FailureRateType
    {
        set => _failureRateType = value;
    }

    /// <summary>
    /// Delivers the calculated failure rate of the provided year, for the failure rate type that was set for that year.
    /// </summary>
    /// <param name="year">The year we want to know the failure rate of</param>
    /// <returns>The calculated failure rate for the provided year, calculations are made using the failure rate type 
    /// that was set for that year. If no failure rate type was set, a constant failure rate is assumed</returns>
    public decimal GetFailureRate(int year, int mtbf)
    {
        switch (_failureRateType)
        {
            case FailureRateTypes.Increasing:
            case FailureRateTypes.Dynamic when mtbf >= 3:
                return Increasing(year);
            case FailureRateTypes.Decreasing:
                return Decreasing(year);
            default:
                return Constant();
        }
    }

    /// <summary>
    /// Delivers the calculated failure rate of a specific year, for the failure rate type increasing.
    /// </summary>
    /// <param name="year">The year to calculate the failure rate for</param>
    /// <returns>The calculated failure rate</returns>
    public decimal Increasing(int year)
    {
        var result = NormalReplace(year);
        if (_mtbf == 0) return result;

        //At low MTBF values (below 3 years), results are considered to have a constant failure rate.
        if (_mtbf > 0 && _mtbf <= 3)
        {
            return Convert.ToDecimal(1 / _mtbf);
        }

        return result;
    }

    /// <summary>
    /// Delivers the calculated failure rate of a specific year, for the failure rate type decreasing.
    /// </summary>
    /// <param name="year">The year to calculate the failure rate for</param>
    /// <returns>The calculated failure rate</returns>
    public decimal Decreasing(int year) => Convert.ToDecimal(1 / _mtbf / year);

    /// <summary>
    /// Delivers the calculated failure rate, for the constant failure rate type.
    /// </summary>
    /// <returns>The calculated failure rate</returns>
    public decimal Constant() => _mtbf == 0 ? 0 : Convert.ToDecimal(1 / _mtbf);
}