using System;

namespace AMprover.BusinessLogic.Exceptions;

/// <summary>
/// Base class for all AMprover exceptions
/// </summary>
public abstract class AMproverException : Exception
{
    public AMproverException() : base()
    { }
    public AMproverException(string message) : base(message)
    { }

    public AMproverException(string message, Exception innerException) : base(message, innerException)
    { }
}