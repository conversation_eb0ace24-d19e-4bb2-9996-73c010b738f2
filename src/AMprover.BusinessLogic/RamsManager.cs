using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Models.Rams.Calculations;
using AMprover.BusinessLogic.Models.Rams.Nodes;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using AMprover.Data.Entities.AM;
using AMprover.Data.Extensions;
using AMprover.Data.Infrastructure;
using AMprover.Data.Repositories;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Buffer = AMprover.BusinessLogic.Models.Rams.Nodes.Buffer;
using Object = AMprover.Data.Entities.AM.Object;
using Parallel = AMprover.BusinessLogic.Models.Rams.Nodes.Parallel;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BusinessLogic;

public interface IRamsManager
{
    #region Diagrams

    Task<List<RamsModel>> GetRamsAsync(int id);

    Task<List<RamsModel>> GetRamsItemByParentAsync(int diagramId, int parentId);

    Task<RamsModel> GetRamsItemAsync(Guid? guid);

    Task<RamsModel> GetRamsItemAsync(int id);

    Task<RamsDiagramModel> GetRamsDiagramAsync(int id);

    Task<RiskObject> GetRiskObjectAsync(RamsDiagramModel ramsDiagramModel);

    Task<RamsDiagramModel> GenerateDiagramFromRiskObjectAsync(RamsDiagramModel ramsDiagram,
        CalculationParameters functionalCalculationParameters,
        CalculationParameters technicalCalculationParameters, RamsDisplayModel displayModel, string language);

    Task<bool> ImportFileUploadAsync(int diagramId, string language, List<RamsFlatModel> ramsItems);

    Task SwitchFunctionalObjectAsync(TreeNodeGeneric<RiskTreeObject> selectedNode,
        RamsComponentModel selectedItem,
        RamsModel node);

    Task<RamsModel> CreateOrEditRamsAsync(RamsModel model);

    Task<RamsDiagramModel> GetRamsDiagramContentAsync(int id);

    Task<RamsDiagramModel> CreateOrEditRamsDiagramAsync(RamsDiagramModel model);

    Task<RamsDiagramModel> RecalculateDiagramAsync(RamsDiagramModel model,
        CalculationParameters functionalCalculationParameters,
        CalculationParameters technicalCalculationParameters, RamsDisplayModel display, bool saveCalculations,
        string language, bool loadFromDatabase = false);

    Task DeleteRamsDiagramAsync(RamsDiagramModel diagram);

    Task DeleteRamsAsync(Guid id);

    #endregion

    #region Tree methods

    Task<TreeNodeGeneric<RamsTreeObject>> GetRamsDiagramTreeByScenarioAsync();

    #endregion
}

public class RamsManager(
    IAssetManagementBaseRepository<RamsDiagram> ramsDiagramRepository,
    IRiskAnalysisManager riskAnalysisManager,
    IAssetManagementBaseRepository<Rams> ramsRepository,
    ILogger<RamsManager> logger,
    IAssetManagementDbContextFactory dbContextFactory,
    AuthenticationStateProvider authenticationProvider,
    IMapper mapper)
    : BaseManager(dbContextFactory, authenticationProvider), IRamsManager
{
    #region Rams GET methods

    public async Task<List<RamsDiagramModel>> GetRamsDiagramsAsync()
    {
        return await ExecuteWithContextAsync(async dbContext =>
            await dbContext.RamsDiagram
                .Include(x => x.Scenario)
                .Select(x => mapper.Map<RamsDiagramModel>(x))
                .ToListAsync());
    }

    public async Task<List<RamsModel>> GetRamsAsync(int id)
    {
        return await ExecuteWithContextAsync(async dbContext =>
            await dbContext.Rams
                .Where(x => x.RamsDiagramId == id)
                .Select(x => mapper.Map<RamsModel>(x))
                .ToListAsync());
    }

    public async Task<RamsModel> GetRamsItemAsync(int id)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var rams = await dbContext.Rams.FirstOrDefaultAsync(x => x.RamsId == id);
            return mapper.Map<RamsModel>(rams);
        });
    }

    public async Task<RamsModel> GetRamsItemAsync(Guid? guid)
    {
        if (guid == null) return null;

        return await ExecuteWithContextAsync(async dbContext =>
        {
            var rams = await dbContext.Rams.FirstOrDefaultAsync(x => x.RamsNodeId == guid);
            return mapper.Map<RamsModel>(rams);
        });
    }

    public async Task<List<RamsModel>> GetRamsItemByParentAsync(int diagramId, int parentId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
            await dbContext.Rams
                .Where(x => x.RamsPartOf == parentId && x.RamsDiagramId == diagramId)
                .Select(x => mapper.Map<RamsModel>(x))
                .ToListAsync());
    }

    public async Task<RamsDiagramModel> GetRamsDiagramAsync(int id)
    {
        var ramsDiagram = await ramsDiagramRepository.GetByIdAsync(id);
        return ramsDiagram != null ? mapper.Map<RamsDiagramModel>(ramsDiagram) : null;
    }

    public async Task<RamsDiagramModel> GetRamsDiagramContentAsync(int id)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var ramsDiagram = await dbContext.RamsDiagram
                .Include(x => x.Rams)
                .FirstOrDefaultAsync(x => x.RamsDgId == id);
            return mapper.Map<RamsDiagramModel>(ramsDiagram);
        });
    }

    #endregion

    #region RAMS Delete methods

    public async Task DeleteRamsDiagramAsync(RamsDiagramModel diagram)
    {
        if (diagram == null) return;

        await ExecuteWithSaveAsync(async dbContext =>
        {
            //Remove blocks
            var ramsToRemove = await dbContext.Rams.Where(x => x.RamsDiagramId == diagram.Id).ToListAsync();
            dbContext.Rams.RemoveRange(ramsToRemove);

            //Remove diagram
            var diagramToDelete = await dbContext.RamsDiagram.FirstOrDefaultAsync(x => x.RamsDgId == diagram.Id);
            if (diagramToDelete != null)
                dbContext.Remove(diagramToDelete);
        });
    }

    public async Task DeleteRamsAsync(Guid id)
    {
        await ExecuteWithContextAsync(async dbContext =>
        {
            var ramsToDelete = await dbContext.Rams.FirstOrDefaultAsync(x => x.RamsNodeId == id);
            if (ramsToDelete != null)
                await ramsRepository.DeleteAsync(ramsToDelete, await GetUserNameAsync());
        });
    }

    #endregion

    #region Save rams helper methods

    public async Task<bool> ImportFileUploadAsync(int diagramId, string language, List<RamsFlatModel> ramsItems)
    {
        try
        {
            return await ExecuteWithSaveAsync(async dbContext =>
            {
                var ramsDbItems = await dbContext.Rams.Where(x => x.RamsDiagramId == diagramId).ToListAsync();

                foreach (var ramsItem in ramsItems)
                {
                    var ramsDbItem = ramsDbItems.Find(x => x.RamsId == ramsItem.Id);

                    if (ramsDbItem != null)
                    {
                        mapper.Map(ramsItem, ramsDbItem);
                        dbContext.Update(ramsDbItem);
                    }
                }

                var diagram = await dbContext.RamsDiagram.FirstOrDefaultAsync(x => x.RamsDgId == diagramId);

                if (diagram == null)
                    return false;

                RamsDiagramContentModel diagramContent;
                using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(diagram.RamsDgSerialized)))
                using (var reader = new StreamReader(stream))
                await using (var jsonReader = new JsonTextReader(reader))
                {
                    var serializer = JsonSerializer.Create(new JsonSerializerSettings
                    {
                        Culture = new CultureInfo(language)
                    });
                    diagramContent = serializer.Deserialize<RamsDiagramContentModel>(jsonReader);
                }

                if (diagramContent.Parts != null && diagramContent.Parts.Count != 0)
                    UpdateChildren(diagramContent.Parts, ramsItems, ramsDbItems);

                diagram.RamsDgSerialized = JsonConvert.SerializeObject(diagramContent);
                dbContext.Update(diagram);

                return true;
            });
        }
        catch (Exception e)
        {
            logger.LogError(e, "Unable to import list of rams items for {DiagramId}", diagramId);
            return false;
        }
    }

    private static void UpdateChildren(List<RamsComponentModel> parts, List<RamsFlatModel> ramsItems,
        List<Rams> ramsDbItems)
    {
        foreach (var part in parts)
        {
            var ramsId = ramsDbItems.FirstOrDefault(x => x.RamsNodeId == part.Id)?.RamsId;
            part.Title = ramsItems.FirstOrDefault(x => x.Id == ramsId)?.Name ?? part.Title;

            if (part.Parts != null && part.Parts.Count != 0)
                UpdateChildren(part.Parts, ramsItems, ramsDbItems);
        }
    }

    public async Task<RiskObject> GetRiskObjectAsync(RamsDiagramModel ramsDiagramModel)
    {
        return await ExecuteWithContextAsync(async dbContext =>
            await dbContext.RiskObject
                .Include(x => x.Risks).ThenInclude(x => x.System)
                .FirstOrDefaultAsync(x => x.RiskObjId == ramsDiagramModel.RiskObject));
    }

    /// <summary>
    /// Generate diagram based on selected risk object. The risk object is the diagram, the underlying systems are the
    /// containers and the risks are the blocks
    /// </summary>
    /// <returns></returns>
    public async Task<RamsDiagramModel> GenerateDiagramFromRiskObjectAsync(RamsDiagramModel ramsDiagramModel,
        CalculationParameters functionalCalculationParameters,
        CalculationParameters technicalCalculationParameters, RamsDisplayModel displayModel, string language)
    {
        if (ramsDiagramModel.RiskObject == null)
            return null;

        var tree = await riskAnalysisManager.GetRisksTreeNodeByRiskObject(ramsDiagramModel.RiskObject.Value);

        await using var dbContext = await dbContextFactory.CreateDbContextAsync();
        var riskObject = await dbContext.RiskObject
            .Include(x => x.Risks).ThenInclude(x => x.System)
            .Include(x => x.Risks).ThenInclude(x => x.Component)
            .Include(x => x.Risks).ThenInclude(x => x.Assembly)
            .FirstOrDefaultAsync(x => x.RiskObjId == ramsDiagramModel.RiskObject);

        if (riskObject == null) return null;

        RamsDiagramModel diagram;

        var dbDiagram = await dbContext.RamsDiagram.FirstOrDefaultAsync(x => x.RamsDgId == ramsDiagramModel.Id);

        var generatedGuid = Guid.NewGuid();
        //Add outer container to the diagram
        //Generate content model and add initial container
        var diagramContent = new RamsDiagramContentModel
        {
            ParallelTracks = 1, Parts =
            [
                new()
                {
                    Id = generatedGuid,
                    Type = RamsComponentType.Container,
                    Title = riskObject.RiskObjName,
                    Changed = DateTime.Now,
                    Dept = 1,
                    Results = new RamsReliabilityResultsModel
                    {
                        MtbfFunctional = 1.0,
                        MtbfTechnical = 1.0
                    }
                }
            ]
        };

        if (dbDiagram != null)
        {
            diagram = mapper.Map<RamsDiagramModel>(dbDiagram);
            var ramsToRemove = await dbContext.Rams.Where(x => x.RamsDiagramId == dbDiagram.RamsDgId).ToListAsync();
            dbContext.Rams.RemoveRange(ramsToRemove);

            diagram.Serialized = JsonConvert.SerializeObject(diagramContent);

            await dbContext.SaveChangesAndClearAsync(await GetUserNameAsync());
        }
        else
        {
            var settings = new JsonSerializerSettings
            {
                Converters = {new AMprover.BusinessLogic.Models.Rams.Converters.InfinityConverter()},
                Formatting = Formatting.Indented // Optional formatting for better readability
            };

            diagram = new RamsDiagramModel
            {
                Name = riskObject.RiskObjName,
                ScenId = riskObject.RiskObjScenarioId,
                Serialized = JsonConvert.SerializeObject(diagramContent, settings)
            };
            diagram = await CreateOrEditRamsDiagramAsync(diagram);
        }

        var rootContainer = new RamsModel
        {
            NodeId = generatedGuid,
            Container = true,
            Descr = riskObject.RiskObjName,
            Name = riskObject.RiskObjName,
            Mtbftechn = 1,
            Mtbffunct = 1,
            MtbfFuncCalced = 1,
            MtbfTecCalced = 1,
            LabdaFunctional = 1,
            LabdaTechnical = 1,
            WeibullShape = 1,
            CharacteristicLife = 1,
            DiagramId = diagram.Id
        };

        await CreateOrEditRamsAsync(rootContainer);

        foreach (var node in tree.Nodes)
        {
            await TraverseTreeAsync(node, diagramContent.Parts.FirstOrDefault(), diagram, rootContainer,
                tree.Nodes.IndexOf(node),
                0, riskObject);
        }

        diagramContent.Parts = await ReOrderComponentsAsync(diagramContent.Parts);
        diagram.Serialized = JsonConvert.SerializeObject(diagramContent);

        var result = await RecalculateDiagramAsync(diagram, functionalCalculationParameters,
            technicalCalculationParameters,
            displayModel, true, language);

        return result;
    }

    #region helper methods loading a diagram based on risk analysis

    private async Task TraverseTreeAsync(TreeNodeGeneric<RiskTreeObject> node, RamsComponentModel parent,
        RamsDiagramModel diagram,
        RamsModel ramsParent, int order, int dept, RiskObject riskObject)
    {
        dept++;

        if (node.Source.NodeType == RiskTreeNodeType.Risk && node.Source.RiskId.HasValue)
        {
            var risk = await ExecuteWithContextAsync(async dbContext =>
                await dbContext.Mrb.FirstOrDefaultAsync(x => x.Mrbid == node.Source.RiskId.Value));
            await GenerateRiskAsync(parent, risk, diagram, ramsParent, order, dept);
            return;
        }

        var functionalObject = await ExecuteWithContextAsync(async dbContext =>
            node.Source.NodeType switch
            {
                RiskTreeNodeType.Collection => await dbContext.Object.FirstOrDefaultAsync(x =>
                    x.ObjId == node.Source.CollectionId),
                RiskTreeNodeType.Installation =>
                    await dbContext.Object.FirstOrDefaultAsync(x => x.ObjId == node.Source.InstallationId),
                RiskTreeNodeType.System => await dbContext.Object.FirstOrDefaultAsync(x =>
                    x.ObjId == node.Source.SystemId),
                RiskTreeNodeType.Component => await dbContext.Object.FirstOrDefaultAsync(x =>
                    x.ObjId == node.Source.ComponentId),
                RiskTreeNodeType.Assembly => await dbContext.Object.FirstOrDefaultAsync(x =>
                    x.ObjId == node.Source.AssemblyId),
                _ => null
            });

        if (functionalObject == null)
            return;

        var (newParent, newContainer) =
            await GenerateContainerAsync(functionalObject, order, dept, ramsParent, parent, diagram, riskObject);

        foreach (var childNode in node.Nodes)
        {
            await TraverseTreeAsync(childNode, newParent, diagram, newContainer, node.Nodes.IndexOf(childNode), dept,
                riskObject);
        }
    }

    private async Task GenerateRiskAsync(RamsComponentModel containerPart, Mrb risk, RamsDiagramModel diagram,
        RamsModel ramsContainer, int order, int dept)
    {
        var part = new RamsComponentModel
        {
            Id = Guid.NewGuid(),
            Order = order + 1,
            GroupId = containerPart.Id,
            Title = risk.MrbName,
            ParallelTrack = 1,
            Type = RamsComponentType.Block,
            Dept = dept,
            Results = new RamsReliabilityResultsModel
            {
                MtbfFunctional = ((double?) risk.MrbMtbfAfter).IsNaNOrInfinite(1) ?? 1,
                MtbfTechnical = ((double?) risk.MrbMtbfAfter).IsNaNOrInfinite(1) ?? 1
            },
            RiskId = risk.Mrbid
        };

        var rams = new RamsModel
        {
            DiagramId = diagram.Id,
            Name = risk.MrbName,
            Descr = risk.MrbDescription,
            NodeId = part.Id,
            PartOf = ramsContainer.Id,
            LinkType = (int) RamsLinkType.LinkedToRisk,
            LinkMethod = (int) RamsLinkMethod.FmecaAfter, //Default setting
            RiskId = risk.Mrbid,
            Mtbffunct = (double?) risk.MrbMtbfAfter,
            Mtbftechn = (double?) risk.MrbMtbfAfter,
            MtbfFuncCalced = ((double?) risk.MrbMtbfAfter).IsNaNOrInfinite(1),
            MtbfTecCalced = ((double?) risk.MrbMtbfAfter).IsNaNOrInfinite(1),
        };

        containerPart.Parts.Add(part);
        await CreateOrEditRamsAsync(rams);
    }

    private async Task<(RamsComponentModel, RamsModel)> GenerateContainerAsync(Object functionalObject, int order,
        int dept,
        RamsModel parentContainer, RamsComponentModel parentContainerPart, RamsDiagramModel diagram,
        RiskObject riskObject)
    {
        var containerPart = new RamsComponentModel
        {
            Id = Guid.NewGuid(),
            Collapsed = false,
            Order = order + 1,
            Title = functionalObject.ObjName.Length > 20 ? functionalObject.ObjName[..20] : functionalObject.ObjName,
            ParallelTrack = 1,
            ParallelTracks = 1,
            Dept = dept,
            Type = RamsComponentType.Container,
            Reliability = new RamsReliabilityParametersModel
            {
                Name = functionalObject.ObjName,
                FuRefId = functionalObject.ObjId,
                RiskObjectId = riskObject.RiskObjId
            },
            Results = new RamsReliabilityResultsModel
            {
                MtbfFunctional = 1,
                MtbfTechnical = 1
            },
            GroupId = parentContainer.NodeId
        };

        var container = new RamsModel
        {
            DiagramId = diagram.Id,
            Name = functionalObject.ObjName.Length > 20 ? functionalObject.ObjName[..20] : functionalObject.ObjName,
            Descr = functionalObject.ObjDescription,
            Container = true,
            NodeId = containerPart.Id,
            LinkType = (int) RamsLinkType.LinkedToObject,
            ObjectId = functionalObject.ObjId,
            PartOf = parentContainer.Id,
            Mtbftechn = 1,
            Mtbffunct = 1,
            MtbfFuncCalced = 1,
            MtbfTecCalced = 1
        };

        parentContainerPart.Parts.Add(containerPart);
        container = await CreateOrEditRamsAsync(container);

        return (containerPart, container);
    }

    #endregion

    public async Task SwitchFunctionalObjectAsync(TreeNodeGeneric<RiskTreeObject> selectedNode,
        RamsComponentModel selectedItem,
        RamsModel node)
    {
        switch (selectedItem.Type)
        {
            case RamsComponentType.Block when node.RiskId != null:
            {
                var selectedRisk = await riskAnalysisManager.GetRiskAsync(selectedNode.Source.RiskId ?? 0);
                selectedItem.RiskId = null;

                if (selectedRisk == null)
                    return;

                selectedItem.Title = selectedRisk.Name.Length > 18 ? selectedRisk.Name[..18] : selectedRisk.Name;
                selectedItem.Reliability.Name = selectedRisk.Name;
                selectedItem.Reliability.FuRefId = selectedRisk.Id;
                selectedItem.Reliability.RiskObjectId = selectedRisk.RiskObjectId;

                selectedItem.Results.MtbfFunctional = (double) (selectedRisk.MtbfAfter ?? 0);
                selectedItem.Results.MtbfTechnical = (double) (selectedRisk.MtbfAfter ?? 0);

                selectedItem.Reliability.MTTR = (double) (selectedRisk.DownTimeAfter ?? 0m);

                selectedItem.RiskId = selectedRisk.Id;

                node.Mtbffunct = (double) (selectedRisk.MtbfAfter ?? 0);
                node.Mtbftechn = (double) (selectedRisk.MtbfAfter ?? 0);
                node.Name = selectedRisk.Name;
                node.RiskId = selectedRisk.Id;
                break;
            }
            case RamsComponentType.Container when node.ObjectId != null:
            {
                var system = await ExecuteWithContextAsync(async dbContext =>
                    await dbContext.Object.FirstOrDefaultAsync(x => x.ObjId == node.ObjectId));

                if (system == null)
                    return;

                selectedItem.Title = system.ObjName;
                selectedItem.Reliability.Name = system.ObjName;
                selectedItem.Reliability.FuRefId = system.ObjId;

                node.Name = system.ObjName;
                node.ObjectId = system.ObjId;
                break;
            }
            case RamsComponentType.PageBreak:
                // Page breaks don't have functional objects to switch
                break;
        }
    }

    public async Task<RamsDiagramModel> CreateOrEditRamsDiagramAsync(RamsDiagramModel model)
    {
        var user = await GetUserNameAsync();

        if (model.Id == 0)
        {
            var newDiagram = new RamsDiagram
            {
                RamsDgScenId = model.ScenId ?? 0,
                RamsDgName = model.Name,
                RamsDgDateModified = DateTime.Now,
                RamsDgDateInitiated = DateTime.Now,
                RamsDgCalculateAvailability = model.CalculateAvailability,
                RamsDgCalculationCompatibilityMode = model.CalculationCompatibilityMode,
                RamsDgModifiedBy = user,
                RamsDgInitiatedBy = user,
                RamsDgSerialized = model.Serialized,
                RamsDgAvailableTime = model.AvailableTime > 0 ? model.AvailableTime : 8760D,  
                RamsDgPeriodFrom = model.PeriodFrom,
                RamsDgPeriodTo = model.PeriodTo > 0 ? model.PeriodTo : 1M,
                RamsDgTestInterval = model.TestInterval,
                RamsDgHorizon = model.Horizon
            };

            var newResult = await ramsDiagramRepository.AddAsync(newDiagram, user);
            return mapper.Map<RamsDiagramModel>(newResult.Item);
        }

        model.ModifiedBy = user;
        model.DateModified = DateTime.Now;

        var dbRamsDiagram = await ramsDiagramRepository.GetByIdAsync(model.Id);

        dbRamsDiagram = mapper.Map(model, dbRamsDiagram);
        var result = await ramsDiagramRepository.UpdateAsync(dbRamsDiagram, user);

        return mapper.Map<RamsDiagramModel>(result.Item);
    }

    public async Task<RamsModel> CreateOrEditRamsAsync(RamsModel model)
    {
        var user = await GetUserNameAsync();
        Rams dbRams = null;

        model.Mtbffunct = model.Mtbffunct.IsNaNOrInfinite(1);
        model.Mtbftechn = model.Mtbftechn.IsNaNOrInfinite(1);
        model.MtbfFuncCalced = model.MtbfFuncCalced.IsNaNOrInfinite(1);
        model.MtbfTecCalced = model.MtbfTecCalced.IsNaNOrInfinite(1);
        model.ReliabilityFunctional = model.ReliabilityFunctional.IsNaNOrInfinite(1);
        model.ReliabilityTechnical = model.ReliabilityTechnical.IsNaNOrInfinite(1);
        model.Mttr = model.Mttr.IsNaNOrInfinite(1);

        if (model.Id == 0)
        {
            dbRams = await ExecuteWithContextAsync(async dbContext =>
                await dbContext.Rams.FirstOrDefaultAsync(x => x.RamsNodeId == model.NodeId));

            if (dbRams == null)
            {
                var rams = mapper.Map<Rams>(model);

                rams.RamsModifiedBy = user;
                rams.RamsInitiatedBy = user;

                rams.RamsDateInitiated = DateTime.Now;
                rams.RamsDateModified = DateTime.Now;

                var newResult = await ramsRepository.AddAsync(rams, user);
                return mapper.Map<RamsModel>(newResult.Item);
            }
        }

        model.ModifiedBy = user;
        model.DateModified = DateTime.Now;

        dbRams ??= await ramsRepository.GetByIdAsync(model.Id);

        dbRams = mapper.Map(model, dbRams);
        var result = await ramsRepository.UpdateAsync(dbRams, user);

        return mapper.Map<RamsModel>(result.Item);
    }

    #endregion

    #region Calculation helper methods

    private async Task RecalculateChildrenAsync(RamsComponentModel component, List<RamsModel> rams,
        CalculationParameters functionalCalculation, CalculationParameters technicalCalculation,
        RamsDisplayModel display, bool saveCalculations, string language, double bufferTimeNext)
    {
        //Recursive iteration through nested collections
        foreach (var part in component.Parts.OrderBy(x => x.Order))
        {
            var currentIndex = component.Parts.OrderBy(x => x.Order).ToList().IndexOf(part);
            var bufferTime = 0;

            if (component.Parts.Count > currentIndex + 1)
            {
                var next = component.Parts.OrderBy(x => x.Order).ToList()[currentIndex + 1];

                if (next is {Status: RamsBlockStatus.Buffer})
                {
                    var nextRams = rams.FirstOrDefault(x => x.NodeId == next.Id);

                    if (nextRams != null)
                    {
                        bufferTime = (int) (nextRams.BufferTime ?? 0);
                    }
                }
            }

            if (part.Type == RamsComponentType.Container)
            {
                await RecalculateChildrenAsync(part, rams, functionalCalculation, technicalCalculation, display,
                    saveCalculations,
                    language, bufferTime);
            }
            else if (part.Type == RamsComponentType.Block)
            {
                part.NodeType = RamsNodeType.SingleNode;
                await CalculateBlockAsync(part, rams, functionalCalculation, technicalCalculation, display,
                    saveCalculations,
                    bufferTime, language);
            }
            // PageBreak components are skipped in calculations as they are purely visual elements
        }

        //After doing nesting calculate container item
        if (component.Type == RamsComponentType.Container)
            await CalculateContainerAsync(component, rams, functionalCalculation, technicalCalculation, display,
                saveCalculations,
                bufferTimeNext);
    }

    private async Task CalculateBlockAsync(RamsComponentModel component, List<RamsModel> rams,
        CalculationParameters functionalCalculation, CalculationParameters technicalCalculation,
        RamsDisplayModel display, bool saveCalculations, double bufferTime, string language)
    {
        var block = rams.Find(x => x.NodeId == component.Id);

        if (block == null) return;

        //Nested diagram
        if (component.DiagramId != null)
        {
            await CalculateNestedDiagramAsync(component.DiagramId.Value, language, component, block,
                functionalCalculation,
                technicalCalculation, display);
        }
        //Process regular blocks
        else
        {
            switch (component.NodeType)
            {
                //Single node
                case RamsNodeType.SingleNode:
                    new SingleNode().Configure(component, block, rams, functionalCalculation, display, bufferTime);
                    break;
                case RamsNodeType.ParallelWithRepair:
                    new ParallelWithRepair().Configure(component, block, rams, functionalCalculation, display,
                        bufferTime);
                    break;
                case RamsNodeType.Buffer:
                    new Buffer().Configure(component, block, rams, functionalCalculation, display, bufferTime);
                    break;
                case RamsNodeType.ColdStandBy:
                    new ColdStandby().Configure(component, block, rams, functionalCalculation, display, bufferTime);
                    break;
                case RamsNodeType.Parallel:
                    new Parallel().Configure(component, block, rams, functionalCalculation, display, bufferTime);
                    break;
                case RamsNodeType.ParallelKEqualsN:
                    new ParallelKequalsN().Configure(component, block, rams, functionalCalculation, display,
                        bufferTime);
                    break;
                case RamsNodeType.Sequential:
                    new Sequential().Configure(component, block, rams, functionalCalculation, display, bufferTime);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            #region update Ramsmodel

            block.ReliabilityFunctional = component.ReliabilityInPeriodFunctionalFunction(functionalCalculation.Period);
            block.ReliabilityTechnical = component.ReliabilityInPeriodTechnicalFunction(technicalCalculation.Period);

            block.LabdaFunctional =
                component.LabdaInPeriodFunctionalFunction(functionalCalculation.Period) * HoursModel.OneYear;
            block.LabdaTechnical =
                component.LabdaInPeriodTechnicalFunction(technicalCalculation.Period) * HoursModel.OneYear;

            block.MtbfFuncCalced = component.MtbfInPeriodFunctionalFunction(functionalCalculation.Period);
            block.MtbfTecCalced = component.MtbfInPeriodTechnicalFunction(technicalCalculation.Period);

            block.AvailabilityInput = component.AvailabilityFunctionalFunction?.Invoke();
            block.AvailabilityOutput = component.AvailabilityTechnicalFunction?.Invoke();

            block.Mttr = component.MttrFunctionalFunction?.Invoke() ?? 0;

            block.CapacityFunct = component.CapacityFunctionalFunction?.Invoke();
            block.CapacityTechn = component.CapacityTechnicalFunction?.Invoke();
            block.CapacityFactor = component.CapacityFactorFunction?.Invoke();

            if (display.ShowPfd || display.ShowSil || display.ShowSilAc)
            {
                block.Pfd = component.PfdFunction?.Invoke();
                block.Hft = component.Hft?.Invoke();
                block.Sil = component.SilAvg?.Invoke().ToString();
            }

            #endregion
        }

        #region Update RamsComponentmodel

        component.Results.LabdaFunctional = block.LabdaFunctional ?? 1;
        component.Results.LabdaTechnical = block.LabdaTechnical ?? 1;

        component.Results.CapacityFunctional = block.CapacityFunct ?? 0;
        component.Results.CapacityTechnical = block.CapacityTechn ?? 0;
        component.Results.CapacityUnit = block.CapacityUnit;

        if (component.Status == RamsBlockStatus.NotInstalled)
        {
            component.Results.MtbfFunctional = component.Results.MtbfTechnical = 10000000;
            component.Results.ReliabilityFunctional = component.Results.ReliabilityTechnical = 1;
            component.Results.AvailFunctional = component.Results.AvailTechnical = 1;
            component.Results.CapacityFunctional = component.Results.CapacityTechnical = 0;
        }
        else
        {
            block.MtbfFuncCalced = block.MtbfFuncCalced.IsNaNOrInfinite(0);
            block.MtbfTecCalced = block.MtbfTecCalced.IsNaNOrInfinite(0);

            component.Reliability.MtbfFunctional = block.MtbfFuncCalced is > 0 ? block.MtbfFuncCalced.Value / 8760D : 1;
            component.Reliability.MtbfTechnical = block.MtbfTecCalced is > 0 ? block.MtbfTecCalced.Value / 8760D : 1;

            //Update risk when RAMS is changed
            if (block.RiskId.HasValue && block.LinkMethod != (int) RamsLinkMethod.NotLinked &&
                block.LinkType == (int) RamsLinkType.LinkedToRams)
            {
                var risk = await riskAnalysisManager.GetRiskAsync(block.RiskId.Value);

                if (risk != null)
                {
                    switch (block.LinkMethod)
                    {
                        case (int) RamsLinkMethod.FmecaAfter:
                            risk.MtbfAfter = (decimal?) block.Mtbffunct;
                            risk.MainDataGrid.CustomMtbfAfter = risk.MtbfAfter?.ToString(Strings.CustomMtbfFormat,
                                CultureInfo.InvariantCulture);
                            break;
                        case (int) RamsLinkMethod.FmecaBefore:
                            risk.MtbfBefore = (decimal?) block.Mtbffunct;
                            risk.MainDataGrid.CustomMtbfBefore = risk.MtbfBefore?.ToString(Strings.CustomMtbfFormat,
                                CultureInfo.InvariantCulture);
                            break;
                    }

                    await riskAnalysisManager.UpdateRiskAsync(risk);
                }
            }

            component.Results.MtbfFunctional = block.Mtbffunct is > 0 ? block.Mtbffunct.Value : 1;
            component.Results.MtbfTechnical = block.Mtbftechn is > 0 ? block.Mtbftechn.Value : 1;

            component.Results.ReliabilityFunctional = block.ReliabilityFunctional is > 0
                ? block.ReliabilityFunctional.Value
                : 0;
            component.Results.ReliabilityTechnical = block.ReliabilityTechnical is > 0
                ? block.ReliabilityTechnical.Value
                : 0;

            component.Results.AvailFunctional =
                block.AvailabilityInput is > 0 ? block.AvailabilityInput.Value : 0;
            component.Results.AvailTechnical =
                block.AvailabilityOutput is > 0 ? block.AvailabilityOutput.Value : 0;
        }

        component.Results.Mttr = block.Mttr ?? 0;
        component.Results.AffectedBufferTime = bufferTime;

        component.Results.PFD = block.Pfd ?? 0;
        component.Results.HFT = block.Hft ?? 0;
        component.Results.RamsSil = block.Sil;
        component.Results.RamsSilAC = block.SilAc;

        component.Reliability.MTTR = block.Mttr ?? 0;

        #endregion

        if (saveCalculations)
            await CreateOrEditRamsAsync(block);
    }

    private async Task CalculateNestedDiagramAsync(int diagramId, string language, RamsComponentModel component,
        RamsModel block,
        CalculationParameters functionalCalculation, CalculationParameters technicalCalculation,
        RamsDisplayModel display)
    {
        var diagram = await GetRamsDiagramAsync(diagramId);
        RamsDiagramContentModel diagramContent;

        using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(diagram.Serialized)))
        using (var reader = new StreamReader(stream))
        await using (var jsonReader = new JsonTextReader(reader))
        {
            var serializer = JsonSerializer.Create(new JsonSerializerSettings
            {
                Culture = new CultureInfo(language)
            });
            diagramContent = serializer.Deserialize<RamsDiagramContentModel>(jsonReader);
        }

        var rootNode = diagramContent.Parts[0];

        block.Mtbftechn = rootNode.Results.MtbfTechnical;
        block.Mtbffunct = rootNode.Results.MtbfFunctional;
        block.Mttr = rootNode.Results.MttrFunctional;
        block.LabdaFunctional = rootNode.Results.LabdaFunctional;
        block.LabdaTechnical = rootNode.Results.LabdaTechnical;
        block.ReliabilityFunctional = rootNode.Results.ReliabilityFunctional;
        block.ReliabilityTechnical = rootNode.Results.ReliabilityTechnical;
        block.AvailabilityInput = rootNode.Results.AvailFunctional;
        block.AvailabilityOutput = rootNode.Results.AvailTechnical;
        block.MtbfFuncCalced = rootNode.MtbfInPeriodFunctionalFunction(functionalCalculation.Period);
        block.MtbfTecCalced = rootNode.MtbfInPeriodTechnicalFunction(technicalCalculation.Period);
        block.Pfd = rootNode.PfdFunction?.Invoke();
        block.Hft = rootNode.Hft?.Invoke();
        block.Sil = rootNode.SilAvg?.Invoke().ToString();
        component.Results = rootNode.Results;
    }

    private async Task CalculateContainerAsync(RamsComponentModel component, List<RamsModel> rams,
        CalculationParameters functionalCalculation, CalculationParameters technicalCalculation,
        RamsDisplayModel display, bool saveCalculations, double bufferTime)
    {
        var container = rams.FirstOrDefault(x => x.NodeId == component.Id);
        if (container == null || component.Results == null) return;

        //Recalculate parallel track
        component.ParallelTracks = component.Parts
            .DistinctBy(x => x.ParallelTrack)
            .Count();

        if (component.XooN > component.ParallelTracks)
            component.XooN = component.ParallelTracks;

        switch (component.NodeType)
        {
            case RamsNodeType.Buffer:
            {
                var buffer = new Buffer();
                buffer.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
            //Cold standby
            case RamsNodeType.ColdStandBy:
            {
                var coldStandby = new ColdStandby();
                coldStandby.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
            //ParallelWithRepair
            case RamsNodeType.ParallelWithRepair:
            {
                var parallel = new ParallelWithRepair();
                parallel.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
            //Parallel
            case RamsNodeType.Parallel:
            {
                var parallel = new Parallel();
                parallel.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
            //ParallelKEqualsN
            case RamsNodeType.ParallelKEqualsN:
            {
                var parallel = new ParallelKequalsN();
                parallel.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
            //Sequential
            case RamsNodeType.Sequential:
            {
                var sequential = new Sequential();
                sequential.Configure(component, container, rams, functionalCalculation, display, bufferTime);
                break;
            }
        }

        #region Update container

        container.XooN = component.XooN;

        container.ReliabilityFunctional = component.ReliabilityInPeriodFunctionalFunction(technicalCalculation.Period);
        container.ReliabilityTechnical = component.ReliabilityInPeriodTechnicalFunction(technicalCalculation.Period);

        container.LabdaFunctional = component.LabdaInPeriodFunctionalFunction(functionalCalculation.Period) *
                                    HoursModel.OneYear;
        container.LabdaTechnical =
            component.LabdaInPeriodTechnicalFunction(functionalCalculation.Period) * HoursModel.OneYear;

        container.MtbfFuncCalced = component.MtbfInPeriodFunctionalFunction(functionalCalculation.Period);
        container.MtbfTecCalced = component.MtbfInPeriodTechnicalFunction(technicalCalculation.Period);

        container.Mttr = component.MttrTechnicalFunction?.Invoke() ?? component.MttrFunctionalFunction?.Invoke();

        container.AvailabilityInput = component.AvailabilityFunctionalFunction?.Invoke();
        container.AvailabilityOutput = component.AvailabilityTechnicalFunction?.Invoke();

        // Update capacity values
        container.CapacityFunct = component.CapacityFunctionalFunction?.Invoke();
        container.CapacityTechn = component.CapacityTechnicalFunction?.Invoke();
        container.CapacityUnit = rams.FirstOrDefault(x => x.PartOf == container.Id && x.CapacityUnit != null)?.CapacityUnit;
        container.CapacityFactor = component.CapacityFactorFunction?.Invoke();

        container.Pfd = component.PfdFunction?.Invoke();
        container.Hft = component.Hft?.Invoke();
        container.Sil = component.SilAvg?.Invoke().ToString();
        container.Sff = (decimal?) component.SffFunction?.Invoke();
        container.SilAc = component.SilAcFunction?.Invoke().ToString();

        #endregion

        #region Update component

        component.Results.AvailFunctional = container.AvailabilityInput is > 0 ? container.AvailabilityInput.Value : 0;
        component.Results.AvailTechnical = container.AvailabilityOutput is > 0 ? container.AvailabilityOutput.Value : 0;

        component.Results.LabdaFunctional = container.LabdaFunctional ?? 0;
        component.Results.LabdaTechnical = container.LabdaTechnical ?? 0;

        // Update capacity results
        component.Results.CapacityFunctional = container.CapacityFunct ?? 0;
        component.Results.CapacityTechnical = container.CapacityTechn ?? 0;
        component.Results.CapacityUnit =
            rams.FirstOrDefault(x => x.PartOf == container.Id && x.CapacityUnit != null)?.CapacityUnit;

        //If infinite just use the infinite value, do not do calculations
        if (container.MtbfFuncCalced == 10000000 || component.Parts.All(x =>
                x.Status == RamsBlockStatus.NotInstalled || x.Results.MtbfFunctional == 10000000))
        {
            component.Results.MtbfTechnical = component.Results.MtbfFunctional = 10000000;
            component.Results.CapacityFunctional = component.Results.CapacityTechnical = 0;
        }
        else
        {
            component.Results.MtbfTechnical = container.MtbfTecCalced / 8760D ?? 1;
            component.Results.MtbfFunctional = container.MtbfFuncCalced / 8760D ?? 1;
        }

        component.Results.ReliabilityFunctional =
            container.ReliabilityFunctional is > 0 ? container.ReliabilityFunctional.Value : 1;
        component.Results.ReliabilityTechnical =
            container.ReliabilityTechnical is > 0 ? container.ReliabilityTechnical.Value : 1;

        component.Results.PFD = container.Pfd ?? 0;
        component.Results.MttrFunctional = component.MttrFunctionalFunction?.Invoke() ?? 0;
        component.Results.MttrTechnical = component.MttrTechnicalFunction?.Invoke() ?? 0;

        component.Reliability.MTTR = component.MttrTechnicalFunction?.Invoke() ?? 0;
        component.Results.AffectedBufferTime = bufferTime;

        #endregion

        if (saveCalculations)
            await CreateOrEditRamsAsync(container);
    }

    private static async Task<List<RamsComponentModel>> ReOrderComponentsAsync(List<RamsComponentModel> components,
        int dept = 0)
    {
        foreach (var component in components.OrderBy(x => x.Order).ThenByDescending(x => x.Changed))
        {
            //Reset dept
            component.Dept = dept + 1;
            //If duplicate orders exist move up a column
            var duplicateOrderItems = components
                .Where(x => x.Order == component.Order && x.ParallelTrack == component.ParallelTrack).ToList();

            //But only the oldest
            if (duplicateOrderItems.Count != 0 && duplicateOrderItems.Count > 1 &&
                component == duplicateOrderItems.MinBy(x => x.Changed))
            {
                component.Order++;
                component.Changed = DateTime.Now;
            }

            //Check if no empty columns in between
            var i = 1;
            if (component.Order > 0)
                while (components.Where(x => x.ParallelTrack == component.ParallelTrack).OrderBy(x => x.Order)
                           .All(x => x.Order != component.Order - 1 && component.Order - 1 > 0) &&
                       i < components.MaxBy(x => x.Order).Order)
                {
                    i++;
                    component.Order--;
                }

            if (component.Type == RamsComponentType.Container)
            {
                await ReOrderComponentsAsync(component.Parts, component.Dept);
            }

            component.Connectors.Clear();

            //Connection to previous block
            if (component.Order > 1)
            {
                //Get previous blocks
                var previousBlocks = components
                    .Where(x => x.Order == component.Order - 1 && x.ParallelTrack == component.ParallelTrack)
                    .OrderBy(x => x.ParallelTrack).ToList();

                if (previousBlocks.Count != 0)
                {
                    foreach (var previousBlock in previousBlocks.Where(previousBlock =>
                                 previousBlock != null && previousBlock.Id != component.Id))
                    {
                        component.Connectors.Add(new RamsComponentConnectorModel
                        {
                            Destination = previousBlock.Id,
                            TypeDestination = previousBlock.Type,
                            LocationDestination = RamsComponentConnectorLocation.right,
                            LocationSource = RamsComponentConnectorLocation.left
                        });
                    }
                }

                //connect from container to last block items
                if (component.Type == RamsComponentType.Container)
                {
                    var lastBlocks = component.Parts.GroupBy(x => x.ParallelTrack).Select(x => x.MaxBy(y => y.Order));

                    foreach (var previousBlock in lastBlocks.Where(x => x.Id != component.Id))
                    {
                        component.Connectors.Add(new RamsComponentConnectorModel
                        {
                            Destination = previousBlock.Id,
                            TypeDestination = previousBlock.Type,
                            LocationDestination = RamsComponentConnectorLocation.right,
                            LocationSource = RamsComponentConnectorLocation.right
                        });
                    }
                }
            }
            //connection to parent
            else if (component.Type == RamsComponentType.Block && component.GroupId != null &&
                     component.Id != component.GroupId)
            {
                component.Connectors.Add(new RamsComponentConnectorModel
                {
                    Destination = component.GroupId.Value,
                    TypeDestination = RamsComponentType.Container,
                    LocationDestination = component.Order == 1
                        ? RamsComponentConnectorLocation.left
                        : RamsComponentConnectorLocation.right,
                    LocationSource = component.Order == 1
                        ? RamsComponentConnectorLocation.left
                        : RamsComponentConnectorLocation.right
                });
            }
            else if (component.Type == RamsComponentType.Container)
            {
                //Connection to previous block inside
                if (component.Parts.Count != 0)
                    component.Connectors.AddRange(component.Parts.GroupBy(x => x.ParallelTrack)
                        .Select(x => x.MaxBy(y => y.Order))
                        .Select(x =>
                            new RamsComponentConnectorModel
                            {
                                Destination = x.Id,
                                TypeDestination = RamsComponentType.Block,
                                LocationDestination = RamsComponentConnectorLocation.right,
                                LocationSource = RamsComponentConnectorLocation.right
                            }).ToList());

                //If nested group connect to group parent
                if (component.GroupId != null)
                {
                    component.Connectors.Add(new RamsComponentConnectorModel
                    {
                        Destination = component.GroupId.Value,
                        LocationDestination = RamsComponentConnectorLocation.left,
                        LocationSource = RamsComponentConnectorLocation.left,
                        TypeDestination = RamsComponentType.Container
                    });
                }
            }
        }

        return components;
    }

    public async Task<RamsDiagramModel> RecalculateDiagramAsync(RamsDiagramModel model,
        CalculationParameters functionalCalculation,
        CalculationParameters technicalCalculation,
        RamsDisplayModel display, bool saveCalculations, string language, bool loadFromDatabase = false)
    {
        //Get diagram content
        var content = new RamsDiagramContentModel();

        try
        {
            content = model.Serialized == null
                ? new RamsDiagramContentModel()
                : JsonConvert.DeserializeObject<RamsDiagramContentModel>(model.Serialized);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Deserialization of diagram failed");
        }

        //Get all rams
        var rams = loadFromDatabase ? (await GetRamsAsync(model.Id)).ToList() : model.Rams;

        for (var index = 0; index < content.Parts.Count; index++)
        {
            var part = content.Parts[index];

            var currentIndex = content.Parts.IndexOf(part);
            var bufferTime = 0;

            if (content.Parts.Count > currentIndex + 1)
            {
                var next = content.Parts[currentIndex + 1];

                if (next is {Status: RamsBlockStatus.Buffer})
                {
                    var nextRams = rams.FirstOrDefault(x => x.NodeId == next.Id);

                    if (nextRams != null)
                    {
                        bufferTime = (int) (nextRams.BufferTime ?? 0);
                    }
                }
            }

            if (part.Type == RamsComponentType.Container)
            {
                await DetermineContainerNodeType(part);

                //If container first process the children
                await RecalculateChildrenAsync(part, rams, functionalCalculation, technicalCalculation, display,
                    saveCalculations,
                    language, bufferTime);
            }
            else if (part.Type == RamsComponentType.Block)
            {
                await CalculateBlockAsync(part, rams, functionalCalculation, technicalCalculation, display,
                    saveCalculations,
                    bufferTime, language);
            }
            // PageBreak components are skipped in calculations as they are purely visual elements
        }

        // Update realization capacity values if we have content parts
        if (content.Parts is {Count: > 0})
        {
            var rootNode = content.Parts[0];

            if (rootNode.Results != null)
            {
                model.RealizationCapacity = rootNode.Results.CapacityTechnical;
                model.RealizationCapacityUnit = rootNode.Results.CapacityUnit;
            }
        }

        model.Serialized = JsonConvert.SerializeObject(content);

        if (saveCalculations)
            await CreateOrEditRamsDiagramAsync(model);

        //Update nested diagrams
        await UpdateNestedDiagramsAsync(model.Id, language);

        return model;
    }

    private async Task UpdateNestedDiagramsAsync(int diagramId, string language)
    {
        var nestedDiagrams = await ExecuteWithContextAsync(async dbContext =>
            //Get diagrams that are being used within diagrams
            await dbContext.Rams.Where(x => x.RamsDiagramRefId == diagramId).ToListAsync());

        if (nestedDiagrams.Count == 0) return;

        var diagram = await GetRamsDiagramAsync(diagramId);
        var diagramContent = JsonConvert.DeserializeObject<RamsDiagramContentModel>(diagram.Serialized,
            new JsonSerializerSettings
            {
                Culture = new CultureInfo(language)
            });

        if (diagramContent?.Parts != null && diagramContent.Parts.Count != 0)
        {
            var rootNode = diagramContent.Parts[0];

            foreach (var nestedDiagram in nestedDiagrams)
            {
                nestedDiagram.RamsMtbftechn = rootNode.Results.MtbfTechnical;
                nestedDiagram.RamsMtbffunct = rootNode.Results.MtbfFunctional;
                nestedDiagram.RamsLabdaFunctional = rootNode.Results.LabdaFunctional;
                nestedDiagram.RamsLabdaTechnical = rootNode.Results.LabdaTechnical;

                // Update capacity values
                nestedDiagram.RamsCapacityTechn = rootNode.Results.CapacityTechnical;
                nestedDiagram.RamsCapacityFunct = rootNode.Results.CapacityFunctional;
                nestedDiagram.RamsCapacityUnit = rootNode.Results.CapacityUnit;

                await CreateOrEditRamsAsync(mapper.Map<RamsModel>(nestedDiagram));
            }
        }
    }

    private async Task DetermineContainerNodeType(RamsComponentModel part)
    {
        var container = await GetRamsItemAsync(part.Id);

        if (container == null) return;

        part.XooN = container.XooN;

        container.N = () => part.N;

        //Buffer
        if (RamsNodeHelper.IsBuffered(part))
        {
            part.NodeType = RamsNodeType.Buffer;
        }
        //Cold standby
        else if (RamsNodeHelper.IsColdStandby(part))
        {
            part.NodeType = RamsNodeType.ColdStandBy;
        }
        //ParallelWithRepair
        else if (RamsNodeHelper.IsParallel(part.ParallelTracks ?? 1) && !RamsNodeHelper.IsColdStandby(part) &&
                 RamsNodeHelper.IsKooNWithRepair(part, container) && container.K() != container.N())
        {
            part.NodeType = RamsNodeType.ParallelWithRepair;
        }
        //ParallelKEqualsN
        else if (RamsNodeHelper.IsParallel(part.ParallelTracks ?? 1) &&
                 !RamsNodeHelper.IsColdStandby(part) && container.K() == container.N())
        {
            part.NodeType = RamsNodeType.ParallelKEqualsN;
        }
        //Parallel
        else if (RamsNodeHelper.IsParallel(part.ParallelTracks ?? 1) &&
                 !RamsNodeHelper.IsColdStandby(part) && !RamsNodeHelper.IsKooNWithRepair(part, container) &&
                 container.K() != container.N())
        {
            part.NodeType = RamsNodeType.Parallel;
        }
        //Sequential
        else if (!RamsNodeHelper.IsBuffered(part) &&
                 RamsNodeHelper.IsSequential(part.ParallelTracks ?? 1))
        {
            part.NodeType = RamsNodeType.Sequential;
        }

        for (var index = 0; index < part.Parts.Count; index++)
        {
            var childPart = part.Parts[index];
            if (childPart.Type == RamsComponentType.Container)
            {
                await DetermineContainerNodeType(childPart);
            }
        }
    }

    #endregion

    #region Tree methods

    public async Task<TreeNodeGeneric<RamsTreeObject>> GetRamsDiagramTreeByScenarioAsync()
    {
        var diagrams = await GetRamsDiagramsAsync();
        var diagramGroups = diagrams.GroupBy(x => x.Scenario.Name);

        var rootNode = GetRamsTreeNode("Root node", RamsTreeNodeType.Root);
        return diagramGroups.OrderBy(x => x.Key).Aggregate(rootNode,
            (current, scenarioGroup) => AddRamsDiagramToTree(current, scenarioGroup));
    }

    private static TreeNodeGeneric<RamsTreeObject> AddRamsDiagramToTree(TreeNodeGeneric<RamsTreeObject> tree,
        IGrouping<string, RamsDiagramModel> scenarioGroup)
    {
        var node = tree;
        var scenario = scenarioGroup.FirstOrDefault();

        var scenarioNode = new TreeNodeGeneric<RamsTreeObject>(
                new RamsTreeObject
                    {Name = scenario?.Name, NodeType = RamsTreeNodeType.Scenario, ScenarioId = scenario?.Id ?? 0},
                node)
            {Name = scenarioGroup.Key, Icon = RamsTreeNodeType.Scenario.ToIcon()};

        foreach (var diagrams in scenarioGroup.OrderBy(x => x.Name))
        {
            scenarioNode.Nodes.Add(new TreeNodeGeneric<RamsTreeObject>(
                new RamsTreeObject
                {
                    Name = diagrams.Name,
                    NodeType = RamsTreeNodeType.Diagram,
                    Id = diagrams.Id,
                    ScenarioId = diagrams.ScenId ?? 0
                }, scenarioNode)
            {
                Name = diagrams.Name,
                Icon = RamsTreeNodeType.Diagram.ToIcon(),
                Id = diagrams.Id
            });
        }

        node.Nodes.Add(scenarioNode);
        return node;
    }

    private static TreeNodeGeneric<RamsTreeObject> GetRamsTreeNode(string name, RamsTreeNodeType type,
        TreeNodeGeneric<RamsTreeObject> parent = null)
    {
        return new TreeNodeGeneric<RamsTreeObject>(
            new RamsTreeObject
            {
                Name = name,
                NodeType = type
            },
            parent)
        {
            Icon = type.ToIcon()
        };
    }

    #endregion
}
