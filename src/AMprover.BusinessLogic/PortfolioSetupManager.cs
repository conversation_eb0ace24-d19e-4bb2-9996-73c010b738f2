using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.Models.Import;
using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using AMprover.Data.Repositories;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BusinessLogic;

public interface IPortfolioSetupManager
{
    #region Common actions/Tasks

    Task<List<CommonTaskModel>> GetCommonActionsAsync();

    Task<List<CommonActionImportModel>> GetCommonActionImportModelsAsync();

    Task<CommonTaskModel> GetCommonActionAsync(int commonActionId);

    Task<CommonTaskModel> UpdateCommonActionAsync(CommonTaskModel commonTaskModel);

    Task<List<int>> GetTaskIdsUsingCommonActionAsync(int commonActionId);

    Task DeleteCommonActionAsync(CommonTaskModel commonTaskModel);

    #endregion

    #region Common Costs

    Task<List<CommonCostModel>> GetCommonCostsAsync();

    Task<CommonCostModel> GetCommonCostAsync(int commonCostId);

    Task<CommonCostModel> UpdateCommonCostAsync(CommonCostModel commonCostModel);

    Task DeleteCommonCostAsync(CommonCostModel commonCostModel);

    Task<Dictionary<CommonCostType, string>> GetCostLevelTypesAsync();

    #endregion
}

public class PortfolioSetupManager(
    IAssetManagementDbContextFactory dbContextFactory,
    AuthenticationStateProvider authenticationStateProvider,
    IAssetManagementBaseRepository<CommonTask> commonTaskRepository,
    IAssetManagementBaseRepository<CommonCost> commonCostRepository,
    IMapper mapper)
    : BaseManager(dbContextFactory, authenticationStateProvider), IPortfolioSetupManager
{

    #region Common actions/Tasks

    public async Task<List<CommonTaskModel>> GetCommonActionsAsync()
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var result = await dbContext.CommonTask
                .AsNoTracking() // Add AsNoTracking for better performance since this is a read-only operation
                .Include(x => x.CmnTaskMxPolicyNavigation)
                .Include(x => x.CmnTaskExecutorNavigation)
                .Include(x => x.CmnTaskInitiatorNavigation)
                .Include(x => x.CmnTaskWorkPackageNavigation)
                .Include(x => x.CmnTaskIntervalUnitNavigation)
                .ToListAsync();

            return mapper.Map<List<CommonTaskModel>>(result);
        });
    }

    public async Task<List<CommonActionImportModel>> GetCommonActionImportModelsAsync()
    {
        var commonActionsTask = GetCommonActionsForImportAsync();
        var unitTypesTask = GetUnitTypesAsync();

        await Task.WhenAll(commonActionsTask, unitTypesTask);

        var commonActions = await commonActionsTask;
        var unitTypes = await unitTypesTask;

        var result = mapper.Map<List<CommonActionImportModel>>(commonActions);

        result.ForEach(x => x.UnitType = unitTypes.FirstOrDefault(y => y.UserDefinedId == int.Parse(x.UnitType))?.UserDefinedShortDescription ?? "N/A");

        return result;

        // Create separate tasks for parallel execution
        async Task<List<CommonTask>> GetCommonActionsForImportAsync()
        {
            return await ExecuteWithContextAsync(async dbContext =>
            {
                return await dbContext.CommonTask
                    .AsNoTracking() // Add AsNoTracking for better performance since this is a read-only operation
                    .Include(x => x.CmnTaskMxPolicyNavigation)
                    .Include(x => x.CmnTaskExecutorNavigation)
                    .Include(x => x.CmnTaskInitiatorNavigation)
                    .Include(x => x.CmnTaskWorkPackageNavigation)
                    .Include(x => x.CmnTaskIntervalUnitNavigation)
                    .ToListAsync();
            });
        }

        async Task<List<LookupUserDefined>> GetUnitTypesAsync()
        {
            return await ExecuteWithContextAsync(async dbContext =>
            {
                return await dbContext.LookupUserDefined
                    .AsNoTracking() // Add AsNoTracking for better performance since this is a read-only operation
                    .Where(x => x.UserDefinedFilter == "UnitTypes")
                    .OrderBy(x => x.UserDefinedShortDescription)
                    .ToListAsync();
            });
        }
    }

    public async Task<CommonTaskModel> GetCommonActionAsync(int commonActionId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var commonAction = await dbContext.CommonTask
                .AsNoTracking() // Add AsNoTracking for better performance since this is a read-only operation
                .Include(x => x.CmnTaskMxPolicyNavigation)
                .Include(x => x.CmnTaskExecutorNavigation)
                .Include(x => x.CmnTaskInitiatorNavigation)
                .Include(x => x.CmnTaskWorkPackageNavigation)
                .Include(x => x.CmnTaskIntervalUnitNavigation)
                .FirstOrDefaultAsync(x => x.CmnTaskId == commonActionId);

            return mapper.Map<CommonTaskModel>(commonAction);
        });
    }

    public async Task<CommonTaskModel> UpdateCommonActionAsync(CommonTaskModel commonTaskModel)
    {
        //Fix to limit all string fields to maxlength
        commonTaskModel.ShortenStringFields();

        if (commonTaskModel.Id == 0)
        {
            var result = await commonTaskRepository.AddAsync(mapper.Map<CommonTask>(commonTaskModel), await GetUserNameAsync());
            return await GetCommonActionAsync(result.Item.CmnTaskId);
        }

        return await ExecuteWithSaveAsync(
            async dbContext =>
            {
                var dbCommonTask = await dbContext.CommonTask.FindAsync(commonTaskModel.Id);
                dbCommonTask = mapper.Map(commonTaskModel, dbCommonTask);
                if (dbCommonTask == null) return null;

                var dbResult = dbContext.CommonTask.Update(dbCommonTask);
                return mapper.Map<CommonTaskModel>(dbResult.Entity);
            },
            async (context, result) =>
            {
                if (result != null)
                {
                    await UpdateTasksDerivedFromCommonActionAsync(result);
                }
                return await GetCommonActionAsync(result.Id);
            });
    }

    private async Task UpdateTasksDerivedFromCommonActionAsync(CommonTaskModel commonTaskModel)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            var tasks = await dbContext.Task.Where(x => x.TskCommonActionId == commonTaskModel.Id).ToListAsync();

            if (tasks.Count == 0)
                return;

            foreach (var task in tasks)
            {
                if (commonTaskModel.CostModifiable != true)
                    task.TskEstCosts = commonTaskModel.Costs;

                if (commonTaskModel.InitiatorModifiable != true)
                    task.TskInitiator = commonTaskModel.InitiatorId;

                if (commonTaskModel.ExecutorModifiable != true)
                    task.TskExecutor = commonTaskModel.ExecutorId;

                if (commonTaskModel.WorkPackageModifiable != true)
                    task.TskWorkpackage = commonTaskModel.WorkPackageId;

                if (commonTaskModel.IntervalModifiable != true)
                    task.TskIntervalUnit = commonTaskModel.IntervalUnitId;

                dbContext.Task.Update(task);
            }
        });
    }

    public async Task<List<int>> GetTaskIdsUsingCommonActionAsync(int commonActionId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            return await dbContext.Task
                .AsNoTracking() // Add AsNoTracking for better performance since this is a read-only operation
                .Where(x => x.TskCommonActionId == commonActionId)
                .Select(x => x.TskId)
                .ToListAsync();
        });
    }

    public async Task DeleteCommonActionAsync(CommonTaskModel commonTaskModel)
    {
        if (commonTaskModel?.Id is null or 0) return;

        await ExecuteWithSaveAsync(async dbContext =>
        {
            var dbCommonTask = await dbContext.CommonTask.FindAsync(commonTaskModel.Id);

            if (dbCommonTask == null) return;

            dbContext.CommonTask.Remove(dbCommonTask);
        });
    }

    #endregion

    #region Common Costs

    public async Task<Dictionary<CommonCostType, string>> GetCostLevelTypesAsync()
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var values = await dbContext.LookupUserDefined
                .AsNoTracking() // Add AsNoTracking for better performance since this is a read-only operation
                .Where(x => x.UserDefinedFilter == "ClustCostTypeName")
                .OrderBy(x => x.UserDefinedValue)
                .ToListAsync();

            var result = new Dictionary<CommonCostType, string>();

            for (int i = 0; i < 4; i++)
            {
                var key = (CommonCostType) i;
                var value = values.FirstOrDefault(x => x.UserDefinedValue == i)?.UserDefinedLongDescription;
                if (string.IsNullOrWhiteSpace(value))
                    value = key.ToString();

                result.Add(key, value.Trim());
            }

            return result;
        });
    }

    public async Task<List<CommonCostModel>> GetCommonCostsAsync()
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            return await dbContext.CommonCost
                .AsNoTracking() // Add AsNoTracking for better performance since this is a read-only operation
                .Select(x => mapper.Map<CommonCostModel>(x))
                .ToListAsync();
        });
    }

    public async Task<CommonCostModel> GetCommonCostAsync(int commonCostId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var commonCost = await dbContext.CommonCost
                .AsNoTracking() // Add AsNoTracking for better performance since this is a read-only operation
                .FirstOrDefaultAsync(x => x.CmnCostId == commonCostId);
            return mapper.Map<CommonCostModel>(commonCost);
        });
    }

    public async Task<CommonCostModel> UpdateCommonCostAsync(CommonCostModel commonCostModel)
    {
        if (commonCostModel.Id == 0)
        {
            var result = await commonCostRepository.AddAsync(mapper.Map<CommonCost>(commonCostModel), await GetUserNameAsync());
            return mapper.Map<CommonCostModel>(result.Item);
        }
        else
        {
            return await ExecuteWithContextAsync(async dbContext =>
            {
                var dbCommonCost = await dbContext.CommonCost.FindAsync(commonCostModel.Id);
                dbCommonCost = mapper.Map(commonCostModel, dbCommonCost);
                if (dbCommonCost == null) return null;

                var result = await commonCostRepository.UpdateAsync(dbCommonCost, await GetUserNameAsync());
                return mapper.Map<CommonCostModel>(result.Item);
            });
        }
    }

    public async Task DeleteCommonCostAsync(CommonCostModel commonCostModel)
    {
        if (commonCostModel?.Id is null or 0) return;

        await ExecuteWithSaveAsync(async dbContext =>
        {
            var dbCommonCost = await dbContext.CommonCost.FindAsync(commonCostModel.Id);

            if (dbCommonCost == null) return;

            dbContext.CommonCost.Remove(dbCommonCost);
        });
    }

    #endregion
}
