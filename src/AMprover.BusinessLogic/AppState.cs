using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using static AMprover.Data.Constants.AMproverIdentityConstants;

namespace AMprover.BusinessLogic;

public class AppState(
    ILookupManager lookupManager,
    IAMproverUserManager userManager) : INotifyPropertyChanged
{
    ILookupManager LookupManager { get; } = lookupManager;
    IAMproverUserManager UserManager { get; } = userManager;

    public async Task InitializeAsync()
    {
        Currency = await LookupManager.GetCurrencyAsync();
        Language = await LookupManager.GetLanguageAsync();
        UserRole = await UserManager.GetUserRole();
    }

    /// <summary>
    /// UserRole
    /// </summary>
    private Role? _userRole;
    public Role? UserRole
    {
        get => _userRole;
        set
        {
            _userRole = value;
            Initialized = true;
            OnPropertyChanged();
        }
    }

    public bool CanEdit => _userRole is Role.Administrators or Role.Users;

    public bool Initialized { get; private set; }

    /// <summary>
    /// SiteMenuOpen
    /// </summary>
    private bool _siteMenuOpen { get; set; }
    public bool SiteMenuOpen
    {
        get => _siteMenuOpen;
        set
        {
            if (_siteMenuOpen != value)
            {
                _siteMenuOpen = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// Currency
    /// </summary>
    private string _currency;
    public string Currency
    {
        get => _currency;
        set
        {
            if (_currency != value)
            {
                _currency = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// Language
    /// </summary>
    private string _language;
    public string Language
    {
        get => _language;
        set
        {
            if (_language != value)
            {
                _language = value;
                OnPropertyChanged();
            }
        }
    }

    public event PropertyChangedEventHandler PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
}
