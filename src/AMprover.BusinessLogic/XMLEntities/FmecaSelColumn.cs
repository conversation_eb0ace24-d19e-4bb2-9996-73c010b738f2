using System.Xml.Serialization;

namespace AMprover.BusinessLogic.XMLEntities;

[XmlRoot(ElementName = "FmecaSelColumn", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
public class FmecaSelColumn
{
    [XmlElement(ElementName = "ColIndex", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string ColIndex { get; set; }
    [XmlElement(ElementName = "CustomAfter", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string CustomAfter { get; set; }
    [XmlElement(ElementName = "CustomBefore", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string CustomBefore { get; set; }
    [XmlElement(ElementName = "CustomPmo", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string CustomPmo { get; set; }
    [XmlElement(ElementName = "SelectAfter", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string SelectAfter { get; set; }
    [XmlElement(ElementName = "SelectBefore", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string SelectBefore { get; set; }
    [XmlElement(ElementName = "SelectPmo", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string SelectPmo { get; set; }
    [XmlElement(ElementName = "TipText", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public TipText TipText { get; set; }
    [XmlElement(ElementName = "ValueAfter", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string ValueAfter { get; set; }
    [XmlElement(ElementName = "ValueBefore", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string ValueBefore { get; set; }
    [XmlElement(ElementName = "ValuePmo", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string ValuePmo { get; set; }
        
    [XmlElement(ElementName = "PointsAfter", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string PointsAfter { get; set; }
    [XmlElement(ElementName = "PointsBefore", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string PointsBefore { get; set; }
    [XmlElement(ElementName = "PointsPmo", Namespace = "http://schemas.datacontract.org/2004/07/AmGlobal")]
    public string PointsPmo { get; set; }
}