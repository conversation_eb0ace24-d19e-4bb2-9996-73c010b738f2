using System;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Distributions;

/// <summary>
/// Contains calculations for Reliability and failure rate that are specific to the exponential distribution type
/// </summary>
public static class Exponential
{
    /// <summary>
    ///	Calculates and delivers the reliability as a chance
    /// </summary>
    /// <param name="rate">The (failure) rate</param>
    /// <param name="t">Time component</param>
    /// <returns>Reliability as a chance</returns>
    public static ChanceModel R(RateModel rate, HoursModel t)
    {
			return Math.Exp(-rate * t);
		}

    /// <summary>
    /// Calculates and delivers the reliability as a chance.
    /// </summary>
    /// <param name="mtbf">The mean time between failures</param>
    /// <param name="t">Time component</param>
    /// <returns>Reliability as a chance</returns>
    public static ChanceModel R(HoursModel mtbf, HoursModel t)
    {
			return Math.Exp(-(Rate(mtbf)) * t);
		}

    /// <summary>
    /// Calculates and delivers the failure rate for the provided mtbf
    /// </summary>
    /// <param name="mtbf">Mean time between failures (in hours)</param>
    /// <returns>Failure rate (per hour)</returns>
    public static RateModel Rate(HoursModel mtbf)
    {
			return (1D / mtbf.Value).Per(1);
		}
}