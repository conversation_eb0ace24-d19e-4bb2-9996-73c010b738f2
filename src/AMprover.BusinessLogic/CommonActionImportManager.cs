using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Import;
using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BusinessLogic;

public interface ICommonActionImportManager
{
    Task<AnalyzeCommonActionImportResult> AnalyzeImportFileAsync(AnalyzeCommonActionImportResult result);

    Task<ImportResult> ImportCommonActionsAsync(AnalyzeCommonActionImportResult importAnalysis);
}

public class CommonActionImportManager(
    IAssetManagementDbContextFactory dbContextFactory,
    AuthenticationStateProvider authenticationStateProvider)
    : BaseManager(dbContextFactory, authenticationStateProvider), ICommonActionImportManager
{
    // These will be loaded per method call instead of cached
    private List<LookupMxPolicy> Policies { get; set; }
    private List<LookupIntervalUnit> IntervalUnits { get; set; }
    private List<LookupInitiator> Initiators { get; set; }
    private List<LookupExecutor> Executors { get; set; }
    private List<Workpackage> WorkPackages { get; set; }
    private List<LookupUserDefined> UnitTypes { get; set; }

    private async Task RefreshRelatedItemsAsync(AssetManagementDbContext dbContext)
    {
        Policies = await dbContext.LookupMxPolicy.ToListAsync();
        IntervalUnits = await dbContext.LookupIntervalUnit.ToListAsync();
        Initiators = await dbContext.LookupInitiator.ToListAsync();
        Executors = await dbContext.LookupExecutor.ToListAsync();
        WorkPackages = await dbContext.Workpackage.ToListAsync();

        UnitTypes = await dbContext.LookupUserDefined
            .Where(x => x.UserDefinedFilter == "UnitTypes")
            .OrderBy(x => x.UserDefinedShortDescription)
            .ToListAsync();
    }

    public async Task<AnalyzeCommonActionImportResult> AnalyzeImportFileAsync(AnalyzeCommonActionImportResult result)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            await RefreshRelatedItemsAsync(dbContext);

            var dbItems = await dbContext.CommonTask.ToListAsync();
            result.ImportItems = result.Excel.GetExcelData<CommonActionImportModel>(1)
                .Select(x => x.Value.TrimStringProperties())
                .ToList();

            result.MissingStrategies = GetMissingStrategies(result.ImportItems);
            result.MissingInitiators = GetMissingInitiators(result.ImportItems);
            result.MissingExecutors = GetMissingExecutors(result.ImportItems);
            result.MissingUnitTypes = GetMissingUnitTypes(result.ImportItems);

            result.MissingIntervalUnits = GetMissingIntervalUnits(result.ImportItems);
            result.MissingWorkpackages = GetMissingWorkPackages(result.ImportItems);

            foreach (var itemToUpdate in result.ImportItems.Select(item => dbItems.FirstOrDefault(x => x.CmnTaskId == item.Id)))
            {
                if (itemToUpdate != null)
                {
                    result.ItemsToUpdate++;
                }
                else
                {
                    result.ItemsToAdd++;
                }
            }

            result.Status = ImportCommonActionStatus.StartImport;
            return result;
        });
    }

    private List<string> GetMissingStrategies(List<CommonActionImportModel> imports)
    {
        return imports
            .Where(x => !string.IsNullOrWhiteSpace(x.Strategy))
            .Select(x => x.Strategy)
            .Distinct()
            .Where(x => Policies.FirstOrDefault(y =>
                y.PolName.Trim().Equals(x.Trim(), StringComparison.OrdinalIgnoreCase)) == null)
            .ToList();
    }

    private List<string> GetMissingInitiators(List<CommonActionImportModel> imports)
    {
        return imports
            .Where(x => !string.IsNullOrWhiteSpace(x.Initiator))
            .Select(x => x.Initiator)
            .Distinct()
            .Where(x => Initiators.FirstOrDefault(y =>
                y.InitiatorName.Trim().Equals(x.Trim(), StringComparison.OrdinalIgnoreCase)) == null)
            .ToList();
    }

    private List<string> GetMissingExecutors(List<CommonActionImportModel> imports)
    {
        return imports
            .Where(x => !string.IsNullOrWhiteSpace(x.Executor))
            .Select(x => x.Executor)
            .Distinct()
            .Where(x => Executors.FirstOrDefault(y =>
                y.ExecutorName.Trim().Equals(x.Trim(), StringComparison.OrdinalIgnoreCase)) == null)
            .ToList();
    }

    private List<string> GetMissingUnitTypes(List<CommonActionImportModel> imports)
    {
        return imports
            .Where(x => !string.IsNullOrWhiteSpace(x.UnitType))
            .Select(x => x.UnitType)
            .Distinct()
            .Where(x => UnitTypes.FirstOrDefault(y =>
                y.UserDefinedShortDescription.Trim().Equals(x.Trim(), StringComparison.OrdinalIgnoreCase)) == null)
            .ToList();
    }

    private List<string> GetMissingIntervalUnits(List<CommonActionImportModel> imports)
    {
        return imports
            .Where(x => !string.IsNullOrWhiteSpace(x.IntervalUnit))
            .Select(x => x.IntervalUnit)
            .Distinct()
            .Where(x => IntervalUnits.FirstOrDefault(y =>
                y.IntUnitName.Trim().Equals(x.Trim(), StringComparison.OrdinalIgnoreCase)) == null)
            .ToList();
    }

    private List<string> GetMissingWorkPackages(List<CommonActionImportModel> imports)
    {
        return imports
            .Where(x => !string.IsNullOrWhiteSpace(x.Workpackage))
            .Select(x => x.Workpackage)
            .Distinct()
            .Where(x => WorkPackages.FirstOrDefault(y =>
                y.WpName.Trim().Equals(x.Trim(), StringComparison.OrdinalIgnoreCase)) == null)
            .ToList();
    }

    public CommonTask GetTasksFromImport(CommonActionImportModel import, CommonTask existingItem = null)
    {
        var policyId =
            Policies.FirstOrDefault(x => x.PolName.Trim().Equals(import.Strategy, StringComparison.OrdinalIgnoreCase))
                ?.PolId ?? 0;
        var intervalId = IntervalUnits.FirstOrDefault(x =>
            x.IntUnitName.Trim().Equals(import.IntervalUnit, StringComparison.OrdinalIgnoreCase))?.IntUnitId ?? 0;
        var initiatorId =
            Initiators.FirstOrDefault(x =>
                x.InitiatorName.Trim().Equals(import.Initiator, StringComparison.OrdinalIgnoreCase))?.InitiatorId ?? 0;
        var executorId =
            Executors.FirstOrDefault(x =>
                x.ExecutorName.Trim().Equals(import.Executor, StringComparison.OrdinalIgnoreCase))?.ExecutorId ?? 0;
        var workpackageId =
            WorkPackages
                .FirstOrDefault(x => x.WpName.Trim().Equals(import.Workpackage, StringComparison.OrdinalIgnoreCase))
                ?.WpId ?? 0;
        var unitTypeId = UnitTypes.FirstOrDefault(x =>
                x.UserDefinedShortDescription.Trim().Equals(import.UnitType, StringComparison.OrdinalIgnoreCase))
            ?.UserDefinedId ?? 0;

        var result = new CommonTask
        {
            CmnTaskId = existingItem?.CmnTaskId ?? 0,
            CmnTaskName = import.Name.LimitLength(60),
            CmnTaskDescription = import.Description,
            CmnTaskReferenceId = import.ReferenceId,
            CmnTaskMxPolicy = policyId,
            CmnTaskInterval = import.Interval,
            CmnTaskIntervalUnit = intervalId,
            CmnTaskIntervalModifiable = import.IntervalModifiable,
            CmnTaskInitiator = initiatorId,
            CmnTaskInitiatorModifiable = import.InitiatorModifiable,
            CmnTaskExecutor = executorId,
            CmnTaskExecutorModifiable = import.ExecutorModifiable,
            CmnTaskWorkPackage = workpackageId,
            CmnTaskWorkPackageModifiable = import.WorkPackageModifiable,
            CmnTaskType = import.Type,
            CmnTaskCosts = import.Costs,
            CmnTaskUnitType = unitTypeId,
            CmnTaskCostModifiable = import.CostsModifiable,
            CmnTaskPriorityCode = import.PriorityCode,
            CmnTaskFilterRef = import.FilterRef
        };

        return result;
    }

    public async Task<ImportResult> ImportCommonActionsAsync(AnalyzeCommonActionImportResult importAnalysis)
    {
        try
        {
            return await ExecuteInTransactionAsync(async dbContext =>
            {
                await RefreshRelatedItemsAsync(dbContext);

                await AddMissingItemsAsync(importAnalysis, dbContext);

                var dbItems = await dbContext.CommonTask.ToListAsync();
                foreach (var item in importAnalysis.ImportItems)
                {
                    var existingItem = dbItems.FirstOrDefault(x => x.CmnTaskId == item.Id);
                    if (existingItem != null)
                    {
                        var commonActionToUpdate = GetTasksFromImport(item, existingItem);
                        dbContext.Update(commonActionToUpdate);
                    }
                    else
                    {
                        dbContext.Add(GetTasksFromImport(item));
                    }
                }
                
                return new ImportResult
                {
                    ItemsAdded = importAnalysis.ItemsToAdd,
                    ItemsUpdated = importAnalysis.ItemsToUpdate,
                    Status = Enums.ImportStatus.Success,
                    Success = true
                };
            });
        }
        catch (Exception ex)
        {
            return new ImportResult
            {
                Success = false,
                ErrorMessage = ex.InnerException?.Message ?? ex.Message,
                Status = Enums.ImportStatus.Error
            };
        }
    }

    private async Task AddMissingItemsAsync(AnalyzeCommonActionImportResult importAnalysis,
        AssetManagementDbContext dbContext)
    {
        if (!importAnalysis.ContainsMissingItems())
        {
            return;
        }

        var now = DateTime.Now;
        var userName = await GetUserNameAsync();

        foreach (var strategy in importAnalysis.MissingStrategies)
        {
            dbContext.LookupMxPolicy.Add(new LookupMxPolicy
            {
                PolName = strategy,
            });
        }

        foreach (var initiator in importAnalysis.MissingInitiators)
        {
            dbContext.LookupInitiator.Add(new LookupInitiator
            {
                InitiatorName = initiator,
            });
        }

        foreach (var executor in importAnalysis.MissingExecutors)
        {
            dbContext.LookupExecutor.Add(new LookupExecutor
            {
                ExecutorName = executor,
            });
        }

        var newUnitTypes = 0;
        foreach (var unitType in importAnalysis.MissingUnitTypes)
        {
            newUnitTypes++;
            dbContext.LookupUserDefined.Add(new LookupUserDefined
            {
                UserDefinedShortDescription = unitType,
                UserDefinedLongDescription = unitType,
                UserDefinedDateModified = now,
                UserDefinedModifiedBy = userName,
                UserDefinedFilter = "UnitTypes",
                UserDefinedValue = UnitTypes.MaxBy(x => x.UserDefinedValue).UserDefinedValue + newUnitTypes
            });
        }

        // When called from ImportCommonActionsAsync, we're already in a transaction
        // and ExecuteInTransactionAsync will handle saving changes
        // But we need to refresh related items after adding the missing items

        // Save changes immediately to get IDs for the newly added items
        await dbContext.SaveChangesAsync();
        await RefreshRelatedItemsAsync(dbContext);
    }
}
