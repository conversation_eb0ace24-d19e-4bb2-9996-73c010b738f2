using System.Collections.Generic;
using AMprover.BusinessLogic.Models.RiskAnalysis;

namespace AMprover.BusinessLogic.Comparers;

public class ObjectModelComparer : IEqualityComparer<ObjectModel>
{
    public bool Equals(ObjectModel b1, ObjectModel b2)
    {
            if (ReferenceEquals(b1, b2))
                return true;

            if (b2 is null || b1 is null)
                return false;

            return b1.Id == b2.Id;
        }

    public int GetHashCode(ObjectModel b) => b.Id;
}