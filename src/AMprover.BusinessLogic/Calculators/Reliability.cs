using System;
using System.Collections.Generic;
using AMprover.BusinessLogic.Calculators.Sets;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Calculators;

public static class Reliability
{
    /// <summary>
    /// Returns a function R(t) which calculates the reliability of a component using the Weibull distribution at time t.
    /// </summary>
    /// <param name="shape">Shape parameter. This determines the 'height' of the curve. using 1 as shape equals the exponential distribution.</param>
    /// <param name="characteristicLife">Determines the location of the highest point on the curve, and is equal to the point where 62.3% of the population have failed. 
    /// When the shape parameter==1, the characteristicLife is equal to the MTBF of the component.
    /// </param>
    /// <returns></returns>
    public static Func<HoursModel, ChanceModel> Rweibull(Func<double> shape, Func<HoursModel> characteristicLife)
    {
            return t => Math.Exp(-Math.Pow(t / characteristicLife(), shape()));
        }

    /// <summary>
    /// Returns a function R(t) which calculates the reliability of a parallel system where at least k items need to work.
    /// </summary>
    /// <param name="items">Reliability function R(t) for each individual component.</param>
    /// <param name="k">number of items required.</param>
    /// <returns></returns>
    public static Func<HoursModel, ChanceModel> RSystem(IEnumerable<Func<HoursModel, ChanceModel>> items,
        Func<int> k)
    {
            return items.SymmetricState(k());
        }

    /// <summary>
    /// Decorates a reliability function by including a repair time. This function is used in previous versions of AMprover
    /// and should be considered obsolete.
    /// </summary>
    /// <param name="r"></param>
    /// <param name="mttr"></param>
    /// <returns></returns>
    private static Func<HoursModel, ChanceModel> RwithRepairtime(Func<HoursModel, ChanceModel> r,
        Func<HoursModel> mttr)
    {
            return t =>
            {
                var repairTime = mttr();
                var labda = -Math.Log(r(repairTime)) / repairTime;
                return Math.Exp(-labda * t);
            };
        }

    /// <summary>
    /// Returns a function R(t) which calculates the reliability of a parallel system where at least k items need to work.
    /// </summary>
    /// <param name="items">Reliability function R(t) for each individual component.</param>
    /// <param name="k">number of items required.</param>
    /// <returns></returns>
    public static Func<HoursModel, ChanceModel> IncludeRepairTimeAssumingExponential(this Func<HoursModel, ChanceModel> r, Func<HoursModel> mttr)
    {
            if (r == null)
            {
                return null;
            }

            if (mttr == null || mttr() == HoursModel.None)
            {
                return r;
            }

            return RwithRepairtime(r, mttr);
        }
}