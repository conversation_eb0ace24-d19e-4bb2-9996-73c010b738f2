using System;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Calculators;

public static class PeriodLabda
{
    public static Func<PeriodModel, RateModel> LabdaInPeriod(Func<HoursModel, ChanceModel> R)
    {
            return period =>
            {
                try
                {
                    if (period.From >= period.To)
                    {
                        return 1;
                    }

                    var t1 = period.From;
                    var t2 = period.To;
                    var r1 = R(t1);
                    var r2 = R(t2);

                    var term1 = r1 == 0D || double.IsNaN(r1) ? 0D : Math.Log(r1);
                    var term2 = r2 == 0D || double.IsNaN(r2) ? 0D : Math.Log(r2);

                    var term3 = term1 - term2;
                    var term4 = t2 - t1;

                    var result = term3 / term4;

                    return new RateModel(result, 1D);
                }
                catch (Exception e)
                {
                    var exc = e;
                    return 0;
                }
            };
        }
}