using System;
using AMprover.BusinessLogic.Models.Rams.Calculations;

namespace AMprover.BusinessLogic.Calculators;

public static class Mttr
{
    public static Func<HoursModel> MttrForCollection(Func<ChanceModel> availability, Func<HoursModel> mtbf) =>
        () => MttrForCollection(availability?.Invoke(), mtbf?.Invoke());


    private static HoursModel MttrForCollection(ChanceModel? availability, HoursModel? mtbf)
    {
        if (availability == null || availability == 0 || mtbf == null || mtbf == 0)
        {
            return HoursModel.None;
        }

        return (mtbf.Value - availability.Value * mtbf.Value) / availability.Value;
    }
}