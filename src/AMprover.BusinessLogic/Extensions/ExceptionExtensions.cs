using System;
using System.Collections.Generic;

namespace AMprover.BusinessLogic.Extensions;

public static class ExceptionExtensions
{
    public static List<string> ToMessages(this Exception ex)
    {
        var result = new List<string> {ex.Message};

        while(ex.InnerException != null)
        {
            result.Add(ex.InnerException.Message);
            ex = ex.InnerException;
        }

        return result;
    }
}