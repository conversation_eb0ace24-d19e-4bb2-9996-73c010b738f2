using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;

namespace AMprover.BusinessLogic.Extensions;

public static class TreeExtensions
{
    public static TreeNodeGeneric<RiskTreeObject> GetFirstRiskNode(this TreeGeneric<RiskTreeObject> tree)
    {
        var flattened = tree?.Node?.Nodes?.Flatten(n => n.Nodes).ToList();
        return flattened?.FirstOrDefault(x => x.Source.RiskId != null);
    }

    public static TreeNodeGeneric<RiskTreeObject> GetRiskTreeNode(this TreeGeneric<RiskTreeObject> tree, int riskId)
    {
        var flattened = tree?.Node?.Nodes?.Flatten(n => n.Nodes).ToList();
        return flattened?.FirstOrDefault(x => x.Source.RiskId == riskId);
    }

    public static TreeNodeGeneric<LccTreeObject> GetLccTreeNode(this TreeGeneric<LccTreeObject> tree, int lccId)
    {
        var flattened = tree?.Node?.Nodes?.Flatten(n => n.Nodes).ToList();
        return flattened?.FirstOrDefault(x => x.Source.Id == lccId);
    }

    public static TreeNodeGeneric<RamsTreeObject> GetRamsTreeNode(this TreeGeneric<RamsTreeObject> tree, int diagramId)
    {
        var flattened = tree?.Node?.Nodes?.Flatten(n => n.Nodes).ToList();
        return flattened?.FirstOrDefault(x => x.Source.Id == diagramId);
    }

    public static TreeNodeGeneric<ClusterTreeObject> GetClusterTreeNode(this TreeGeneric<ClusterTreeObject> tree, int clusterId)
    {
        var flattened = tree?.Node?.Nodes?.Flatten(n => n.Nodes).ToList();
        return flattened?.FirstOrDefault(x => x.Source.Id == clusterId);
    }

    public static IEnumerable<TSource> Flatten<TSource>(
        this TSource source,
        Func<TSource, IEnumerable<TSource>> getChildrenFunction)
    {
        return new[] { source }.Flatten(getChildrenFunction).ToList();
    }

    public static IEnumerable<T> Flatten<T>(
       this IEnumerable<T> source,
       Func<T, IEnumerable<T>> elementSelector)
    {
        var stack = new Stack<IEnumerator<T>>();
        var e = source.GetEnumerator();
        try
        {
            while (true)
            {
                while (e.MoveNext())
                {
                    var item = e.Current;
                    yield return item;
                    var elements = elementSelector(item);
                    if (elements == null) continue;
                    stack.Push(e);
                    e = elements.GetEnumerator();
                }
                if (stack.Count == 0) break;
                e.Dispose();
                e = stack.Pop();
            }
        }
        finally
        {
            e.Dispose();
            while (stack.Count != 0) stack.Pop().Dispose();
        }
    }
}
