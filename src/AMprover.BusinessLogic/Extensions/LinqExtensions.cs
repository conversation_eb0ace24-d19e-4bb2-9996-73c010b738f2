using System;
using System.Collections.Generic;

namespace AMprover.BusinessLogic.Extensions;

public static class LinqExtensions
{
    public static T FirstOrDefaultBinarySearch<T, TKey>(this IList<T> list, Func<T, TKey> keySelector, TKey key) where TKey : IComparable<TKey>
    {
            if (list.Count == 0)
                throw new InvalidOperationException("Item not found");

            var min = 0;
            var max = list.Count;
            while (min < max)
            {
                var mid = min + ((max - min) / 2);
                var midItem = list[mid];
                var midKey = keySelector(midItem);
                var comp = midKey.CompareTo(key);
                
                switch (comp)
                {
                    case < 0:
                        min = mid + 1;
                        break;
                    case > 0:
                        max = mid - 1;
                        break;
                    default:
                        return midItem;
                }
            }
            if (min == max &&
                min < list.Count &&
                keySelector(list[min]).CompareTo(key) == 0)
            {
                return list[min];
            }

            return default;
        }
}