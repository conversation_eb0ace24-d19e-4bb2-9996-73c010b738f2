using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.Data.Entities.AM;
using AMprover.Data.Infrastructure;
using AMprover.Data.Repositories;
using AutoMapper;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BusinessLogic;

public interface IRiskOrganizerManager
{
    Task<List<RiskObjectModel>> GetRiskObjectsFromScenariosV2Async(List<ScenarioModel> scenarios);

    Task<RiskObjectModel> CopyRiskObjectAsync(RiskObjectModel model);

    Task<RiskObjectModel> TryGetRiskObjectAsync(int id);

    Task<RiskObjectModel> GetRiskObjectAsync(int id);

    Task<RiskObjectModel> GetRiskObjectWithRisksAsync(int id);

    Task<RiskObjectModel> UpdateRiskObjectAsync(RiskObjectModel riskObject);

    Task UpdateScenarioOnLccAsync(RiskObjectModel riskObject);

    Task UpdateCollectionAndInstallationOnLccsAsync(RiskObjectModel riskObject);

    Task UpdateRiskAsync(RiskModel risk);

    Task UpdateRisksObjectStructureAsync(List<RiskModel> risks, int? collectionId, int? installationId);

    Task<RiskObjectModel> CreateRiskObjectAsync(NewRiskObjectSettings settings);

    List<string> GetAnalysisTypes();

    Task<Dictionary<int, string>> GetSheqItemsAsync(int id); // for SAPA Oview

    Task DeleteRiskObjectAsync(int id);

    Task<int> GetFirstRiskIdAsync(RiskObjectModel riskObject);
}

public class RiskOrganizerManager(
    IAssetManagementDbContextFactory dbContextFactory,
    AuthenticationStateProvider authenticationStateProvider,
    IMapper mapper,
    IAssetManagementBaseRepository<Fmeca> fmecaRepository)
    : BaseManager(dbContextFactory, authenticationStateProvider), IRiskOrganizerManager
{

    public async Task<List<RiskObjectModel>> GetRiskObjectsFromScenariosV2Async(List<ScenarioModel> scenarios)
    {
        // Early exit for empty scenarios
        if (scenarios == null || scenarios.Count == 0)
            return [];

        return await ExecuteWithContextAsync(async dbContext =>
        {
            // Extract the scenario IDs from the ScenarioModel objects
            var scenarioIds = scenarios.Select(y => y.Id).ToArray();

            // Create a list to hold all risk objects
            var riskObjectsDb = new List<RiskObject>();

            // Use batching to reduce the number of queries
            const int batchSize = 50;
            for (var i = 0; i < scenarioIds.Length; i += batchSize)
            {
                var currentBatch = scenarioIds.Skip(i).Take(batchSize).ToArray();

                // Process each scenario ID in the current batch
                foreach (var scenarioId in currentBatch)
                {
                    var partialResults = await dbContext.RiskObject
                        .Where(x => x.RiskObjScenarioId == scenarioId)
                        .Include(x => x.RiskObjParentObject)
                        .Include(x => x.RiskObjScenario)
                        .Include(x => x.RiskObjObject)
                        .Include(x => x.RiskObjFmeca)
                        .Include(x => x.RiskObjStatusNavigation)
                        .Include(x => x.RiskObjDepartment)
                        .AsSplitQuery() // Optimize large queries with multiple includes
                        .ToListAsync();

                    riskObjectsDb.AddRange(partialResults);
                }
            }

            // Sort the combined results by name
            riskObjectsDb = riskObjectsDb.OrderBy(x => x.RiskObjName).ToList();

            // Remove Fmeca to prevent XML deserialization
            foreach (var ro in riskObjectsDb)
            {
                ro.RiskObjFmeca = null;
            }

            var riskObjects = mapper.Map<List<RiskObjectModel>>(riskObjectsDb);

            // Separate the risk objects by analysis type for more efficient processing
            var pmoRiskObjects = riskObjects.Where(ro => ro.AnalysisType == "PMO").ToList();
            var nonPmoRiskObjects = riskObjects.Where(ro => ro.AnalysisType != "PMO").ToList();

            // Process non-PMO risk objects (LCC data)
            if (nonPmoRiskObjects.Count != 0)
            {
                var nonPmoRiskObjectIds = nonPmoRiskObjects.Select(ro => ro.Id).ToArray();
                var lccDict = new Dictionary<int, List<Lcc>>();

                // Batch LCC queries
                for (var i = 0; i < nonPmoRiskObjectIds.Length; i += batchSize)
                {
                    var currentBatch = nonPmoRiskObjectIds.Skip(i).Take(batchSize).ToArray();

                    foreach (var riskObjectId in currentBatch)
                    {
                        var lccsForRiskObject = await dbContext.Lcc
                            .Where(x => x.LccRiskObject == riskObjectId
                                        && x.LccPartOf == null
                                        && x.LccChildObject2 == null
                                        && x.LccChildObject3 == null
                                        && x.LccChildObject4 == null)
                            .ToListAsync();

                        if (lccsForRiskObject.Count != 0)
                        {
                            lccDict[riskObjectId] = lccsForRiskObject;
                        }
                    }
                }

                // Apply LCC data to risk objects
                foreach (var ro in nonPmoRiskObjects)
                {
                    if (!lccDict.TryGetValue(ro.Id, out var lccsForRiskObject) || lccsForRiskObject.Count == 0)
                        continue;

                    var lcc = lccsForRiskObject.FirstOrDefault();

                    if (lcc == null)
                        continue;

                    ro.Lcc = mapper.Map<LccModel>(lcc, opt =>
                        opt.AfterMap((_, dest) => dest.SkipGenerateGraph = true));

                    ro.Aec = lcc.LccAec;
                    ro.CorrectiveCostLcc = lcc.LccOptimalAverageCorrectiveCost;
                    ro.PreventiveCostLcc = lcc.LccOptimalAveragePreventiveCost;
                    ro.LccPotential = lcc.LccPotential;
                }
            }

            // Process PMO risk objects (MRB data)
            if (pmoRiskObjects.Count != 0)
            {
                var pmoRiskObjectIds = pmoRiskObjects.Select(ro => ro.Id).ToArray();
                var mrbDict = new Dictionary<int, List<Mrb>>();

                // Batch MRB queries
                for (var i = 0; i < pmoRiskObjectIds.Length; i += batchSize)
                {
                    var currentBatch = pmoRiskObjectIds.Skip(i).Take(batchSize).ToArray();

                    foreach (var riskObjectId in currentBatch)
                    {
                        var mrbsForRiskObject = await dbContext.Mrb
                            .Where(x => x.MrbRiskObject == riskObjectId)
                            .ToListAsync();

                        if (mrbsForRiskObject.Count != 0)
                        {
                            mrbDict[riskObjectId] = mrbsForRiskObject;
                        }
                    }
                }

                // Apply MRB data to risk objects
                foreach (var ro in pmoRiskObjects)
                {
                    if (!mrbDict.TryGetValue(ro.Id, out var mrbsForRiskObject) || mrbsForRiskObject.Count == 0)
                        continue;

                    ro.DirectCorrectiveCostAfter =
                        mrbsForRiskObject.Sum(x => x.MrbDirectCostAfter / (x.MrbMtbfAfter ?? 1000000000));
                    ro.CorrectiveCostAfter = mrbsForRiskObject.Sum(x => x.MrbRiskAfter);
                    ro.PreventiveCostAfter = mrbsForRiskObject.Sum(x => x.MrbActionCosts);
                    ro.DirectCorrectiveCostPmo =
                        mrbsForRiskObject.Sum(x => x.MrbDirectCostPmo / (x.MrbMtbfPmo ?? 1000000000));
                    ro.CorrectiveCostPmo = mrbsForRiskObject.Sum(x => x.MrbRiskPmo);
                    ro.PreventiveCostPmo = mrbsForRiskObject.Sum(x => x.MrbCurrentActionCosts);
                }
            }

            return riskObjects;
        });
    }

    public async Task<RiskObjectModel> TryGetRiskObjectAsync(int id)
    {
        try
        {
            return await GetRiskObjectAsync(id);
        }
        catch (KeyNotFoundException)
        {
            return null;
        }
    }

    public async Task<RiskObjectModel> GetRiskObjectAsync(int id)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var riskObject = await dbContext.Set<RiskObject>()
                .Include(x => x.Risks).ThenInclude(x => x.Spares)
                .Include(x => x.Risks).ThenInclude(x => x.Tasks)
                .Include(x => x.RiskObjScenario)
                .Include(x => x.RiskObjFmeca)
                .Include(x => x.RiskObjObject)
                .Include(x => x.RiskObjParentObject)
                .FirstOrDefaultAsync(x => x.RiskObjId == id);

            if (riskObject == null)
            {
                throw new KeyNotFoundException();
            }

            return mapper.Map<RiskObjectModel>(riskObject);
        });
    }

    public async Task<RiskObjectModel> GetRiskObjectWithRisksAsync(int id)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var riskObject = await dbContext.Set<RiskObject>()
                                 .Include(x => x.RiskObjScenario)
                                 .Include(x => x.RiskObjFmeca)
                                 .Include(x => x.RiskObjObject)
                                 .Include(x => x.RiskObjDepartment)
                                 .Include(x => x.RiskObjParentObject)
                                 .Include(x => x.Risks).ThenInclude(x => x.Spares)
                                 .Include(x => x.Risks).ThenInclude(x => x.ChildObject)
                                 .Include(x => x.Risks).ThenInclude(x => x.Installation)
                                 .Include(x => x.Risks).ThenInclude(x => x.System)
                                 .Include(x => x.Risks).ThenInclude(x => x.Component)
                                 .Include(x => x.Risks).ThenInclude(x => x.Assembly)
                                 .Include(x => x.Risks).ThenInclude(x => x.Tasks)
                                 .ThenInclude(x => x.TskIntervalUnitNavigation)
                                 .Include(x => x.Risks).ThenInclude(x => x.Tasks).ThenInclude(x => x.TskMxPolicyNavigation)
                                 .Include(x => x.Risks).ThenInclude(x => x.Tasks).ThenInclude(x => x.TskInitiatorNavigation)
                                 .Include(x => x.Risks).ThenInclude(x => x.Tasks).ThenInclude(x => x.TskExecutorNavigation)
                                 .FirstOrDefaultAsync(x => x.RiskObjId == id)
                             ?? throw new KeyNotFoundException();

            var result = mapper.Map<RiskObjectModel>(riskObject);

            if (riskObject.RiskObjAnalyseType != "PMO")
            {
                var lcc = await dbContext.Lcc.FirstOrDefaultAsync(x =>
                    x.LccRiskObject == result.Id
                    && x.LccChildObject1 == result.ObjectId);

                result.Lcc = mapper.Map<LccModel>(lcc);
                result.Aec = lcc?.LccAec;
                result.CorrectiveCostLcc = lcc?.LccOptimalAverageCorrectiveCost;
                result.PreventiveCostLcc = lcc?.LccOptimalAveragePreventiveCost;
                result.LccPotential = lcc?.LccPotential;
            }
            else
            {
                var risksDb = await dbContext.Mrb
                    .Where(x => x.MrbRiskObject == result.Id)
                    .ToListAsync();

                result.DirectCorrectiveCostAfter = risksDb.Sum(x => x.MrbDirectCostAfter / (x.MrbMtbfAfter ?? 1000000000));
                result.CorrectiveCostAfter = risksDb.Sum(x => x.MrbRiskAfter);
                result.PreventiveCostAfter = risksDb.Sum(x => x.MrbActionCosts);
                result.DirectCorrectiveCostPmo = risksDb.Sum(x => x.MrbDirectCostPmo / (x.MrbMtbfPmo ?? 1000000000));
                result.CorrectiveCostPmo = risksDb.Sum(x => x.MrbRiskPmo);
                result.PreventiveCostPmo = risksDb.Sum(x => x.MrbCurrentActionCosts);
            }

            return result;
        });
    }

    public async Task<RiskObjectModel> CopyRiskObjectAsync(RiskObjectModel model)
    {
        var clonedRiskObject = await ExecuteWithContextAsync(async dbContext =>
        {
            //Get item with all related children
            var riskObject = await dbContext.RiskObject
                .Include(x => x.Risks).ThenInclude(x => x.Spares)
                .Include(x => x.Risks).ThenInclude(x => x.Tasks).ThenInclude(x => x.TskParent)
                .Include(x => x.RiskObjScenario)
                .Include(x => x.RiskObjFmeca)
                .Include(x => x.RiskObjObject)
                .FirstOrDefaultAsync(x => x.RiskObjId == model.Id);

            if (riskObject == null)
                return null;

            var clonedObj = mapper.Map<RiskObjectModel>(riskObject).CopyAsNew<RiskObjectModel>();

            //Maxlength == 50 so maxlength of name without copy should be 43
            if (clonedObj.Name.Length > 43)
                clonedObj.Name = clonedObj.Name[..43];

            clonedObj.Name += " (copy)";

            foreach (var risk in clonedObj.Risks)
            {
                // Currently in ClonedRiskObject the TaskParents exists twice.
                // once as a normal Task, and once as a parent. These are not the same instance.
                // If we save this to the DB, there would be 2 instances.
                // instead, fix the references and the saving will act properly.
                risk.Tasks?.ToList().ForEach(x =>
                {
                    if (x.Parent != null)
                    {
                        x.Parent = risk.Tasks.FirstOrDefault(y => y.Id == x.Parent.Id);
                    }
                });

                risk.Id = 0;
                risk.Spares?.ToList().ForEach(x => x.Id = 0);
                risk.Tasks?.ToList().ForEach(x => x.Id = 0);
            }

            return clonedObj;
        });

        if (clonedRiskObject == null)
            return null;

        return await UpdateRiskObjectAsync(clonedRiskObject);
    }

    public async Task<RiskObjectModel> UpdateRiskObjectAsync(RiskObjectModel riskObject)
    {
        if (riskObject.Fmeca == null)
            throw new NotSupportedException("RiskObject does not contain Fmeca");

        riskObject.ModifiedBy = (await GetUserNameAsync()).Truncate(30);
        riskObject.DateModified = DateTime.Now;

        return await ExecuteWithSaveAsync(
            async dbContext =>
            {
                if (riskObject.Id == 0)
                {
                    var dbModel = mapper.Map<RiskObject>(riskObject);
                    var dbResult = dbContext.Add(dbModel);
                    return dbResult.Entity;
                }

                var model = mapper.Map<RiskObject>(riskObject);
                var result = dbContext.Update(model);
                return result.Entity;
            },
            async (_, entity) => await GetRiskObjectAsync(entity.RiskObjId));
    }

    public async Task UpdateScenarioOnLccAsync(RiskObjectModel riskObject)
    {
        if (riskObject.Lcc == null)
            return;

        await ExecuteWithSaveAsync(async dbContext =>
        {
            var lcc = await dbContext.Lcc.FirstOrDefaultAsync(x => x.LccId == riskObject.Lcc.Id);

            if (lcc != null)
            {
                lcc.LccScenarioId = riskObject.ScenarioId;
                dbContext.Update(lcc);
            }
        });
    }

    public async Task UpdateCollectionAndInstallationOnLccsAsync(RiskObjectModel riskObject)
    {
        if (riskObject.Lcc == null)
            return;

        await ExecuteWithSaveAsync(async dbContext =>
        {
            var lccs = await dbContext.Lcc.Where(x => x.LccRiskObject == riskObject.Id).ToListAsync();
            lccs.ForEach(x =>
            {
                x.LccChildObject = riskObject.ParentObjectId;
                x.LccChildObject1 = riskObject.ObjectId;
            });

            dbContext.UpdateRange(lccs);
        });
    }

    public async Task UpdateRiskAsync(RiskModel risk)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            dbContext.Update(mapper.Map<Mrb>(risk));
        });
    }

    public async Task UpdateRisksObjectStructureAsync(List<RiskModel> risks, int? collectionId, int? installationId)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            var dbRisks = await dbContext.Mrb.Where(x => risks.Select(y => y.Id).Contains(x.Mrbid)).ToListAsync();

            foreach (var dbRisk in dbRisks)
            {
                dbRisk.MrbChildObject = collectionId;
                dbRisk.MrbChildObject1 = installationId;
            }

            dbContext.Mrb.UpdateRange(dbRisks);
        });
    }


    public async Task<RiskObjectModel> CreateRiskObjectAsync(NewRiskObjectSettings settings)
    {
        settings.ShortenStringFields();

        return await ExecuteWithSaveAsync<RiskObject, RiskObjectModel>(
            async dbContext =>
            {
                var result = dbContext.RiskObject.Add(mapper.Map<RiskObject>(settings));
                return result.Entity;
            },
            async (context, entity) =>
            {
                // Map the entity after saving changes
                return mapper.Map<RiskObjectModel>(entity);
            });
    }

    public List<string> GetAnalysisTypes()
    {
        return ["ECO", "HAZOP", "PMO", "RCM", "SAPA", "CSIR"];
    }

    public async Task<Dictionary<int, string>> GetSheqItemsAsync(int riskObjectId)
    {
        return await ExecuteWithContextAsync(async dbContext =>
        {
            var riskObject = await dbContext.Set<RiskObject>()
                .Include(x => x.RiskObjFmeca)
                .FirstOrDefaultAsync(x => x.RiskObjId == riskObjectId);

            if (riskObject == null)
                return new Dictionary<int, string>();

            var fmeca = await fmecaRepository.GetByIdAsync(riskObject.RiskObjFmecaId);
            if (fmeca == null) throw new KeyNotFoundException();
            var template = mapper.Map<RiskMatrixTemplateModel>(fmeca);
            template.BuildMatrix();

            var sheqItem = new string[7];
            //decimal[] costItem = new decimal[7];
            var noFmecaRows = 0;

            int.TryParse(template.Data.FmecaMainGrid.EffectColumns, out var effectColumnsCount);

            for (var index = 0; index < effectColumnsCount; index++)
            {
                try
                {
                    var mainColumn = template.MainGrid.TableColumns[index];
                    var subgridTemplate = template.SubGrids.FirstOrDefault(x => x.ColumnId == index);

                    if (mainColumn.HasSubColumns && subgridTemplate != null)
                    {
                        var noSubFmecaColumns = subgridTemplate.TableColumns.Count;
                        noFmecaRows = subgridTemplate.TableContent.Count;
                        var parentIndex = index;

                        for (var subindex = 0; subindex < noSubFmecaColumns; subindex++)
                        {
                            if (subgridTemplate.TableColumns[subindex].Type == 4) //SHE column 
                            {
                                if (subgridTemplate.TableContent[1].Cells[subindex].Value.ToDecimal() >
                                    subgridTemplate.TableContent[2].Cells[subindex].Value.ToDecimal())
                                {
                                    // Matrix is reversed (high value on top)
                                    for (var subRow = 0; subRow < noFmecaRows; subRow++)
                                    {
                                        try
                                        {
                                            if (subRow == 0)
                                                sheqItem[subRow] = subgridTemplate.TableContent[subRow].Cells[subindex]
                                                    .Description; // Set Title SHEQ Column
                                            else
                                                sheqItem[subRow] = template.MainGrid.TableContent[subRow]
                                                    .Cells[parentIndex]
                                                    .Description;
                                            //costItem[subRow] = (decimal)template.MainGrid.TableContent[subRow].Cells[parentIndex].Value.ToDecimal();
                                        }
                                        catch
                                        {
                                            // apparently index can be greater, for deleted columns? 
                                        }
                                    }
                                }
                                else
                                {
                                    // Matrix is normal (high value on bottom)
                                    for (var subRow = noFmecaRows - 1; subRow >= 0; subRow--)
                                    {
                                        try
                                        {
                                            if (subRow == 0)
                                                sheqItem[subRow] = subgridTemplate.TableContent[subRow].Cells[subindex]
                                                    .Description; // Set Title SHEQ Column
                                            else
                                                sheqItem[subRow] = template.MainGrid.TableContent[subRow]
                                                    .Cells[parentIndex]
                                                    .Description;
                                            //costItem[subRow] = (decimal)template.MainGrid.TableContent[subRow].Cells[parentIndex].Value.ToDecimal();
                                        }
                                        catch
                                        {
                                            // apparently index can be greater, for deleted columns? 
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                catch
                {
                    // apparently index can be greater, for deleted columns? 
                }
            }

            //To Do: kan mooier, Dennis, Thom?
            try
            {
                if (noFmecaRows == 1)
                    return new Dictionary<int, string>
                        {{0, sheqItem[0]}};
                if (noFmecaRows == 2)
                    return new Dictionary<int, string>
                    {
                        {0, sheqItem[0]},
                        {1, sheqItem[1]}
                    };
                if (noFmecaRows == 3)
                    return new Dictionary<int, string>
                    {
                        {0, sheqItem[0]},
                        {1, sheqItem[1]},
                        {2, sheqItem[2]}
                    };
                if (noFmecaRows == 4)
                    return new Dictionary<int, string>
                    {
                        {0, sheqItem[0]},
                        {1, sheqItem[1]},
                        {2, sheqItem[2]},
                        {3, sheqItem[3]}
                    };
                if (noFmecaRows == 5)
                    return new Dictionary<int, string>
                    {
                        {0, sheqItem[0]},
                        {1, sheqItem[1]},
                        {2, sheqItem[2]},
                        {3, sheqItem[3]},
                        {4, sheqItem[4]}
                    };
                if (noFmecaRows == 6)
                    return new Dictionary<int, string>
                    {
                        {0, sheqItem[0]},
                        {1, sheqItem[1]},
                        {2, sheqItem[2]},
                        {3, sheqItem[3]},
                        {4, sheqItem[4]},
                        {5, sheqItem[5]}
                    };
                if (noFmecaRows == 7)
                    return new Dictionary<int, string>
                    {
                        {0, sheqItem[0]},
                        {1, sheqItem[1]},
                        {2, sheqItem[2]},
                        {3, sheqItem[3]},
                        {4, sheqItem[4]},
                        {5, sheqItem[5]},
                        {6, sheqItem[6]}
                    };
                if (noFmecaRows == 8)
                    return new Dictionary<int, string>
                    {
                        {0, sheqItem[0]},
                        {1, sheqItem[1]},
                        {2, sheqItem[2]},
                        {3, sheqItem[3]},
                        {4, sheqItem[4]},
                        {5, sheqItem[5]},
                        {6, sheqItem[6]},
                        {7, sheqItem[7]}
                    };
            }
            catch (Exception)
            {
                return null;
            }

            return null;
        });
    }

    public async Task DeleteRiskObjectAsync(int id)
    {
        await ExecuteWithSaveAsync(async dbContext =>
        {
            var riskObject = await dbContext.RiskObject
                .Include(x => x.Risks).ThenInclude(x => x.Spares)
                .Include(x => x.Risks).ThenInclude(x => x.Tasks)
                .Include(x => x.Risks).ThenInclude(x => x.PickSis)
                .Include(x => x.LccItems)
                .Include(x => x.SiLinkFilters)
                .AsSplitQuery()
                .FirstOrDefaultAsync(x => x.RiskObjId == id);

            if (riskObject != null)
            {
                // Deleting Filters and FilterSelectionList items is not possible with Entity Framework Cascade behaviour
                var filterIds = riskObject.SiLinkFilters.Select(x => x.SifFilterId).ToArray();
                var filters = dbContext.Filters
                    .Include(x => x.FiltersSelectionList)
                    .Where(x => filterIds.Contains(x.SqlSelId));
                dbContext.Filters.RemoveRange(filters);

                dbContext.RiskObject.Remove(riskObject);
            }
        });
    }

    public async Task<int> GetFirstRiskIdAsync(RiskObjectModel riskObject)
    {
        return await ExecuteWithContextAsync(async dbContext =>
            (await dbContext.Mrb.FirstOrDefaultAsync(x => x.MrbRiskObject == riskObject.Id))?.Mrbid ?? 0);
    }
}
